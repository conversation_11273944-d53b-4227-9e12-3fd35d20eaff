# 🔍 Análise de Melhorias — Baseada no PROMPTS_DOCUMENTATION.md

> Data: 2025-09-04  
> Sistema: Atendimento Inteligente (Trinks)  
> Objetivo: Reduzir perda de fluxo, garantir reset de estado pós‑ação, melhorar consistência de horários/profissionais e reduzir alucinações, deixando a conversa mais fluida e humana.

---

## 📌 Resumo Executivo

Sintomas reportados em produção:
- Perda de fluxo em conversas complexas (ex.: pede para agendar de novo após concluir; confunde cancelamento x reagendamento)
- Estado não é resetado após executar ações (agendar/cancelar)
- Inconsistências na apresentação de horários e profissionais; às vezes mistura ou omite dados
- A IA nem sempre recebe todo o contexto necessário para responder de forma inteligente e humana
- Edge cases funcionam melhor quando a conversa entra em um fluxo de intenção (LangGraph)

Causas-raiz identificadas vs PROMPTS_DOCUMENTATION.md:
1) Falta de injeção sistemática do estado atual e das ações concluídas nos prompts (doc menciona histórico, mas não “estado operacional”).
2) Reset de estado pós‑ação incompleto/ausente; `schedulingData` e `currentStage` permanecem com valores antigos.
3) Disponibilidade e profissionais gerados/formatados sem “fonte de verdade” única e sem checagens de compatibilidade serviço→profissional.
4) Uso parcial do LangGraph: grafos ajudam nos edge cases, mas nem sempre são roteados como primeira opção.
5) Guardrails anti‑alucinação existem no texto, porém faltam chaves técnicas (“STRICT DATA MODE”, validação pós‑geração, temperaturas) nos pontos críticos.

Resultado esperado após as melhorias:
- Fluxos lineares e previsíveis (agendar/cancelar/consultar) com resets claros
- Respostas mais curtas, humanas, consistentes, sem “vou verificar” e sem horários inventados
- IA sempre ciente do estado, do que já foi feito e do que se espera do cliente
- Menos loops e menos mudanças de intenção acidentais

---

## 🔎 Gaps entre a Documentação e o que é Entregue ao Modelo

A documentação já define regras fortes (“não inventar horários”, “sem ‘vou verificar’”, uso de histórico, mapeamento profissional→serviços). Porém, faltam (ou estão inconsistentes) no prompt final gerado:
- Contexto de estado: `currentStage`, `executedActions`, `pendingClarifications`, `expectedResponse`.
- Sinalização explícita de “usar apenas os dados que estão nesta seção” para disponibilidade e profissionais.
- Evidências de compatibilidade serviço→profissional e filtro por serviço antes de exibir horários.
- Regras condicionais por estado (ex.: se ação concluída, não pedir confirmação novamente).

---

## ✅ Melhorias Prioritárias

### 1) Estado da Conversa — Reset, Memória e Regras

- Após sucesso em agendar/cancelar:
  - Definir `conversation.currentStage = 'completed'`
  - Gravar em `conversation.executedActions.push({ type, details, timestamp })`
  - Limpar `conversation.schedulingData` e campos transitórios
  - Após pequena pausa (2–3s) ou na próxima mensagem, transicionar para `greeting`
- Padronizar estágios: `customer_identification → service_selection → professional_selection → availability_check → schedule_confirmation → completed → greeting`
- Evitar loops: se `lastCompletedAction.type === 'scheduling'` nos últimos N minutos, não perguntar “Posso confirmar?” novamente; oferecer ajuda complementar.

### 2) Enriquecer TODOS os Prompts com Estado Operacional

Adicionar uma seção obrigatória no prompt composto, antes das instruções finais:

```
## 🎯 CONTEXTO ATUAL DA CONVERSA
<conversation_state>
Estágio: {{currentStage}}
Ações concluídas: {{executedActionsCompact}}
Dados confirmados: {{confirmedData}}
Tentativas atual: {{attemptCount}}
Último erro: {{lastError}}
</conversation_state>

## ⚠️ REGRAS PELO ESTADO
{{state_specific_rules}}
```

- Popular `state_specific_rules` dinamicamente, por exemplo:
  - `awaiting_appointment_confirmation`: “Você JÁ pediu confirmação; aguarde resposta objetiva; não mude de assunto; não repita.”
  - `completed`: “Ação concluída; não peça para repetir; ofereça ajuda adicional.”

### 3) Disponibilidade e Profissionais — Fonte Única e à Prova de Alucinação

- Construir `availabilityContext` SIEMPRE com:
  - `targetDate`, `professionalId/name` (quando específico), `availableSlots` (lista crua), `formattedSlots` (agrupado manhã/tarde/noite), `hasAvailability`, `mustUseData: true`
  - `serviceFilterApplied: true` e `compatibleProfessionals` (apenas quem realiza o serviço escolhido)
- No Prompt, instruir: “Só mencione horários EXPLÍCITOS em availableSlots/formattedSlots. Se vazio, diga ‘sem horários’ para aquele recorte.”
- Usar timezone brasileiro e normalizar horários (sem segundos), garantindo consistência.
- Para profissional específico, NUNCA misturar slots de outros profissionais.
- Para consulta geral, agrupar por período e destacar 3–5 sugestões máximas.

### 4) LangGraph como Orquestrador por Padrão

- Garantir que toda mudança relevante de contexto/intenção passe pelo grafo de intenção (IntentGraphAdapter) e, a partir dele, acione o `SchedulingGraph`/`CancellationGraph`.
- Adicionar nó final de “cleanup/reset” após sucesso, que atualiza `currentStage`, limpa dados temporários e registra `executedActions`.
- Introduzir um nó de “repair/clarification” para mensagens ambíguas, reduzindo mudança de intenção acidental em conversas complexas.

### 5) Extrações Estruturadas e Temperaturas Baixas

- Em prompts de extração (confirmação de agendamento/cancelamento):
  - `temperature: 0.1–0.2`
  - 2 tentativas com validação de JSON; fallback controlado
  - Campos obrigatórios e enums estritos; log de inconsistências
- Confirmar modificações versus rejeição versus outra intenção de forma determinística.

### 6) Guardrails Técnicos Anti‑Alucinação

- “STRICT DATA MODE” nas seções de disponibilidade e de agendamento: responder apenas com dados presentes.
- Validação pós‑geração: rejeitar respostas com frases proibidas e/ou que citem horários não presentes em `availableSlots`.
- Reduzir `temperature` de templates críticos (availability, confirmação) e limitar `maxTokens` para manter respostas curtas.

### 7) Humanização com Consistência

- Já há diretrizes na base-identity; reforçar nos templates finais: 1–2 frases, português brasileiro, primeiro nome, empatia pontual, sem robôs (“vou verificar”).
- Reforçar que, se o cliente já tem agendamentos futuros, mencionar isso naturalmente na saudação e evitar conflito de horários.

---

## 🧩 Propostas de Ajustes por Arquivo

- server/config/prompt-templates.js
  - Expandir `variables` dos templates para incluir: `currentStage`, `executedActionsCompact`, `confirmedData`, `attemptCount`, `state_specific_rules`.
  - Baixar `temperature` dos templates de confirmação e disponibilidade.
  - Incluir variantes v2 para `cancellation_confirm` e `availability_suggestion` com regras condicionadas pelo estado.

- server/services/PromptComposer.js
  - Injetar a seção “CONTEXTO ATUAL DA CONVERSA” no `finalPrompt` com base no `conversation` real.
  - Garantir `availabilityContext` com `serviceFilterApplied`, `compatibleProfessionals` e `mustUseData: true`.
  - Após `appointmentResult`/`cancellationResult.success`, sinalizar claramente `completed` e orientar a resposta a não repetir confirmação.
  - Validar resposta: checar horários citados ∈ `availableSlots` quando houver.

- server/langgraph/graphs/SchedulingGraph.js (e cancelamento equivalente)
  - Adicionar nó “cleanup” e transição automática `completed → greeting` (com cooldown curto) + limpeza de `schedulingData`.
  - Registrar `executedActions` no state manager.

- server/services/ai.js
  - Após sucesso em agendamento/cancelamento: atualizar `conversation.currentStage`, limpar dados e gravar `lastCompletedAction`.
  - Extrações: temperatura baixa, retries, validação de schema rígida.

- Guardrails/Validações
  - Rejeitar respostas com horários não presentes no contexto; logar e regenerar.
  - Proibir “vou verificar/checar/aguarde” (já descrito) com verificação automatizada.

---

## 🧪 Plano de Testes

- Unitários
  - Validador de resposta: detecta menção a horário ausente e reprova.
  - Funções de extração: garantem JSON válido e enums corretos.
  - Formatação de disponibilidade: cobre casos sem slots, um profissional, múltiplos profissionais, filtragem por serviço.

- Integração
  - Fluxo completo de agendamento (com e sem profissional específico), incluindo reset para `greeting` após sucesso.
  - Fluxo de cancelamento com múltiplos agendamentos futuros (confirma qual, confirma motivo, executa, reseta estado).
  - Mudança de intenção no meio do fluxo (agendar → cancelar e vice‑versa) via LangGraph.

- E2E (cenários reais)
  - “Agendar corte amanhã 15h com João” → confirmar → não repetir → oferecer ajuda.
  - “Quero cancelar o de amanhã” → identificar, confirmar, cancelar, não entrar em novo agendamento automaticamente.
  - “Tem horário hoje?” com poucos/muitos slots → sugerir 2–5 horários agrupados por período sem listar tudo.

Métricas de sucesso (pós‑deploy):
- Loops de confirmação < 5%
- Alucinações de horário < 5%
- Respostas com frases proibidas: ~0
- CSAT > 85%

---

## 🛠️ Próximos Passos (Implementação em 2 Fases)

Fase 1 — Correções estruturais (1–2 dias)
1. Reset pós‑ação (estado + memória + limpeza de dados)
2. Injeção do bloco de estado operacional em todos os prompts
3. Strict data mode para disponibilidade; baixar temperaturas críticas

Fase 2 — Orquestração e robustez (2–3 dias)
4. LangGraph como default + nó de cleanup
5. Validador de resposta (horários ∈ contexto; frases proibidas)
6. Retries e validação rígida nos prompts de extração

---

## 📎 Apêndice — Exemplos de Blocos para Reutilização

Bloco de estado operacional (para PromptComposer):

```
const stateBlock = `
## 🎯 CONTEXTO ATUAL DA CONVERSA
<conversation_state>
Estágio: ${conversation.currentStage}
Ações concluídas: ${formatActions(conversation.executedActions)}
Dados confirmados: ${formatConfirmed(conversation)}
Tentativas atual: ${conversation.attemptCount || 0}
Último erro: ${conversation.lastError || '—'}
</conversation_state>

## ⚠️ REGRAS PELO ESTADO
${buildStateRules(conversation.currentStage)}
`;
```

Disponibilidade com “strict data mode” (trecho de instrução):

```
## 📅 DISPONIBILIDADE
<strict_data_mode>true</strict_data_mode>
- Data: {{targetDate}}
- Profissional: {{professionalName}}
- Horários disponíveis (fonte única): {{formattedSlots}}
REGRAS: Cite SOMENTE horários acima. Se vazio, diga que não há horários nesse recorte.
```

---

## ✔️ Conclusão

As melhorias propostas alinham o sistema às regras já documentadas, adicionando camadas técnicas para fazer as regras valerem na prática (estado explícito, strict data mode, validação pós‑geração, LangGraph por padrão). Isso deve resolver os loops de confirmação, melhorar a consistência de horários/profissionais e tornar a conversa mais humana, objetiva e sem alucinações.

