# 🚀 Deploy e Monitoramento - Sistema de Atendimento Inteligente

## 🔄 Workflow Git

### Comandos Básicos para Contribuição
```bash
# Criar nova feature
git checkout -b feature/nova-funcionalidade
git add .
git commit -m "feat: descrição da mudança"
git push origin feature/nova-funcionalidade

# Merge para main
git checkout main
git pull origin main
git merge feature/nova-funcionalidade
git push origin main
```

### Padrões de Commit
```bash
feat: nova funcionalidade
fix: correção de bug
refactor: refatoração de código
docs: atualização de documentação
test: adição/modificação de testes
style: formatação, ponto e vírgula, etc
perf: melhoria de performance
chore: tarefas de build, configuração, etc
```

### Processo de Release
1. **Desenvolvimento**: Feature branches individuais
2. **Code Review**: Pull requests obrigatórios para main
3. **Testes**: CI/CD roda testes automaticamente
4. **Deploy**: Merge na main dispara deploy automático
5. **Monitoramento**: Acompanhar logs e métricas pós-deploy

## 📊 Sistema de Logs

### Estrutura de Logging (Winston)
```javascript
// Níveis de log
error: 0,    // Erros críticos
warn: 1,     // Avisos importantes  
info: 2,     // Informações gerais
http: 3,     // Requests HTTP
verbose: 4,  // Detalhes verbose
debug: 5,    // Debug detalhado
silly: 6     // Tudo
```

### Arquivos de Log
```
logs/
├── error.log        # Apenas erros
├── combined.log     # Todos os níveis
├── api.log         # Chamadas para APIs externas
├── socket.log      # Eventos WebSocket
└── whatsapp.log    # Integração WhatsApp
```

### Rotação de Logs
- **Frequência**: Diária
- **Retenção**: 30 dias
- **Compressão**: Gzip automático
- **Tamanho máximo**: 50MB por arquivo

### Monitoramento de Logs
```bash
# Acompanhar logs em tempo real
tail -f logs/combined.log

# Filtrar apenas erros
tail -f logs/error.log

# Buscar por termo específico
grep -i "anthropic" logs/combined.log

# Logs das últimas 24h
find logs/ -name "*.log" -mtime -1
```

## 🔧 Performance e Escalabilidade

### Cache Strategy
- **Redis**: Dados frequentes (disponibilidade, profissionais)
- **Memory**: Fallback quando Redis indisponível
- **PostgreSQL**: Cache semântico de templates
- **TTL**: Configurável por tipo de dado

### Otimizações Implementadas
```javascript
// Compressão de responses
app.use(compression());

// Lazy loading de componentes
const ComponenteGrande = lazy(() => import('./ComponenteGrande'));

// Query optimization
const profissionais = await cache.get('profissionais', () => {
  return api.trinks.getProfissionais();
}, '5m');
```

### Métricas de Performance
- **Response Time API**: < 2s
- **Cache Hit Rate**: > 50%
- **WebSocket Latency**: < 100ms
- **Memory Usage**: < 512MB
- **CPU Usage**: < 70%

## 🏗️ Ambiente de Produção

### Configuração do Servidor
```env
NODE_ENV=production
PORT=3001

# APIs Externas
ANTHROPIC_API_KEY=<chave_producao>
TRINKS_API_KEY=i0fFPhCnix6JuqiOWAIZNaJNHZx89zst9zfSH22c
TRINKS_ESTABLISHMENT_ID=188253

# Cache e Database  
DATABASE_URL=<postgresql_producao>
REDIS_URL=<redis_producao>

# Segurança
CORS_ORIGIN=https://app.trinks.com

# Monitoring
LOG_LEVEL=info
DISABLE_RESPONSE_CACHE=false
```

### Health Checks
```javascript
// Endpoint de saúde da aplicação
GET /health
{
  "status": "healthy",
  "uptime": 3600,
  "memory": "245MB",
  "database": "connected",
  "external_apis": {
    "anthropic": "healthy",
    "trinks": "healthy"
  }
}
```

### Process Management (PM2)
```json
{
  "name": "atendimento-ia",
  "script": "index.js",
  "instances": 2,
  "exec_mode": "cluster",
  "env": {
    "NODE_ENV": "production",
    "PORT": 3001
  },
  "error_file": "./logs/pm2-error.log",
  "out_file": "./logs/pm2-out.log",
  "log_file": "./logs/pm2-combined.log"
}
```

## 📈 Monitoramento e Alertas

### Métricas de IA
```javascript
// Tracking de chamadas IA
{
  timestamp: "2024-01-15T10:30:00Z",
  model: "claude-sonnet-4",
  tokens_used: 1250,
  response_time: 3400,
  cache_hit: false,
  customer_id: "12345",
  intent: "appointment_booking"
}
```

### Alertas Configurados
- **API Trinks Down**: > 5 falhas consecutivas
- **Memory Usage**: > 80% por 5min
- **Response Time**: > 5s por 2min
- **Cache Miss Rate**: > 80% por 10min
- **Error Rate**: > 5% por 5min

### Dashboard de Monitoramento
```
📊 Métricas Tempo Real:
├── Conversas Ativas: 45
├── Taxa de Cache: 67%
├── Tempo Resposta Médio: 1.2s
├── API Trinks Status: ✅ Healthy
├── Claude API Status: ✅ Healthy
└── Memória Usada: 234MB / 512MB
```

## 🔐 Segurança em Produção

### Headers de Segurança (Helmet)
```javascript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"]
    }
  }
}));
```


### Validação de Input
- **Express-validator**: Sanitização automática
- **Schema validation**: Joi para objetos complexos
- **File uploads**: Validação de tipo e tamanho
- **SQL Injection**: Queries parametrizadas

## 🚀 Deploy Automático

### CI/CD Pipeline (GitHub Actions)
```yaml
# .github/workflows/deploy.yml
on:
  push:
    branches: [main]

jobs:
  test-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Run Tests
        run: npm test
      
      - name: Deploy to Production
        run: |
          ssh deploy@server "cd /app && git pull"
          ssh deploy@server "cd /app && npm install --production"
          ssh deploy@server "pm2 reload atendimento-ia"
```

### Rollback Strategy
```bash
# Rollback para versão anterior
pm2 stop atendimento-ia
git checkout HEAD~1
npm install --production  
pm2 start atendimento-ia

# Verificar saúde após rollback
curl https://api.app.com/health
```

## 📋 Checklist de Deploy

### Pre-Deploy
- [ ] Testes passando (100% success rate)
- [ ] Variáveis de ambiente configuradas
- [ ] Database migrations executadas
- [ ] Cache limpo se necessário
- [ ] API keys válidas e ativas

### Post-Deploy
- [ ] Health check OK
- [ ] Logs sem erros críticos
- [ ] Métricas de performance normais
- [ ] Funcionalidades principais testadas
- [ ] Monitoramento ativo