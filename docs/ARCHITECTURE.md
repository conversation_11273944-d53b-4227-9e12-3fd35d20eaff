# 🏗️ Arquitetura Técnica - Sistema de Atendimento Inteligente

## 🎨 Sistema de Templates - Arquitetura Detalhada

### Estrutura de Diretórios
```
server/
├── config/
│   ├── prompt-templates.js          # Sistema de templates
│   └── custom-prompt-templates.json # Templates personalizados
├── cache/
│   ├── HumanizedResponseCache.js   # Cache PostgreSQL híbrido
│   ├── PlaceholderProcessor.js     # Processamento de placeholders
│   └── PlaceholderSchemas.js       # Esquemas de validação
├── routes/
│   └── prompt-templates.js         # API REST completa
└── services/
    └── ai.js                       # Integração templates + IA
```

### Sistema de Cache Híbrido

#### PostgreSQL Direto (Sem Supabase SDK)
```javascript
// Conexão direta via connection string
const { Client } = require('pg');
const client = new Client({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
});

// Schema de cache semântico
CREATE TABLE humanized_responses (
  id SERIAL PRIMARY KEY,
  cache_key VARCHAR(255) UNIQUE NOT NULL,
  response_text TEXT NOT NULL,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL
);

CREATE INDEX idx_cache_key ON humanized_responses(cache_key);
CREATE INDEX idx_expires_at ON humanized_responses(expires_at);
```

#### Memory Fallback
```javascript
// Cache em memória quando PostgreSQL indisponível
class MemoryFallbackCache {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
  }

  set(key, value, ttlMs) {
    this.cache.set(key, value);
    
    // Auto-expiring com setTimeout
    if (this.timers.has(key)) {
      clearTimeout(this.timers.get(key));
    }
    
    const timer = setTimeout(() => {
      this.cache.delete(key);
      this.timers.delete(key);
    }, ttlMs);
    
    this.timers.set(key, timer);
  }
}
```

#### TTL Inteligente por Contexto
```javascript
const TTL_CONFIG = {
  'cancellation_confirm': 2 * 60 * 60 * 1000,    // 2h - contexto urgente
  'appointment_confirm': 4 * 60 * 60 * 1000,     // 4h - processo mais longo  
  'greeting_personalized': 24 * 60 * 60 * 1000,  // 24h - menos crítico
  'rescheduling_offer': 6 * 60 * 60 * 1000,      // 6h - situação intermediária
  'default': 1 * 60 * 60 * 1000                  // 1h - padrão
};
```

### Processamento de Placeholders

#### Schema de Validação
```javascript
// PlaceholderSchemas.js
const PLACEHOLDER_SCHEMAS = {
  cancellation_confirm: {
    required: ['professional', 'date', 'time'],
    optional: ['service', 'customerName', 'establishmentName'],
    validation: {
      professional: 'string',
      date: 'date_br',      // formato DD/MM/YYYY
      time: 'time_br'       // formato HH:mm
    }
  },
  
  appointment_confirm: {
    required: ['professional', 'service', 'date', 'time'],
    optional: ['price', 'duration', 'customerName'],
    validation: {
      price: 'currency_br', // formato R$ 99,90
      duration: 'minutes'   // formato "60 minutos"
    }
  }
};
```

#### Processamento Dinâmico
```javascript
// PlaceholderProcessor.js
class PlaceholderProcessor {
  static process(template, data, schema) {
    // Validar dados obrigatórios
    this.validateRequired(data, schema.required);
    
    // Formatar dados conforme tipo
    const formattedData = this.formatData(data, schema.validation);
    
    // Substituir placeholders
    return template.replace(/{(\w+)}/g, (match, key) => {
      return formattedData[key] || match;
    });
  }

  static formatData(data, validation) {
    const formatted = { ...data };
    
    Object.entries(validation).forEach(([key, type]) => {
      if (data[key]) {
        formatted[key] = this.formatByType(data[key], type);
      }
    });
    
    return formatted;
  }
}
```

## 🤖 Integração LangGraph Detalhada

### StateManager - Gerenciamento de Estado
```javascript
// langgraph/core/StateManager.js
class StateManager {
  constructor() {
    this.states = new Map(); // customerId -> state
    this.conversations = new Map(); // customerId -> messages[]
    this.locks = new Map(); // customerId -> timestamp
  }

  updateState(customerId, newState, context = {}) {
    // Prevenção de race conditions
    if (this.locks.has(customerId)) {
      const lockTime = this.locks.get(customerId);
      if (Date.now() - lockTime < 1000) { // 1s lock
        throw new Error('State update in progress');
      }
    }
    
    this.locks.set(customerId, Date.now());
    
    const currentState = this.states.get(customerId) || {
      stage: 'reception',
      context: {},
      history: []
    };

    const updatedState = {
      ...currentState,
      stage: newState.stage || currentState.stage,
      context: { ...currentState.context, ...context },
      lastUpdate: Date.now()
    };

    this.states.set(customerId, updatedState);
    this.locks.delete(customerId);
    
    return updatedState;
  }
}
```

### IntentGraphAdapter - Ponte ai.js ↔ LangGraph
```javascript
// langgraph/adapters/IntentGraphAdapter.js
class IntentGraphAdapter {
  static async processMessage(customerId, message, conversation) {
    // Transferir histórico completo do ai.js para LangGraph
    const graphState = {
      customerId,
      currentMessage: message,
      conversation: conversation.slice(-10), // Últimas 10 mensagens
      timestamp: Date.now()
    };
    
    logger.info(`LangGraph: Received ${conversation.length} messages from ai.js`);
    
    // Executar grafo de intenção
    const graph = new IntentGraph();
    const result = await graph.execute(graphState);
    
    // Retornar resultado para ai.js
    return {
      intent: result.intent,
      confidence: result.confidence,
      extractedData: result.extractedData,
      suggestedAction: result.suggestedAction
    };
  }
}
```

### Fluxo de Detecção de Intenção
```javascript
// IntentGraph.js - Grafo principal
class IntentGraph {
  constructor() {
    this.nodes = {
      'classify_intent': new IntentClassificationNode(),
      'validate_context': new ContextValidationNode(),
      'confirm_intent': new IntentConfirmationNode(),
      'route_action': new IntentRoutingNode()
    };
  }

  async execute(state) {
    // 1. Classificar intenção via Claude API
    state = await this.nodes.classify_intent.execute(state);
    
    // 2. Validar contexto conversacional
    state = await this.nodes.validate_context.execute(state);
    
    // 3. Confirmar intenção com base no histórico
    state = await this.nodes.confirm_intent.execute(state);
    
    // 4. Rotear para ação apropriada
    state = await this.nodes.route_action.execute(state);
    
    return state;
  }
}
```

## 🔄 Sistema de Cache Semântico

### Geração de Cache Keys
```javascript
// Cache key baseado em contexto semântico
function generateSemanticCacheKey(templateType, extractedData) {
  // Normalizar dados para consistência
  const normalized = {
    template: templateType,
    professional: extractedData.professional?.toLowerCase()?.trim(),
    date: formatDateBR(extractedData.date),
    intent: extractedData.intent
  };
  
  // Gerar hash do contexto
  const contextHash = crypto
    .createHash('md5')
    .update(JSON.stringify(normalized))
    .digest('hex');
    
  return `${templateType}_${contextHash.substring(0, 8)}`;
}
```

### Performance Monitoring
```javascript
// Métricas de cache em tempo real
class CacheMetrics {
  constructor() {
    this.hits = 0;
    this.misses = 0;
    this.errors = 0;
    this.totalRequests = 0;
  }
  
  recordHit() {
    this.hits++;
    this.totalRequests++;
  }
  
  recordMiss() {
    this.misses++;
    this.totalRequests++;
  }
  
  getHitRate() {
    return this.totalRequests > 0 
      ? (this.hits / this.totalRequests * 100).toFixed(2)
      : 0;
  }
}
```

## 🔌 API REST de Templates

### Validação de Templates
```javascript
// routes/prompt-templates.js
const templateValidator = {
  validateTemplate(templateData) {
    const errors = [];
    
    // Validações obrigatórias
    if (!templateData.template || templateData.template.trim() === '') {
      errors.push('Template content cannot be empty');
    }
    
    if (templateData.temperature < 0.1 || templateData.temperature > 1.0) {
      errors.push('Temperature must be between 0.1 and 1.0');
    }
    
    if (templateData.maxTokens < 50 || templateData.maxTokens > 500) {
      errors.push('MaxTokens must be between 50 and 500');
    }
    
    // Validar placeholders obrigatórios
    const schema = PLACEHOLDER_SCHEMAS[templateData.type];
    if (schema) {
      schema.required.forEach(placeholder => {
        if (!templateData.template.includes(`{${placeholder}}`)) {
          errors.push(`Missing required placeholder: {${placeholder}}`);
        }
      });
    }
    
    return errors;
  }
};
```

### Endpoint de Teste de Templates
```javascript
app.post('/api/prompt-templates/:type/test', async (req, res) => {
  try {
    const { type } = req.params;
    const testData = req.body;
    
    // Obter template
    const template = await getTemplate(type);
    
    // Processar com dados de teste
    const processedTemplate = PlaceholderProcessor.process(
      template.template, 
      testData, 
      PLACEHOLDER_SCHEMAS[type]
    );
    
    // Gerar resposta via IA (se configurada)
    let aiResponse = null;
    if (process.env.ANTHROPIC_API_KEY) {
      aiResponse = await generateAIResponse(processedTemplate);
    }
    
    res.json({
      templateType: type,
      processedTemplate,
      testData,
      aiResponse,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(400).json({ error: error.message });
  }
});
```

## 📊 Monitoramento de IA

### AI Call Tracking
```javascript
// utils/AIInteractionLogger.js
class AIInteractionLogger {
  static logInteraction(data) {
    const logEntry = {
      timestamp: new Date().toISOString(),
      model: data.model || 'claude-sonnet-4',
      tokensUsed: data.tokensUsed || 0,
      responseTime: data.responseTime || 0,
      cacheHit: data.cacheHit || false,
      customerId: data.customerId,
      intent: data.intent,
      templateType: data.templateType,
      success: data.success || false,
      errorMessage: data.errorMessage || null
    };
    
    // Log estruturado para análise
    logger.info('AI_INTERACTION', logEntry);
    
    // Salvar métricas para dashboard
    this.updateMetrics(logEntry);
  }
}
```

### Health Check Detalhado
```javascript
app.get('/health/detailed', async (req, res) => {
  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    
    // Componentes do sistema
    components: {
      database: await checkDatabaseHealth(),
      cache: await checkCacheHealth(),
      ai: await checkAIHealth(),
      external_apis: {
        trinks: await checkTrinksAPI(),
        anthropic: await checkAnthropicAPI()
      }
    },
    
    // Métricas de performance
    metrics: {
      activeConnections: getActiveConnections(),
      cacheHitRate: getCacheHitRate(),
      averageResponseTime: getAverageResponseTime(),
      errorRate: getErrorRate()
    }
  };
  
  const hasErrors = Object.values(health.components).some(c => c.status !== 'healthy');
  health.status = hasErrors ? 'degraded' : 'healthy';
  
  res.status(hasErrors ? 503 : 200).json(health);
});
```