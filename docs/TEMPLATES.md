# 🎨 Sistema de Templates Configuráveis - Exemplos e Casos de Uso

## 🎯 Tipos de Template Disponíveis

### Templates Implementados
1. **`cancellation_confirm`** ✅ - Confirmação de cancelamento
2. **`appointment_confirm`** ✅ - Confirmação de agendamento
3. **`appointment_request_confirm`** ✅ - Solicitação de confirmação de agendamento
4. **`rescheduling_offer`** - Oferta de reagendamento  
5. **`greeting_personalized`** - Saudações personalizadas

## 🎨 Exemplos de Personalização Comprovados

### Salão Premium (Elegante)
```javascript
{
  name: 'Confirmação Elegante',
  template: `Prezado(a) cliente, compreendemos sua solicitação de cancelamento do agendamento com {professional} em {date} às {time}. 

Para confirmar esta alteração, responda "SIM" ou "CONFIRMO". 

Estamos à disposição para reagendar quando desejar.

Atenciosamente,
{establishmentName}`,
  temperature: 0.2,
  maxTokens: 180,
  style: 'formal'
}
```

### Barbearia (Casual)  
```javascript
{
  name: 'Confirmação Descontraída',
  template: `E aí! Beleza? 👋

Vi que você quer cancelar com {professional} em {date} às {time}. 

Só responde "sim" pra eu confirmar aqui, beleza?

Se quiser remarcar é só falar! 😉`,
  temperature: 0.8,
  maxTokens: 120,
  style: 'casual'
}
```

### Clínica de Estética (Profissional)
```javascript
{
  name: 'Confirmação Médica',
  template: `Olá, recebemos sua solicitação de cancelamento do procedimento com {professional} agendado para {date} às {time}.

Para confirmar o cancelamento, responda "CONFIRMO".

Lembramos que cancelamentos com menos de 24h podem estar sujeitos a taxas conforme nosso regulamento.

Equipe {establishmentName}`,
  temperature: 0.3,
  maxTokens: 200,
  style: 'medical'
}
```

## 🔄 Casos de Uso por Segmento

### 💄 Salão de Beleza Feminino
```javascript
{
  greeting: "Oi querida! ✨ Como posso ajudar você hoje?",
  confirmation: "Perfeito! Confirmado seu horário com a {professional} 💅",
  cancellation: "Tudo bem, amor! Cancelei seu agendamento. Qualquer coisa é só chamar! 💕"
}
```

### ✂️ Barbearia Masculina
```javascript
{
  greeting: "E aí, brother! Bora agendar?",
  confirmation: "Fechou! {professional} te espera às {time} 👊",
  cancellation: "Tranquilo, mano! Cancelado. Quando quiser remarcar é só falar!"
}
```

### 🏥 Clínica Médica
```javascript
{
  greeting: "Olá, em que podemos ajudá-lo(a) hoje?",
  confirmation: "Agendamento confirmado. Dr(a). {professional} em {date} às {time}",
  cancellation: "Cancelamento realizado. Lembramos das políticas de reagendamento."
}
```

## 📊 Configurações de Template

### Parâmetros Disponíveis

#### Placeholders
- `{professional}` - Nome do profissional
- `{date}` - Data do agendamento (formato brasileiro)
- `{time}` - Horário do agendamento
- `{service}` - Serviço solicitado
- `{establishmentName}` - Nome do estabelecimento
- `{customerName}` - Nome do cliente
- `{conversationHistory}` - Histórico da conversa

#### Configurações de IA
- `temperature`: 0.1 (formal) → 0.9 (criativo)
- `maxTokens`: 80-300 (controle de tamanho)
- `style`: formal, casual, medical, friendly

### TTL por Tipo de Template
- **Cancelamentos**: 2 horas (contexto urgente)
- **Agendamentos**: 4 horas (processo mais longo)
- **Saudações**: 24 horas (menos crítico)

## 🧪 Resultados dos Testes

### Performance Comprovada
- **✅ Claude API Real**: Funcionando perfeitamente (4s response)
- **✅ Cache HIT**: Instantâneo (0ms) para contextos similares  
- **✅ Templates**: Tons completamente diferentes por salão
- **✅ Persistência**: PostgreSQL + Memory fallback robusto
- **✅ Hit Rate**: 50% demonstrado em testes

### Casos de Sucesso

#### Teste A: Salão Premium
```
Input: "Quero cancelar com a Ágata amanhã"
Output: "Prezada cliente, compreendemos sua solicitação..."
Resultado: Tom elegante aplicado ✅
```

#### Teste B: Barbearia
```
Input: "Quero cancelar com a Ágata amanhã"  
Output: "E aí! Vi que você quer cancelar..."
Resultado: Tom descontraído aplicado ✅
```

#### Comparação de Estilos
| Estabelecimento | Temperature | Palavras | Tom |
|----------------|-------------|-----------|-----|
| Salão Premium | 0.2 | "Prezado(a)", "Atenciosamente" | Formal |
| Barbearia | 0.8 | "E aí!", "Beleza?" | Casual |
| Clínica | 0.3 | "Procedimento", "Regulamento" | Profissional |

## 🔌 API de Templates

### Endpoints para Personalização
```bash
# Listar todos os templates
GET /api/prompt-templates

# Obter template específico
GET /api/prompt-templates/cancellation_confirm

# Atualizar template
PUT /api/prompt-templates/cancellation_confirm
Content-Type: application/json
{
  "name": "Meu Template Personalizado",
  "template": "Nova mensagem com {placeholders}...",
  "temperature": 0.5,
  "maxTokens": 150
}

# Testar template
POST /api/prompt-templates/cancellation_confirm/test
{
  "professional": "Ágata",
  "date": "25/12/2024",
  "time": "15:00"
}
```

### Validação de Templates
- Placeholders obrigatórios devem estar presentes
- Temperature entre 0.1 e 1.0
- MaxTokens entre 50 e 500
- Template não pode estar vazio
- Deve gerar resposta válida com dados de teste

## 💡 Boas Práticas

### Criação de Templates
1. **Defina o tom** antes de escrever (formal/casual/profissional)
2. **Use placeholders** para personalização automática
3. **Teste com dados reais** do seu estabelecimento
4. **Mantenha conciso** (1-2 frases máximo)
5. **Considere o contexto** da conversa

### Manutenção
- Monitore hit rate do cache (objetivo: >50%)
- Revise templates baseado em feedback
- Teste após mudanças na API Trinks
- Mantenha backup dos templates customizados
- Documente mudanças para a equipe