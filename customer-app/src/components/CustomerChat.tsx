import React, { useState, useEffect, useRef } from 'react';
import { Message, Customer, ChatConnection, ConversationState, ArchitectureInfo } from '../types';
import customerSocketService from '../services/socket';
import customerApiService from '../services/api';
import AudioRecorder from './AudioRecorder';
import AudioMessage from './AudioMessage';

const CustomerChat: React.FC = () => {
  const [customer, setCustomer] = useState<Customer>({
    nome: 'Fernando',
    telefone: '21998217917'
  });
  const [connection, setConnection] = useState<ChatConnection | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [architectureInfo, setArchitectureInfo] = useState<ArchitectureInfo | null>(null);
  const [conversationState, setConversationState] = useState<ConversationState | null>(null);
  const [loading, setLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Verificar arquitetura do servidor
    const checkArchitecture = async () => {
      try {
        const archInfo = await customerApiService.checkArchitecture();
        setArchitectureInfo(archInfo);
        console.log('🏗️ Arquitetura detectada:', archInfo);
      } catch (error) {
        console.warn('Não foi possível verificar arquitetura, usando fallback');
        setArchitectureInfo({ isNewArchitecture: false, version: '1.0.0' });
      }
    };

    checkArchitecture();

    // Conectar ao servidor
    const socket = customerSocketService.connect();
    
    if (socket) {
      socket.on('connect', () => {
        setIsConnected(true);
        console.log('🔌 Conectado à nova arquitetura refatorada');
      });
      socket.on('disconnect', () => setIsConnected(false));
    }

    // Event listeners para nova arquitetura
    customerSocketService.onConversationStarted((data) => {
      const { conversationId, customer: customerData, initialMessage } = data;
      setConnection({ 
        conversationId, 
        connected: true,
        architecture: 'refactored',
        version: '2.0.0'
      });
      console.log('💬 Conversa iniciada (Nova Arquitetura):', conversationId);
      
      // Adicionar mensagem inicial se fornecida
      if (initialMessage) {
        const welcomeMessage: Message = {
          id: `welcome_${Date.now()}`,
          content: initialMessage,
          sender: 'salon',
          timestamp: new Date(),
          type: 'text',
          status: 'delivered'
        };
        setMessages([welcomeMessage]);
      }
    });

    customerSocketService.onSalonResponse(({ conversationId, message }) => {
      if (connection?.conversationId === conversationId) {
        // Criar mensagem com campos da nova arquitetura
        const enhancedMessage: Message = {
          ...message,
          timestamp: new Date(message.timestamp)
        };
        setMessages(prev => [...prev, enhancedMessage]);
        
        // Atualizar estado da conversa se disponível
        if (message.state) {
          setConversationState(prev => prev ? {
            ...prev,
            currentState: message.state!,
            agent: message.agent
          } : null);
        }
      }
    });

    customerSocketService.onSalonTyping(({ conversationId, isTyping: typing }) => {
      if (connection?.conversationId === conversationId) {
        setIsTyping(typing);
      }
    });

    return () => {
      customerSocketService.removeAllListeners();
      customerSocketService.disconnect();
    };
  }, [connection?.conversationId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleStartConversation = (e: React.FormEvent) => {
    e.preventDefault();
    if (customer.nome.trim() && customer.telefone.trim()) {
      // Formatar telefone brasileiro
      const formattedPhone = customer.telefone.startsWith('+55') 
        ? customer.telefone 
        : `+55${customer.telefone.replace(/\D/g, '')}`;
      
      customerSocketService.startConversation(formattedPhone, customer.nome);
    }
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (messageInput.trim() && connection) {
      const userMessage: Message = {
        id: Date.now().toString(),
        content: messageInput.trim(),
        sender: 'user',
        timestamp: new Date(),
        status: 'sent',
        type: 'text'
      };

      setMessages(prev => [...prev, userMessage]);
      
      customerSocketService.sendMessage(
        connection.conversationId,
        messageInput.trim(),
        customer.telefone.startsWith('+55') ? customer.telefone : `+55${customer.telefone.replace(/\D/g, '')}`
      );
      
      setMessageInput('');
    }
  };

  const handleAudioReady = (audioBlob: Blob, duration: number) => {
    if (connection) {
      const audioMessage: Message = {
        id: Date.now().toString(),
        content: 'Mensagem de voz',
        sender: 'user',
        timestamp: new Date(),
        status: 'sent',
        type: 'audio',
        audioBlob: audioBlob,
        audioDuration: duration
      };

      setMessages(prev => [...prev, audioMessage]);
      
      // Convert blob to base64 for sending
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64Audio = reader.result as string;
        customerSocketService.sendAudioMessage(
          connection.conversationId,
          base64Audio,
          duration,
          customer.telefone.startsWith('+55') ? customer.telefone : `+55${customer.telefone.replace(/\D/g, '')}`
        );
      };
      reader.readAsDataURL(audioBlob);
    }
  };

  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 11) {
      if (numbers.length <= 2) {
        return numbers;
      } else if (numbers.length <= 7) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
      } else if (numbers.length <= 10) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
      } else {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
      }
    }
    return value;
  };

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (!connection) {
    return (
      <div className="min-h-screen bg-whatsapp-600 flex items-center justify-center p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-md p-6">
          <div className="text-center mb-6">
            <div className="w-20 h-20 bg-whatsapp-400 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-white text-3xl">👤</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-800 mb-2">
              Cliente Trinks
            </h1>
            <p className="text-gray-600">
              Entre em contato com o salão
            </p>
          </div>

          <form onSubmit={handleStartConversation} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Seu Nome
              </label>
              <input
                type="text"
                value={customer.nome}
                onChange={(e) => setCustomer({...customer, nome: e.target.value})}
                placeholder="Digite seu nome"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-whatsapp-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Seu Telefone
              </label>
              <input
                type="tel"
                value={customer.telefone}
                onChange={(e) => setCustomer({...customer, telefone: formatPhone(e.target.value)})}
                placeholder="(11) 99999-9999"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-whatsapp-500"
              />
            </div>

            <button
              type="submit"
              disabled={!customer.nome.trim() || !customer.telefone.trim() || !isConnected}
              className="w-full py-3 bg-whatsapp-400 text-white rounded-lg font-medium hover:bg-whatsapp-500 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              {!isConnected ? 'Conectando...' : 'Iniciar Conversa'}
            </button>
          </form>

          <div className="mt-4 text-center space-y-2">
            <div className={`inline-flex items-center space-x-2 text-sm ${
              isConnected ? 'text-green-600' : 'text-red-600'
            }`}>
              <div className={`w-2 h-2 rounded-full ${
                isConnected ? 'bg-green-400' : 'bg-red-400'
              }`}></div>
              <span>{isConnected ? 'Conectado' : 'Desconectado'}</span>
            </div>
            
            {architectureInfo && (
              <div className="text-xs text-gray-500">
                {architectureInfo.isNewArchitecture ? (
                  <span className="inline-flex items-center space-x-1">
                    <span className="w-1.5 h-1.5 bg-blue-400 rounded-full"></span>
                    <span>Nova Arquitetura v{architectureInfo.version}</span>
                  </span>
                ) : (
                  <span className="inline-flex items-center space-x-1">
                    <span className="w-1.5 h-1.5 bg-gray-400 rounded-full"></span>
                    <span>Arquitetura Legada v{architectureInfo.version}</span>
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {/* Header */}
      <div className="bg-whatsapp-500 text-white px-4 py-3">
        <div className="flex items-center">
          <div className="w-10 h-10 bg-whatsapp-600 rounded-full flex items-center justify-center text-white font-semibold mr-3">
            {customer.nome.charAt(0).toUpperCase()}
          </div>
          <div className="flex-1">
            <h3 className="font-medium">
              {customer.nome}
            </h3>
            <p className="text-sm text-whatsapp-100 flex items-center space-x-2">
              <span>Conversando com Salão Trinks</span>
              {conversationState && (
                <span className="text-xs bg-whatsapp-600 px-2 py-0.5 rounded">
                  {conversationState.agent || conversationState.currentState}
                </span>
              )}
            </p>
          </div>
          <div className={`w-2 h-2 rounded-full ${
            isConnected ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto px-4 py-4 space-y-3">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <div className="text-4xl mb-4">💬</div>
            <p>Envie uma mensagem para iniciar o atendimento</p>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                {message.sender === 'system' ? (
                  <div className="bg-gray-100 text-gray-600 px-3 py-2 rounded-full text-xs max-w-xs text-center mx-auto">
                    {message.content}
                  </div>
                ) : (
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
                      message.sender === 'user'
                        ? 'bg-whatsapp-400 text-white'
                        : 'bg-white text-gray-800 border border-gray-200'
                    }`}
                  >
                    {message.type === 'audio' ? (
                      <AudioMessage
                        audioBlob={message.audioBlob}
                        audioUrl={message.audioUrl}
                        duration={message.audioDuration}
                        sender={message.sender === 'user' ? 'user' : 'salon'}
                      />
                    ) : (
                      <>
                        <div className="text-sm leading-relaxed whitespace-pre-wrap">
                          {message.content}
                        </div>
                        <div
                          className={`text-xs mt-1 ${
                            message.sender === 'user' ? 'text-whatsapp-100' : 'text-gray-500'
                          }`}
                        >
                          {formatTime(message.timestamp)}
                          {message.sender === 'user' && (
                            <span className="ml-1">
                              {message.status === 'sent' && '✓'}
                              {message.status === 'delivered' && '✓✓'}
                              {message.status === 'read' && (
                                <span className="text-blue-200">✓✓</span>
                              )}
                            </span>
                          )}
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>
            ))}

            {isTyping && (
              <div className="flex justify-start">
                <div className="bg-white border border-gray-200 px-4 py-3 rounded-2xl max-w-xs">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input */}
      <div className="bg-gray-100 border-t border-gray-200 px-4 py-3">
        <form onSubmit={handleSendMessage} className="flex items-end space-x-3">
          <div className="flex-1">
            <input
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              placeholder="Digite sua mensagem..."
              disabled={isTyping}
              className="w-full px-4 py-3 border-0 rounded-full focus:outline-none resize-none shadow-sm bg-white hover:shadow-md focus:shadow-md disabled:bg-gray-200"
            />
          </div>
          
          {/* Audio Recorder */}
          <AudioRecorder 
            onAudioReady={handleAudioReady}
            disabled={isTyping}
          />
          
          <button
            type="submit"
            disabled={!messageInput.trim() || isTyping}
            className="px-6 py-3 rounded-full font-medium transition-all duration-200 bg-whatsapp-400 text-white hover:bg-whatsapp-500 disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
              />
            </svg>
          </button>
        </form>
      </div>
    </div>
  );
};

export default CustomerChat;