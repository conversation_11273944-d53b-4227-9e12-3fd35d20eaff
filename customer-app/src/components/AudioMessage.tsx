import React, { useState, useRef, useEffect } from 'react';

interface AudioMessageProps {
  audioUrl?: string;
  audioBlob?: Blob;
  duration?: number;
  sender: 'user' | 'salon' | 'system';
}

const AudioMessage: React.FC<AudioMessageProps> = ({ 
  audioUrl, 
  audioBlob, 
  duration = 0,
  sender 
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [totalDuration, setTotalDuration] = useState(duration);
  const audioRef = useRef<HTMLAudioElement>(null);
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Create audio element
    if (audioRef.current) {
      if (audioBlob) {
        const url = URL.createObjectURL(audioBlob);
        audioRef.current.src = url;
        return () => URL.revokeObjectURL(url);
      } else if (audioUrl) {
        audioRef.current.src = audioUrl;
      }
    }
  }, [audioUrl, audioBlob]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      setTotalDuration(Math.floor(audio.duration));
    };

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('ended', handleEnded);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('ended', handleEnded);
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    };
  }, []);

  const togglePlayback = () => {
    const audio = audioRef.current;
    if (!audio) return;

    if (isPlaying) {
      audio.pause();
      setIsPlaying(false);
      if (progressIntervalRef.current) {
        clearInterval(progressIntervalRef.current);
      }
    } else {
      audio.play().catch(error => {
        console.error('Error playing audio:', error);
      });
      setIsPlaying(true);
      
      // Update progress
      progressIntervalRef.current = setInterval(() => {
        if (audio.currentTime >= audio.duration) {
          setIsPlaying(false);
          setCurrentTime(0);
          if (progressIntervalRef.current) {
            clearInterval(progressIntervalRef.current);
          }
        } else {
          setCurrentTime(Math.floor(audio.currentTime));
        }
      }, 100);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = totalDuration > 0 ? (currentTime / totalDuration) * 100 : 0;

  return (
    <div className={`flex items-center space-x-3 p-2 rounded-lg ${
      sender === 'user' ? 'bg-whatsapp-300/20' : 'bg-gray-50'
    }`}>
      <audio ref={audioRef} />
      
      <button
        onClick={togglePlayback}
        className={`p-2 rounded-full transition-colors ${
          sender === 'user' 
            ? 'bg-whatsapp-400 hover:bg-whatsapp-500 text-white' 
            : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
        }`}
      >
        {isPlaying ? (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <rect x="6" y="5" width="4" height="14" rx="1" />
            <rect x="14" y="5" width="4" height="14" rx="1" />
          </svg>
        ) : (
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z" />
          </svg>
        )}
      </button>

      <div className="flex-1">
        <div className="flex items-center space-x-2">
          {/* Audio waveform visualization */}
          <div className="flex items-center space-x-1 flex-1">
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                className={`w-1 rounded-full transition-all duration-200 ${
                  i < (progressPercentage / 5)
                    ? sender === 'user' ? 'bg-whatsapp-500' : 'bg-gray-600'
                    : sender === 'user' ? 'bg-whatsapp-200' : 'bg-gray-300'
                }`}
                style={{
                  height: `${8 + Math.sin(i * 0.5) * 8}px`,
                }}
              />
            ))}
          </div>
        </div>
        
        <div className="flex justify-between items-center mt-1">
          <span className={`text-xs ${
            sender === 'user' ? 'text-whatsapp-600' : 'text-gray-500'
          }`}>
            {isPlaying ? formatTime(currentTime) : formatTime(totalDuration)}
          </span>
        </div>
      </div>

      {/* Microphone icon */}
      <div className={`p-1 ${sender === 'user' ? 'text-whatsapp-400' : 'text-gray-400'}`}>
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z" />
          <path d="M19 10v2a7 7 0 0 1-14 0v-2" stroke="currentColor" strokeWidth="2" fill="none" />
        </svg>
      </div>
    </div>
  );
};

export default AudioMessage;