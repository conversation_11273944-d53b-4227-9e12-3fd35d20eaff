import axios from 'axios';
import { Message } from '../types';
import Logger from '../utils/logger';

const API_BASE_URL = 'http://localhost:3001/api';

class CustomerApiService {
  private apiClient;

  constructor() {
    this.apiClient = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Request interceptor
    this.apiClient.interceptors.request.use(
      (config) => {
        Logger.api('API Request', {
          method: config.method,
          url: config.url,
          data: config.data
        });
        return config;
      },
      (error) => {
        Logger.api('API Request Error', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.apiClient.interceptors.response.use(
      (response) => {
        Logger.api('API Response', {
          status: response.status,
          data: response.data
        });
        return response;
      },
      (error) => {
        Logger.api('API Response Error', {
          status: error.response?.status,
          message: error.message,
          data: error.response?.data
        });
        return Promise.reject(error);
      }
    );
  }

  // Start new conversation via REST API
  async startConversation(customerInfo: {
    name: string;
    phone: string;
    email?: string;
  }) {
    try {
      const response = await this.apiClient.post('/conversation/start', {
        customerInfo,
        source: 'customer-app'
      });

      return response.data;
    } catch (error) {
      console.error('Error starting conversation via API:', error);
      throw error;
    }
  }

  // Send message via REST API
  async sendMessage(sessionId: string, message: string, userId?: string) {
    try {
      const response = await this.apiClient.post('/conversation', {
        message,
        sessionId,
        userId
      });

      return response.data;
    } catch (error) {
      console.error('Error sending message via API:', error);
      throw error;
    }
  }

  // Get conversation history
  async getConversationHistory(sessionId: string, limit = 50, offset = 0) {
    try {
      const response = await this.apiClient.get(`/conversation/history/${sessionId}`, {
        params: { limit, offset }
      });

      return response.data;
    } catch (error) {
      console.error('Error getting conversation history:', error);
      throw error;
    }
  }

  // Get conversation state
  async getConversationState(sessionId: string) {
    try {
      const response = await this.apiClient.get(`/conversation/state/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error getting conversation state:', error);
      throw error;
    }
  }

  // Reset conversation
  async resetConversation(sessionId: string) {
    try {
      const response = await this.apiClient.post(`/conversation/reset/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error resetting conversation:', error);
      throw error;
    }
  }

  // Classify intent
  async classifyIntent(message: string, context = {}) {
    try {
      const response = await this.apiClient.post('/conversation/classify', {
        message,
        context
      });

      return response.data;
    } catch (error) {
      console.error('Error classifying intent:', error);
      throw error;
    }
  }

  // Get server health
  async getServerHealth() {
    try {
      const response = await this.apiClient.get('/health', {
        baseURL: 'http://localhost:3001' // Direct health endpoint
      });

      return response.data;
    } catch (error) {
      console.error('Error getting server health:', error);
      throw error;
    }
  }

  // Get WebSocket info
  async getWebSocketInfo() {
    try {
      const response = await this.apiClient.get('/websocket/info');
      return response.data;
    } catch (error) {
      console.error('Error getting WebSocket info:', error);
      throw error;
    }
  }

  // Get conversation metrics
  async getMetrics() {
    try {
      const response = await this.apiClient.get('/conversation/metrics');
      return response.data;
    } catch (error) {
      console.error('Error getting metrics:', error);
      throw error;
    }
  }

  // Check if using new architecture
  async checkArchitecture() {
    try {
      const health = await this.getServerHealth();
      return {
        isNewArchitecture: health.architecture === 'refactored',
        version: health.version || '1.0.0'
      };
    } catch (error) {
      return {
        isNewArchitecture: false,
        version: '1.0.0'
      };
    }
  }
}

const customerApiService = new CustomerApiService();
export default customerApiService;