import { io, Socket } from 'socket.io-client';
import { Message, SocketEvents } from '../types';
import Logger from '../utils/logger';

class CustomerSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isNewArchitecture = true; // Flag para nova arquitetura

  connect() {
    if (this.socket?.connected) {
      return this.socket;
    }

    this.socket = io('http://localhost:3001', {
      transports: ['websocket', 'polling'],
      upgrade: true,
      timeout: 10000,
      forceNew: true
    });

    this.socket.on('connect', () => {
      console.log('🔌 Cliente conectado ao servidor (Nova Arquitetura v2.0)');
      Logger.socket('Connected to refactored server', { 
        socketId: this.socket?.id,
        architecture: 'v2.0-refactored'
      });
      this.reconnectAttempts = 0;
    });

    this.socket.on('disconnect', () => {
      console.log('❌ Cliente desconectado do servidor');
    });

    this.socket.on('connect_error', (error) => {
      console.error('Erro de conexão:', error);
      this.handleReconnect();
    });

    // Listener para erros da nova arquitetura
    this.socket.on('error', (error) => {
      console.error('🚨 Erro da nova arquitetura:', error);
      Logger.socket('Architecture error', error);
    });

    return this.socket;
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 Tentando reconectar... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, 2000 * this.reconnectAttempts);
    } else {
      console.error('❌ Máximo de tentativas de reconexão atingido');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Iniciar conversa como cliente
  startConversation(customerPhone: string, customerName: string) {
    if (this.socket) {
      this.socket.emit('customer_start_conversation', {
        phone: customerPhone,
        name: customerName
      });
    }
  }

  // Enviar mensagem como cliente
  sendMessage(conversationId: string, content: string, customerPhone: string) {
    if (this.socket) {
      this.socket.emit('customer_send_message', {
        conversationId,
        content,
        customerPhone
      });
    }
  }

  // Enviar mensagem de áudio como cliente
  sendAudioMessage(conversationId: string, audioData: string, duration: number, customerPhone: string) {
    if (this.socket) {
      this.socket.emit('customer_send_audio', {
        conversationId,
        audioData,
        duration,
        customerPhone
      });
      Logger.socket('Audio message sent', { conversationId, duration });
    }
  }

  // Ouvir resposta do salão
  onSalonResponse(callback: (data: SocketEvents['SalonResponse']) => void) {
    if (this.socket) {
      this.socket.on('salon_response', callback);
    }
  }

  // Confirmar conexão estabelecida
  onConversationStarted(callback: (data: SocketEvents['ConversationStarted']) => void) {
    if (this.socket) {
      this.socket.on('conversation_started', callback);
    }
  }

  // Ouvir indicador de digitação do salão
  onSalonTyping(callback: (data: SocketEvents['SalonTyping']) => void) {
    if (this.socket) {
      this.socket.on('salon_typing', callback);
    }
  }

  // Limpeza de listeners
  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  get isConnected() {
    return this.socket?.connected || false;
  }
}

const customerSocketService = new CustomerSocketService();
export default customerSocketService;