// Frontend logger utility for customer app
export class Logger {
  private static logs: LogEntry[] = [];
  private static maxLogs = 1000;
  private static isRecording = process.env.NODE_ENV === 'development' || 
    (typeof window !== 'undefined' && localStorage.getItem('enableLogging') === 'true');

  static info(message: string, data?: any) {
    this.log('INFO', message, data);
  }

  static warn(message: string, data?: any) {
    this.log('WARN', message, data);
  }

  static error(message: string, data?: any) {
    this.log('ERROR', message, data);
  }

  static debug(message: string, data?: any) {
    this.log('DEBUG', message, data);
  }

  static socket(event: string, data?: any) {
    this.log('SOCKET', `Event: ${event}`, data);
  }

  static api(message: string, data?: any) {
    this.log('API', message, data);
  }

  private static log(level: LogLevel, message: string, data?: any) {
    const timestamp = new Date().toISOString();
    const logEntry: LogEntry = {
      timestamp,
      level,
      message,
      data: data ? JSON.stringify(data, null, 2) : undefined,
      component: 'customer-app'
    };

    // Add to memory
    this.logs.push(logEntry);
    
    // Keep only recent logs
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    // Console output with formatting
    const consoleMsg = `[${timestamp}] [CUSTOMER] [${level}] ${message}`;
    switch (level) {
      case 'ERROR':
        console.error(consoleMsg, data || '');
        break;
      case 'WARN':
        console.warn(consoleMsg, data || '');
        break;
      case 'DEBUG':
        console.debug(consoleMsg, data || '');
        break;
      default:
        console.log(consoleMsg, data || '');
    }

    // Save to localStorage for persistence
    if (this.isRecording) {
      this.saveToStorage();
    }

    // Send to server if available
    this.sendToServer(logEntry);
  }

  private static saveToStorage() {
    if (typeof window === 'undefined') return;
    
    try {
      const recentLogs = this.logs.slice(-100); // Keep last 100 logs in storage
      localStorage.setItem('customer-app-logs', JSON.stringify(recentLogs));
    } catch (error) {
      console.warn('Failed to save logs to localStorage:', error);
    }
  }

  private static sendToServer(logEntry: LogEntry) {
    // Try to send log to server for centralized logging
    if (typeof fetch !== 'undefined') {
      fetch('/api/logs/client', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(logEntry)
      }).catch(() => {
        // Silently fail - don't spam console if server is unavailable
      });
    }
  }

  static getLogs(): LogEntry[] {
    return [...this.logs];
  }

  static downloadLogs() {
    const logsText = this.logs
      .map(log => `[${log.timestamp}] [${log.level}] ${log.message}${log.data ? '\nData: ' + log.data : ''}`)
      .join('\n');
    
    const blob = new Blob([logsText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `customer-app-logs-${new Date().toISOString().split('T')[0]}.log`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  static clearLogs() {
    this.logs = [];
    if (typeof window !== 'undefined') {
      localStorage.removeItem('customer-app-logs');
    }
  }

  static enableLogging(enable: boolean = true) {
    this.isRecording = enable;
    if (typeof window !== 'undefined') {
      localStorage.setItem('enableLogging', enable.toString());
    }
  }

  // Load logs from localStorage on app start
  static loadStoredLogs() {
    if (typeof window === 'undefined') return;
    
    try {
      const stored = localStorage.getItem('customer-app-logs');
      if (stored) {
        const storedLogs = JSON.parse(stored);
        this.logs = [...storedLogs, ...this.logs];
      }
    } catch (error) {
      console.warn('Failed to load stored logs:', error);
    }
  }
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: string;
  component: string;
}

type LogLevel = 'INFO' | 'WARN' | 'ERROR' | 'DEBUG' | 'SOCKET' | 'API';

// Initialize logger
Logger.loadStoredLogs();

export default Logger;