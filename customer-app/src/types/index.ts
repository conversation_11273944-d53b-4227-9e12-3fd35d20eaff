export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'salon' | 'system' | 'admin';
  timestamp: Date;
  status?: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  type: 'text' | 'audio' | 'image';
  audioUrl?: string;
  audioBlob?: Blob;
  audioDuration?: number;
  // New architecture fields
  state?: string;
  agent?: string;
  confidence?: number;
  processingTime?: number;
}

export interface Customer {
  id?: string;
  nome: string;
  telefone: string;
  email?: string;
}

export interface ChatConnection {
  conversationId: string;
  connected: boolean;
  architecture?: string;
  version?: string;
}

export interface ConversationState {
  sessionId: string;
  currentState: string;
  agent?: string;
  context: {
    userId: string;
    customerInfo: Customer;
    timestamp: Date;
    messageCount: number;
  };
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ArchitectureInfo {
  isNewArchitecture: boolean;
  version: string;
  endpoint?: string;
  features?: string[];
}

export interface SocketEvents {
  ConversationStarted: {
    conversationId: string;
    customer: any;
    initialMessage?: string;
  };
  
  SalonResponse: {
    conversationId: string;
    message: Message;
  };
  
  SalonTyping: {
    conversationId: string;
    isTyping: boolean;
  };
  
  Error: {
    message: string;
    code?: string;
  };
}