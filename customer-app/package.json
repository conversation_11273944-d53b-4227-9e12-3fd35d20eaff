{"name": "trinks-customer-app", "version": "0.1.0", "private": true, "dependencies": {"@types/node": "^16.18.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/jest": "^27.5.2", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.15", "postcss": "^8.4.29"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}