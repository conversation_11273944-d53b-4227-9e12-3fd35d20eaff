# Trinks Cliente - Aplicação de Cliente

Esta é a aplicação cliente que simula o WhatsApp do cliente para testar o sistema de atendimento inteligente da Trinks.

## 🚀 Como usar

### 1. Instalar dependências
```bash
npm install
```

### 2. Iniciar a aplicação
```bash
npm start
```

A aplicação cliente rodará em: http://localhost:3000

### 3. Como testar

1. **Preencha os dados do cliente:**
   - Nome: Digite seu nome (ex: "Maria Silva")
   - Telefone: Digite um número de telefone brasileiro (ex: "21999999999")

2. **Inicie a conversa:**
   - Clique em "Iniciar Conversa"
   - O sistema buscará o cliente na API da Trinks
   - Se não encontrar, criará um cliente novo

3. **Envie mensagens:**
   - Digite mensagens como um cliente real
   - Exemplos: "Oi! Quero agendar um horário", "Bom dia! Preciso cortar o cabelo"
   - A IA do salão responderá automaticamente

## 🎯 Objetivo

Esta aplicação simula o lado do cliente para criar um ambiente de teste mais realista para o sistema de atendimento do salão. Permite testar:

- Comunicação cliente-salão em tempo real
- Resposta automática da IA
- Transferência para atendente humano
- Integração com API da Trinks

## 🔄 Fluxo de Teste Completo

1. **Inicie o servidor** (terminal 1):
   ```bash
   cd ../server
   npm run dev
   ```

2. **Inicie a aplicação do salão** (terminal 2):
   ```bash
   cd ../client
   npm start
   ```

3. **Inicie a aplicação do cliente** (terminal 3):
   ```bash
   cd ../customer-app
   npm start
   ```

4. **Teste o fluxo:**
   - No app cliente (localhost:3000): Envie mensagens
   - No app salão (localhost:3000): Veja as respostas da IA e gerencie conversas
   - Use o botão "Transferir" no salão para alternar entre IA e atendente humano

## 📱 Interface

A interface simula o WhatsApp com:
- Tela de configuração inicial (nome + telefone)
- Chat com mensagens em tempo real
- Indicador de digitação do salão
- Status de conexão
- Interface responsiva e amigável