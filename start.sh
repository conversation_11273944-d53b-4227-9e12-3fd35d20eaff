#!/bin/bash

# 🎯 Trinks IA - Script de Inicialização Unificado
# Sistema de Atendimento Inteligente para Salões de Beleza
# Versão: 2.0

set -e  # Exit on any error

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Variables
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVER_PID=""
CLIENT_PID=""
CUSTOMER_PID=""

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}${BOLD}$1${NC}"
}

# Função para verificar se uma porta está em uso
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Porta em uso
    else
        return 1  # Porta livre
    fi
}

# Função para parar processos em uma porta
kill_port() {
    local port=$1
    local service_name=$2
    
    if check_port $port; then
        log_warning "Parando $service_name na porta $port..."
        lsof -ti:$port | xargs kill -9 2>/dev/null || true
        sleep 2
        
        if check_port $port; then
            log_error "Não foi possível parar $service_name na porta $port"
            return 1
        else
            log_success "$service_name parado"
        fi
    else
        log_info "$service_name não estava rodando na porta $port"
    fi
}

# Função para verificar e instalar dependências
check_dependencies() {
    log_info "Verificando dependências..."
    
    # Dependências principais
    if [ ! -d "$PROJECT_ROOT/node_modules" ]; then
        log_warning "Instalando dependências principais..."
        cd "$PROJECT_ROOT" && npm install
    fi
    
    # Dependências do servidor
    if [ ! -d "$PROJECT_ROOT/server/node_modules" ]; then
        log_warning "Instalando dependências do servidor..."
        cd "$PROJECT_ROOT/server" && npm install
    fi
    
    # Dependências do cliente (salão)
    if [ ! -d "$PROJECT_ROOT/client/node_modules" ]; then
        log_warning "Instalando dependências do cliente..."
        cd "$PROJECT_ROOT/client" && npm install
    fi
    
    # Dependências do customer app
    if [ ! -d "$PROJECT_ROOT/customer-app/node_modules" ]; then
        log_warning "Instalando dependências do customer app..."
        cd "$PROJECT_ROOT/customer-app" && npm install
    fi
    
    cd "$PROJECT_ROOT"
    log_success "Dependências verificadas"
}

# Função de cleanup
cleanup() {
    echo ""
    log_info "Parando todos os serviços..."
    
    # Parar processos pelos PIDs
    if [ -n "$SERVER_PID" ]; then kill $SERVER_PID 2>/dev/null || true; fi
    if [ -n "$CLIENT_PID" ]; then kill $CLIENT_PID 2>/dev/null || true; fi
    if [ -n "$CUSTOMER_PID" ]; then kill $CUSTOMER_PID 2>/dev/null || true; fi
    
    # Parar por porta também
    kill_port 3001 "Backend"
    kill_port 3002 "Frontend Salão"
    kill_port 3000 "Customer App"
    
    log_success "Todos os serviços foram parados"
    exit 0
}

# Configurar trap para cleanup
trap cleanup SIGINT SIGTERM

# Função para mostrar status
show_status() {
    echo ""
    log_header "📊 STATUS DOS SERVIÇOS"
    echo "════════════════════════════════════════"
    
    if check_port 3001; then
        echo -e "   🟢 Backend: ${GREEN}RODANDO${NC} (http://localhost:3001)"
    else
        echo -e "   🔴 Backend: ${RED}PARADO${NC}"
    fi
    
    if check_port 3002; then
        echo -e "   🟢 Frontend Salão: ${GREEN}RODANDO${NC} (http://localhost:3002)"
    else
        echo -e "   🔴 Frontend Salão: ${RED}PARADO${NC}"
    fi
    
    if check_port 3000; then
        echo -e "   🟢 Customer App: ${GREEN}RODANDO${NC} (http://localhost:3000)"
    else
        echo -e "   🔴 Customer App: ${RED}PARADO${NC}"
    fi
    
    # Verificar arquivos importantes
    echo ""
    log_header "📁 VERIFICAÇÕES DO SISTEMA"
    echo "════════════════════════════════════════"
    
    if [ -f "$PROJECT_ROOT/.env" ]; then
        echo -e "   ✅ Arquivo .env: ${GREEN}ENCONTRADO${NC}"
        if grep -q "ANTHROPIC_API_KEY" "$PROJECT_ROOT/.env" 2>/dev/null; then
            echo -e "   ✅ ANTHROPIC_API_KEY: ${GREEN}CONFIGURADA${NC}"
        else
            echo -e "   ⚠️  ANTHROPIC_API_KEY: ${YELLOW}NÃO CONFIGURADA${NC}"
        fi
    else
        echo -e "   ❌ Arquivo .env: ${RED}NÃO ENCONTRADO${NC}"
    fi
    
    if [ -d "$PROJECT_ROOT/node_modules" ]; then
        echo -e "   ✅ Dependências principais: ${GREEN}INSTALADAS${NC}"
    else
        echo -e "   ❌ Dependências principais: ${RED}NÃO INSTALADAS${NC}"
    fi
    
    echo ""
}

# Função para iniciar backend
start_backend() {
    log_info "Iniciando backend (porta 3001)..."
    cd "$PROJECT_ROOT/server"
    
    if [ -f "index.js" ]; then
        node index.js &
        SERVER_PID=$!
        log_success "Backend iniciado com PID: $SERVER_PID"
    else
        log_error "Arquivo index.js não encontrado no diretório server/"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
    sleep 3
}

# Função para iniciar frontend salão
start_frontend() {
    log_info "Iniciando frontend do salão (porta 3002)..."
    cd "$PROJECT_ROOT/client"
    
    if [ -f "package.json" ]; then
        npm run dev &
        CLIENT_PID=$!
        log_success "Frontend salão iniciado com PID: $CLIENT_PID"
    else
        log_error "Arquivo package.json não encontrado no diretório client/"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
    sleep 3
}

# Função para iniciar customer app
start_customer() {
    log_info "Iniciando customer app (porta 3000)..."
    cd "$PROJECT_ROOT/customer-app"
    
    if [ -f "package.json" ]; then
        npm start &
        CUSTOMER_PID=$!
        log_success "Customer app iniciado com PID: $CUSTOMER_PID"
    else
        log_error "Arquivo package.json não encontrado no diretório customer-app/"
        exit 1
    fi
    
    cd "$PROJECT_ROOT"
    sleep 3
}

# Função para iniciar todos os serviços
start_all() {
    log_header "🚀 INICIANDO SISTEMA COMPLETO"
    echo "════════════════════════════════════════"
    
    check_dependencies
    
    # Limpar portas antes de iniciar
    kill_port 3001 "Backend"
    kill_port 3002 "Frontend Salão"
    kill_port 3000 "Customer App"
    
    start_backend
    start_frontend
    start_customer
    
    echo ""
    log_success "Sistema iniciado com sucesso!"
    echo ""
    log_header "🌐 ACESSE AS APLICAÇÕES:"
    echo "════════════════════════════════════════"
    echo -e "   🏢 ${BOLD}Salão (CRM):${NC}     http://localhost:3002"
    echo -e "   👤 ${BOLD}Cliente:${NC}         http://localhost:3000"
    echo -e "   🔧 ${BOLD}API Backend:${NC}     http://localhost:3001"
    echo ""
    log_header "📱 COMO TESTAR:"
    echo "════════════════════════════════════════"
    echo "1. Acesse http://localhost:3000 (app do cliente)"
    echo "2. Configure nome e telefone"
    echo "3. Inicie uma conversa"
    echo "4. Acesse http://localhost:3002 (app do salão)"
    echo "5. Veja a conversa aparecer automaticamente"
    echo ""
    log_info "Pressione Ctrl+C para parar todos os serviços"
    echo ""
    
    # Aguardar processos
    wait
}

# Função para parar todos os serviços
stop_all() {
    log_header "🛑 PARANDO TODOS OS SERVIÇOS"
    echo "════════════════════════════════════════"
    
    kill_port 3001 "Backend"
    kill_port 3002 "Frontend Salão"
    kill_port 3000 "Customer App"
    
    # Parar outros processos relacionados
    log_info "Verificando outros processos Node.js..."
    
    # Processos específicos do projeto
    PIDS=$(ps aux | grep -E "(trinks|atendimento)" | grep -v grep | awk '{print $2}' 2>/dev/null || true)
    if [ -n "$PIDS" ]; then
        log_warning "Parando processos relacionados..."
        for pid in $PIDS; do
            kill -9 $pid 2>/dev/null || true
        done
    fi
    
    log_success "Todos os serviços foram parados"
}

# Função para mostrar logs
show_logs() {
    log_header "📋 LOGS DO SISTEMA"
    echo "════════════════════════════════════════"
    
    echo "Escolha qual log visualizar:"
    echo "1) Logs do servidor"
    echo "2) Logs de erro"
    echo "3) Logs em tempo real"
    echo ""
    read -p "Digite sua opção (1-3): " LOG_OPTION
    
    case $LOG_OPTION in
        1)
            if [ -f "$PROJECT_ROOT/server/logs/combined.log" ]; then
                tail -50 "$PROJECT_ROOT/server/logs/combined.log"
            else
                log_warning "Arquivo de log não encontrado"
            fi
            ;;
        2)
            if [ -f "$PROJECT_ROOT/server/logs/error.log" ]; then
                tail -50 "$PROJECT_ROOT/server/logs/error.log"
            else
                log_warning "Arquivo de log de erro não encontrado"
            fi
            ;;
        3)
            log_info "Pressione Ctrl+C para voltar ao menu"
            if [ -f "$PROJECT_ROOT/server/logs/combined.log" ]; then
                tail -f "$PROJECT_ROOT/server/logs/combined.log"
            else
                log_warning "Arquivo de log não encontrado"
            fi
            ;;
        *)
            log_error "Opção inválida"
            ;;
    esac
}

# Função para limpar cache
clean_cache() {
    log_header "🧹 LIMPANDO CACHE E SESSÕES"
    echo "════════════════════════════════════════"
    
    # Parar serviços primeiro
    stop_all
    
    log_info "Limpando node_modules..."
    rm -rf "$PROJECT_ROOT/node_modules" 2>/dev/null || true
    rm -rf "$PROJECT_ROOT/server/node_modules" 2>/dev/null || true
    rm -rf "$PROJECT_ROOT/client/node_modules" 2>/dev/null || true
    rm -rf "$PROJECT_ROOT/customer-app/node_modules" 2>/dev/null || true
    
    log_info "Limpando cache npm..."
    npm cache clean --force 2>/dev/null || true
    
    log_info "Limpando builds..."
    rm -rf "$PROJECT_ROOT/client/dist" 2>/dev/null || true
    rm -rf "$PROJECT_ROOT/client/build" 2>/dev/null || true
    rm -rf "$PROJECT_ROOT/customer-app/build" 2>/dev/null || true
    
    log_info "Limpando sessões e logs..."
    rm -rf "$PROJECT_ROOT/sessions" 2>/dev/null || true
    rm -rf "$PROJECT_ROOT/.wwebjs_auth" 2>/dev/null || true
    rm -rf "$PROJECT_ROOT/server/.wwebjs_auth" 2>/dev/null || true
    
    log_success "Cache e sessões limpos"
    log_info "Execute './start.sh' para reinstalar dependências"
}

# Função do menu principal
show_menu() {
    clear
    log_header "🎯 TRINKS IA - SISTEMA DE ATENDIMENTO INTELIGENTE"
    echo "════════════════════════════════════════════════════════════════"
    echo "   Sistema completo para salões de beleza com IA conversacional"
    echo "════════════════════════════════════════════════════════════════"
    
    show_status
    
    echo ""
    log_header "🎯 OPÇÕES DISPONÍVEIS:"
    echo "════════════════════════════════════════"
    echo ""
    echo "📱 SERVIÇOS:"
    echo "   1) 🚀 Iniciar sistema completo"
    echo "   2) 🔧 Iniciar apenas backend"
    echo "   3) 🏢 Iniciar apenas frontend salão"
    echo "   4) 👤 Iniciar apenas customer app"
    echo ""
    echo "🛠️  GERENCIAMENTO:"
    echo "   5) 🛑 Parar todos os serviços"
    echo "   6) 📊 Mostrar status detalhado"
    echo "   7) 📋 Ver logs"
    echo "   8) 🧹 Limpar cache e reinstalar"
    echo ""
    echo "   0) 🚪 Sair"
    echo ""
    
    read -p "Digite sua opção (0-8): " MENU_OPTION
    
    case $MENU_OPTION in
        1)
            start_all
            ;;
        2)
            check_dependencies
            kill_port 3001 "Backend"
            start_backend
            log_info "Backend rodando. Pressione Ctrl+C para parar"
            wait
            ;;
        3)
            check_dependencies
            kill_port 3002 "Frontend Salão"
            start_frontend
            log_info "Frontend salão rodando. Pressione Ctrl+C para parar"
            wait
            ;;
        4)
            check_dependencies
            kill_port 3000 "Customer App"
            start_customer
            log_info "Customer app rodando. Pressione Ctrl+C para parar"
            wait
            ;;
        5)
            stop_all
            echo ""
            read -p "Pressione Enter para voltar ao menu..."
            show_menu
            ;;
        6)
            show_status
            echo ""
            read -p "Pressione Enter para voltar ao menu..."
            show_menu
            ;;
        7)
            show_logs
            echo ""
            read -p "Pressione Enter para voltar ao menu..."
            show_menu
            ;;
        8)
            clean_cache
            echo ""
            read -p "Pressione Enter para voltar ao menu..."
            show_menu
            ;;
        0)
            log_info "Saindo..."
            echo "👋 Obrigado por usar o Trinks IA!"
            exit 0
            ;;
        *)
            log_error "Opção inválida"
            sleep 2
            show_menu
            ;;
    esac
}

# Verificar argumentos da linha de comando
case "${1:-}" in
    --all)
        start_all
        ;;
    --stop)
        stop_all
        ;;
    --status)
        show_status
        ;;
    --clean)
        clean_cache
        ;;
    --help)
        echo "🎯 Trinks IA - Sistema de Atendimento Inteligente"
        echo ""
        echo "Uso: ./start.sh [opção]"
        echo ""
        echo "Opções:"
        echo "  (sem argumento)  Mostrar menu interativo"
        echo "  --all           Iniciar sistema completo"
        echo "  --stop          Parar todos os serviços"
        echo "  --status        Mostrar status dos serviços"
        echo "  --clean         Limpar cache e reinstalar"
        echo "  --help          Mostrar esta ajuda"
        echo ""
        ;;
    "")
        show_menu
        ;;
    *)
        log_error "Opção inválida: $1"
        echo "Use './start.sh --help' para ver as opções disponíveis"
        exit 1
        ;;
esac