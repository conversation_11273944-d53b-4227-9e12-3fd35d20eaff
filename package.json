{"name": "atendimento-inteligente", "version": "2.0.0", "description": "Sistema de atendimento inteligente para salão de beleza com IA", "main": "server/app.js", "engines": {"node": ">=16.0.0"}, "scripts": {"start": "node server/app.js", "dev": "nodemon server/app.js", "test": "node server/tests/automated/run-tests.js", "test:unit": "node server/tests/automated/run-tests.js unit", "test:integration": "node server/tests/automated/run-tests.js integration", "test:e2e": "node server/tests/automated/run-tests.js e2e", "test:security": "node server/tests/security.js", "test:validation": "node server/tests/validation.js", "test:watch": "jest --config=server/tests/automated/config/jest.config.js --watch", "test:coverage": "jest --config=server/tests/automated/config/jest.config.js --coverage", "lint": "eslint server/**/*.js --fix", "format": "prettier --write server/**/*.js", "build": "echo 'No build step required for Node.js'", "clean": "rm -rf coverage .jest-cache node_modules/.cache", "docs": "jsdoc -c jsdoc.conf.json", "security-audit": "npm audit && node server/tests/security.js", "performance-test": "node server/tests/performance.js", "validate-system": "node server/tests/validation.js", "setup": "npm install && npm run validate-system", "deploy-check": "npm run test && npm run security-audit && npm run lint"}, "keywords": ["ai", "chatbot", "beauty-salon", "scheduling", "customer-service", "whatsapp", "nodejs", "express", "langchain"], "author": "Sistema de Atendimento Inteligente", "license": "MIT", "dependencies": {"@langchain/community": "^0.3.50", "@langchain/core": "^0.3.70", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "form-data": "^4.0.0", "helmet": "^7.1.0", "langchain": "^0.3.30", "mime-types": "^2.1.35", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cache": "^5.1.2", "node-fetch": "^3.3.2", "openai": "^4.20.1", "puppeteer": "^21.5.2", "redis": "^4.6.11", "sharp": "^0.32.6", "socket.io-client": "^4.8.1", "uuid": "^9.0.1", "validator": "^13.11.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.14.2"}, "devDependencies": {"@babel/preset-env": "^7.23.5", "babel-jest": "^29.7.0", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-node": "^11.1.0", "jest": "^29.7.0", "jest-html-reporters": "^3.1.5", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "jsdoc": "^4.0.2", "nock": "^13.4.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "sinon": "^17.0.1", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["<rootDir>/server/tests/automated/config/test-setup.js"], "testMatch": ["<rootDir>/server/tests/automated/**/*.test.js"], "collectCoverage": true, "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "collectCoverageFrom": ["server/**/*.js", "!server/tests/**", "!server/node_modules/**"]}, "babel": {"presets": [["@babel/preset-env", {"targets": {"node": "16"}}]]}, "eslintConfig": {"env": {"node": true, "es2021": true, "jest": true}, "extends": ["standard"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module"}, "rules": {"no-console": "off", "no-unused-vars": ["error", {"argsIgnorePattern": "^_"}]}}, "prettier": {"semi": true, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5"}, "repository": {"type": "git", "url": "https://github.com/seu-usuario/atendimento-inteligente.git"}, "bugs": {"url": "https://github.com/seu-usuario/atendimento-inteligente/issues"}, "homepage": "https://github.com/seu-usuario/atendimento-inteligente#readme"}