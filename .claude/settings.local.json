{"permissions": {"allow": ["Bash(TRINKS_API_KEY=i0fFPhCnix6JuqiOWAIZNaJNHZx89zst9zfSH22c TRINKS_ESTABLISHMENT_ID=188253 ANTHROPIC_API_KEY=sk-ant-api03-pGHJMCHgFiOhO59zGYu0FGHJqaJgX5gx1YZR9HI7W-N6rZfBKXyOUgUhJ4LQ1DsE0gqF1-9OdKkgNJwQJfzXIA-jSAQBAA node -e \"\nconsole.log(''🧪 TESTE CORRIGIDO: Gerar primeiro log com parâmetros corretos'');\nconsole.log(''💬 Mensagem: \"\"Teria massagem quick ara amanha com o <PERSON>lson?\"\"'');\nconsole.log(''=''.repeat(80));\n\nconst aiService = require(''./services/ai'');\n\n// Criar prompt de teste simples \nconst testPrompt = ''Você é um atendente de salão de beleza. Responda de forma natural e brasileira à pergunta do cliente: \"\"Teria massagem quick ara amanha com o Jailson?\"\"'';\n\n// Conversation messages simples\nconst conversationMessages = [\n  { role: ''user'', content: ''Teria massagem quick ara amanha com o Jailson?'' }\n];\n\nconsole.log(''📋 TESTE SIMPLIFICADO:'');\nconsole.log(''  📝 Prompt:'', testPrompt.substring(0, 100) + ''...'');\nconsole.log(''  💬 Mensagens:'', conversationMessages.length);\n\n(async () => {\n  try {\n    console.log(''\\n🔄 CHAMANDO generateSimpleResponse COM PARÂMETROS CORRETOS...'');\n    \n    const startTime = Date.now();\n    \n    // Chamada com assinatura correta: generateSimpleResponse(prompt, conversationContext)\n    const response = await aiService.generateSimpleResponse(\n      testPrompt,           // string: prompt\n      conversationMessages  // array: contexto de mensagens\n    );\n    \n    const duration = Date.now() - startTime;\n    \n    console.log(''\\n📊 RESULTADO DA CHAMADA:'');\n    console.log(''  ✅ Resposta gerada:'', !!response);\n    console.log(''  📏 Tamanho resposta:'', response ? response.length : 0, ''chars'');\n    console.log(''  ⏱️ Duração total:'', duration + ''ms'');\n    \n    if (response) {\n      console.log(''  💬 Resposta gerada:'');\n      console.log(''    \"\"'' + response + ''\"\"'');\n    }\n    \n    console.log(''\\n🗂️ AGUARDANDO LOGS ASSÍNCRONOS...'');\n    \n    // Aguardar logs assíncronos (tempo maior para garantir)\n    await new Promise(resolve => setTimeout(resolve, 5000));\n    \n    // Verificar se logs foram criados\n    const fs = require(''fs'');\n    const path = require(''path'');\n    const logsDir = path.join(__dirname, ''logs'', ''claude-evaluation'');\n    \n    try {\n      if (!fs.existsSync(logsDir)) {\n        console.log(''\\n❌ PASTA DE LOGS NÃO EXISTE:'', logsDir);\n        return;\n      }\n      \n      const files = fs.readdirSync(logsDir);\n      const mdFiles = files.filter(f => f.endsWith(''.md''));\n      const sortedFiles = mdFiles.sort();\n      \n      console.log(''\\n📂 VERIFICAÇÃO DE LOGS:'');\n      console.log(''  📁 Pasta:'', logsDir);\n      console.log(''  📄 Total arquivos:'', files.length);\n      console.log(''  📄 Arquivos .md encontrados:'', mdFiles.length);\n      \n      if (files.length > 0) {\n        console.log(''  📂 Todos os arquivos:'', files.join('', ''));\n      }\n      \n      if (mdFiles.length > 0) {\n        const firstFile = sortedFiles[0];\n        const latestFile = sortedFiles[sortedFiles.length - 1];\n        \n        console.log(''  🔍 Primeiro arquivo:'', firstFile);  \n        console.log(''  🔍 Arquivo mais recente:'', latestFile);\n        \n        // Mostrar conteúdo do primeiro arquivo de log\n        const firstLogPath = path.join(logsDir, firstFile);\n        const firstLogContent = fs.readFileSync(firstLogPath, ''utf8'');\n        \n        console.log(''\\n'' + ''=''.repeat(80));\n        console.log(''📄 PRIMEIRO ARQUIVO DE LOG GERADO COM SUCESSO!'');\n        console.log(''📝 Nome: '' + firstFile);\n        console.log(''📏 Tamanho: '' + firstLogContent.length + '' chars'');\n        console.log(''=''.repeat(80));\n        \n        // Mostrar apenas primeiro trecho do log (para não saturar)\n        const lines = firstLogContent.split(''\\n'');\n        const preview = lines.slice(0, 50).join(''\\n'');\n        console.log(preview);\n        if (lines.length > 50) {\n          console.log(''\\n... [arquivo truncado - '' + (lines.length - 50) + '' linhas restantes]'');\n        }\n        \n        console.log(''=''.repeat(80));\n        \n        // Análise do arquivo\n        const hasCallId = firstLogContent.includes(''ID da Chamada'');\n        const hasEvaluation = firstLogContent.includes(''Avaliação Automática'');\n        const hasScores = firstLogContent.includes(''Pontuações (1-10)'');\n        const hasATENCAO = firstFile.startsWith(''ATENCAO-'');\n        const hasJailsonContext = firstLogContent.includes(''Jailson'') || firstLogContent.includes(''massagem'');\n        \n        console.log(''\\n📊 VALIDAÇÃO DO LOG:'');\n        console.log(''  ✅ Tem ID da chamada:'', hasCallId);\n        console.log(''  ✅ Tem avaliação automática:'', hasEvaluation);\n        console.log(''  ✅ Tem pontuações 1-10:'', hasScores);\n        console.log(''  ⚠️ Tem prefixo ATENÇÃO:'', hasATENCAO);\n        console.log(''  ✅ Contexto Jailson/massagem:'', hasJailsonContext);\n        \n        console.log(''\\n🎉 SUCESSO COMPLETO!'');\n        console.log(''✅ Erro de parâmetros corrigido'');\n        console.log(''✅ Log individual gerado com sucesso'');\n        console.log(''✅ Avaliação por Sonnet 4 funcionando'');\n        console.log(''✅ Sistema de interceptação transparente ativo'');\n        \n      } else {\n        console.log(''\\n❌ NENHUM LOG .MD ENCONTRADO'');\n        console.log(''⚠️ Isso pode indicar que a interceptação não funcionou ou avaliação falhou'');\n        \n        if (files.length > 0) {\n          console.log(''📂 Mas existem outros arquivos na pasta de logs'');\n        }\n      }\n      \n    } catch (logError) {\n      console.log(''\\n❌ Erro ao verificar logs:'', logError.message);\n    }\n    \n  } catch (error) {\n    console.error(''\\n❌ ERRO no teste corrigido:'', error.message);\n    console.error(''Stack:'', error.stack);\n  }\n})();\n\")"], "deny": [], "ask": []}}