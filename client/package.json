{"name": "trinks-ia-client", "version": "0.1.0", "private": true, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-query": "^5.84.2", "@testing-library/dom": "^10.4.1", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "axios": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.537.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.62.0", "react-icons": "^5.5.0", "react-router-dom": "^7.8.0", "react-scripts": "5.0.1", "socket.io-client": "^4.7.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "web-vitals": "^2.1.4", "zod": "^4.0.15"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^27.5.2", "@types/node": "^24.2.1", "@vitejs/plugin-react-swc": "^4.0.0", "autoprefixer": "^10.4.15", "postcss": "^8.4.29", "tailwindcss": "^3.3.0", "typescript": "^5.9.2", "vite": "^7.1.1"}, "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "preview": "vite preview", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:ci": "react-scripts test --coverage --watchAll=false --ci", "test:integration": "react-scripts test --testPathPattern=integration", "test:e2e": "react-scripts test --testPathPattern=e2e", "test:components": "react-scripts test --testPathPattern=components"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"moduleNameMapper": {"^@/(.*)$": "<rootDir>/src/$1"}, "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/tests/**/*", "!src/index.tsx", "!src/reportWebVitals.ts"], "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}}}