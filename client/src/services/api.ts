import axios from 'axios';
import { Customer, AvailabilityResponse, AppointmentRequest } from '../types/index';

const api = axios.create({
  baseURL: 'http://localhost:3001/api',
  timeout: 10000,
});

// Request interceptor for logging
api.interceptors.request.use(
  (config) => {
    console.log(`🌐 [CRM] API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ [CRM] API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for logging and error handling
api.interceptors.response.use(
  (response) => {
    console.log(`✅ [CRM] API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ [CRM] API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

export const customerAPI = {
  async getByPhone(telefone: string): Promise<Customer | null> {
    try {
      const response = await api.get(`/cliente/${telefone}`);
      return response.data;
    } catch (error: any) {
      if (error.response?.status === 404) {
        return null;
      }
      throw error;
    }
  },
};

export const scheduleAPI = {
  async getAvailability(data: string, servico?: string): Promise<AvailabilityResponse> {
    const response = await api.get('/agenda/disponibilidade', {
      params: { data, servico }
    });
    return response.data;
  },

  async createAppointment(appointment: AppointmentRequest) {
    const response = await api.post('/agendamento', appointment);
    return response.data;
  },
};

export const servicesAPI = {
  async getAll() {
    const response = await api.get('/servicos');
    return response.data;
  },
};

export const professionalsAPI = {
  async getAll() {
    const response = await api.get('/profissionais');
    return response.data;
  },
};

// New Architecture APIs
export const conversationAPI = {
  // Start new conversation
  async startConversation(customerInfo: any) {
    const response = await api.post('/conversation/start', { customerInfo });
    return response.data;
  },

  // Send message through state machine
  async sendMessage(sessionId: string, message: string, userId?: string) {
    const response = await api.post('/conversation', {
      message,
      sessionId,
      userId
    });
    return response.data;
  },

  // Get conversation history
  async getHistory(sessionId: string, limit?: number, offset?: number) {
    const response = await api.get(`/conversation/history/${sessionId}`, {
      params: { limit, offset }
    });
    return response.data;
  },

  // Get conversation state
  async getState(sessionId: string) {
    const response = await api.get(`/conversation/state/${sessionId}`);
    return response.data;
  },

  // Reset conversation
  async resetConversation(sessionId: string) {
    const response = await api.post(`/conversation/reset/${sessionId}`);
    return response.data;
  },

  // Get all active sessions
  async getSessions() {
    const response = await api.get('/conversation/sessions');
    return response.data;
  },

  // Classify intent without processing
  async classifyIntent(message: string, context?: any) {
    const response = await api.post('/conversation/classify', {
      message,
      context
    });
    return response.data;
  },

  // Get conversation metrics
  async getMetrics() {
    const response = await api.get('/conversation/metrics');
    return response.data;
  }
};

export const promptsAPI = {
  // Get all prompts
  async getAll() {
    const response = await api.get('/prompts');
    return response.data;
  },

  // Get specific prompt
  async getPrompt(category: string, name: string) {
    const response = await api.get(`/prompts/${category}/${name}`);
    return response.data;
  },

  // Update prompt
  async updatePrompt(category: string, name: string, content: string) {
    const response = await api.put(`/prompts/${category}/${name}`, { content });
    return response.data;
  },

  // Create new prompt
  async createPrompt(category: string, name: string, content: string) {
    const response = await api.post(`/prompts/${category}/${name}`, { content });
    return response.data;
  },

  // Delete prompt
  async deletePrompt(category: string, name: string) {
    const response = await api.delete(`/prompts/${category}/${name}`);
    return response.data;
  },

  // Preview prompt with variables
  async previewPrompt(category: string, name: string, variables?: any) {
    const response = await api.post(`/prompts/${category}/${name}/preview`, { variables });
    return response.data;
  },

  // Reload prompts from files
  async reloadPrompts() {
    const response = await api.post('/prompts/reload');
    return response.data;
  },

  // Get and set global variables
  async getVariables() {
    const response = await api.get('/prompts/variables');
    return response.data;
  },

  async setVariables(variables: any) {
    const response = await api.put('/prompts/variables', { variables });
    return response.data;
  },

  // Export prompts
  async exportPrompts() {
    const response = await api.get('/prompts/export');
    return response.data;
  },

  // Validate prompt syntax
  async validatePrompt(category: string, name: string, content: string) {
    const response = await api.post(`/prompts/${category}/${name}/validate`, { content });
    return response.data;
  },

  // Get prompt usage statistics
  async getStats() {
    const response = await api.get('/prompts/stats');
    return response.data;
  }
};

// Template API - integrated with existing backend /api/prompt-templates
export const templatesAPI = {
  // List all templates
  async getAll() {
    const response = await api.get('/prompt-templates');
    return response.data;
  },

  // Get specific template
  async getTemplate(type: string) {
    const response = await api.get(`/prompt-templates/${type}`);
    return response.data;
  },

  // Update template
  async updateTemplate(type: string, templateData: {
    name: string;
    template: string;
    description?: string;
    variables?: string[];
    maxTokens?: number;
    temperature?: number;
  }) {
    const response = await api.put(`/prompt-templates/${type}`, templateData);
    return response.data;
  },

  // Reset template to default
  async resetTemplate(type: string) {
    const response = await api.delete(`/prompt-templates/${type}`);
    return response.data;
  },

  // Validate template
  async validateTemplate(type: string, templateData: {
    name: string;
    template: string;
    description?: string;
    variables?: string[];
    maxTokens?: number;
    temperature?: number;
  }) {
    const response = await api.post(`/prompt-templates/${type}/validate`, templateData);
    return response.data;
  },

  // Test template with example data
  async previewTemplate(type: string, variables?: Record<string, any>) {
    const response = await api.post(`/prompt-templates/${type}/test`, { variables, useCustom: true });
    return response.data;
  },

  // Get cache statistics
  async getCacheStats() {
    const response = await api.get('/prompt-templates/cache/stats');
    return response.data;
  }
};

export const systemAPI = {
  // Health check
  async healthCheck() {
    const response = await api.get('/health');
    return response.data;
  },

  // Get system metrics
  async getMetrics() {
    const response = await api.get('/metrics');
    return response.data;
  }
};

export default api;