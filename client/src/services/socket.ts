import { io, Socket } from 'socket.io-client';
import { Message, Conversation, Customer } from '../types/index';
import Logger from '../utils/logger';

class SocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect() {
    if (this.socket?.connected) {
      return;
    }

    // Connect directly to the backend server
    this.socket = io('http://localhost:3001', {
      transports: ['websocket', 'polling'],
      upgrade: true,
      timeout: 20000,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5
    });

    this.socket.on('connect', () => {
      console.log('🔌 [CRM] Connected to refactored server');
      Logger.socket('Connected to refactored server', { 
        socketId: this.socket?.id,
        architecture: 'v2.0-refactored'
      });
      this.reconnectAttempts = 0;
      
      // Emit connection event for the new architecture
      this.socket?.emit('crm_connected', {
        clientType: 'crm',
        architecture: 'v2.0-refactored'
      });

      // Join admin room to receive broadcasts
      this.joinAdminRoom();
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ Disconnected from server:', reason);
      Logger.socket('Disconnected from server', { reason });
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.socket?.connect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('Connection error:', error);
      this.handleReconnect();
    });

    this.socket.on('reconnect', (attemptNumber) => {
      console.log('🔄 Reconnected to server after', attemptNumber, 'attempts');
    });

    this.socket.on('reconnect_error', (error) => {
      console.error('Reconnection error:', error);
    });

    return this.socket;
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, 2000 * this.reconnectAttempts);
    } else {
      console.error('❌ Max reconnection attempts reached');
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }

  // Message events - Updated for new architecture
  sendMessage(conversationId: string, content: string, customerPhone: string) {
    if (this.socket) {
      // Use the new customer_send_message event from SocketStateMachineAdapter
      this.socket.emit('customer_send_message', {
        conversationId,
        content,
        customerPhone
      });
    }
  }

  // Admin sends message through CRM
  sendAdminMessage(sessionId: string, message: string) {
    if (this.socket) {
      this.socket.emit('admin_send_message', {
        sessionId,
        message,
        fromAdmin: true
      });
    }
  }

  async sendAudio(conversationId: string, audioBlob: Blob, duration: number, customerPhone: string) {
    if (this.socket && audioBlob) {
      try {
        // Convert blob to base64 for transmission
        const base64Audio = await this.blobToBase64(audioBlob);
        
        console.log('🎤 Sending audio via WebSocket:');
        console.log('  - Duration:', duration);
        console.log('  - Base64 length:', base64Audio.length);
        console.log('  - Base64 starts with:', base64Audio.substring(0, 50));
        
        this.socket.emit('send_audio', {
          conversationId,
          audioData: base64Audio,
          duration,
          customerPhone
        });
      } catch (error) {
        console.error('Error sending audio:', error);
      }
    }
  }

  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        if (typeof reader.result === 'string') {
          resolve(reader.result);
        } else {
          reject(new Error('Failed to convert blob to base64'));
        }
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  onMessageReceived(callback: (data: { conversationId: string; message: Message }) => void) {
    if (this.socket) {
      // Listen for the new salon_response event from SocketStateMachineAdapter
      this.socket.on('salon_response', (data) => {
        console.log('🔥 [CRM] salon_response event:', data);
        // Convert to the expected format
        const formattedData = {
          conversationId: data.conversationId,
          message: data.message
        };
        callback(formattedData);
      });

      // Keep backward compatibility
      this.socket.on('message_received', (data) => {
        console.log('🔥 [CRM] message_received event:', data);
        callback(data);
      });
    }
  }

  onMessageSent(callback: (data: { conversationId: string; message: Message }) => void) {
    if (this.socket) {
      this.socket.on('message_sent', callback);
    }
  }

  onMessageUpdated(callback: (data: { conversationId: string; message: Message }) => void) {
    if (this.socket) {
      this.socket.on('message_updated', callback);
    }
  }

  // Customer search events
  searchCustomer(phone: string) {
    if (this.socket) {
      this.socket.emit('search_customer', { phone });
    }
  }

  onCustomerFound(callback: (data: { customer: Customer; conversationId: string }) => void) {
    if (this.socket) {
      this.socket.on('customer_found', (data) => {
        console.log('🔥 DEBUG: customer_found event in socket service:', data);
        callback(data);
      });
    }
  }

  onCustomerNotFound(callback: (data: { phone: string; conversationId: string }) => void) {
    if (this.socket) {
      this.socket.on('customer_not_found', callback);
    }
  }

  // Conversation events
  selectConversation(conversationId: string) {
    if (this.socket) {
      this.socket.emit('select_conversation', { conversationId });
    }
  }

  onConversationSelected(callback: (data: { conversationId: string; conversation: Conversation }) => void) {
    if (this.socket) {
      this.socket.on('conversation_selected', callback);
    }
  }

  onNewConversation(callback: (conversation: Conversation) => void) {
    if (this.socket) {
      this.socket.on('new_conversation', (data) => {
        console.log('🔥 DEBUG: new_conversation event in socket service:', data);
        callback(data);
      });
    }
  }

  onConversationUpdated(callback: (data: { conversationId: string; conversation: Conversation }) => void) {
    if (this.socket) {
      this.socket.on('conversation_updated', callback);
    }
  }

  // Typing events
  onAITypingStart(callback: (data: { conversationId: string }) => void) {
    if (this.socket) {
      this.socket.on('ai_typing_start', callback);
    }
  }

  onAITypingStop(callback: (data: { conversationId: string }) => void) {
    if (this.socket) {
      this.socket.on('ai_typing_stop', callback);
    }
  }

  // Simulate customer message
  emit(event: string, data: any) {
    if (this.socket) {
      this.socket.emit(event, data);
    }
  }

  // AI Mode toggle
  toggleAiMode(conversationId: string, isAiMode: boolean) {
    if (this.socket) {
      this.socket.emit('toggle_ai_mode', { conversationId, isAiMode });
    }
  }

  onAiModeToggled(callback: (data: { conversationId: string; isAiMode: boolean }) => void) {
    if (this.socket) {
      this.socket.on('ai_mode_toggled', callback);
    }
  }

  // WhatsApp events
  onWhatsAppQR(callback: (data: { qr: string }) => void) {
    if (this.socket) {
      this.socket.on('whatsapp_qr', (data) => {
        console.log('🔥 DEBUG: whatsapp_qr event in socket service:', data);
        Logger.socket('whatsapp_qr', { qrLength: data.qr?.length || 0 });
        callback(data);
      });
    }
  }

  onWhatsAppStatus(callback: (data: { status: string; type?: string; number?: string; reason?: string; message?: string }) => void) {
    if (this.socket) {
      this.socket.on('whatsapp_status', (data) => {
        console.log('🔥 DEBUG: whatsapp_status event in socket service:', data);
        callback(data);
      });
    }
  }

  onWhatsAppLog(callback: (data: { message: string; timestamp: string }) => void) {
    if (this.socket) {
      this.socket.on('whatsapp_log', (data) => {
        console.log('🔥 DEBUG: whatsapp_log event in socket service:', data);
        callback(data);
      });
    }
  }

  onWhatsAppHistoryLoaded(callback: (data: { conversationId: string; phoneNumber: string; history: any[]; totalHistoryMessages: number }) => void) {
    if (this.socket) {
      this.socket.on('whatsapp_history_loaded', (data) => {
        console.log('📚 WhatsApp history loaded:', data);
        callback(data);
      });
    }
  }

  loadMoreWhatsAppHistory(phoneNumber: string, limit: number = 10, offset: number = 0) {
    if (this.socket) {
      this.socket.emit('whatsapp_load_more_history', {
        phoneNumber,
        limit,
        offset
      });
    }
  }

  // Conversation history loading
  loadConversationHistory(phoneNumber: string, limit: number = 10) {
    if (this.socket) {
      this.socket.emit('load_conversation_history', { phoneNumber, limit });
    }
  }

  onConversationHistoryResponse(callback: (data: { success: boolean; phoneNumber: string; history: Message[]; error?: string }) => void) {
    if (this.socket) {
      this.socket.on('conversation_history_response', callback);
    }
  }

  // Error handling
  onError(callback: (error: any) => void) {
    if (this.socket) {
      this.socket.on('error', callback);
    }
  }

  // Generic event listener
  on(event: string, callback: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.on(event, callback);
    }
  }

  // One-time event listener
  once(event: string, callback: (...args: any[]) => void) {
    if (this.socket) {
      this.socket.once(event, callback);
    }
  }

  // New Architecture Events
  // Monitor all customer conversations
  onCustomerConversationUpdate(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('customer_message_processed', callback);
    }
  }

  // Monitor state machine metrics
  onMetricsUpdate(callback: (data: any) => void) {
    if (this.socket) {
      this.socket.on('metrics_update', callback);
    }
  }

  // Request current metrics
  requestMetrics() {
    if (this.socket) {
      this.socket.emit('request_metrics');
    }
  }

  // Join admin room for notifications
  joinAdminRoom() {
    if (this.socket) {
      this.socket.emit('join_admin_room');
    }
  }

  // Listen for state changes
  onStateChange(callback: (data: { sessionId: string; oldState: string; newState: string; agent: string }) => void) {
    if (this.socket) {
      this.socket.on('state_changed', callback);
    }
  }

  // Listen for intent classifications
  onIntentClassified(callback: (data: { sessionId: string; intent: string; confidence: number }) => void) {
    if (this.socket) {
      this.socket.on('intent_classified', callback);
    }
  }

  // Generic event emitter (alias for emit)
  off(event: string, callback?: (...args: any[]) => void) {
    if (this.socket) {
      if (callback) {
        this.socket.off(event, callback);
      } else {
        this.socket.off(event);
      }
    }
  }

  // Cleanup listeners
  removeAllListeners() {
    if (this.socket) {
      this.socket.removeAllListeners();
    }
  }

  get isConnected() {
    return this.socket?.connected || false;
  }

  getSocket() {
    return this.socket;
  }
}

const socketService = new SocketService();
export default socketService;