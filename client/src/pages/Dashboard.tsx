import React, { useState, useEffect, useRef } from 'react';
import { Conversation, Customer, Message } from '../types/index';
import socketService from '../services/socket';
import Sidebar from '../components/Sidebar/Sidebar';
import Chat from '../components/Chat/Chat';
import CustomerPanel from '../components/CustomerPanel/CustomerPanel';
import WebhookDebugPanel from '../components/Debug/WebhookDebugPanel';

function Dashboard() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [currentCustomer, setCurrentCustomer] = useState<Customer | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [aiModeConversations, setAiModeConversations] = useState<Map<string, boolean>>(new Map());
  const [rightSidebarWidth, setRightSidebarWidth] = useState(405);
  const [isResizing, setIsResizing] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState(false);
  const resizeRef = useRef<HTMLDivElement>(null);

  // Auto-sync active conversation with conversations list
  useEffect(() => {
    if (activeConversation) {
      const updatedConversation = conversations.find(conv => conv.id === activeConversation.id);
      if (updatedConversation && updatedConversation.messages.length !== activeConversation.messages.length) {
        setActiveConversation(prev => {
          if (!prev || prev.id !== updatedConversation.id) return prev;
          return updatedConversation;
        });
      }
    }
  }, [conversations, activeConversation?.id]);

  useEffect(() => {
    // Initialize socket connection
    const socket = socketService.connect();
    
    if (socket) {
      socket.on('connect', () => {
        setIsConnected(true);
      });
      socket.on('disconnect', () => setIsConnected(false));
    }

    // Set up event listeners
    setupSocketListeners();

    return () => {
      socketService.removeAllListeners();
      socketService.disconnect();
    };
  }, []);

  // Handle resizing
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const windowWidth = window.innerWidth;
      const newWidth = windowWidth - e.clientX;
      
      const constrainedWidth = Math.max(352, Math.min(506, newWidth));
      setRightSidebarWidth(constrainedWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const handleResizeStart = () => {
    setIsResizing(true);
  };

  const setupSocketListeners = () => {
    // Message events
    socketService.onMessageReceived(({ conversationId, message }) => {
      updateConversationMessage(conversationId, message);
    });

    socketService.onMessageSent(({ conversationId, message }) => {
      updateConversationMessage(conversationId, message);
    });

    socketService.onMessageUpdated(({ conversationId, message }) => {
      updateConversationMessage(conversationId, message);
    });

    // Customer search events
    socketService.onCustomerFound(({ customer, conversationId }) => {
      setCurrentCustomer(customer);
      
      setConversations(prev => {
        const existingConversation = prev.find(conv => 
          conv.id === conversationId || conv.customerPhone === customer.telefone
        );
        
        if (!existingConversation) {
          const newConversation: Conversation = {
            id: conversationId,
            customerPhone: customer.telefone,
            customerName: customer.nome,
            messages: [],
            lastMessage: new Date(),
            unreadCount: 0
          };
          
          setTimeout(() => {
            setActiveConversation(newConversation);
          }, 100);
          
          return [newConversation, ...prev];
        } else {
          const updatedConversations = prev.map(conv => {
            if (conv.id === conversationId || conv.customerPhone === customer.telefone) {
              return { 
                ...conv, 
                id: conversationId,
                customerName: customer.nome, 
                customerPhone: customer.telefone 
              };
            }
            return conv;
          });
          
          setTimeout(() => {
            const updatedConv = updatedConversations.find(conv => conv.id === conversationId);
            if (updatedConv) {
              setActiveConversation(updatedConv);
            }
          }, 100);
          
          return updatedConversations;
        }
      });
    });

    // ... (resto dos listeners do App.tsx original)
  };

  const updateConversationMessage = (conversationId: string, message: Message) => {
    // Mesma lógica do App.tsx original
    setConversations(prev => {
      let foundConversation = false;
      const updated = prev.map(conv => {
        if (conv.id === conversationId) {
          foundConversation = true;
          const updatedMessages = [...conv.messages];

          const existingIndex = updatedMessages.findIndex(m => m.id === message.id);

          if (existingIndex >= 0) {
            const existingMessage = updatedMessages[existingIndex];
            if (message.type === 'audio' && existingMessage.type === 'audio') {
              updatedMessages[existingIndex] = {
                ...existingMessage,
                ...message,
                type: 'audio' as const,
                audioUrl: message.audioUrl || existingMessage.audioUrl,
                audioDuration: message.audioDuration || existingMessage.audioDuration,
                audioTranscription: message.audioTranscription || existingMessage.audioTranscription,
                isProcessing: message.isProcessing !== undefined ? message.isProcessing : existingMessage.isProcessing
              };
            } else {
              updatedMessages[existingIndex] = message;
            }
          } else {
            updatedMessages.push(message);
            updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
          }

          return {
            ...conv,
            messages: updatedMessages,
            lastMessage: message.timestamp,
            unreadCount: (message.sender === 'user' && activeConversation?.id !== conversationId)
              ? conv.unreadCount + 1
              : conv.unreadCount
          };
        }
        return conv;
      });
      
      if (!foundConversation) {
        const newConversation: Conversation = {
          id: conversationId,
          customerPhone: message.sender === 'user' ? 'Unknown' : 'Unknown',
          customerName: 'Cliente',
          messages: [message],
          lastMessage: message.timestamp,
          unreadCount: message.sender === 'user' ? 1 : 0
        };
        
        return [newConversation, ...prev];
      }
      
      return updated;
    });
    
    if (activeConversation?.id === conversationId) {
      setActiveConversation(prev => {
        if (!prev) return null;

        const updatedMessages = [...prev.messages];
        const existingIndex = updatedMessages.findIndex(m => m.id === message.id);

        if (existingIndex >= 0) {
          const existingMessage = updatedMessages[existingIndex];
          if (message.type === 'audio' && existingMessage.type === 'audio') {
            updatedMessages[existingIndex] = {
              ...existingMessage,
              ...message,
              type: 'audio' as const,
              audioUrl: message.audioUrl || existingMessage.audioUrl,
              audioDuration: message.audioDuration || existingMessage.audioDuration,
              audioTranscription: message.audioTranscription || existingMessage.audioTranscription,
              isProcessing: message.isProcessing !== undefined ? message.isProcessing : existingMessage.isProcessing
            };
          } else {
            updatedMessages[existingIndex] = message;
          }
        } else {
          updatedMessages.push(message);
          updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        }

        return {
          ...prev,
          messages: updatedMessages,
          lastMessage: message.timestamp
        };
      });
    }
  };

  const handleSearchCustomer = (phone: string) => {
    socketService.searchCustomer(phone);
  };

  const handleSelectConversation = (conversation: Conversation) => {
    setActiveConversation(conversation);
    
    if (conversation.unreadCount > 0) {
      const updatedConversation = { ...conversation, unreadCount: 0 };
      setConversations(prev => 
        prev.map(conv => conv.id === conversation.id ? updatedConversation : conv)
      );
    }

    if (conversation.customerPhone) {
      socketService.searchCustomer(conversation.customerPhone);
    } else {
      setCurrentCustomer(null);
    }
  };

  const handleSendMessage = (content: string) => {
    if (!activeConversation) return;

    socketService.sendMessage(
      activeConversation.id,
      content,
      activeConversation.customerPhone
    );
  };

  const handleSendAudio = async (audioBlob: Blob, duration: number) => {
    if (!activeConversation) return;

    const audioUrl = URL.createObjectURL(audioBlob);

    const audioMessage: Message = {
      id: Date.now().toString(),
      content: 'Mensagem de áudio',
      sender: 'user',
      timestamp: new Date(),
      status: 'sent',
      type: 'audio',
      audioUrl,
      audioDuration: duration,
      isProcessing: true
    };

    updateConversationMessage(activeConversation.id, audioMessage);

    try {
      await socketService.sendAudio(
        activeConversation.id,
        audioBlob,
        duration,
        activeConversation.customerPhone
      );
    } catch (error) {
      URL.revokeObjectURL(audioUrl);
    }
  };

  const handleTransferToHuman = () => {
    if (!activeConversation) return;
    
    const conversationId = activeConversation.id;
    const currentMode = aiModeConversations.get(conversationId) ?? true;
    
    socketService.toggleAiMode(conversationId, !currentMode);
  };

  const handleLoadMoreWhatsAppHistory = (phoneNumber: string) => {
    socketService.loadMoreWhatsAppHistory(phoneNumber, 10);
  };

  const handleLoadConversationHistory = async (phoneNumber: string, limit: number = 10): Promise<void> => {
    socketService.loadConversationHistory(phoneNumber, limit);
  };

  const toggleDebugPanel = () => {
    setShowDebugPanel(!showDebugPanel);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar - Conversations List */}
      <div className="w-[405px] bg-white border-r border-gray-200 flex flex-col shadow-sm">
        <Sidebar
          conversations={conversations}
          activeConversation={activeConversation}
          onSelectConversation={handleSelectConversation}
          onSearchCustomer={handleSearchCustomer}
        />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <Chat
            conversation={activeConversation}
            onSendMessage={handleSendMessage}
            onSendAudio={handleSendAudio}
            isTyping={isTyping}
            onTransferToHuman={handleTransferToHuman}
            isAiMode={aiModeConversations.get(activeConversation.id) ?? true}
            onLoadMoreWhatsAppHistory={handleLoadMoreWhatsAppHistory}
            onLoadConversationHistory={handleLoadConversationHistory}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <img 
                src="/trinks-logo.png" 
                alt="Trinks" 
                className="h-16 mx-auto mb-6"
              />
              <h2 className="text-xl font-medium text-trinks-500 mb-2">
                Vem dar um up no seu atendimento!
              </h2>
              <p className="text-gray-600 text-sm max-w-md">
                A Trinks está aqui para impulsionar o crescimento do seu negócio de beleza. Conecte-se com seus clientes e transforme sonhos em realidade.
              </p>
              <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-gray-400">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
                <span>{isConnected ? 'Conectado' : 'Desconectado'}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Resize Handle */}
      <div 
        ref={resizeRef}
        className={`relative w-1 bg-transparent hover:bg-gray-100 cursor-col-resize transition-all duration-200 group ${
          isResizing ? 'bg-gray-200' : ''
        }`}
        onMouseDown={handleResizeStart}
        title="Arrastar para redimensionar"
      >
        <div className={`absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-px bg-gray-200 transition-all duration-200 ${
          isResizing ? 'bg-trinks-400' : 'group-hover:bg-gray-300'
        }`} />
      </div>

      {/* Customer Panel - CRM */}
      <div 
        className="bg-white border-l border-gray-200 flex-shrink-0 shadow-sm"
        style={{ width: `${rightSidebarWidth}px` }}
      >
        <CustomerPanel
          customer={currentCustomer}
          conversation={activeConversation}
        />
      </div>

      {/* Debug Panel */}
      <WebhookDebugPanel 
        isVisible={showDebugPanel}
        onToggle={toggleDebugPanel}
      />
    </div>
  );
}

export default Dashboard;