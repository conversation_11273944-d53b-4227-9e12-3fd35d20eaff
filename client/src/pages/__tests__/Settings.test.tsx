import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import Settings from '../Settings';

// Mock the SettingsForm component
jest.mock('@/components/forms/SettingsForm', () => {
  return {
    SettingsForm: () => <div data-testid="settings-form">Settings Form Component</div>
  };
});

describe('Settings Page', () => {
  it('renders settings page', () => {
    render(<Settings />);
    
    expect(screen.getByTestId('settings-form')).toBeInTheDocument();
  });
});