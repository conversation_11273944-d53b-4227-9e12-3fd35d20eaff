import React, { useState, useEffect, useRef } from 'react';
import { Conversation, Customer, Message } from './types/index';
import socketService from './services/socket';
import Sidebar from './components/Sidebar/Sidebar';
import Chat from './components/Chat/Chat';
import CustomerPanel from './components/CustomerPanel/CustomerPanel';
import WebhookDebugPanel from './components/Debug/WebhookDebugPanel';

function App() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [activeConversation, setActiveConversation] = useState<Conversation | null>(null);
  const [currentCustomer, setCurrentCustomer] = useState<Customer | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [aiModeConversations, setAiModeConversations] = useState<Map<string, boolean>>(new Map());
  const [rightSidebarWidth, setRightSidebarWidth] = useState(405); // Increased 10% from 368px
  const [isResizing, setIsResizing] = useState(false);
  const [showDebugPanel, setShowDebugPanel] = useState(false); // Debug panel hidden by default
  const resizeRef = useRef<HTMLDivElement>(null);

  // Auto-sync active conversation with conversations list
  useEffect(() => {
    if (activeConversation) {
      const updatedConversation = conversations.find(conv => conv.id === activeConversation.id);
      if (updatedConversation && updatedConversation.messages.length !== activeConversation.messages.length) {
        // Only sync if we're not already in the process of updating active conversation
        setActiveConversation(prev => {
          if (!prev || prev.id !== updatedConversation.id) return prev;
          return updatedConversation;
        });
      }
    }
  }, [conversations, activeConversation?.id]); // Add activeConversation.id to dependencies

  useEffect(() => {
    // Initialize socket connection
    const socket = socketService.connect();
    
    if (socket) {
      socket.on('connect', () => {
        setIsConnected(true);
        // Note: We no longer auto-load all chats on startup
        // Chats will be loaded only when new conversations arrive
      });
      socket.on('disconnect', () => setIsConnected(false));
    }

    // Set up event listeners
    setupSocketListeners();

    return () => {
      socketService.removeAllListeners();
      socketService.disconnect();
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Handle resizing
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      
      const windowWidth = window.innerWidth;
      const newWidth = windowWidth - e.clientX;
      
      // Constrain width between 352px and 506px for symmetry (increased 10%)
      const constrainedWidth = Math.max(352, Math.min(506, newWidth));
      setRightSidebarWidth(constrainedWidth);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isResizing]);

  const handleResizeStart = () => {
    setIsResizing(true);
  };

  const setupSocketListeners = () => {
    // Message events
    socketService.onMessageReceived(({ conversationId, message }) => {
      updateConversationMessage(conversationId, message);
    });

    socketService.onMessageSent(({ conversationId, message }) => {
      updateConversationMessage(conversationId, message);
    });

    socketService.onMessageUpdated(({ conversationId, message }) => {
      updateConversationMessage(conversationId, message);
    });

    // Customer search events
    socketService.onCustomerFound(({ customer, conversationId }) => {
      setCurrentCustomer(customer);
      
      // Find or create conversation in state first
      setConversations(prev => {
        // Check for duplicates by both ID and phone number
        const existingConversation = prev.find(conv => 
          conv.id === conversationId || conv.customerPhone === customer.telefone
        );
        
        if (!existingConversation) {
          // Create conversation if it doesn't exist
          const newConversation: Conversation = {
            id: conversationId,
            customerPhone: customer.telefone,
            customerName: customer.nome,
            messages: [],
            lastMessage: new Date(),
            unreadCount: 0
          };
          
          // Auto-select this new conversation
          setTimeout(() => {
            setActiveConversation(newConversation);
          }, 100);
          
          return [newConversation, ...prev];
        } else {
          // Update existing conversation with customer data and ensure consistent ID
          const updatedConversations = prev.map(conv => {
            if (conv.id === conversationId || conv.customerPhone === customer.telefone) {
              return { 
                ...conv, 
                id: conversationId, // Ensure consistent ID
                customerName: customer.nome, 
                customerPhone: customer.telefone 
              };
            }
            return conv;
          });
          
          // Select the existing conversation
          setTimeout(() => {
            const updatedConv = updatedConversations.find(conv => conv.id === conversationId);
            if (updatedConv) {
              setActiveConversation(updatedConv);
            }
          }, 100);
          
          return updatedConversations;
        }
      });
    });

    socketService.onCustomerNotFound(({ phone, conversationId }) => {
      setCurrentCustomer(null);
      
      // Find or create conversation for new customer
      setConversations(prev => {
        // Check for duplicates by both ID and phone number
        const existingConversation = prev.find(conv => 
          conv.id === conversationId || conv.customerPhone === phone
        );
        
        if (!existingConversation) {
          // Create conversation for new customer - show only phone number
          const newConversation: Conversation = {
            id: conversationId,
            customerPhone: phone,
            customerName: phone, // Show phone number instead of "Cliente Novo"
            messages: [],
            lastMessage: new Date(),
            unreadCount: 0
          };
          
          // Auto-select this new conversation
          setTimeout(() => {
            setActiveConversation(newConversation);
          }, 100);
          
          return [newConversation, ...prev];
        } else {
          // Update existing conversation with consistent ID
          const updatedConversations = prev.map(conv => {
            if (conv.id === conversationId || conv.customerPhone === phone) {
              return { 
                ...conv, 
                id: conversationId, // Ensure consistent ID
                customerPhone: phone,
                customerName: conv.customerName || 'Cliente Novo'
              };
            }
            return conv;
          });
          
          // Select the existing conversation
          setTimeout(() => {
            const updatedConv = updatedConversations.find(conv => conv.id === conversationId);
            if (updatedConv) {
              setActiveConversation(updatedConv);
              }
          }, 100);
          
          return updatedConversations;
        }
      });
    });

    // Conversation events
    socketService.onNewConversation((conversation) => {
      setConversations(prev => {
        // Check if conversation already exists by ID or phone number
        const existingConversation = prev.find(conv => 
          conv.id === conversation.id || 
          conv.customerPhone === conversation.customerPhone
        );
        
        if (!existingConversation) {
          // Buscar dados do cliente para nova conversa
          if (conversation.customerPhone) {
            socketService.searchCustomer(conversation.customerPhone);
          }
          
          return [conversation, ...prev];
        }
        
        // Update existing conversation instead of creating duplicate
        return prev.map(conv => {
          if (conv.id === conversation.id || conv.customerPhone === conversation.customerPhone) {
            return {
              ...conv,
              ...conversation,
              messages: conversation.messages || conv.messages, // Preserve existing messages
              id: conversation.id // Ensure consistent ID
            };
          }
          return conv;
        });
      });
    });

    socketService.onConversationUpdated(({ conversationId, conversation }) => {

      // Process messages to ensure audio messages maintain proper type and structure
      const processedMessages = conversation.messages ? conversation.messages.map(message => {
        // Ensure audio messages maintain their type and properties
        if (message.type === 'audio') {

          // Ensure audio message has all required properties with proper typing
          const audioMessage: Message = {
            ...message,
            type: 'audio' as const, // Explicitly maintain audio type with proper typing
            audioUrl: message.audioUrl || '',
            audioDuration: message.audioDuration || 0,
            audioTranscription: message.audioTranscription || undefined,
            isProcessing: message.isProcessing || false
          };
          return audioMessage;
        }
        return message;
      }) : undefined;

      const updatedConversation = {
        ...conversation,
        messages: processedMessages
      };

      setConversations(prev =>
        prev.map(conv => {
          if (conv.id === conversationId) {
            // Merge the updated conversation data, keeping existing messages if not provided
            return {
              ...conv,
              ...updatedConversation,
              customerName: updatedConversation.customerName || conv.customerName,
              messages: updatedConversation.messages || conv.messages,
              lastMessage: updatedConversation.lastMessage || conv.lastMessage
            };
          }
          return conv;
        })
      );

      // Update active conversation if it's the same one
      if (activeConversation?.id === conversationId) {
        setActiveConversation(prev => {
          if (!prev) return null;
          return {
            ...prev,
            ...updatedConversation,
            customerName: updatedConversation.customerName || prev.customerName,
            messages: updatedConversation.messages || prev.messages,
            lastMessage: updatedConversation.lastMessage || prev.lastMessage
          };
        });
      }
    });

    // Typing events
    socketService.onAITypingStart(({ conversationId }) => {
      if (activeConversation?.id === conversationId) {
        setIsTyping(true);
      }
    });

    socketService.onAITypingStop(({ conversationId }) => {
      if (activeConversation?.id === conversationId) {
        setIsTyping(false);
      }
    });

    // AI Mode events
    socketService.onAiModeToggled(({ conversationId, isAiMode }) => {
      setAiModeConversations(prev => {
        const newMap = new Map(prev);
        newMap.set(conversationId, isAiMode);
        return newMap;
      });
    });

    // WhatsApp history events
    socketService.onWhatsAppHistoryLoaded(({ conversationId, history, totalHistoryMessages }) => {
      
      setConversations(prev => {
        return prev.map(conversation => {
          if (conversation.id === conversationId && history.length > 0) {
            // Add history messages to the beginning of the conversation
            return {
              ...conversation,
              messages: [...history, ...conversation.messages],
              whatsappHistoryLoaded: true,
              totalWhatsAppMessages: totalHistoryMessages
            };
          }
          return conversation;
        });
      });
    });

    // Conversation history events
    socketService.onConversationHistoryResponse(({ success, phoneNumber, history, error }) => {
      if (success) {

        // Find the conversation by phone number and add history messages
        setConversations(prev =>
          prev.map(conv => {
            if (conv.customerPhone === phoneNumber) {
              // Add history messages to the beginning, avoiding duplicates
              const existingIds = new Set(conv.messages.map(msg => msg.id));
              const newMessages = history.filter(msg => !existingIds.has(msg.id));

              return {
                ...conv,
                messages: [...newMessages, ...conv.messages]
              };
            }
            return conv;
          })
        );

        // Update active conversation if it matches
        if (activeConversation?.customerPhone === phoneNumber) {
          setActiveConversation(prev => {
            if (!prev) return null;

            const existingIds = new Set(prev.messages.map(msg => msg.id));
            const newMessages = history.filter(msg => !existingIds.has(msg.id));

            return {
              ...prev,
              messages: [...newMessages, ...prev.messages]
            };
          });
        }
      }
    });

    // Load all chats events
    socketService.on('all_chats_loaded', ({ success, chats, error }) => {
      if (success && chats) {
        
        // Process each chat and create/update conversations
        setConversations(prev => {
          const updatedConversations = [...prev];
          
          chats.forEach((chatData: any) => {
            const { conversationId, phoneNumber, customerName, conversation } = chatData;
            
            // Check if conversation already exists
            const existingIndex = updatedConversations.findIndex(conv => 
              conv.id === conversationId || conv.customerPhone === phoneNumber
            );
            
            if (existingIndex >= 0) {
              // Update existing conversation
              updatedConversations[existingIndex] = {
                ...updatedConversations[existingIndex],
                ...conversation,
                customerName: customerName || updatedConversations[existingIndex].customerName
              };
            } else {
              // Add new conversation
              updatedConversations.unshift(conversation);
            }
          });
          
          // Sort conversations by lastMessage (most recent first)
          return updatedConversations.sort((a, b) => 
            new Date(b.lastMessage).getTime() - new Date(a.lastMessage).getTime()
          );
        });
      }
    });

    // Appointment events for sidebar updates
    socketService.on('appointment_created', ({ success, appointment, customerPhone, conversationId, source }) => {
      console.log(`📅 Appointment created event received from ${source}:`, appointment);
      
      // Force refresh customer data for the active conversation if it matches
      if (activeConversation && 
          (activeConversation.customerPhone === customerPhone || activeConversation.id === conversationId)) {
        console.log(`🔄 Refreshing customer data for sidebar update after appointment creation`);
        
        // Trigger customer data refresh by simulating a customer search
        setTimeout(() => {
          socketService.searchCustomer(customerPhone);
        }, 500); // Small delay to ensure appointment is fully processed
      }
    });

    socketService.on('appointment_cancelled', ({ success, appointment, customerPhone, conversationId, source }) => {
      console.log(`🚫 Appointment cancelled event received from ${source}:`, appointment);
      
      // Force refresh customer data for the active conversation if it matches
      if (activeConversation && 
          (activeConversation.customerPhone === customerPhone || activeConversation.id === conversationId)) {
        console.log(`🔄 Refreshing customer data for sidebar update after appointment cancellation`);
        
        // Trigger customer data refresh by simulating a customer search
        setTimeout(() => {
          socketService.searchCustomer(customerPhone);
        }, 500); // Small delay to ensure cancellation is fully processed
      }
    });

    // Error handling
    socketService.onError((error) => {
    });
  };

  const updateConversationMessage = (conversationId: string, message: Message) => {

    // Update conversations list
    setConversations(prev => {
      let foundConversation = false;
      const updated = prev.map(conv => {
        if (conv.id === conversationId) {
          foundConversation = true;
          const updatedMessages = [...conv.messages];

          // Check if message already exists (for updates)
          const existingIndex = updatedMessages.findIndex(m => m.id === message.id);

          if (existingIndex >= 0) {
            // Update existing message - preserve audio properties for audio messages
            const existingMessage = updatedMessages[existingIndex];
            if (message.type === 'audio' && existingMessage.type === 'audio') {
              // Merge audio properties carefully
              updatedMessages[existingIndex] = {
                ...existingMessage,
                ...message,
                type: 'audio' as const,
                audioUrl: message.audioUrl || existingMessage.audioUrl,
                audioDuration: message.audioDuration || existingMessage.audioDuration,
                audioTranscription: message.audioTranscription || existingMessage.audioTranscription,
                isProcessing: message.isProcessing !== undefined ? message.isProcessing : existingMessage.isProcessing
              };
            } else {
              updatedMessages[existingIndex] = message;
            }
          } else {
            // Add new message and sort by timestamp to maintain order
            updatedMessages.push(message);
            updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
          }

          return {
            ...conv,
            messages: updatedMessages,
            lastMessage: message.timestamp,
            // Don't increment unread count if this is the active conversation
            unreadCount: (message.sender === 'user' && activeConversation?.id !== conversationId)
              ? conv.unreadCount + 1
              : conv.unreadCount
          };
        }
        return conv;
      });
      
      // If conversation doesn't exist, create it automatically from message data
      if (!foundConversation) {
        const newConversation: Conversation = {
          id: conversationId,
          customerPhone: message.sender === 'user' ? 'Unknown' : 'Unknown', // Will be updated when customer info comes
          customerName: 'Cliente',
          messages: [message],
          lastMessage: message.timestamp,
          unreadCount: message.sender === 'user' ? 1 : 0
        };
        
        return [newConversation, ...prev];
      }
      
      return updated;
    });
    
    // Also update active conversation if it matches
    if (activeConversation?.id === conversationId) {
      setActiveConversation(prev => {
        if (!prev) return null;

        const updatedMessages = [...prev.messages];
        const existingIndex = updatedMessages.findIndex(m => m.id === message.id);

        if (existingIndex >= 0) {
          // Update existing message - preserve audio properties for audio messages
          const existingMessage = updatedMessages[existingIndex];
          if (message.type === 'audio' && existingMessage.type === 'audio') {
            // Merge audio properties carefully
            updatedMessages[existingIndex] = {
              ...existingMessage,
              ...message,
              type: 'audio' as const,
              audioUrl: message.audioUrl || existingMessage.audioUrl,
              audioDuration: message.audioDuration || existingMessage.audioDuration,
              audioTranscription: message.audioTranscription || existingMessage.audioTranscription,
              isProcessing: message.isProcessing !== undefined ? message.isProcessing : existingMessage.isProcessing
            };
          } else {
            updatedMessages[existingIndex] = message;
          }
        } else {
          updatedMessages.push(message);
          updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        }

        return {
          ...prev,
          messages: updatedMessages,
          lastMessage: message.timestamp
        };
      });
    }
  };

  // const loadConversation = (conversationId: string) => {
  //   console.log('📂 Loading conversation:', conversationId);
  //   
  //   // Find the conversation in our local state
  //   const conversation = conversations.find(conv => conv.id === conversationId);
  //   
  //   if (conversation) {
  //     setActiveConversation(conversation);
  //     console.log('📂 Active conversation set:', conversation.customerName);
  //   }
  //   
  //   socketService.selectConversation(conversationId);
  // };

  const handleSearchCustomer = (phone: string) => {
    socketService.searchCustomer(phone);
  };

  const handleSelectConversation = (conversation: Conversation) => {
    setActiveConversation(conversation);
    
    // Mark as read
    if (conversation.unreadCount > 0) {
      const updatedConversation = { ...conversation, unreadCount: 0 };
      setConversations(prev => 
        prev.map(conv => conv.id === conversation.id ? updatedConversation : conv)
      );
    }

    // Always search for customer data when switching conversations
    // This ensures the sidebar is updated with the correct customer context
    if (conversation.customerPhone) {
      socketService.searchCustomer(conversation.customerPhone);
    } else {
      // If no phone number, clear customer data
      setCurrentCustomer(null);
    }
  };

  const handleSendMessage = (content: string) => {
    if (!activeConversation) return;

    socketService.sendMessage(
      activeConversation.id,
      content,
      activeConversation.customerPhone
    );
  };

  const handleSendAudio = async (audioBlob: Blob, duration: number) => {
    if (!activeConversation) return;

    // Create audio URL for playback
    const audioUrl = URL.createObjectURL(audioBlob);

    // Create audio message immediately
    const audioMessage: Message = {
      id: Date.now().toString(),
      content: 'Mensagem de áudio',
      sender: 'user',
      timestamp: new Date(),
      status: 'sent',
      type: 'audio',
      audioUrl,
      audioDuration: duration,
      isProcessing: true
    };

    // Add to conversation immediately
    updateConversationMessage(activeConversation.id, audioMessage);

    // Send audio to server for processing via WebSocket
    try {
      await socketService.sendAudio(
        activeConversation.id,
        audioBlob,
        duration,
        activeConversation.customerPhone
      );
    } catch (error) {
      // Clean up the blob URL if sending fails
      URL.revokeObjectURL(audioUrl);
    }

    // Note: The blob URL will be cleaned up by the AudioMessage component
    // when it processes the URL or when the component unmounts
  };

  const handleTransferToHuman = () => {
    if (!activeConversation) return;
    
    const conversationId = activeConversation.id;
    const currentMode = aiModeConversations.get(conversationId) ?? true;
    
    // Toggle AI mode via socket (server will handle system message)
    socketService.toggleAiMode(conversationId, !currentMode);
  };

  const handleLoadMoreWhatsAppHistory = (phoneNumber: string) => {
    socketService.loadMoreWhatsAppHistory(phoneNumber, 10);
  };

  const handleLoadConversationHistory = async (phoneNumber: string, limit: number = 10): Promise<void> => {
    socketService.loadConversationHistory(phoneNumber, limit);
  };

  const toggleDebugPanel = () => {
    setShowDebugPanel(!showDebugPanel);
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar - Conversations List */}
      <div className="w-[405px] bg-white border-r border-gray-200 flex flex-col shadow-sm">
        <Sidebar
          conversations={conversations}
          activeConversation={activeConversation}
          onSelectConversation={handleSelectConversation}
          onSearchCustomer={handleSearchCustomer}
        />
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {activeConversation ? (
          <Chat
            conversation={activeConversation}
            onSendMessage={handleSendMessage}
            onSendAudio={handleSendAudio}
            isTyping={isTyping}
            onTransferToHuman={handleTransferToHuman}
            isAiMode={aiModeConversations.get(activeConversation.id) ?? true}
            onLoadMoreWhatsAppHistory={handleLoadMoreWhatsAppHistory}
            onLoadConversationHistory={handleLoadConversationHistory}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <img 
                src="/trinks-logo.png" 
                alt="Trinks" 
                className="h-16 mx-auto mb-6"
              />
              <h2 className="text-xl font-medium mb-2" style={{ color: '#EC5702' }}>
                Vem dar um up no seu atendimento!
              </h2>
              <p className="text-gray-600 text-sm max-w-md">
                A Trinks está aqui para impulsionar o crescimento do seu negócio de beleza. Conecte-se com seus clientes e transforme sonhos em realidade.
              </p>
              <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-gray-400">
                <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
                <span>{isConnected ? 'Conectado' : 'Desconectado'}</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Resize Handle */}
      <div 
        ref={resizeRef}
        className={`relative w-1 bg-transparent hover:bg-gray-100 cursor-col-resize transition-all duration-200 group ${
          isResizing ? 'bg-gray-200' : ''
        }`}
        onMouseDown={handleResizeStart}
        title="Arrastar para redimensionar"
      >
        {/* Ultra thin visual line */}
        <div className={`absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-px bg-gray-200 transition-all duration-200 ${
          isResizing ? 'bg-gray-300' : 'group-hover:bg-gray-300'
        }`} />
      </div>

      {/* Customer Panel - CRM */}
      <div 
        className="bg-white border-l border-gray-200 flex-shrink-0 shadow-sm"
        style={{ width: `${rightSidebarWidth}px` }}
      >
        <CustomerPanel
          customer={currentCustomer}
          conversation={activeConversation}
        />
      </div>

      {/* Debug Panel */}
      <WebhookDebugPanel 
        isVisible={showDebugPanel}
        onToggle={toggleDebugPanel}
      />
    </div>
  );
}

export default App;