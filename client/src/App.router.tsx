import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './pages/Dashboard';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<Dashboard />} />
        <Route path="/chat" element={<Dashboard />} />
        <Route path="/settings" element={<div>Settings Page (Coming Soon)</div>} />
        <Route path="/dashboard" element={<div>Analytics Dashboard (Coming Soon)</div>} />
      </Routes>
    </Router>
  );
}

export default App;