import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

// Mock do serviço de socket
jest.mock('./services/socket', () => {
  const mockSocket = {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  };
  
  return {
    __esModule: true,
    default: {
      connect: jest.fn().mockReturnValue(mockSocket),
      disconnect: jest.fn(),
      removeAllListeners: jest.fn(),
      onMessageReceived: jest.fn(),
      onMessageSent: jest.fn(),
      onMessageUpdated: jest.fn(),
      onCustomerFound: jest.fn(),
      onCustomerNotFound: jest.fn(),
      onNewConversation: jest.fn(),
      onConversationUpdated: jest.fn(),
      onAITypingStart: jest.fn(),
      onAITypingStop: jest.fn(),
      onAiModeToggled: jest.fn(),
      onWhatsAppHistoryLoaded: jest.fn(),
      onConversationHistoryResponse: jest.fn(),
      onError: jest.fn(),
      on: jest.fn(),
    },
  };
});

// Mock adicional para scrollIntoView
Element.prototype.scrollIntoView = jest.fn();

describe('App - Teste de Integração Básico', () => {
  test('should render the main application without crashing', () => {
    render(<App />);
    expect(screen.getByText(/Vem dar um up no seu atendimento/i)).toBeInTheDocument();
  });

  test('should show connection status', () => {
    render(<App />);
    // Should show at least one connection status
    const connectionStatuses = screen.getAllByText(/conectado|desconectado/i);
    expect(connectionStatuses.length).toBeGreaterThan(0);
  });

  test('should display Trinks logo and branding', () => {
    render(<App />);
    // Check for any Trinks logo
    const logos = screen.getAllByAltText('Trinks');
    expect(logos.length).toBeGreaterThan(0);
  });
});