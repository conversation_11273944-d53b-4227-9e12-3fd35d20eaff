@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Cores principais adaptadas para Trinks */
    --background: 0 0% 100%;
    --foreground: 0 0% 15%;
    
    /* Cor primária - <PERSON><PERSON> */
    --primary: 24 95% 53%; /* #EC5702 */
    --primary-foreground: 0 0% 100%;
    
    /* Cores secundárias */
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 15%;
    
    /* Estados */
    --muted: 0 0% 95%;
    --muted-foreground: 0 0% 45%;
    
    --accent: 24 40% 92%;
    --accent-foreground: 0 0% 15%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    
    /* Elementos de UI */
    --border: 0 0% 88%;
    --input: 0 0% 92%;
    --ring: 24 95% 53%; /* Matching primary */
    
    /* Cards e popovers */
    --card: 0 0% 100%;
    --card-foreground: 0 0% 15%;
    
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 15%;
    
    /* <PERSON><PERSON> pad<PERSON> */
    --radius: 0.75rem;
  }

  .dark {
    --background: 0 0% 9%;
    --foreground: 0 0% 95%;
    
    --primary: 24 95% 53%;
    --primary-foreground: 0 0% 100%;
    
    --secondary: 0 0% 14%;
    --secondary-foreground: 0 0% 95%;
    
    --muted: 0 0% 14%;
    --muted-foreground: 0 0% 63%;
    
    --accent: 0 0% 14%;
    --accent-foreground: 0 0% 95%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 95%;
    
    --border: 0 0% 14%;
    --input: 0 0% 14%;
    --ring: 24 95% 53%;
    
    --card: 0 0% 9%;
    --card-foreground: 0 0% 95%;
    
    --popover: 0 0% 9%;
    --popover-foreground: 0 0% 95%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    margin: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
      'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
      sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Message animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-enter {
  animation: fadeInUp 0.3s ease-out;
}

/* Typing indicator animation */
@keyframes bounce {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.typing-dot {
  animation: bounce 1.4s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}