declare module 'react-icons/fa' {
  import { ComponentType } from 'react';
  
  interface IconProps {
    color?: string;
    size?: string | number;
    className?: string;
    style?: React.CSSProperties;
    title?: string;
  }
  
  export const FaWhatsapp: ComponentType<IconProps>;
  export const FaQrcode: ComponentType<IconProps>;
  export const FaCloud: ComponentType<IconProps>;
  export const FaPhone: ComponentType<IconProps>;
  export const FaKey: ComponentType<IconProps>;
  export const FaCheck: ComponentType<IconProps>;
  export const FaTimes: ComponentType<IconProps>;
  export const FaSpinner: ComponentType<IconProps>;
  export const FaCog: ComponentType<IconProps>;
  export const FaExclamationTriangle: ComponentType<IconProps>;
}