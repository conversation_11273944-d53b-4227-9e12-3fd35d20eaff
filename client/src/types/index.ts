export interface AppointmentsData {
  all: Service[];
  past: Service[];
  future: Service[];
  total: number;
}

export interface Customer {
  id: string;
  nome: string;
  telefone: string;
  email: string;
  ultimosServicos: Service[];
  preferencias: string;
  appointmentsData?: AppointmentsData;
  created_at?: string;
}

export interface Service {
  data: string;
  servico: string;
  profissional: string;
  valor: number;
  status?: string;
  observacoes?: string;
  id?: string;
  isFuture?: boolean;
  isPast?: boolean;
}

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai' | 'system' | 'salon' | 'admin';
  timestamp: Date;
  status?: 'sent' | 'delivered' | 'read';
  type?: 'text' | 'audio';
  audioUrl?: string;
  audioDuration?: number;
  audioTranscription?: string;
  isProcessing?: boolean;
  // New architecture fields
  state?: string;
  agent?: string;
  confidence?: number;
  processingTime?: number;
  intent?: string;
  debugInfo?: {
    apiUsed?: boolean;
    mockResponse?: boolean;
    systemPrompt?: string;
    messagesContext?: any[];
    rawResponse?: any;
    model?: string;
    lastMessage?: string;
    stage?: string;
    customerData?: any;
    trinksApiCalls?: any[];
    appointmentCreated?: {
      success: boolean;
      appointmentId?: string;
      customerCreated?: boolean;
      error?: string;
    };
  };
}

export interface Conversation {
  id: string;
  customerPhone: string;
  customerName: string;
  messages: Message[];
  lastMessage: Date;
  unreadCount: number;
  whatsappHistoryLoaded?: boolean;
  totalWhatsAppMessages?: number;
  status?: 'active' | 'waiting' | 'resolved' | 'archived';
}

export interface TimeSlot {
  hora: string;
  profissional: string;
  disponivel: boolean;
}

export interface AvailabilityResponse {
  data: string;
  horarios: TimeSlot[];
}

export interface AppointmentRequest {
  cliente_id: string;
  data: string;
  hora: string;
  servico: string;
  profissional: string;
}

export interface MoodData {
  category: 'VERY_POSITIVE' | 'POSITIVE' | 'NEUTRAL' | 'FRUSTRATED' | 'ANGRY' | 'VERY_ANGRY';
  score: number;
  confidence: number;
  escalationRecommended: boolean;
  trend: 'improving' | 'declining' | 'stable';
  timestamp?: string;
  reasoning?: string;
  emotionalIndicators?: string[];
  conversationTrend?: string;
}

export interface EscalationAlert {
  conversationId: string;
  customerPhone: string;
  escalationReason: string;
  moodCategory: string;
  confidence: number;
  timestamp: string;
}

// New Architecture Types
export interface ConversationSession {
  sessionId: string;
  userId: string;
  customerInfo: {
    phone: string;
    name?: string;
  };
  currentState: string;
  messageCount: number;
  lastActivity: string;
  source: string;
}

export interface ConversationState {
  sessionId: string;
  currentState: string;
  context: {
    userId: string;
    customerInfo: any;
    timestamp: string;
    messageCount: number;
  };
}

export interface ConversationMetrics {
  totalConversations: number;
  averageResponseTime: number;
  intentClassifications: Record<string, number>;
  errorRate: number;
}

export interface IntentClassification {
  intent: string;
  confidence: number;
  entities?: Record<string, any>;
}

export interface ConversationHistory {
  sessionId: string;
  history: Message[];
  total: number;
  currentState: string;
}

export interface PromptData {
  path: string;
  category: string;
  name: string;
  content: string;
}

export interface PromptValidation {
  valid: boolean;
  issues: string[];
  warnings: string[];
  statistics: {
    length: number;
    lines: number;
    templateVariables: number;
    simpleVariables: number;
    codeBlocks: number;
  };
}

export interface SystemHealth {
  status: string;
  timestamp: string;
  version: string;
  architecture: string;
  activeSessions: number;
  metrics: ConversationMetrics;
}