import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { Conversation, Customer, Message } from '../types';
import socketService from '../services/socket';

// Types
interface AppState {
  // Connection
  isConnected: boolean;
  
  // Conversations
  conversations: Conversation[];
  activeConversation: Conversation | null;
  aiModeConversations: Map<string, boolean>;
  
  // Customer
  currentCustomer: Customer | null;
  
  // UI State
  isTyping: boolean;
  rightSidebarWidth: number;
  isResizing: boolean;
  showDebugPanel: boolean;
}

type AppAction = 
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_CONVERSATIONS'; payload: Conversation[] }
  | { type: 'ADD_CONVERSATION'; payload: Conversation }
  | { type: 'UPDATE_CONVERSATION'; payload: { id: string; updates: Partial<Conversation> } }
  | { type: 'SET_ACTIVE_CONVERSATION'; payload: Conversation | null }
  | { type: 'SET_CURRENT_CUSTOMER'; payload: Customer | null }
  | { type: 'SET_TYPING'; payload: boolean }
  | { type: 'UPDATE_CONVERSATION_MESSAGE'; payload: { conversationId: string; message: Message } }
  | { type: 'SET_AI_MODE'; payload: { conversationId: string; isAiMode: boolean } }
  | { type: 'SET_RIGHT_SIDEBAR_WIDTH'; payload: number }
  | { type: 'SET_IS_RESIZING'; payload: boolean }
  | { type: 'SET_SHOW_DEBUG_PANEL'; payload: boolean };

interface AppContextType extends AppState {
  dispatch: React.Dispatch<AppAction>;
  // Actions
  handleSearchCustomer: (phone: string) => void;
  handleSelectConversation: (conversation: Conversation) => void;
  handleSendMessage: (content: string) => void;
  handleSendAudio: (audioBlob: Blob, duration: number) => Promise<void>;
  handleTransferToHuman: () => void;
  handleLoadMoreWhatsAppHistory: (phoneNumber: string) => void;
  handleLoadConversationHistory: (phoneNumber: string, limit?: number) => Promise<void>;
  toggleDebugPanel: () => void;
}

// Initial state
const initialState: AppState = {
  isConnected: false,
  conversations: [],
  activeConversation: null,
  aiModeConversations: new Map(),
  currentCustomer: null,
  isTyping: false,
  rightSidebarWidth: 405,
  isResizing: false,
  showDebugPanel: false,
};

// Reducer
function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_CONNECTED':
      return { ...state, isConnected: action.payload };
      
    case 'SET_CONVERSATIONS':
      return { ...state, conversations: action.payload };
      
    case 'ADD_CONVERSATION':
      return {
        ...state,
        conversations: [action.payload, ...state.conversations]
      };
      
    case 'UPDATE_CONVERSATION':
      return {
        ...state,
        conversations: state.conversations.map(conv =>
          conv.id === action.payload.id
            ? { ...conv, ...action.payload.updates }
            : conv
        )
      };
      
    case 'SET_ACTIVE_CONVERSATION':
      return { ...state, activeConversation: action.payload };
      
    case 'SET_CURRENT_CUSTOMER':
      return { ...state, currentCustomer: action.payload };
      
    case 'SET_TYPING':
      return { ...state, isTyping: action.payload };
      
    case 'UPDATE_CONVERSATION_MESSAGE': {
      const { conversationId, message } = action.payload;
      
      let foundConversation = false;
      const updatedConversations = state.conversations.map(conv => {
        if (conv.id === conversationId) {
          foundConversation = true;
          const updatedMessages = [...conv.messages];

          const existingIndex = updatedMessages.findIndex(m => m.id === message.id);

          if (existingIndex >= 0) {
            const existingMessage = updatedMessages[existingIndex];
            if (message.type === 'audio' && existingMessage.type === 'audio') {
              updatedMessages[existingIndex] = {
                ...existingMessage,
                ...message,
                type: 'audio' as const,
                audioUrl: message.audioUrl || existingMessage.audioUrl,
                audioDuration: message.audioDuration || existingMessage.audioDuration,
                audioTranscription: message.audioTranscription || existingMessage.audioTranscription,
                isProcessing: message.isProcessing !== undefined ? message.isProcessing : existingMessage.isProcessing
              };
            } else {
              updatedMessages[existingIndex] = message;
            }
          } else {
            updatedMessages.push(message);
            updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
          }

          return {
            ...conv,
            messages: updatedMessages,
            lastMessage: message.timestamp,
            unreadCount: (message.sender === 'user' && state.activeConversation?.id !== conversationId)
              ? conv.unreadCount + 1
              : conv.unreadCount
          };
        }
        return conv;
      });
      
      // If conversation doesn't exist, create it
      if (!foundConversation) {
        const newConversation: Conversation = {
          id: conversationId,
          customerPhone: message.sender === 'user' ? 'Unknown' : 'Unknown',
          customerName: 'Cliente',
          messages: [message],
          lastMessage: message.timestamp,
          unreadCount: message.sender === 'user' ? 1 : 0
        };
        
        updatedConversations.unshift(newConversation);
      }
      
      // Also update active conversation if it matches
      let updatedActiveConversation = state.activeConversation;
      if (state.activeConversation?.id === conversationId) {
        const updatedMessages = [...state.activeConversation.messages];
        const existingIndex = updatedMessages.findIndex(m => m.id === message.id);

        if (existingIndex >= 0) {
          const existingMessage = updatedMessages[existingIndex];
          if (message.type === 'audio' && existingMessage.type === 'audio') {
            updatedMessages[existingIndex] = {
              ...existingMessage,
              ...message,
              type: 'audio' as const,
              audioUrl: message.audioUrl || existingMessage.audioUrl,
              audioDuration: message.audioDuration || existingMessage.audioDuration,
              audioTranscription: message.audioTranscription || existingMessage.audioTranscription,
              isProcessing: message.isProcessing !== undefined ? message.isProcessing : existingMessage.isProcessing
            };
          } else {
            updatedMessages[existingIndex] = message;
          }
        } else {
          updatedMessages.push(message);
          updatedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
        }

        updatedActiveConversation = {
          ...state.activeConversation,
          messages: updatedMessages,
          lastMessage: message.timestamp
        };
      }
      
      return {
        ...state,
        conversations: updatedConversations,
        activeConversation: updatedActiveConversation
      };
    }
      
    case 'SET_AI_MODE': {
      const newMap = new Map(state.aiModeConversations);
      newMap.set(action.payload.conversationId, action.payload.isAiMode);
      return { ...state, aiModeConversations: newMap };
    }
      
    case 'SET_RIGHT_SIDEBAR_WIDTH':
      return { ...state, rightSidebarWidth: action.payload };
      
    case 'SET_IS_RESIZING':
      return { ...state, isResizing: action.payload };
      
    case 'SET_SHOW_DEBUG_PANEL':
      return { ...state, showDebugPanel: action.payload };
      
    default:
      return state;
  }
}

// Context
const AppContext = createContext<AppContextType | undefined>(undefined);

// Provider
export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Actions
  const handleSearchCustomer = (phone: string) => {
    socketService.searchCustomer(phone);
  };

  const handleSelectConversation = (conversation: Conversation) => {
    dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: conversation });
    
    if (conversation.unreadCount > 0) {
      dispatch({
        type: 'UPDATE_CONVERSATION',
        payload: { id: conversation.id, updates: { unreadCount: 0 } }
      });
    }

    if (conversation.customerPhone) {
      socketService.searchCustomer(conversation.customerPhone);
    } else {
      dispatch({ type: 'SET_CURRENT_CUSTOMER', payload: null });
    }
  };

  const handleSendMessage = (content: string) => {
    if (!state.activeConversation) return;

    socketService.sendMessage(
      state.activeConversation.id,
      content,
      state.activeConversation.customerPhone
    );
  };

  const handleSendAudio = async (audioBlob: Blob, duration: number) => {
    if (!state.activeConversation) return;

    const audioUrl = URL.createObjectURL(audioBlob);

    const audioMessage: Message = {
      id: Date.now().toString(),
      content: 'Mensagem de áudio',
      sender: 'user',
      timestamp: new Date(),
      status: 'sent',
      type: 'audio',
      audioUrl,
      audioDuration: duration,
      isProcessing: true
    };

    dispatch({
      type: 'UPDATE_CONVERSATION_MESSAGE',
      payload: { conversationId: state.activeConversation.id, message: audioMessage }
    });

    try {
      await socketService.sendAudio(
        state.activeConversation.id,
        audioBlob,
        duration,
        state.activeConversation.customerPhone
      );
    } catch (error) {
      URL.revokeObjectURL(audioUrl);
    }
  };

  const handleTransferToHuman = () => {
    if (!state.activeConversation) return;
    
    const conversationId = state.activeConversation.id;
    const currentMode = state.aiModeConversations.get(conversationId) ?? true;
    
    socketService.toggleAiMode(conversationId, !currentMode);
  };

  const handleLoadMoreWhatsAppHistory = (phoneNumber: string) => {
    socketService.loadMoreWhatsAppHistory(phoneNumber, 10);
  };

  const handleLoadConversationHistory = async (phoneNumber: string, limit: number = 10): Promise<void> => {
    socketService.loadConversationHistory(phoneNumber, limit);
  };

  const toggleDebugPanel = () => {
    dispatch({ type: 'SET_SHOW_DEBUG_PANEL', payload: !state.showDebugPanel });
  };

  // Socket listeners setup
  useEffect(() => {
    const socket = socketService.connect();
    
    if (socket) {
      socket.on('connect', () => {
        dispatch({ type: 'SET_CONNECTED', payload: true });
      });
      socket.on('disconnect', () => {
        dispatch({ type: 'SET_CONNECTED', payload: false });
      });
    }

    // Message events
    socketService.onMessageReceived(({ conversationId, message }) => {
      dispatch({ type: 'UPDATE_CONVERSATION_MESSAGE', payload: { conversationId, message } });
    });

    socketService.onMessageSent(({ conversationId, message }) => {
      dispatch({ type: 'UPDATE_CONVERSATION_MESSAGE', payload: { conversationId, message } });
    });

    socketService.onMessageUpdated(({ conversationId, message }) => {
      dispatch({ type: 'UPDATE_CONVERSATION_MESSAGE', payload: { conversationId, message } });
    });

    // Customer search events
    socketService.onCustomerFound(({ customer, conversationId }) => {
      dispatch({ type: 'SET_CURRENT_CUSTOMER', payload: customer });
      
      // Handle conversation creation/update logic here
      // (simplified for now - could be moved to a separate action)
    });

    socketService.onCustomerNotFound(({ phone, conversationId }) => {
      dispatch({ type: 'SET_CURRENT_CUSTOMER', payload: null });
    });

    // AI Mode events
    socketService.onAiModeToggled(({ conversationId, isAiMode }) => {
      dispatch({ type: 'SET_AI_MODE', payload: { conversationId, isAiMode } });
    });

    // Typing events
    socketService.onAITypingStart(({ conversationId }) => {
      if (state.activeConversation?.id === conversationId) {
        dispatch({ type: 'SET_TYPING', payload: true });
      }
    });

    socketService.onAITypingStop(({ conversationId }) => {
      if (state.activeConversation?.id === conversationId) {
        dispatch({ type: 'SET_TYPING', payload: false });
      }
    });

    return () => {
      socketService.removeAllListeners();
      socketService.disconnect();
    };
  }, [state.activeConversation?.id]);

  // Auto-sync active conversation with conversations list
  useEffect(() => {
    if (state.activeConversation) {
      const updatedConversation = state.conversations.find(conv => conv.id === state.activeConversation!.id);
      if (updatedConversation && updatedConversation.messages.length !== state.activeConversation.messages.length) {
        dispatch({ type: 'SET_ACTIVE_CONVERSATION', payload: updatedConversation });
      }
    }
  }, [state.conversations, state.activeConversation]);

  const contextValue: AppContextType = {
    ...state,
    dispatch,
    handleSearchCustomer,
    handleSelectConversation,
    handleSendMessage,
    handleSendAudio,
    handleTransferToHuman,
    handleLoadMoreWhatsAppHistory,
    handleLoadConversationHistory,
    toggleDebugPanel,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// Hook
export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

export default AppContext;