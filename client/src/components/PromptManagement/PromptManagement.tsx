import React, { useState, useEffect } from 'react';
import { templatesAPI } from '../../services/api';

interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  variables: string[];
  isActive?: boolean;
}

interface PromptManagementProps {
  onClose: () => void;
}

const PromptManagement: React.FC<PromptManagementProps> = ({ onClose }) => {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [activeTab, setActiveTab] = useState<string>('base-identity');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<PromptTemplate | null>(null);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [activeView, setActiveView] = useState<'editor' | 'preview' | 'help'>('editor');
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [originalContent, setOriginalContent] = useState<string>('');

  // Prompt templates disponíveis
  const availableTemplates = [
    {
      id: 'base-identity',
      name: 'Identidade Base',
      description: 'Personalidade e tom de voz principal da assistente',
      icon: '🎭'
    },
    {
      id: 'greeting',
      name: 'Saudação',
      description: 'Mensagens de boas-vindas e primeiros contatos',
      icon: '👋'
    },
    {
      id: 'appointment-confirmation',
      name: 'Confirmação de Agendamento',
      description: 'Confirmação de novos agendamentos',
      icon: '📅'
    },
    {
      id: 'appointment-cancellation',
      name: 'Cancelamento',
      description: 'Gestão de cancelamentos e reagendamentos',
      icon: '❌'
    },
    {
      id: 'service-inquiry',
      name: 'Consulta de Serviços',
      description: 'Informações sobre serviços e preços',
      icon: '💄'
    }
  ];

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      setIsLoading(true);
      
      // Criar templates padrão simulados para demonstração
      const defaultTemplates: PromptTemplate[] = availableTemplates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        content: getDefaultPromptContent(template.id),
        variables: ['establishmentName', 'customerName', 'serviceName', 'professionalName', 'appointmentTime', 'conversationHistory'],
        isActive: template.id === 'base-identity'
      }));
      
      // Carregar templates salvos do localStorage
      const savedTemplatesStr = localStorage.getItem('trinks-prompt-templates');
      if (savedTemplatesStr) {
        try {
          const savedTemplatesData = JSON.parse(savedTemplatesStr);
          // Mesclar templates salvos com os padrão
          defaultTemplates.forEach(template => {
            if (savedTemplatesData[template.id]) {
              template.content = savedTemplatesData[template.id].content || template.content;
            }
          });
        } catch (e) {
          console.warn('Error parsing saved templates from localStorage:', e);
        }
      }
      
      try {
        // Tentar carregar do backend
        const response = await templatesAPI.getAll();
        const backendTemplates = response.templates || response || [];
        
        if (Array.isArray(backendTemplates) && backendTemplates.length > 0) {
          setTemplates(backendTemplates);
        } else {
          // Usar templates padrão se backend não retornar dados válidos
          setTemplates(defaultTemplates);
        }
      } catch (apiError) {
        console.warn('Backend API not available, using default templates:', apiError);
        // Usar templates padrão se API falhar
        setTemplates(defaultTemplates);
      }
      
      // Selecionar o primeiro template após o setTemplates
      const firstTemplate = defaultTemplates[0];
      if (firstTemplate) {
        console.log('🎯 Selecting first template:', firstTemplate.name);
        setSelectedTemplate(firstTemplate);
        setOriginalContent(firstTemplate.content);
        setActiveTab(firstTemplate.id);
      } else {
        console.warn('⚠️ No templates available to select');
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      
      // Fallback final - garantir que sempre há templates
      const fallbackTemplates: PromptTemplate[] = [{
        id: 'base-identity',
        name: 'Identidade Base',
        description: 'Personalidade e tom de voz principal da assistente',
        content: 'Você é uma assistente virtual da Trinks que ajuda empreendedores de beleza e bem-estar.',
        variables: ['establishmentName', 'customerName'],
        isActive: true
      }];
      
      setTemplates(fallbackTemplates);
      setSelectedTemplate(fallbackTemplates[0]);
      setOriginalContent(fallbackTemplates[0].content);
      setActiveTab('base-identity');
    } finally {
      setIsLoading(false);
    }
  };

  // Função para obter conteúdo padrão dos templates
  const getDefaultPromptContent = (templateId: string): string => {
    const defaultContents: Record<string, string> = {
      'base-identity': `Você é uma assistente virtual da Trinks, a parceira de gestão e tecnologia que impulsiona o crescimento de negócios de beleza e bem-estar.

**A SUA IDENTIDADE:**
- A Trinks é feminina: sempre use "A Trinks" (nunca "O Trinks")
- Tom coloquial e acolhedor: "Vem dar um up no seu negócio!"
- Propósito: Transformar sonhos de empreendedores em negócios de sucesso
- Especialista em beleza e bem-estar

**DIRETRIZES:**
- Mensagens curtas (1-2 frases máximo)
- Linguagem brasileira autêntica
- Sempre profissional mas próxima
- Foque na transformação e crescimento

**ESTABELECIMENTO:** {establishmentName}
**CONVERSA ATUAL:** {conversationHistory}`,

      'greeting': `Oi! 👋 Sou a assistente da {establishmentName}!

Como posso ajudar a dar um up no seu dia hoje? 

Estamos aqui para transformar seus sonhos de beleza em realidade! ✨`,

      'appointment-confirmation': `Perfeito! 📅 Vou confirmar seu agendamento:

**Serviço:** {serviceName}
**Profissional:** {professionalName} 
**Horário:** {appointmentTime}

Tudo certinho, {customerName}? 

A Trinks está aqui para tornar esse momento especial! ✨`,

      'appointment-cancellation': `Entendi que precisa cancelar/reagendar, {customerName}.

Sem problemas! A Trinks está aqui para te ajudar.

Quer reagendar para outro dia ou preferir cancelar mesmo? 

Estamos aqui para o que precisar! 🤝`,

      'service-inquiry': `Ótima escolha! 💄 

Temos diversos serviços incríveis para dar aquele up especial que você merece:

{serviceName} - Nossos profissionais são especialistas!

Quer saber mais detalhes ou já partir pro agendamento? 

A Trinks tá aqui pra te deixar ainda mais linda! ✨`
    };

    return defaultContents[templateId] || `Template ${templateId} - Configure o conteúdo aqui.`;
  };

  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');

  const handleSaveTemplate = async () => {
    if (!selectedTemplate) return;
    
    try {
      setSaveStatus('saving');
      setIsSaving(true);
      
      try {
        // Tentar salvar no backend - ajustar formato da API
        await templatesAPI.updateTemplate(selectedTemplate.id, {
          name: selectedTemplate.name,
          template: selectedTemplate.content,
          description: selectedTemplate.description,
          variables: selectedTemplate.variables,
        });
      } catch (apiError) {
        console.warn('Backend save failed, saving to localStorage:', apiError);
        // Fallback para localStorage
        const savedTemplates = localStorage.getItem('trinks-prompt-templates');
        let templatesData: any = {};
        
        if (savedTemplates) {
          templatesData = JSON.parse(savedTemplates);
        }
        
        templatesData[selectedTemplate.id] = selectedTemplate;
        localStorage.setItem('trinks-prompt-templates', JSON.stringify(templatesData));
      }
      
      // Atualizar o estado local
      const updatedTemplates = templates.map(t => 
        t.id === selectedTemplate.id ? selectedTemplate : t
      );
      setTemplates(updatedTemplates);
      
      // Success feedback with animation
      setSaveStatus('success');
      setHasUnsavedChanges(false);
      setOriginalContent(selectedTemplate.content);
      setTimeout(() => setSaveStatus('idle'), 2000);
      setIsSaving(false);
    } catch (error) {
      console.error('Error saving template:', error);
      setSaveStatus('error');
      setTimeout(() => setSaveStatus('idle'), 2000);
      setIsSaving(false);
    }
  };

  const handlePreview = async () => {
    if (!selectedTemplate) return;
    
    try {
      const mockData = {
        establishmentName: 'Salão Trinks',
        customerName: 'Maria',
        serviceName: 'Corte + Escova',
        professionalName: 'Ana',
        appointmentTime: '15:00',
        conversationHistory: 'Cliente perguntou sobre disponibilidade para corte de cabelo.'
      };
      
      try {
        // Tentar usar o backend para preview
        const response = await templatesAPI.previewTemplate(selectedTemplate.id, mockData);
        setPreviewContent(response.preview);
      } catch (apiError) {
        console.warn('Backend preview failed, generating local preview:', apiError);
        // Fallback: substituir manualmente as variáveis
        let previewText = selectedTemplate.content;
        
        // Substituir variáveis básicas
        Object.entries(mockData).forEach(([key, value]) => {
          const regex = new RegExp(`{${key}}`, 'g');
          previewText = previewText.replace(regex, value);
        });
        
        setPreviewContent(previewText);
      }
      
      setActiveView('preview');
    } catch (error) {
      console.error('Error generating preview:', error);
      setPreviewContent('Erro ao gerar prévia. Verifique o conteúdo do template.');
      setActiveView('preview');
    }
  };

  const handleTemplateSelect = (templateId: string) => {
    let template = templates.find(t => t.id === templateId);
    
    // Se não encontrar nos templates carregados, criar um padrão
    if (!template) {
      const templateConfig = availableTemplates.find(t => t.id === templateId);
      if (templateConfig) {
        template = {
          id: templateId,
          name: templateConfig.name,
          description: templateConfig.description,
          content: getDefaultPromptContent(templateId),
          variables: ['establishmentName', 'customerName', 'serviceName', 'professionalName', 'appointmentTime', 'conversationHistory'],
        };
      }
    }
    
    if (template) {
      setSelectedTemplate(template);
      setOriginalContent(template.content);
      setActiveTab(templateId);
      setActiveView('editor');
      setHasUnsavedChanges(false);
    }
  };

  // Monitor changes to detect unsaved content
  const handleContentChange = (newContent: string) => {
    if (selectedTemplate) {
      setSelectedTemplate({
        ...selectedTemplate,
        content: newContent
      });
      setHasUnsavedChanges(newContent !== originalContent);
    }
  };

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-8 shadow-2xl">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 border-2 border-trinks-500 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-gray-700 font-medium">Carregando prompts...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 animate-in fade-in duration-300">
      <div className="bg-white rounded-2xl max-w-7xl w-full max-h-[90vh] overflow-hidden flex flex-col shadow-2xl animate-in zoom-in-95 duration-300 slide-in-from-bottom-4">
        {/* Header */}
        <div className="bg-gradient-to-r from-trinks-50 to-white border-b border-trinks-100 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-trinks-500 rounded-xl flex items-center justify-center shadow-lg">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Prompts da IA</h1>
                <p className="text-sm text-trinks-600 font-medium">Configure a personalidade da sua assistente</p>
              </div>
            </div>
            
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-xl transition-all duration-200 group"
            >
              <svg className="w-6 h-6 text-gray-400 group-hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="flex flex-1 overflow-hidden">
          {/* Sidebar - Template List */}
          <div className="w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
            <div className="p-4 border-b border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-1">Templates Disponíveis</h3>
              <p className="text-xs text-gray-500">Personalize cada tipo de interação</p>
            </div>
            
            <div className="flex-1 overflow-y-auto p-2" style={{ 
              scrollbarWidth: 'thin',
              scrollbarColor: '#D1D5DB #F3F4F6'
            }}>
              <div className="space-y-1">
                {availableTemplates.map((template) => (
                  <button
                    key={template.id}
                    onClick={() => handleTemplateSelect(template.id)}
                    className={`w-full text-left p-4 rounded-xl transition-all duration-200 group ${
                      activeTab === template.id
                        ? 'bg-white shadow-md border-2 border-trinks-200 shadow-trinks-100/30'
                        : 'hover:bg-white hover:shadow-sm border-2 border-transparent'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center text-lg transition-all duration-200 ${
                        activeTab === template.id
                          ? 'bg-trinks-100 text-trinks-700'
                          : 'bg-gray-100 text-gray-600 group-hover:bg-trinks-50 group-hover:text-trinks-600'
                      }`}>
                        {template.icon}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className={`font-semibold text-sm transition-colors duration-200 ${
                          activeTab === template.id ? 'text-trinks-900' : 'text-gray-900'
                        }`}>
                          {template.name}
                        </h4>
                        <p className={`text-xs mt-1 transition-colors duration-200 ${
                          activeTab === template.id ? 'text-trinks-600' : 'text-gray-500'
                        }`}>
                          {template.description}
                        </p>
                      </div>
                      <div className="flex flex-col items-center gap-1">
                        {activeTab === template.id && (
                          <div className="w-2 h-2 bg-trinks-500 rounded-full"></div>
                        )}
                        {hasUnsavedChanges && activeTab === template.id && (
                          <div className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-ping"></div>
                        )}
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {selectedTemplate && (
              <>
                {/* Tab Navigation */}
                <div className="border-b border-gray-200 bg-white">
                  <div className="flex px-6">
                    {[
                      { id: 'editor', name: 'Editor', icon: '📝' },
                      { id: 'preview', name: 'Visualização', icon: '👁️' },
                      { id: 'help', name: 'Ajuda', icon: '❓' }
                    ].map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveView(tab.id as any)}
                        className={`px-6 py-4 font-medium text-sm transition-all duration-200 border-b-2 ${
                          activeView === tab.id
                            ? 'text-trinks-600 border-trinks-500 bg-trinks-50/30'
                            : 'text-gray-500 border-transparent hover:text-gray-700 hover:border-gray-300'
                        }`}
                      >
                        <span className="flex items-center gap-2">
                          <span>{tab.icon}</span>
                          {tab.name}
                        </span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Content Area */}
                <div className="flex-1 overflow-auto p-6">
                  {activeView === 'editor' && (
                    <div className="space-y-6">
                      {/* Template Info */}
                      <div className="bg-gradient-to-r from-trinks-50 to-orange-50 border border-trinks-200 rounded-xl p-6">
                        <div className="flex items-start gap-4">
                          <div className="w-12 h-12 bg-trinks-500 rounded-xl flex items-center justify-center text-white text-xl">
                            {availableTemplates.find(t => t.id === selectedTemplate.id)?.icon}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-bold text-trinks-900 mb-2">
                              {selectedTemplate.name}
                            </h3>
                            <p className="text-trinks-700 text-sm leading-relaxed">
                              {selectedTemplate.description}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Editor */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <label className="text-sm font-semibold text-gray-900">
                              Conteúdo do Prompt
                            </label>
                            {hasUnsavedChanges && (
                              <div className="flex items-center gap-1 text-xs text-orange-600 bg-orange-50 px-2 py-1 rounded-full border border-orange-200 animate-pulse">
                                <span className="w-1.5 h-1.5 bg-orange-500 rounded-full animate-ping"></span>
                                Alterações não salvas
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <button
                              onClick={handlePreview}
                              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-all duration-300 text-sm font-medium flex items-center gap-2 transform hover:scale-105 active:scale-95 hover:shadow-lg"
                              title="Gerar prévia com dados de exemplo"
                            >
                              <span>👁️</span>
                              Visualizar
                            </button>
                            <button
                              onClick={handleSaveTemplate}
                              disabled={isSaving}
                              className={`px-4 py-2 rounded-lg transition-all duration-300 text-sm font-medium flex items-center gap-2 disabled:opacity-50 transform hover:scale-105 active:scale-95 ${
                                saveStatus === 'success' 
                                  ? 'bg-green-500 text-white animate-pulse' 
                                  : saveStatus === 'error'
                                  ? 'bg-red-500 text-white'
                                  : 'bg-trinks-500 text-white hover:bg-trinks-600 hover:shadow-lg'
                              }`}
                            >
                              <span className={saveStatus === 'saving' ? 'animate-spin' : ''}>
                                {saveStatus === 'saving' ? '⏳' : 
                                 saveStatus === 'success' ? '✅' : 
                                 saveStatus === 'error' ? '❌' : '💾'}
                              </span>
                              {saveStatus === 'saving' ? 'Salvando...' : 
                               saveStatus === 'success' ? 'Salvo!' : 
                               saveStatus === 'error' ? 'Erro!' : 'Salvar'}
                            </button>
                          </div>
                        </div>

                        <textarea
                          value={selectedTemplate.content}
                          onChange={(e) => handleContentChange(e.target.value)}
                          rows={20}
                          className={`w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:border-trinks-500 font-mono text-sm leading-relaxed resize-none transition-all duration-200 ${
                            hasUnsavedChanges 
                              ? 'border-orange-300 focus:ring-orange-500 bg-orange-50/30' 
                              : 'border-gray-300 focus:ring-trinks-500'
                          }`}
                          placeholder="Digite o prompt aqui..."
                        />
                      </div>
                    </div>
                  )}

                  {activeView === 'preview' && (
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6">
                        <h3 className="font-bold text-blue-900 mb-2 flex items-center gap-2">
                          <span>👁️</span>
                          Visualização do Prompt
                        </h3>
                        <p className="text-blue-700 text-sm">
                          Veja como o prompt ficará com dados reais substituídos
                        </p>
                      </div>

                      <div className="bg-white border-2 border-gray-200 rounded-xl p-6">
                        <pre className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap font-sans">
                          {previewContent || 'Clique em "Visualizar" para gerar a prévia'}
                        </pre>
                      </div>
                    </div>
                  )}

                  {activeView === 'help' && (
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl p-6">
                        <h3 className="font-bold text-purple-900 mb-2 flex items-center gap-2">
                          <span>❓</span>
                          Como usar variáveis
                        </h3>
                        <p className="text-purple-700 text-sm">
                          Use as variáveis abaixo para personalizar seus prompts dinamicamente
                        </p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {[
                          { var: '{establishmentName}', desc: 'Nome do estabelecimento' },
                          { var: '{customerName}', desc: 'Nome do cliente' },
                          { var: '{serviceName}', desc: 'Nome do serviço solicitado' },
                          { var: '{professionalName}', desc: 'Nome do profissional' },
                          { var: '{appointmentTime}', desc: 'Horário do agendamento' },
                          { var: '{conversationHistory}', desc: 'Histórico da conversa' }
                        ].map((variable) => (
                          <div key={variable.var} className="bg-white border border-gray-200 rounded-lg p-4">
                            <code className="text-sm font-mono text-trinks-600 bg-trinks-50 px-2 py-1 rounded">
                              {variable.var}
                            </code>
                            <p className="text-xs text-gray-600 mt-2">{variable.desc}</p>
                          </div>
                        ))}
                      </div>

                      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-xl p-6">
                        <h4 className="font-semibold text-orange-900 mb-2 flex items-center gap-2">
                          <span>💡</span>
                          Dicas da Trinks
                        </h4>
                        <ul className="text-sm text-orange-800 space-y-2">
                          <li className="flex items-start gap-2">
                            <span className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></span>
                            <span><strong>Tom Feminino:</strong> Use sempre "A Trinks" (nunca "O Trinks") para manter a identidade da marca</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></span>
                            <span><strong>Mensagens Curtas:</strong> Máximo 1-2 frases por resposta para simular WhatsApp real</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></span>
                            <span><strong>Propósito:</strong> Sempre lembre "Transformar sonhos em negócios de sucesso"</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="w-1.5 h-1.5 bg-orange-500 rounded-full mt-2 flex-shrink-0"></span>
                            <span><strong>Convite:</strong> Use "Vem dar um up no seu negócio" quando apropriado</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptManagement;