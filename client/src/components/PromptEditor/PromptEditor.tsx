import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { templatesAPI } from '@/services/api';
import { 
  FileText, 
  Eye, 
  Save, 
  RotateCcw, 
  Info, 
  Settings, 
  Zap,
  CheckCircle2,
  AlertCircle,
  Code2
} from 'lucide-react';

// Types
interface PromptTemplate {
  name: string;
  description: string;
  template: string;
  variables: string[];
  maxTokens: number;
  temperature: number;
  source?: 'default' | 'custom';
  isCustomized?: boolean;
  updatedAt?: string;
}

interface TemplateVariable {
  name: string;
  description: string;
  example: string;
  required: boolean;
}

interface PreviewData {
  prompt: string;
  fullLength: number;
  settings: {
    maxTokens: number;
    temperature: number;
  };
}

// Variable documentation for the base identity template
const baseIdentityVariables: Record<string, TemplateVariable> = {
  current_date_context: {
    name: 'current_date_context',
    description: 'Contexto de data e hora atual em timezone brasileiro',
    example: '- Data de hoje: 03/01/2025 (quinta-feira)\n- Amanhã: 04/01/2025 (sexta-feira)\n- Horário atual: 14:30 (Brasil/São Paulo)',
    required: true
  }
};

// Example data for preview
const exampleData = {
  current_date_context: `- Data de hoje: 03/01/2025 (quinta-feira)
- Amanhã: 04/01/2025 (sexta-feira)  
- Horário atual: 14:30 (Brasil/São Paulo)
- IMPORTANTE: Todas as datas e horários estão no timezone Brasil/São Paulo`
};

export function PromptEditor() {
  const { toast } = useToast();
  const [template, setTemplate] = useState<PromptTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isPreviewing, setIsPreviewing] = useState(false);
  const [previewData, setPreviewData] = useState<PreviewData | null>(null);
  const [templateContent, setTemplateContent] = useState('');
  const [templateName, setTemplateName] = useState('');
  const [templateDescription, setTemplateDescription] = useState('');
  const [maxTokens, setMaxTokens] = useState(150);
  const [temperature, setTemperature] = useState(0.3);
  const [validation, setValidation] = useState<{isValid: boolean; errors: string[]; warnings: string[]} | null>(null);

  // Load template on mount (starting with base identity)
  useEffect(() => {
    loadTemplate();
  }, []);

  const loadTemplate = async () => {
    setIsLoading(true);
    try {
      // Try to get from API first
      let baseTemplate: PromptTemplate;
      
      try {
        const response = await templatesAPI.getTemplate('base_identity');
        baseTemplate = {
          name: response.template.name,
          description: response.template.description,
          template: response.template.template,
          variables: response.template.variables || ['current_date_context'],
          maxTokens: response.template.maxTokens || 150,
          temperature: response.template.temperature || 0.3,
          source: response.template.isCustomized ? 'custom' : 'default',
          isCustomized: response.template.isCustomized
        };
      } catch (apiError) {
        // Fallback to hardcoded template if API fails
        console.warn('API failed, using fallback template:', apiError);
        baseTemplate = {
        name: 'Identidade Base do Sistema',
        description: 'Define a personalidade básica da IA como recepcionista de salão brasileira',
        template: `# Identidade e Personalidade - Recepcionista do Salão

## 🎯 IDENTIDADE CRÍTICA
Você é uma recepcionista experiente e calorosa de um salão de beleza que adora o que faz! O cliente NÃO DEVE suspeitar que está falando com uma IA. Você é uma pessoa real, com anos de experiência em salão, que conhece cada cliente e se importa genuinamente com eles.

## 📍 CONTEXTO PROFISSIONAL
- Você trabalha há anos neste salão e conhece todos os clientes
- Cada cliente é especial para você e você se lembra das preferências deles  
- Você ama fazer as pessoas se sentirem bonitas e confiantes
- Você fala como uma brasileira autêntica, com calor humano
- **IMPORTANTE:** O cliente que está conversando com você É SEMPRE o cliente do agendamento

## 💖 SUA PERSONALIDADE
- **Calorosa e acolhedora** - você abraça todo mundo que chega
- **Atenta aos detalhes** - lembra da cor do cabelo que a cliente fez
- **Sempre positiva** - encontra algo legal para falar sobre qualquer situação  
- **Genuinamente interessada** - pergunta sobre a vida dos clientes
- **Proativa** - sugere coisas antes mesmo de perguntarem

## 🗣️ COMO VOCÊ SE COMUNICA

### Linguagem Brasileira Autêntica
- **Use expressões coloquiais:** "E aí, tudo bem?", "Opa!", "Que massa!", "Nossa!", "Legal demais!"
- **Mensagens curtas como WhatsApp:** máximo 2-3 linhas, como uma pessoa real conversando
- **Demonstre empolgação genuína:** "Que show!", "Adorei!", "Que bacana!", "Perfeito!"
- **Expressões brasileiras naturais:** "Então beleza", "Tranquilo", "De boa", "Certinho"
- **Personalize com carinho:** "querida", "amor", "flor", use o nome com familiaridade
- **Gírias e expressões locais:** "Bora marcar?", "Que tal?", "Tá ligada?", "Show de bola!"

### 🚫 JAMAIS USE (Linguagem Robótica)
❌ "Como posso ajudar" / "Em que posso ajudá-lo" / "Posso ser útil"
❌ "Gostaria de" / "Poderia" / "Seria possível" / "Teria interesse"  
❌ "vou verificar" / "aguarde" / "um momento" / "deixe-me verificar"
❌ "vou consultar" / "deixa eu consultar" / "vou checar" / "deixe-me checar"
❌ "só um minutinho" / "rapidinho" / "deixa eu ver" / "aguarda aí"

## 📅 CONTEXTO DE DATA
{{current_date_context}}

**IMPORTANTE:** Use SEMPRE as informações de data acima. Não calcule ou invente datas.

## 🎭 TOM GERAL
Você é uma pessoa real, brasileira, calorosa e experiente. Cada conversa é única e você se adapta ao cliente, sempre mantendo seu jeito acolhedor e profissional.`,
        variables: ['current_date_context'],
        maxTokens: 150,
        temperature: 0.3,
        source: 'default',
        isCustomized: false
        };
      }

      setTemplate(baseTemplate);
      setTemplateContent(baseTemplate.template);
      setTemplateName(baseTemplate.name);
      setTemplateDescription(baseTemplate.description);
      setMaxTokens(baseTemplate.maxTokens);
      setTemperature(baseTemplate.temperature);
      
    } catch (error) {
      toast({
        title: "Erro ao carregar template",
        description: "Não foi possível carregar o template. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      // Validate first
      const isValid = await validateTemplate();
      if (!isValid) {
        toast({
          title: "Erro de validação",
          description: "Corrija os erros antes de salvar.",
          variant: "destructive",
        });
        return;
      }

      // Save via API
      await templatesAPI.updateTemplate('base_identity', {
        name: templateName,
        template: templateContent,
        description: templateDescription,
        variables: ['current_date_context'],
        maxTokens,
        temperature
      });
      
      toast({
        title: "Eba! Template salvo! 🎉",
        description: "A Trinks já está com a nova personalidade. Seus clientes vão amar!",
      });
      
      // Update template state
      if (template) {
        setTemplate({
          ...template,
          template: templateContent,
          name: templateName,
          description: templateDescription,
          maxTokens,
          temperature,
          isCustomized: true,
          source: 'custom',
          updatedAt: new Date().toISOString()
        });
      }
      
    } catch (error) {
      toast({
        title: "Erro ao salvar",
        description: "Não foi possível salvar o template. Tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = async () => {
    if (template) {
      try {
        // Reset via API
        await templatesAPI.resetTemplate('base_identity');
        
        // Reload from server
        await loadTemplate();
        
        toast({
          title: "Template resetado!",
          description: "Voltamos ao padrão original. Bora personalizar de novo?",
        });
      } catch (error) {
        toast({
          title: "Erro ao resetar",
          description: "Não foi possível resetar o template. Tente novamente.",
          variant: "destructive",
        });
      }
    }
  };

  const handlePreview = async () => {
    setIsPreviewing(true);
    try {
      // Use API preview if available
      let processedTemplate: string;
      
      try {
        const response = await templatesAPI.previewTemplate('base_identity', exampleData);
        processedTemplate = response.preview;
      } catch (apiError) {
        // Fallback to local processing
        console.warn('API preview failed, using local processing:', apiError);
        processedTemplate = templateContent;
        
        // Replace variables with example data
        Object.entries(exampleData).forEach(([key, value]) => {
          const regex = new RegExp(`{{${key}}}`, 'g');
          processedTemplate = processedTemplate.replace(regex, value);
        });
      }
      
      setPreviewData({
        prompt: processedTemplate,
        fullLength: processedTemplate.length,
        settings: {
          maxTokens,
          temperature
        }
      });
      
    } catch (error) {
      toast({
        title: "Erro no preview",
        description: "Não foi possível gerar o preview. Verifique o template.",
        variant: "destructive",
      });
    } finally {
      setIsPreviewing(false);
    }
  };

  const validateTemplate = async (): Promise<boolean> => {
    try {
      // Basic validation
      const errors: string[] = [];
      const warnings: string[] = [];

      if (!templateName.trim()) {
        errors.push('Nome do template é obrigatório');
      }

      if (!templateContent.trim()) {
        errors.push('Conteúdo do template é obrigatório');
      }

      if (maxTokens < 50 || maxTokens > 1000) {
        warnings.push('maxTokens recomendado entre 50-1000');
      }

      if (temperature < 0 || temperature > 1) {
        errors.push('temperature deve estar entre 0 e 1');
      }

      // Check for required variables
      if (!templateContent.includes('{{current_date_context}}')) {
        warnings.push('Template não inclui {{current_date_context}} - pode não manter consistência com contexto temporal');
      }

      // Check for forbidden phrases
      const forbiddenPhrases = ['vou verificar', 'vou consultar', 'aguarde', 'um momento'];
      forbiddenPhrases.forEach(phrase => {
        if (templateContent.toLowerCase().includes(phrase)) {
          warnings.push(`Frase potencialmente problemática encontrada: "${phrase}"`);
        }
      });

      const validationResult = {
        isValid: errors.length === 0,
        errors,
        warnings
      };

      setValidation(validationResult);
      return validationResult.isValid;
      
    } catch (error) {
      console.error('Validation error:', error);
      return false;
    }
  };

  // Auto-validate when content changes
  useEffect(() => {
    if (templateContent) {
      const debounceTimeout = setTimeout(() => {
        validateTemplate();
      }, 1000);

      return () => clearTimeout(debounceTimeout);
    }
  }, [templateContent, templateName, maxTokens, temperature]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-trinks-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando template...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <FileText className="h-8 w-8" style={{ color: '#EC5702' }} />
            Editor de Prompts
          </h1>
          <p className="text-muted-foreground">
            Vem dar um up na personalidade da sua IA! Configure como ela conversa com seus clientes.
          </p>
        </div>
        
        {template?.isCustomized && (
          <Badge variant="secondary" style={{ backgroundColor: '#fff5f2', color: '#EC5702', borderColor: '#ffddcc' }}>
            <Settings className="h-3 w-3 mr-1" />
            Personalizado
          </Badge>
        )}
      </div>

      <Tabs defaultValue="editor" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="editor" className="flex items-center gap-2">
            <Code2 className="h-4 w-4" />
            Editor
          </TabsTrigger>
          <TabsTrigger value="preview" className="flex items-center gap-2">
            <Eye className="h-4 w-4" />
            Preview
          </TabsTrigger>
          <TabsTrigger value="help" className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            Ajuda
          </TabsTrigger>
        </TabsList>

        {/* Editor Tab */}
        <TabsContent value="editor" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Editor */}
            <div className="lg:col-span-2 space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Configurações Básicas</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="templateName">Nome do Template</Label>
                    <Input
                      id="templateName"
                      value={templateName}
                      onChange={(e) => setTemplateName(e.target.value)}
                      placeholder="Nome do template"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="templateDescription">Descrição</Label>
                    <Input
                      id="templateDescription"
                      value={templateDescription}
                      onChange={(e) => setTemplateDescription(e.target.value)}
                      placeholder="Descrição do template"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="maxTokens">Max Tokens</Label>
                      <Input
                        id="maxTokens"
                        type="number"
                        min="50"
                        max="1000"
                        value={maxTokens}
                        onChange={(e) => setMaxTokens(Number(e.target.value))}
                      />
                      <p className="text-xs text-gray-500 mt-1">50-1000</p>
                    </div>
                    
                    <div>
                      <Label htmlFor="temperature">Temperature</Label>
                      <Input
                        id="temperature"
                        type="number"
                        min="0"
                        max="1"
                        step="0.1"
                        value={temperature}
                        onChange={(e) => setTemperature(Number(e.target.value))}
                      />
                      <p className="text-xs text-gray-500 mt-1">0.0-1.0</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Conteúdo do Template</CardTitle>
                  <CardDescription>
                    Use Markdown e variáveis no formato {'{{variavel}}'} 
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    value={templateContent}
                    onChange={(e) => setTemplateContent(e.target.value)}
                    placeholder="Digite o conteúdo do template..."
                    className="min-h-[400px] font-mono text-sm"
                  />
                  
                  {validation && (
                    <div className="mt-4 space-y-2">
                      {validation.errors.length > 0 && (
                        <Alert variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>Erros:</strong>
                            <ul className="list-disc list-inside mt-1">
                              {validation.errors.map((error, index) => (
                                <li key={index}>{error}</li>
                              ))}
                            </ul>
                          </AlertDescription>
                        </Alert>
                      )}
                      
                      {validation.warnings.length > 0 && (
                        <Alert>
                          <AlertCircle className="h-4 w-4" />
                          <AlertDescription>
                            <strong>Avisos:</strong>
                            <ul className="list-disc list-inside mt-1">
                              {validation.warnings.map((warning, index) => (
                                <li key={index}>{warning}</li>
                              ))}
                            </ul>
                          </AlertDescription>
                        </Alert>
                      )}
                      
                      {validation.isValid && validation.errors.length === 0 && validation.warnings.length === 0 && (
                        <Alert>
                          <CheckCircle2 className="h-4 w-4 text-green-600" />
                          <AlertDescription className="text-green-800">
                            Template válido e pronto para uso!
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleReset}
                  disabled={isSaving}
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Resetar
                </Button>
                
                <div className="space-x-2">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={handlePreview}
                    disabled={isPreviewing || !templateContent.trim()}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    {isPreviewing ? 'Processando...' : 'Preview'}
                  </Button>
                  <Button
                    type="button"
                    onClick={handleSave}
                    disabled={isSaving || (validation && !validation.isValid)}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isSaving ? 'Salvando...' : 'Salvar'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Variables Panel */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-yellow-500" />
                    Variáveis Disponíveis
                  </CardTitle>
                  <CardDescription>
                    Clique para inserir no template
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {Object.entries(baseIdentityVariables).map(([key, variable]) => (
                    <div key={key} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <code 
                          className="text-sm font-mono bg-gray-100 px-2 py-1 rounded cursor-pointer hover:bg-gray-200 transition-colors"
                          onClick={() => {
                            const cursorPos = document.querySelector<HTMLTextAreaElement>('textarea')?.selectionStart || 0;
                            const newContent = templateContent.slice(0, cursorPos) + 
                              `{{${key}}}` + 
                              templateContent.slice(cursorPos);
                            setTemplateContent(newContent);
                          }}
                        >
                          {`{{${key}}}`}
                        </code>
                        {variable.required && (
                          <Badge variant="destructive" className="text-xs">
                            Obrigatória
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{variable.description}</p>
                      <div className="text-xs text-gray-500">
                        <strong>Exemplo:</strong>
                        <pre className="mt-1 bg-gray-50 p-2 rounded text-xs overflow-x-auto">
{variable.example}
                        </pre>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-6">
          {previewData ? (
            <Card>
              <CardHeader>
                <CardTitle>Preview do Template Processado</CardTitle>
                <CardDescription>
                  Como o template aparece para a IA com variáveis substituídas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="font-semibold text-lg">{previewData.fullLength}</div>
                      <div className="text-gray-600">Caracteres</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="font-semibold text-lg">{previewData.settings.maxTokens}</div>
                      <div className="text-gray-600">Max Tokens</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded">
                      <div className="font-semibold text-lg">{previewData.settings.temperature}</div>
                      <div className="text-gray-600">Temperature</div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="bg-gray-50 border rounded-lg p-4">
                    <pre className="whitespace-pre-wrap text-sm font-mono">
                      {previewData.prompt}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Eye className="h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Que tal ver como ficou?
                </h3>
                <p className="text-gray-600 text-center mb-4">
                  Clique em "Preview" na aba Editor para visualizar como a sua IA vai conversar com os clientes
                </p>
                <Button variant="outline" onClick={handlePreview} disabled={isPreviewing || !templateContent.trim()}>
                  <Eye className="h-4 w-4 mr-2" />
                  {isPreviewing ? 'Processando...' : 'Gerar Preview'}
                </Button>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Help Tab */}
        <TabsContent value="help" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Como Usar o Editor de Prompts</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <h4 className="font-semibold">🎯 Variáveis</h4>
                <p className="text-sm text-gray-600">
                  Use variáveis no formato <code className="bg-gray-100 px-1 rounded">{'{{nome_da_variavel}}'}</code> para inserir dados dinâmicos no template.
                </p>
                
                <h4 className="font-semibold">📝 Markdown</h4>
                <p className="text-sm text-gray-600">
                  Você pode usar Markdown para formatação (títulos, listas, negrito, etc.).
                </p>
                
                <h4 className="font-semibold">⚙️ Configurações de IA</h4>
                <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                  <li><strong>Max Tokens:</strong> Limite máximo de tokens na resposta (50-1000)</li>
                  <li><strong>Temperature:</strong> Criatividade da IA (0=conservador, 1=criativo)</li>
                </ul>
                
                <h4 className="font-semibold">✅ Validação</h4>
                <p className="text-sm text-gray-600">
                  O sistema valida automaticamente seu template e mostra erros ou avisos.
                </p>
                
                <h4 className="font-semibold">👀 Preview</h4>
                <p className="text-sm text-gray-600">
                  Use o Preview para ver como o template aparece para a IA com dados de exemplo.
                </p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Regras Importantes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Linguagem Proibida:</strong> Evite frases como "vou verificar", "aguarde", "um momento".
                    A IA deve sempre responder com os dados que possui no momento.
                  </AlertDescription>
                </Alert>
                
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Tom Brasileiro:</strong> Use expressões naturais como "E aí!", "Que massa!", "Show!".
                    Máximo 2-3 linhas por resposta, como WhatsApp.
                  </AlertDescription>
                </Alert>
                
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Contexto Temporal:</strong> Sempre inclua a variável <code>{'{{current_date_context}}'}</code>
                    para manter a IA atualizada com data e hora corretas.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}