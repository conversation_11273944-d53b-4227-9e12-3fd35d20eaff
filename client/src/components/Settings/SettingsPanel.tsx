import React, { useState, useEffect } from 'react';
import WhatsAppSettingsModal from '../WhatsApp/WhatsAppSettingsFixed';
import WhatsAppTab from './WhatsAppTab';
import ConfirmDialog from '../ui/ConfirmDialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Separator } from '../ui/separator';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Smartphone, 
  TestTube2, 
  Settings, 
  Globe, 
  Zap, 
  CheckCircle2, 
  AlertCircle,
  Sparkles,
  Store,
  Users,
  Clock,
  Palette
} from 'lucide-react';

interface SettingsData {
  establishmentName: string;
  services: string[];
  professionals: string[];
  workingHours: string;
  customPrompt: string;
  anthropicApiKey: string;
  openaiApiKey: string;
  trinksApiUrl: string;
  trinksApiKey: string;
  trinksEnabled: boolean;
  trinksEstabelecimentoId: string;
  operationMode: 'whatsapp' | 'test';
  // WhatsApp settings
  whatsappEnabled: boolean;
  whatsappType: 'twilio' | 'waha' | 'customer-app';
  twilioAccountSid: string;
  twilioAuthToken: string;
  twilioPhoneNumber: string;
  wahaApiUrl: string;
  wahaApiKey: string;
  wahaWebhookUrl?: string;
  wahaWebhookPaused?: boolean;
}

interface SettingsPanelProps {
  onClose: () => void;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const [settings, setSettings] = useState<SettingsData>({
    establishmentName: 'Salão Trinks',
    services: ['Corte Feminino', 'Corte + Escova', 'Pintura + Corte', 'Hidratação', 'Corte Masculino', 'Corte + Barba', 'Manicure', 'Pedicure'],
    professionals: ['Ana', 'Carla', 'Roberto', 'Paula'],
    workingHours: '9:00 às 18:00, Segunda a Sábado',
    customPrompt: '',
    anthropicApiKey: '',
    openaiApiKey: '',
    trinksApiUrl: 'https://api.trinks.com',
    trinksApiKey: '',
    trinksEnabled: false,
    trinksEstabelecimentoId: '188253',
    operationMode: 'test',
    // WhatsApp settings
    whatsappEnabled: false,
    whatsappType: 'waha',
    twilioAccountSid: '',
    twilioAuthToken: '',
    twilioPhoneNumber: '',
    wahaApiUrl: 'http://localhost:8090',
    wahaApiKey: '',
    wahaWebhookUrl: '',
    wahaWebhookPaused: false
  });

  const [activeTab, setActiveTab] = useState<'general' | 'whatsapp' | 'api' | 'trinks'>('general');
  const [isSaving, setIsSaving] = useState(false);
  const [showWhatsAppSettings, setShowWhatsAppSettings] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  useEffect(() => {
    // Load settings from backend first, then localStorage as fallback
    const loadSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const backendSettings = await response.json();
          setSettings(backendSettings);
          
          // Only set initial tab if no operation mode is set (first time user)
          if (!backendSettings.operationMode) {
            setActiveTab('mode-selection');
          }
          return;
        }
      } catch (error) {
        console.warn('Could not load settings from backend, using localStorage:', error);
      }
      
      // Fallback to localStorage if backend fails
      const savedSettings = localStorage.getItem('trinks-settings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        setSettings(parsedSettings);

        // Only set initial tab if no operation mode is set (first time user)
        if (!parsedSettings.operationMode) {
          setActiveTab('mode-selection');
        }
      }
    };

    loadSettings();
  }, []);

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // Save to localStorage
      localStorage.setItem('trinks-settings', JSON.stringify(settings));
      
      // Send to backend
      const response = await fetch('/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        alert('Configurações salvas com sucesso!');
        onClose();
      } else {
        throw new Error('Erro ao salvar configurações');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      // Still save locally even if backend fails
      alert('Configurações salvas localmente. Algumas funcionalidades podem não funcionar sem a API key.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClearTestData = () => {
    localStorage.removeItem('test-conversations');
    localStorage.removeItem('test-customers');
    setShowConfirmDialog(false);
    alert('Dados de teste limpos com sucesso!');
  };

  const handleCancelClearData = () => {
    setShowConfirmDialog(false);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full h-[90vh] flex flex-col">
        {/* Header */}
        <div className="border-b p-4 flex items-center justify-between shrink-0">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold">Configurações do Sistema</h3>
            {/* Mode Indicator */}
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
              settings.operationMode === 'whatsapp'
                ? 'bg-green-100 text-green-800'
                : 'bg-purple-100 text-purple-800'
            }`}>
              {settings.operationMode === 'whatsapp' ? '📱 Modo WhatsApp' : '🧪 Modo Teste'}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs com design moderno */}
        <div className="flex-1 flex flex-col min-h-0">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col min-h-0">
            <TabsList className="mx-6 mt-4 grid w-auto grid-cols-4 shrink-0">
              <TabsTrigger value="general" className="flex items-center gap-2">
                <Store className="h-4 w-4" />
                Geral
              </TabsTrigger>
              <TabsTrigger value="whatsapp" className="flex items-center gap-2">
                <Smartphone className="h-4 w-4" />
                WhatsApp
              </TabsTrigger>
              <TabsTrigger value="api" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Claude API
              </TabsTrigger>
              <TabsTrigger value="trinks" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Trinks API
              </TabsTrigger>
            </TabsList>

            {/* Content */}
            <div className="flex-1 overflow-y-auto px-6 min-h-0">
              <TabsContent value="general" className="p-6 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Store className="w-5 h-5 text-primary" />
                      Informações do Estabelecimento
                    </CardTitle>
                    <CardDescription>Configure as informações básicas do seu negócio</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="establishment-name">Nome do Estabelecimento</Label>
                      <Input
                        id="establishment-name"
                        value={settings.establishmentName}
                        onChange={(e) => setSettings({...settings, establishmentName: e.target.value})}
                        placeholder="Ex: Salão Bella Vita"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="services">Serviços Oferecidos (um por linha)</Label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            try {
                              const response = await fetch('/api/update-settings', {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json'
                                }
                              });
                              
                              const result = await response.json();
                              
                              if (response.ok && result.success) {
                                const settingsResponse = await fetch('/api/settings');
                                if (settingsResponse.ok) {
                                  const updatedSettings = await settingsResponse.json();
                                  setSettings(updatedSettings);
                                }
                                
                                alert(`✅ ${result.message}`);
                              } else {
                                alert(`❌ ${result.message || 'Erro ao atualizar configurações'}`);
                              }
                            } catch (error) {
                              alert('Erro ao conectar com a API: ' + (error as Error).message);
                            }
                          }}
                        >
                          <Sparkles className="w-4 h-4 mr-1" />
                          Sincronizar
                        </Button>
                      </div>
                      <Textarea
                        id="services"
                        value={settings.services.join('\n')}
                        onChange={(e) => setSettings({...settings, services: e.target.value.split('\n').filter(s => s.trim())})}
                        rows={6}
                        placeholder="Corte Feminino&#10;Coloração&#10;Manicure&#10;Pedicure"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="professionals">Profissionais (um por linha)</Label>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            try {
                              const response = await fetch('/api/update-settings', {
                                method: 'POST',
                                headers: {
                                  'Content-Type': 'application/json'
                                }
                              });
                              
                              const result = await response.json();
                              
                              if (response.ok && result.success) {
                                const settingsResponse = await fetch('/api/settings');
                                if (settingsResponse.ok) {
                                  const updatedSettings = await settingsResponse.json();
                                  setSettings(updatedSettings);
                                }
                                
                                alert(`✅ ${result.message}`);
                              } else {
                                alert(`❌ ${result.message || 'Erro ao atualizar configurações'}`);
                              }
                            } catch (error) {
                              alert('Erro ao conectar com a API: ' + (error as Error).message);
                            }
                          }}
                        >
                          <Users className="w-4 h-4 mr-1" />
                          Sincronizar
                        </Button>
                      </div>
                      <Textarea
                        id="professionals"
                        value={settings.professionals.join('\n')}
                        onChange={(e) => setSettings({...settings, professionals: e.target.value.split('\n').filter(p => p.trim())})}
                        rows={4}
                        placeholder="Ana Silva&#10;Carlos Mendes&#10;Maria Santos"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="working-hours">Horário de Funcionamento</Label>
                      <Input
                        id="working-hours"
                        value={settings.workingHours}
                        onChange={(e) => setSettings({...settings, workingHours: e.target.value})}
                        placeholder="9:00 às 18:00, Segunda a Sábado"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="whatsapp" className="py-6">
                <WhatsAppTab
                  settings={{
                    operationMode: settings.operationMode,
                    whatsappEnabled: settings.whatsappEnabled,
                    whatsappType: settings.whatsappType,
                    twilioAccountSid: settings.twilioAccountSid,
                    twilioAuthToken: settings.twilioAuthToken,
                    twilioPhoneNumber: settings.twilioPhoneNumber,
                    wahaApiUrl: settings.wahaApiUrl,
                    wahaApiKey: settings.wahaApiKey,
                    wahaWebhookUrl: settings.wahaWebhookUrl,
                    wahaWebhookPaused: settings.wahaWebhookPaused
                  }}
                  onSettingsChange={(whatsappSettings) => {
                    setSettings({
                      ...settings,
                      operationMode: whatsappSettings.operationMode,
                      whatsappEnabled: whatsappSettings.whatsappEnabled,
                      whatsappType: whatsappSettings.whatsappType,
                      twilioAccountSid: whatsappSettings.twilioAccountSid,
                      twilioAuthToken: whatsappSettings.twilioAuthToken,
                      twilioPhoneNumber: whatsappSettings.twilioPhoneNumber,
                      wahaApiUrl: whatsappSettings.wahaApiUrl,
                      wahaApiKey: whatsappSettings.wahaApiKey,
                      wahaWebhookUrl: whatsappSettings.wahaWebhookUrl,
                      wahaWebhookPaused: whatsappSettings.wahaWebhookPaused
                    });
                  }}
                  onOpenAdvancedSettings={() => setShowWhatsAppSettings(true)}
                />
              </TabsContent>

              <TabsContent value="api" className="py-6 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Zap className="w-5 h-5 text-primary" />
                      Configurações de IA
                    </CardTitle>
                    <CardDescription>Configure as chaves de API para IA e transcrição</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Sobre a API:</strong> Para usar a IA real do Claude, você precisa configurar uma API key. 
                        Acesse <a href="https://console.anthropic.com" target="_blank" rel="noopener noreferrer" className="underline text-primary hover:text-primary/80">console.anthropic.com</a> 
                        para obter sua chave.
                      </AlertDescription>
                    </Alert>

                    <div className="space-y-2">
                      <Label htmlFor="claude-api-key">Claude API Key (Anthropic)</Label>
                      <Input
                        id="claude-api-key"
                        type="password"
                        value={settings.anthropicApiKey}
                        onChange={(e) => setSettings({...settings, anthropicApiKey: e.target.value})}
                        placeholder="sk-ant-api03-..."
                      />
                      <p className="text-xs text-muted-foreground">
                        {settings.anthropicApiKey 
                          ? '🟢 Configurado - IA real será utilizada'
                          : '🟡 Não configurado - usando respostas simuladas'
                        }
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="openai-api-key">OpenAI API Key (Whisper)</Label>
                      <Input
                        id="openai-api-key"
                        type="password"
                        value={settings.openaiApiKey}
                        onChange={(e) => setSettings({...settings, openaiApiKey: e.target.value})}
                        placeholder="sk-..."
                      />
                      <p className="text-xs text-muted-foreground">
                        {settings.openaiApiKey 
                          ? '🟢 Configurado - transcrição real será utilizada'
                          : '🟡 Não configurado - usando transcrição simulada'
                        }
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="trinks" className="py-6 space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Globe className="w-5 h-5 text-primary" />
                      Integração Trinks API
                    </CardTitle>
                    <CardDescription>Configure a conexão com a API oficial da Trinks</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Configure a integração com a API oficial da Trinks para acessar dados reais de clientes, 
                        agendamentos e profissionais. 
                        <a href="https://trinks.readme.io/reference/introducao" target="_blank" rel="noopener noreferrer" className="underline text-primary hover:text-primary/80 ml-1">
                          Ver documentação
                        </a>
                      </AlertDescription>
                    </Alert>

                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="trinksEnabled"
                        checked={settings.trinksEnabled}
                        onChange={(e) => setSettings({...settings, trinksEnabled: e.target.checked})}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <Label htmlFor="trinksEnabled">🚀 Habilitar integração com API Trinks</Label>
                    </div>

                    <div className={`space-y-4 ${!settings.trinksEnabled ? 'opacity-50' : ''}`}>
                      <div className="space-y-2">
                        <Label htmlFor="trinks-api-key">API Key Trinks</Label>
                        <Input
                          id="trinks-api-key"
                          type="password"
                          value={settings.trinksApiKey}
                          onChange={(e) => setSettings({...settings, trinksApiKey: e.target.value})}
                          disabled={!settings.trinksEnabled}
                          placeholder="Sua chave de API da Trinks..."
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="establishment-id">ID do Estabelecimento</Label>
                        <Input
                          id="establishment-id"
                          value={settings.trinksEstabelecimentoId}
                          onChange={(e) => setSettings({...settings, trinksEstabelecimentoId: e.target.value})}
                          disabled={!settings.trinksEnabled}
                          placeholder="188253"
                        />
                        <p className="text-xs text-muted-foreground">
                          ID do seu estabelecimento na plataforma Trinks
                        </p>
                      </div>
                    </div>

                    <div className={`rounded-lg p-4 ${
                      settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                        ? 'bg-green-50 border border-green-200' 
                        : settings.trinksEnabled 
                          ? 'bg-yellow-50 border border-yellow-200'
                          : 'bg-muted border border-border'
                    }`}>
                      <p className={`text-sm ${
                        settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                          ? 'text-green-800' 
                          : settings.trinksEnabled 
                            ? 'text-yellow-800'
                            : 'text-muted-foreground'
                      }`}>
                        {settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                          ? '✅ API Trinks configurada e pronta para uso'
                          : settings.trinksEnabled 
                            ? '⚠️ Configuração incompleta - API Key e ID do estabelecimento necessários'
                            : '🔒 Usando dados simulados (mock)'
                        }
                      </p>
                    </div>

                    {/* Test API Connection */}
                    {settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId && (
                      <Button 
                        onClick={async () => {
                          try {
                            const response = await fetch('/api/trinks/test-connection', {
                              method: 'POST'
                            });
                            const result = await response.json();
                            
                            if (result.success) {
                              alert(`✅ ${result.message}`);
                            } else {
                              alert(`❌ ${result.message}`);
                            }
                          } catch (error) {
                            alert('❌ Erro ao testar conexão com a API');
                          }
                        }}
                        className="w-full"
                      >
                        🔍 Testar Conexão com API
                      </Button>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </div>
          </Tabs>
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex justify-end space-x-3 shrink-0">
          <Button 
            onClick={onClose}
            variant="outline"
          >
            Cancelar
          </Button>
          <Button 
            onClick={handleSave}
            disabled={isSaving}
          >
            {isSaving ? 'Salvando...' : 'Salvar Configurações'}
          </Button>
        </div>
      </div>

      {/* WhatsApp Settings Modal */}
      {showWhatsAppSettings && (
        <WhatsAppSettingsModal onClose={() => setShowWhatsAppSettings(false)} />
      )}

      {/* Confirm Dialog */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        title="Limpar Dados de Teste"
        message="Tem certeza que deseja limpar todos os dados de teste? Esta ação não pode ser desfeita."
        confirmText="Sim, limpar"
        cancelText="Cancelar"
        onConfirm={handleClearTestData}
        onCancel={handleCancelClearData}
        variant="danger"
      />
    </div>
  );
};

export default SettingsPanel;