import React, { useState, useEffect } from 'react';
import Logger from '../../utils/logger';
import ConfirmDialog from '../ui/ConfirmDialog';

interface LogsPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const LogsPanel: React.FC<LogsPanelProps> = ({ isOpen, onClose }) => {
  const [logs, setLogs] = useState<any[]>([]);
  const [filter, setFilter] = useState<string>('ALL');
  const [autoRefresh, setAutoRefresh] = useState<boolean>(true);
  const [showClearConfirm, setShowClearConfirm] = useState<boolean>(false);

  useEffect(() => {
    if (isOpen) {
      refreshLogs();
      
      if (autoRefresh) {
        const interval = setInterval(refreshLogs, 2000);
        return () => clearInterval(interval);
      }
    }
  }, [isOpen, autoRefresh]);

  const refreshLogs = () => {
    setLogs(Logger.getLogs());
  };

  const filteredLogs = logs.filter(log => {
    if (filter === 'ALL') return true;
    return log.level === filter;
  });

  const downloadLogs = () => {
    Logger.downloadLogs();
  };

  const clearLogs = () => {
    setShowClearConfirm(true);
  };

  const handleConfirmClear = () => {
    Logger.clearLogs();
    setLogs([]);
    setShowClearConfirm(false);
  };

  const handleCancelClear = () => {
    setShowClearConfirm(false);
  };

  const toggleLogging = () => {
    const isEnabled = localStorage.getItem('enableLogging') === 'true';
    Logger.enableLogging(!isEnabled);
    alert(`Logging ${!isEnabled ? 'habilitado' : 'desabilitado'}!`);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-5/6 h-5/6 max-w-6xl max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">📄 Logs do Salão</h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Controls */}
        <div className="flex items-center gap-4 p-4 border-b bg-gray-50">
          <div className="flex items-center gap-2">
            <label className="text-sm font-medium">Filtro:</label>
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm"
            >
              <option value="ALL">Todos</option>
              <option value="ERROR">Errors</option>
              <option value="WARN">Warnings</option>
              <option value="INFO">Info</option>
              <option value="DEBUG">Debug</option>
              <option value="SOCKET">Socket</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <label className="flex items-center gap-1 text-sm">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
              Auto-refresh
            </label>
          </div>

          <div className="flex gap-2 ml-auto">
            <button
              onClick={refreshLogs}
              className="px-3 py-1 bg-blue-500 text-white rounded-md text-sm hover:bg-blue-600"
            >
              🔄 Atualizar
            </button>
            <button
              onClick={downloadLogs}
              className="px-3 py-1 bg-green-500 text-white rounded-md text-sm hover:bg-green-600"
            >
              💾 Download
            </button>
            <button
              onClick={clearLogs}
              className="px-3 py-1 bg-red-500 text-white rounded-md text-sm hover:bg-red-600"
            >
              🗑️ Limpar
            </button>
            <button
              onClick={toggleLogging}
              className="px-3 py-1 bg-purple-500 text-white rounded-md text-sm hover:bg-purple-600"
            >
              🔧 Toggle Logging
            </button>
          </div>
        </div>

        {/* Logs Display */}
        <div className="flex-1 overflow-auto p-4 bg-gray-900 text-green-400 font-mono text-sm">
          {filteredLogs.length === 0 ? (
            <div className="text-center text-gray-500 mt-10">
              Nenhum log encontrado para o filtro selecionado.
            </div>
          ) : (
            <div className="space-y-1">
              {filteredLogs.slice(-500).map((log, index) => (
                <div
                  key={index}
                  className={`flex gap-4 p-2 rounded ${
                    log.level === 'ERROR' ? 'bg-red-900/20 text-red-300' :
                    log.level === 'WARN' ? 'bg-yellow-900/20 text-yellow-300' :
                    log.level === 'DEBUG' ? 'bg-blue-900/20 text-blue-300' :
                    log.level === 'SOCKET' ? 'bg-purple-900/20 text-purple-300' :
                    'bg-gray-800/20'
                  }`}
                >
                  <span className="text-gray-400 w-20 flex-shrink-0">
                    {new Date(log.timestamp).toLocaleTimeString()}
                  </span>
                  <span className={`w-16 flex-shrink-0 font-bold ${
                    log.level === 'ERROR' ? 'text-red-400' :
                    log.level === 'WARN' ? 'text-yellow-400' :
                    log.level === 'DEBUG' ? 'text-blue-400' :
                    log.level === 'SOCKET' ? 'text-purple-400' :
                    'text-green-400'
                  }`}>
                    [{log.level}]
                  </span>
                  <span className="flex-1">{log.message}</span>
                  {log.data && (
                    <details className="text-gray-300">
                      <summary className="cursor-pointer">📋</summary>
                      <pre className="mt-2 text-xs bg-gray-800 p-2 rounded max-w-md overflow-auto">
                        {log.data}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-gray-50 text-sm text-gray-600">
          <div className="flex justify-between items-center">
            <span>
              Mostrando {filteredLogs.slice(-500).length} de {filteredLogs.length} logs
              {filteredLogs.length > 500 && ' (últimos 500)'}
            </span>
            <span>
              Logging: {localStorage.getItem('enableLogging') === 'true' ? '✅ Habilitado' : '❌ Desabilitado'}
            </span>
          </div>
        </div>
      </div>

      {/* Confirm Dialog */}
      <ConfirmDialog
        isOpen={showClearConfirm}
        title="Limpar Logs"
        message="Tem certeza que deseja limpar todos os logs? Esta ação não pode ser desfeita."
        confirmText="Sim, limpar"
        cancelText="Cancelar"
        onConfirm={handleConfirmClear}
        onCancel={handleCancelClear}
        variant="danger"
      />
    </div>
  );
};

export default LogsPanel;