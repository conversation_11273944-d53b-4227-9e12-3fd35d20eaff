import React, { useState, useEffect } from 'react';
import WhatsAppSettingsModal from '../WhatsApp/WhatsAppSettings';
import ConfirmDialog from '../ui/ConfirmDialog';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Input } from '../ui/input';
import { Textarea } from '../ui/textarea';
import { Label } from '../ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Separator } from '../ui/separator';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Smartphone, 
  TestTube2, 
  Settings, 
  Globe, 
  Zap, 
  CheckCircle2, 
  AlertCircle,
  Sparkles,
  Store,
  Users,
  Clock,
  Palette
} from 'lucide-react';

interface SettingsData {
  establishmentName: string;
  services: string[];
  professionals: string[];
  workingHours: string;
  customPrompt: string;
  apiKey: string;
  openaiApiKey: string;
  trinksApiUrl: string;
  trinksApiKey: string;
  trinksEnabled: boolean;
  trinksEstabelecimentoId: string;
  operationMode: 'whatsapp' | 'test';
}

interface SettingsPanelProps {
  onClose: () => void;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ onClose }) => {
  const [settings, setSettings] = useState<SettingsData>({
    establishmentName: 'Salão Trinks',
    services: ['Corte Feminino', 'Corte + Escova', 'Pintura + Corte', 'Hidratação', 'Corte Masculino', 'Corte + Barba', 'Manicure', 'Pedicure'],
    professionals: ['Ana', 'Carla', 'Roberto', 'Paula'],
    workingHours: '9:00 às 18:00, Segunda a Sábado',
    customPrompt: '',
    apiKey: '',
    openaiApiKey: '',
    trinksApiUrl: 'https://api.trinks.com',
    trinksApiKey: '',
    trinksEnabled: false,
    trinksEstabelecimentoId: '188253',
    operationMode: 'test'
  });

  const [activeTab, setActiveTab] = useState<'mode-selection' | 'general' | 'api' | 'trinks'>('general');
  const [isSaving, setIsSaving] = useState(false);
  const [showWhatsAppSettings, setShowWhatsAppSettings] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = localStorage.getItem('trinks-settings');
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings);
      setSettings(parsedSettings);

      // Only set initial tab if no operation mode is set (first time user)
      if (!parsedSettings.operationMode) {
        setActiveTab('mode-selection');
      }
    }
  }, []);



  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // Save to localStorage
      localStorage.setItem('trinks-settings', JSON.stringify(settings));
      
      // Send to backend
      const response = await fetch('http://localhost:3001/api/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
      });

      if (response.ok) {
        alert('Configurações salvas com sucesso!');
        onClose();
      } else {
        throw new Error('Erro ao salvar configurações');
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      // Still save locally even if backend fails
      alert('Configurações salvas localmente. Algumas funcionalidades podem não funcionar sem a API key.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleClearTestData = () => {
    localStorage.removeItem('test-conversations');
    localStorage.removeItem('test-customers');
    setShowConfirmDialog(false);
    alert('Dados de teste limpos com sucesso!');
  };

  const handleCancelClearData = () => {
    setShowConfirmDialog(false);
  };

  const defaultPrompt = `Você é uma assistente de IA que ajuda o SALÃO a atender seus CLIENTES via WhatsApp.

CONTEXTO IMPORTANTE:
- Você está auxiliando a EQUIPE DO SALÃO a responder mensagens dos clientes
- O cliente está enviando mensagens para o salão pedindo agendamentos
- Você deve gerar respostas que o atendente do salão enviaria para o cliente
- Seja uma recepcionista digital profissional e simpática

CARACTERÍSTICAS DAS RESPOSTAS:
- Seja simpática, profissional e acolhedora
- Use mensagens curtas como no WhatsApp (máximo 2-3 linhas)  
- Personalize baseado no histórico do cliente
- Sugira horários de forma inteligente
- Confirme sempre antes de finalizar
- Use emojis ocasionalmente para ser mais amigável
- Seja natural, evite soar robótica
- Responda como se fosse a recepcionista do salão

INFORMAÇÕES DO ESTABELECIMENTO:
Nome: {{establishmentName}}
Serviços disponíveis: {{services}}
Profissionais: {{professionals}}
Horário de funcionamento: {{workingHours}}

FLUXO DE ATENDIMENTO:
1. Cumprimente o cliente calorosamente
2. Identifique qual serviço ele deseja
3. Sugira profissional (baseado no histórico se houver)
4. Pergunte sobre data preferida
5. Ofereça horários disponíveis
6. Confirme todos os dados
7. Finalize o agendamento

REGRAS IMPORTANTES:
- SEMPRE confirme todos os dados antes de finalizar
- Se o cliente tem preferência por profissional, respeite
- Ofereça alternativas se não houver disponibilidade
- Mantenha o tom acolhedor como um salão de qualidade
- Responda sempre na perspectiva do SALÃO para o CLIENTE`;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="border-b p-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h3 className="text-lg font-semibold">Configurações do Sistema</h3>
            {/* Mode Indicator */}
            <div className={`px-3 py-1 rounded-full text-xs font-medium ${
              settings.operationMode === 'whatsapp'
                ? 'bg-green-100 text-green-800'
                : 'bg-purple-100 text-purple-800'
            }`}>
              {settings.operationMode === 'whatsapp' ? '📱 Modo WhatsApp' : '🧪 Modo Teste'}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs com design moderno */}
        <div className="px-6 pt-4">
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="mode-selection" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Modo
              </TabsTrigger>
              <TabsTrigger value="general" className="flex items-center gap-2">
                <Store className="h-4 w-4" />
                Geral
              </TabsTrigger>
              <TabsTrigger value="api" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Claude API
              </TabsTrigger>
              <TabsTrigger value="trinks" className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Trinks API
              </TabsTrigger>
            </TabsList>

        {/* Content */}
        <div className="flex-1 overflow-auto">
            <TabsContent value="mode-selection" className="p-6 space-y-6">
            <div className="space-y-6">
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-foreground mb-2">Vem dar um up no seu atendimento! 🚀</h3>
                <p className="text-muted-foreground">Escolha como A Trinks vai funcionar no seu estabelecimento</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
                {/* WhatsApp Mode */}
                <Card 
                  className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                    settings.operationMode === 'whatsapp'
                      ? 'ring-2 ring-primary bg-gradient-to-br from-green-50 to-emerald-50 shadow-md'
                      : 'hover:ring-1 hover:ring-muted-foreground/25'
                  }`}
                  onClick={() => setSettings({...settings, operationMode: 'whatsapp'})}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        settings.operationMode === 'whatsapp' ? 'bg-green-500' : 'bg-muted'
                      }`}>
                        <Smartphone className={`w-6 h-6 ${settings.operationMode === 'whatsapp' ? 'text-white' : 'text-muted-foreground'}`} />
                      </div>
                      <div>
                        <CardTitle className={`text-xl ${settings.operationMode === 'whatsapp' ? 'text-green-900' : 'text-card-foreground'}`}>
                          Modo WhatsApp
                        </CardTitle>
                        <CardDescription className={settings.operationMode === 'whatsapp' ? 'text-green-700' : undefined}>
                          Atendimento real com clientes
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                        Conecta com seu WhatsApp Business
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                        Atendimento inteligente 24/7
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                        Integração total com A Trinks
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-green-500" />
                        Análise de humor em tempo real
                      </p>
                    </div>
                    {settings.operationMode === 'whatsapp' && (
                      <Badge variant="secondary" className="w-fit bg-green-100 text-green-800 border-green-200">
                        <CheckCircle2 className="w-3 h-3 mr-1" />
                        Modo Ativo
                      </Badge>
                    )}
                  </CardContent>
                </Card>

                {/* Test Mode */}
                <Card 
                  className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                    settings.operationMode === 'test'
                      ? 'ring-2 ring-purple-500 bg-gradient-to-br from-purple-50 to-violet-50 shadow-md'
                      : 'hover:ring-1 hover:ring-muted-foreground/25'
                  }`}
                  onClick={() => setSettings({...settings, operationMode: 'test'})}
                >
                  <CardHeader className="pb-4">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 rounded-xl flex items-center justify-center ${
                        settings.operationMode === 'test' ? 'bg-purple-500' : 'bg-muted'
                      }`}>
                        <TestTube2 className={`w-6 h-6 ${settings.operationMode === 'test' ? 'text-white' : 'text-muted-foreground'}`} />
                      </div>
                      <div>
                        <CardTitle className={`text-xl ${settings.operationMode === 'test' ? 'text-purple-900' : 'text-card-foreground'}`}>
                          Modo Teste
                        </CardTitle>
                        <CardDescription className={settings.operationMode === 'test' ? 'text-purple-700' : undefined}>
                          Ambiente seguro para praticar
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="space-y-2 text-sm text-muted-foreground">
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-purple-500" />
                        Customer App para simulação
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-purple-500" />
                        Dados fictícios e seguros
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-purple-500" />
                        Teste todas as funcionalidades
                      </p>
                      <p className="flex items-center gap-2">
                        <CheckCircle2 className="w-4 h-4 text-purple-500" />
                        Sem impacto em clientes reais
                      </p>
                    </div>
                    {settings.operationMode === 'test' && (
                      <Badge variant="secondary" className="w-fit bg-purple-100 text-purple-800 border-purple-200">
                        <CheckCircle2 className="w-3 h-3 mr-1" />
                        Modo Ativo
                      </Badge>
                    )}
                  </CardContent>
                </Card>
              </div>

              <Alert className="max-w-2xl mx-auto">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Importante:</strong> A Trinks pode operar apenas em um modo por vez. Escolha o modo adequado para sua situação atual.
                  Você pode alternar entre os modos a qualquer momento.
                </AlertDescription>
              </Alert>

              {/* Configurações específicas do modo selecionado */}
              {settings.operationMode && (
                <div className="mt-8 border-t pt-8">
                  {settings.operationMode === 'whatsapp' && (
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-10 h-10 bg-green-500 rounded-xl flex items-center justify-center">
                            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.595z"/>
                            </svg>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-green-900">Configurações do WhatsApp</h3>
                            <p className="text-sm text-green-700">Configure sua conexão WhatsApp Business</p>
                          </div>
                        </div>
                      </div>

                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <div className="text-center py-8">
                          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </div>
                          <h4 className="text-lg font-semibold text-gray-900 mb-2">Configurações do WhatsApp</h4>
                          <p className="text-gray-600 text-sm mb-6 max-w-md mx-auto">
                            Configure sua conexão WhatsApp para começar a receber e responder mensagens automaticamente.
                          </p>
                          <button
                            onClick={() => setShowWhatsAppSettings(true)}
                            className="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors font-medium"
                          >
                            📱 Abrir Configurações WhatsApp
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {settings.operationMode === 'test' && (
                    <div className="space-y-6">
                      <div className="bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 rounded-lg p-6">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-10 h-10 bg-purple-500 rounded-xl flex items-center justify-center">
                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-purple-900">Configurações do Modo Teste</h3>
                            <p className="text-sm text-purple-700">Ambiente seguro para testar funcionalidades</p>
                          </div>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Test Mode Status */}
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                              <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                              </svg>
                            </div>
                            <h4 className="font-semibold text-gray-900">Status do Modo Teste</h4>
                          </div>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <span className="text-sm text-green-800 font-medium">Customer App</span>
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Ativo</span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <span className="text-sm text-green-800 font-medium">Análise de Humor</span>
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Ativo</span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                              <span className="text-sm text-green-800 font-medium">Simulação IA</span>
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Ativo</span>
                            </div>
                          </div>
                        </div>

                        {/* Test Actions */}
                        <div className="bg-white border border-gray-200 rounded-lg p-6">
                          <div className="flex items-center gap-3 mb-4">
                            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                              </svg>
                            </div>
                            <h4 className="font-semibold text-gray-900">Ações de Teste</h4>
                          </div>
                          <div className="space-y-3">
                            <button
                              onClick={() => window.open('http://localhost:3000/customer-app', '_blank')}
                              className="w-full text-left p-3 bg-purple-50 border border-purple-200 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                            >
                              🚀 Abrir Customer App
                            </button>
                            <button
                              onClick={() => window.open('http://localhost:3000/mood-demo.html', '_blank')}
                              className="w-full text-left p-3 bg-blue-50 border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                            >
                              🎭 Demo Análise de Humor
                            </button>
                            <button
                              onClick={() => setShowConfirmDialog(true)}
                              className="w-full text-left p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg hover:bg-red-100 transition-colors"
                            >
                              🧹 Limpar Dados de Teste
                            </button>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-6">
                        <div className="flex items-start gap-3">
                          <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mt-1">
                            <svg className="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                          </div>
                          <div>
                            <h4 className="font-medium text-orange-900 mb-2">Como usar o Modo Teste</h4>
                            <ul className="text-sm text-orange-800 space-y-1">
                              <li><strong>Customer App:</strong> Simule conversas como se fosse um cliente real</li>
                              <li><strong>Análise de Humor:</strong> Teste a detecção de emoções em tempo real</li>
                              <li><strong>Respostas da IA:</strong> Experimente diferentes tipos de mensagens</li>
                              <li><strong>Integração CRM:</strong> Veja como as informações aparecem no sistema</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>

            <TabsContent value="general" className="p-6 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Store className="w-5 h-5 text-primary" />
                    Informações do Estabelecimento
                  </CardTitle>
                  <CardDescription>Configure as informações básicas do seu negócio</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="establishment-name">Nome do Estabelecimento</Label>
                    <Input
                      id="establishment-name"
                      value={settings.establishmentName}
                      onChange={(e) => setSettings({...settings, establishmentName: e.target.value})}
                      placeholder="Ex: Salão Bella Vita"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="services">Serviços Oferecidos (um por linha)</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          try {
                            const response = await fetch('/api/update-settings', {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              }
                            });
                            
                            const result = await response.json();
                            
                            if (response.ok && result.success) {
                              const settingsResponse = await fetch('/api/settings');
                              if (settingsResponse.ok) {
                                const updatedSettings = await settingsResponse.json();
                                setSettings(updatedSettings);
                              }
                              
                              alert(`✅ ${result.message}`);
                            } else {
                              alert(`❌ ${result.message || 'Erro ao atualizar configurações'}`);
                            }
                          } catch (error) {
                            alert('Erro ao conectar com a API: ' + (error as Error).message);
                          }
                        }}
                      >
                        <Sparkles className="w-4 h-4 mr-1" />
                        Sincronizar
                      </Button>
                    </div>
                    <Textarea
                      id="services"
                      value={settings.services.join('\n')}
                      onChange={(e) => setSettings({...settings, services: e.target.value.split('\n').filter(s => s.trim())})}
                      rows={6}
                      placeholder="Corte Feminino&#10;Coloração&#10;Manicure&#10;Pedicure"
                    />
                  </div>

                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="professionals">Profissionais (um por linha)</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={async () => {
                          try {
                            const response = await fetch('/api/update-settings', {
                              method: 'POST',
                              headers: {
                                'Content-Type': 'application/json'
                              }
                            });
                            
                            const result = await response.json();
                            
                            if (response.ok && result.success) {
                              const settingsResponse = await fetch('/api/settings');
                              if (settingsResponse.ok) {
                                const updatedSettings = await settingsResponse.json();
                                setSettings(updatedSettings);
                              }
                              
                              alert(`✅ ${result.message}`);
                            } else {
                              alert(`❌ ${result.message || 'Erro ao atualizar configurações'}`);
                            }
                          } catch (error) {
                            alert('Erro ao conectar com a API: ' + (error as Error).message);
                          }
                        }}
                      >
                        <Users className="w-4 h-4 mr-1" />
                        Sincronizar
                      </Button>
                    </div>
                    <Textarea
                      id="professionals"
                      value={settings.professionals.join('\n')}
                      onChange={(e) => setSettings({...settings, professionals: e.target.value.split('\n').filter(p => p.trim())})}
                      rows={4}
                      placeholder="Ana Silva&#10;Carlos Mendes&#10;Maria Santos"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="working-hours">Horário de Funcionamento</Label>
                    <Input
                      id="working-hours"
                      value={settings.workingHours}
                      onChange={(e) => setSettings({...settings, workingHours: e.target.value})}
                      placeholder="9:00 às 18:00, Segunda a Sábado"
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="api" className="p-6 space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="w-5 h-5 text-primary" />
                    Configurações de IA
                  </CardTitle>
                  <CardDescription>Configure as chaves de API para IA e transcrição</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Sobre a API:</strong> Para usar a IA real do Claude, você precisa configurar uma API key. 
                      Acesse <a href="https://console.anthropic.com" target="_blank" rel="noopener noreferrer" className="underline text-primary hover:text-primary/80">console.anthropic.com</a> 
                      para obter sua chave.
                    </AlertDescription>
                  </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Test Mode Status */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                      </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900">Status do Modo Teste</h4>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <span className="text-sm text-green-800 font-medium">Customer App</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Ativo</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <span className="text-sm text-green-800 font-medium">Análise de Humor</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Ativo</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <span className="text-sm text-green-800 font-medium">Simulação IA</span>
                      <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">Ativo</span>
                    </div>
                  </div>
                </div>

                {/* Test Actions */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z"/>
                      </svg>
                    </div>
                    <h4 className="font-semibold text-gray-900">Ações de Teste</h4>
                  </div>
                  <div className="space-y-3">
                    <button 
                      onClick={() => window.open('http://localhost:3002', '_blank')}
                      className="w-full text-left p-3 bg-purple-50 border border-purple-200 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors"
                    >
                      🚀 Abrir Customer App
                    </button>
                    <button 
                      onClick={() => window.open('http://localhost:3000/mood-demo.html', '_blank')}
                      className="w-full text-left p-3 bg-blue-50 border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors"
                    >
                      🎭 Demo Análise de Humor
                    </button>
                    <button
                      onClick={() => setShowConfirmDialog(true)}
                      className="w-full text-left p-3 bg-red-50 border border-red-200 text-red-700 rounded-lg hover:bg-red-100 transition-colors"
                    >
                      🧹 Limpar Dados de Teste
                    </button>
                  </div>
                </div>
              </div>

              {/* Instructions */}
              <div className="bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-lg p-6">
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                    <svg className="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div>
                    <h4 className="font-semibold text-orange-900 mb-2">Como usar o Modo Teste</h4>
                    <div className="text-sm text-orange-800 space-y-2">
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        <span><strong>Customer App:</strong> Simule conversas como se fosse um empreendedor real</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        <span><strong>Análise de Humor:</strong> Teste a detecção de emoções em tempo real</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        <span><strong>Responses da IA:</strong> Experimente diferentes tipos de mensagens</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-orange-500 rounded-full"></div>
                        <span><strong>CRM Integration:</strong> Veja como as informações aparecem no sistema</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'prompt' && (
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Como personalizar o prompt</h4>
                <p className="text-sm text-blue-800">
                  O prompt define como a IA se comporta. Você pode personalizar a personalidade, 
                  tom de voz e instruções específicas. Use as variáveis {'{'}establishmentName{'}'} e outras
                  para inserir informações dinâmicas.
                </p>
              </div>

              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-sm font-medium text-gray-700">
                    Prompt Personalizado da IA
                  </label>
                  <button
                    onClick={() => setSettings({...settings, customPrompt: defaultPrompt})}
                    className="text-sm text-trinks-600 hover:text-trinks-700"
                  >
                    Restaurar padrão
                  </button>
                </div>
                <textarea
                  value={settings.customPrompt || defaultPrompt}
                  onChange={(e) => setSettings({...settings, customPrompt: e.target.value})}
                  rows={15}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-trinks-500 font-mono text-sm"
                  placeholder="Digite o prompt personalizado da IA..."
                />
              </div>
            </div>
          )}

          {activeTab === 'api' && (
            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-medium text-yellow-900 mb-2">⚠️ Sobre a API Key</h4>
                <p className="text-sm text-yellow-800 mb-2">
                  <strong>Atualmente:</strong> O sistema está funcionando com respostas simuladas (mock). 
                  Para usar a IA real do Claude, você precisa configurar uma API key.
                </p>
                <p className="text-sm text-yellow-800">
                  <strong>Como obter:</strong> Acesse <a href="https://console.anthropic.com" target="_blank" rel="noopener noreferrer" className="underline">console.anthropic.com</a> 
                  e crie uma conta para obter sua API key.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Anthropic Claude API Key
                </label>
                <div className="flex space-x-2">
                  <input
                    type="password"
                    value={settings.apiKey}
                    onChange={(e) => setSettings({...settings, apiKey: e.target.value})}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-trinks-500"
                    placeholder="sk-ant-api03-..."
                  />
                  <button
                    onClick={() => {
                      const input = document.querySelector('input[type="password"]') as HTMLInputElement;
                      input.type = input.type === 'password' ? 'text' : 'password';
                    }}
                    className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                    title="Mostrar/Ocultar"
                  >
                    👁️
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  OpenAI API Key (para transcrição de áudio)
                </label>
                <div className="flex space-x-2">
                  <input
                    type="password"
                    value={settings.openaiApiKey}
                    onChange={(e) => setSettings({...settings, openaiApiKey: e.target.value})}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-trinks-500"
                    placeholder="sk-..."
                  />
                  <button
                    onClick={() => {
                      const inputs = document.querySelectorAll('input[type="password"]');
                      const input = inputs[1] as HTMLInputElement;
                      input.type = input.type === 'password' ? 'text' : 'password';
                    }}
                    className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
                    title="Mostrar/Ocultar"
                  >
                    👁️
                  </button>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Necessário para transcrição de mensagens de áudio usando Whisper
                </p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h4 className="font-medium text-green-900 mb-2">✅ Status Atual</h4>
                <div className="space-y-1">
                  <p className="text-sm text-green-800">
                    <strong>Claude:</strong> {settings.apiKey 
                      ? '🟢 Configurado - IA real será utilizada'
                      : '🟡 Não configurado - usando respostas simuladas'
                    }
                  </p>
                  <p className="text-sm text-green-800">
                    <strong>Whisper:</strong> {settings.openaiApiKey 
                      ? '🟢 Configurado - transcrição real será utilizada'
                      : '🟡 Não configurado - usando transcrição simulada'
                    }
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'trinks' && (
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">🔗 Integração com API Trinks</h4>
                <p className="text-sm text-blue-800 mb-2">
                  Configure a integração com a API oficial da Trinks para acessar dados reais de clientes, 
                  agendamentos e profissionais.
                </p>
                <p className="text-sm text-blue-800">
                  <strong>Documentação:</strong> <a href="https://api.trinks.com/swagger/index.html" target="_blank" rel="noopener noreferrer" className="underline">
                    api.trinks.com/swagger
                  </a>
                </p>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="trinksEnabled"
                  checked={settings.trinksEnabled}
                  onChange={(e) => setSettings({...settings, trinksEnabled: e.target.checked})}
                  className="h-4 w-4 text-trinks-600 focus:ring-trinks-500 border-gray-300 rounded"
                />
                <label htmlFor="trinksEnabled" className="text-sm font-medium text-gray-700">
                  🚀 Habilitar integração com API Trinks
                </label>
              </div>

              <div className={`space-y-4 ${!settings.trinksEnabled ? 'opacity-50' : ''}`}>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    URL da API Trinks
                  </label>
                  <input
                    type="url"
                    value={settings.trinksApiUrl}
                    onChange={(e) => setSettings({...settings, trinksApiUrl: e.target.value})}
                    disabled={!settings.trinksEnabled}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-trinks-500 disabled:bg-gray-100"
                    placeholder="https://api.trinks.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    API Key Trinks
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="password"
                      value={settings.trinksApiKey}
                      onChange={(e) => setSettings({...settings, trinksApiKey: e.target.value})}
                      disabled={!settings.trinksEnabled}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-trinks-500 disabled:bg-gray-100"
                      placeholder="Sua chave de API da Trinks..."
                    />
                    <button
                      onClick={() => {
                        const inputs = document.querySelectorAll('input[type="password"]');
                        const trinksInput = inputs[inputs.length - 1] as HTMLInputElement;
                        trinksInput.type = trinksInput.type === 'password' ? 'text' : 'password';
                      }}
                      disabled={!settings.trinksEnabled}
                      className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
                      title="Mostrar/Ocultar"
                    >
                      👁️
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    ID do Estabelecimento
                  </label>
                  <input
                    type="text"
                    value={settings.trinksEstabelecimentoId}
                    onChange={(e) => setSettings({...settings, trinksEstabelecimentoId: e.target.value})}
                    disabled={!settings.trinksEnabled}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-trinks-500 disabled:bg-gray-100"
                    placeholder="188253"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    ID do seu estabelecimento na plataforma Trinks
                  </p>
                </div>
              </div>

              <div className={`rounded-lg p-4 ${
                settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                  ? 'bg-green-50 border border-green-200' 
                  : settings.trinksEnabled 
                    ? 'bg-yellow-50 border border-yellow-200'
                    : 'bg-gray-50 border border-gray-200'
              }`}>
                <h4 className={`font-medium mb-2 ${
                  settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                    ? 'text-green-900' 
                    : settings.trinksEnabled 
                      ? 'text-yellow-900'
                      : 'text-gray-700'
                }`}>
                  {settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                    ? '✅ Status: Configurado' 
                    : settings.trinksEnabled 
                      ? '⚠️ Status: Configuração incompleta'
                      : '🔒 Status: Desabilitado'
                  }
                </h4>
                <p className={`text-sm ${
                  settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                    ? 'text-green-800' 
                    : settings.trinksEnabled 
                      ? 'text-yellow-800'
                      : 'text-gray-600'
                }`}>
                  {settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId
                    ? `API Trinks configurada para estabelecimento ${settings.trinksEstabelecimentoId}. Os dados reais de clientes serão utilizados.`
                    : settings.trinksEnabled 
                      ? 'Integração habilitada mas API Key e ID do estabelecimento são necessários para funcionar.'
                      : 'Usando dados simulados (mock). Habilite a integração para usar dados reais.'
                  }
                </p>
              </div>

              {/* Test API Connection */}
              {settings.trinksEnabled && settings.trinksApiKey && settings.trinksEstabelecimentoId && (
                <div className="border-t pt-4">
                  <button 
                    onClick={async () => {
                      try {
                        const response = await fetch('http://localhost:3001/api/trinks/test-connection', {
                          method: 'POST'
                        });
                        const result = await response.json();
                        
                        if (result.success) {
                          alert(`✅ ${result.message}`);
                        } else {
                          alert(`❌ ${result.message}`);
                        }
                      } catch (error) {
                        alert('❌ Erro ao testar conexão com a API');
                      }
                    }}
                    className="w-full py-2 px-4 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                  >
                    🔍 Testar Conexão com API
                  </button>
                </div>
              )}
            </div>
          )}

          {activeTab === 'logs' && (
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">📄 Sistema de Logs</h4>
                <p className="text-blue-700 text-sm">
                  Visualize e gerencie logs detalhados da aplicação do salão, incluindo eventos Socket.io, 
                  erros, avisos e informações de debug.
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <h5 className="font-medium mb-2">📊 Status do Logging</h5>
                  <div className="space-y-2 text-sm">
                    <div>
                      Estado: {(typeof window !== 'undefined' && localStorage.getItem('enableLogging') === 'true') ? 
                        <span className="text-green-600 font-medium">✅ Habilitado</span> : 
                        <span className="text-red-600 font-medium">❌ Desabilitado</span>
                      }
                    </div>
                    <div>
                      Logs em memória: <span className="font-mono">~{Logger?.getLogs?.().length || 0}</span>
                    </div>
                    <div>
                      Armazenamento: LocalStorage + Servidor
                    </div>
                  </div>
                </div>

                <div className="p-4 border rounded-lg">
                  <h5 className="font-medium mb-2">🎯 Tipos de Log</h5>
                  <div className="space-y-1 text-sm">
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-red-500 rounded-full"></span>
                      <span>ERROR - Erros críticos</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-yellow-500 rounded-full"></span>
                      <span>WARN - Avisos importantes</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      <span>INFO - Informações gerais</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                      <span>DEBUG - Debug detalhado</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                      <span>SOCKET - Eventos Socket.io</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <LogsPanel isOpen={false} onClose={() => {}} />
                <button
                  onClick={() => {
                    // This will be handled by a state to open the logs panel
                    const event = new CustomEvent('openLogsPanel');
                    window.dispatchEvent(event);
                  }}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 flex items-center gap-2"
                >
                  📄 Visualizar Logs Completos
                </button>
                
                <button
                  onClick={() => {
                    if (Logger?.downloadLogs) {
                      Logger.downloadLogs();
                    }
                  }}
                  className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 flex items-center gap-2"
                >
                  💾 Download Logs
                </button>

                <button
                  onClick={() => {
                    const isEnabled = typeof window !== 'undefined' && localStorage.getItem('enableLogging') === 'true';
                    if (Logger?.enableLogging) {
                      Logger.enableLogging(!isEnabled);
                      alert(`Logging ${!isEnabled ? 'habilitado' : 'desabilitado'}!`);
                      // Force re-render to update the status display
                      setActiveTab('logs');
                    }
                  }}
                  className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 flex items-center gap-2"
                >
                  🔧 Toggle Logging
                </button>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h5 className="font-medium mb-2">💡 Dicas de Uso</h5>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>• Logs são salvos automaticamente no localStorage e enviados ao servidor</li>
                  <li>• Use filtros para encontrar tipos específicos de eventos</li>
                  <li>• Download gera arquivo .log com timestamp para análise offline</li>
                  <li>• Logs Socket.io mostram comunicação em tempo real com o servidor</li>
                  <li>• Logging pode ser habilitado/desabilitado conforme necessário</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex justify-end space-x-3">
          <button 
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancelar
          </button>
          <button 
            onClick={handleSave}
            disabled={isSaving}
            className="px-4 py-2 bg-trinks-500 text-white rounded-md hover:bg-trinks-600 disabled:opacity-50"
          >
            {isSaving ? 'Salvando...' : 'Salvar Configurações'}
          </button>
        </div>
      </div>

      {/* WhatsApp Settings Modal */}
      {showWhatsAppSettings && (
        <WhatsAppSettingsModal onClose={() => setShowWhatsAppSettings(false)} />
      )}

      {/* Confirm Dialog */}
      <ConfirmDialog
        isOpen={showConfirmDialog}
        title="Limpar Dados de Teste"
        message="Tem certeza que deseja limpar todos os dados de teste? Esta ação não pode ser desfeita."
        confirmText="Sim, limpar"
        cancelText="Cancelar"
        onConfirm={handleClearTestData}
        onCancel={handleCancelClearData}
        variant="danger"
      />
    </div>
  );
};

export default SettingsPanel;