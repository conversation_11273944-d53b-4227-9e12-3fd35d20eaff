import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';
import socketService from '../../services/socket';
import { 
  MessageSquare,
  QrCode,
  Cloud,
  TestTube2,
  CheckCircle2,
  Phone,
  RefreshCw,
  Loader2,
  Power,
  PowerOff,
  Wifi,
  WifiOff,
  Link,
  AlertTriangle
} from 'lucide-react';

// Definição dos tipos de conexão
const connectionTypes = [
  {
    id: 'waha',
    label: 'WhatsApp Web',
    subtitle: 'QR Code • Recomendado',
    mode: 'whatsapp' as const,
    icon: QrCode,
    color: 'trinks', // Cor principal da <PERSON>nks
    description: 'Conecte via QR Code do WhatsApp Web'
  },
  {
    id: 'twilio',
    label: 'API Oficial',
    subtitle: 'Twilio • Empresarial',
    mode: 'whatsapp' as const,
    icon: Cloud,
    color: 'blue',
    description: 'Integração oficial via Twilio'
  },
  {
    id: 'customer-app',
    label: 'Simulador',
    subtitle: 'Customer App • Testes',
    mode: 'test' as const,
    icon: TestTube2,
    color: 'purple',
    description: 'Ambiente seguro para testes e treinamento'
  }
];

interface WAHAHealthStatus {
  isHealthy: boolean;
  version: string | null;
  message: string;
  lastChecked: string | null;
  isChecking: boolean;
}

interface WebhookStatus {
  lastCheck: number | null;
  expectedUrl: string;
  currentWebhooks: any[];
  isConfigured: boolean;
  isMatching: boolean;
  mode: 'webhook' | 'polling';
  details: {
    url?: string;
    events?: string[];
  };
}

interface WhatsAppStatus {
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  connectionType: 'twilio' | 'waha' | null;
  qrCode: string | null;
  phoneNumber: string | null;
  isLoadingQR?: boolean;
}

interface SettingsData {
  operationMode: 'whatsapp' | 'test';
  whatsappEnabled: boolean;
  whatsappType: 'twilio' | 'waha' | 'customer-app';
  twilioAccountSid: string;
  twilioAuthToken: string;
  twilioPhoneNumber: string;
  wahaApiUrl: string;
  wahaApiKey: string;
  wahaWebhookUrl?: string;
  wahaWebhookPaused?: boolean;
}

interface WhatsAppTabProps {
  settings: SettingsData;
  onSettingsChange: (settings: SettingsData) => void;
}

const WhatsAppTab: React.FC<WhatsAppTabProps> = ({ settings, onSettingsChange }) => {
  const [selectedType, setSelectedType] = useState<string>(settings.whatsappType);
  const [wahaHealth, setWahaHealth] = useState<WAHAHealthStatus>({
    isHealthy: false,
    version: null,
    message: 'Não verificado',
    lastChecked: null,
    isChecking: false
  });
  const [webhookStatus, setWebhookStatus] = useState<WebhookStatus | null>(null);
  const [isCheckingWebhook, setIsCheckingWebhook] = useState(false);
  const [isReconfiguringWebhook, setIsReconfiguringWebhook] = useState(false);
  const [whatsappStatus, setWhatsappStatus] = useState<WhatsAppStatus>({
    isConnected: false,
    connectionStatus: 'disconnected',
    connectionType: null,
    qrCode: null,
    phoneNumber: null,
    isLoadingQR: false
  });
  const [isConnecting, setIsConnecting] = useState(false);

  const handleTypeSelection = (typeId: string) => {
    const connectionType = connectionTypes.find(t => t.id === typeId);
    if (!connectionType) return;

    setSelectedType(typeId);
    
    // Atualiza as configurações baseado na seleção
    const newSettings = {
      ...settings,
      whatsappType: typeId as 'twilio' | 'waha' | 'customer-app',
      operationMode: connectionType.mode,
      whatsappEnabled: connectionType.mode === 'whatsapp'
    };
    
    onSettingsChange(newSettings);
    
    // Check WAHA health when switching to WAHA
    if (typeId === 'waha') {
      checkWahaHealth();
      checkWebhookStatus();
    }
  };

  const getColorClasses = (color: string, isSelected: boolean) => {
    const colorMap = {
      trinks: {
        bg: isSelected ? 'bg-[#EC5702]' : 'bg-gray-100',
        text: isSelected ? 'text-white' : 'text-gray-600',
        ring: 'ring-[#EC5702]',
        gradient: 'from-orange-50 to-orange-100',
        badge: 'bg-[#EC5702] text-white'
      },
      blue: {
        bg: isSelected ? 'bg-blue-500' : 'bg-gray-100',
        text: isSelected ? 'text-white' : 'text-gray-600',
        ring: 'ring-blue-500',
        gradient: 'from-blue-50 to-blue-100',
        badge: 'bg-blue-500 text-white'
      },
      purple: {
        bg: isSelected ? 'bg-purple-500' : 'bg-gray-100',
        text: isSelected ? 'text-white' : 'text-gray-600',
        ring: 'ring-purple-500',
        gradient: 'from-purple-50 to-purple-100',
        badge: 'bg-purple-500 text-white'
      }
    };
    
    return colorMap[color as keyof typeof colorMap] || colorMap.trinks;
  };

  const checkWahaHealth = async () => {
    if (selectedType !== 'waha') return;
    
    try {
      setWahaHealth(prev => ({ ...prev, isChecking: true }));
      
      const response = await fetch('/api/whatsapp/waha-health');
      if (response.ok) {
        const data = await response.json();
        setWahaHealth({
          isHealthy: data.isHealthy,
          version: data.version,
          message: data.message,
          lastChecked: data.lastChecked,
          isChecking: false
        });
      } else {
        throw new Error('Falha ao verificar saúde do WAHA');
      }
    } catch (error) {
      console.error('Error checking WAHA health:', error);
      setWahaHealth({
        isHealthy: false,
        version: null,
        message: '❌ Erro ao verificar conectividade',
        lastChecked: new Date().toISOString(),
        isChecking: false
      });
    }
  };

  const checkWebhookStatus = async () => {
    if (selectedType !== 'waha') return;
    
    setIsCheckingWebhook(true);
    
    try {
      const response = await fetch('/api/whatsapp/webhook-status');
      const result = await response.json();
      
      if (result.success) {
        setWebhookStatus(result.status);
      } else {
        console.error('Failed to get webhook status:', result.error);
        setWebhookStatus(null);
      }
    } catch (error) {
      console.error('Error checking webhook status:', error);
      setWebhookStatus(null);
    } finally {
      setIsCheckingWebhook(false);
    }
  };

  const applyWebhookConfiguration = async () => {
    if (selectedType !== 'waha') return;
    
    setIsReconfiguringWebhook(true);
    
    try {
      const response = await fetch('/api/whatsapp/reconfigure-webhook', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      const result = await response.json();
      
      if (result.success) {
        // Update webhook status with the new status
        setWebhookStatus(result.status);
        // Also refresh the status to be sure
        await checkWebhookStatus();
      } else {
        console.error('Failed to apply webhook configuration:', result.error);
      }
    } catch (error) {
      console.error('Error applying webhook configuration:', error);
    } finally {
      setIsReconfiguringWebhook(false);
    }
  };

  const toggleWebhookPause = async () => {
    if (selectedType !== 'waha') return;
    
    const newPausedState = !settings.wahaWebhookPaused;
    
    // Update local state immediately for better UX
    onSettingsChange({
      ...settings,
      wahaWebhookPaused: newPausedState
    });
    
    try {
      const response = await fetch('/api/whatsapp/webhook-pause', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          paused: newPausedState
        })
      });
      
      const result = await response.json();
      
      if (result.success) {
        // Update webhook status if provided
        if (result.status) {
          setWebhookStatus(result.status);
        }
        console.log(`Webhook ${newPausedState ? 'paused' : 'resumed'} successfully`);
      } else {
        // Revert state on failure
        onSettingsChange({
          ...settings,
          wahaWebhookPaused: !newPausedState
        });
        console.error('Failed to toggle webhook pause:', result.error);
      }
    } catch (error) {
      // Revert state on error
      onSettingsChange({
        ...settings,
        wahaWebhookPaused: !newPausedState
      });
      console.error('Error toggling webhook pause:', error);
    }
  };

  // Check WAHA health on component mount and when WAHA settings change
  useEffect(() => {
    if (selectedType === 'waha') {
      checkWahaHealth();
      checkWebhookStatus();
    }
  }, [selectedType, settings.wahaApiUrl, settings.wahaApiKey]);

  // Remove the automatic webhook configuration - now handled by pause/resume button
  // useEffect(() => {
  //   if (selectedType === 'waha' && settings.wahaWebhookUrl !== undefined) {
  //     const timer = setTimeout(() => {
  //       applyWebhookConfiguration();
  //     }, 2000);
  //     return () => clearTimeout(timer);
  //   }
  // }, [settings.wahaWebhookUrl, selectedType]);

  // Setup WhatsApp status monitoring
  useEffect(() => {
    // Check initial status
    checkWhatsAppStatus();
    
    // Setup socket listeners
    if (!socketService.isConnected) {
      socketService.connect();
    }
    
    const handleQRCode = (data: { qr: string }) => {
      console.log('📱 QR Code received in WhatsApp Tab:', data);
      setWhatsappStatus(prev => ({
        ...prev,
        qrCode: data.qr,
        connectionStatus: 'connecting',
        isLoadingQR: false
      }));
    };
    
    const handleStatusUpdate = (data: { status: string; type?: string; number?: string; message?: string }) => {
      console.log('📊 WhatsApp status update in WhatsApp Tab:', data);
      
      if (data.status === 'connected') {
        setWhatsappStatus({
          isConnected: true,
          connectionStatus: 'connected',
          connectionType: data.type as 'twilio' | 'waha' || 'waha',
          phoneNumber: data.number || null,
          qrCode: null,
          isLoadingQR: false
        });
      } else if (data.status === 'disconnected') {
        setWhatsappStatus({
          isConnected: false,
          connectionStatus: 'disconnected',
          connectionType: null,
          phoneNumber: null,
          qrCode: null,
          isLoadingQR: false
        });
      } else if (data.status === 'error') {
        setWhatsappStatus(prev => ({
          ...prev,
          isConnected: false,
          connectionStatus: 'error',
          isLoadingQR: false,
          qrCode: null
        }));
      }
    };
    
    socketService.onWhatsAppQR(handleQRCode);
    socketService.onWhatsAppStatus(handleStatusUpdate);
    
    return () => {
      // Cleanup listeners if needed
    };
  }, []);

  const checkWhatsAppStatus = async () => {
    try {
      const response = await fetch('/api/whatsapp/status');
      if (response.ok) {
        const data = await response.json();
        setWhatsappStatus(data.status || {
          isConnected: false,
          connectionStatus: 'disconnected',
          connectionType: null,
          qrCode: null,
          phoneNumber: null
        });
      }
    } catch (error) {
      console.error('Error checking WhatsApp status:', error);
    }
  };

  const connectWhatsApp = async () => {
    try {
      setIsConnecting(true);
      
      if (selectedType === 'waha') {
        setWhatsappStatus(prev => ({ ...prev, isLoadingQR: true }));
      }
      
      const response = await fetch('/api/whatsapp/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: selectedType,
          settings: settings
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setWhatsappStatus(data.status);
        } else {
          throw new Error(data.error || 'Erro ao conectar');
        }
      } else {
        throw new Error('Falha na conexão');
      }
    } catch (error) {
      console.error('Error connecting WhatsApp:', error);
      setWhatsappStatus(prev => ({ ...prev, isLoadingQR: false }));
    } finally {
      setIsConnecting(false);
    }
  };

  const disconnectWhatsApp = async () => {
    try {
      setIsConnecting(true);
      
      const response = await fetch('/api/whatsapp/disconnect', {
        method: 'POST',
      });

      if (response.ok) {
        setWhatsappStatus({
          isConnected: false,
          connectionStatus: 'disconnected',
          connectionType: null,
          qrCode: null,
          phoneNumber: null,
          isLoadingQR: false
        });
      } else {
        throw new Error('Erro ao desconectar');
      }
    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  const getConnectionStatusColor = () => {
    switch (whatsappStatus.connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getConnectionStatusText = () => {
    switch (whatsappStatus.connectionStatus) {
      case 'connected': return 'Conectado';
      case 'connecting': return 'Conectando...';
      case 'error': return 'Erro';
      default: return 'Desconectado';
    }
  };

  return (
    <div className="space-y-8">
      {/* Header com Branding Trinks */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-4">
          <div className="w-14 h-14 rounded-full bg-gradient-to-br from-[#EC5702] to-[#d83900] flex items-center justify-center shadow-lg">
            <MessageSquare className="w-7 h-7 text-white" />
          </div>
          <div className="text-left">
            <h2 className="text-2xl font-bold text-gray-900 leading-tight">
              Conecte seu WhatsApp e dê um up no atendimento! 🚀
            </h2>
            <p className="text-gray-600 text-base">
              A Trinks transforma seu atendimento e dá aquele up nos seus resultados
            </p>
          </div>
        </div>
      </div>

      {/* Como conectar seu WhatsApp */}
      <div className="space-y-6">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-gray-900 mb-2">
            Como conectar seu WhatsApp
          </h3>
          <p className="text-gray-600">
            Escolha sua opção e dê aquele up no seu negócio!
          </p>
        </div>

        {/* Cards de Seleção */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {connectionTypes.map((type) => {
            const isSelected = selectedType === type.id;
            const colors = getColorClasses(type.color, isSelected);
            const Icon = type.icon;

            return (
              <Card 
                key={type.id}
                className={cn(
                  "cursor-pointer transition-all duration-300 border-2",
                  "hover:shadow-lg hover:-translate-y-1",
                  isSelected 
                    ? `ring-2 ${colors.ring} bg-gradient-to-br ${colors.gradient} border-transparent shadow-md`
                    : "border-gray-200 hover:border-gray-300"
                )}
                onClick={() => handleTypeSelection(type.id)}
              >
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center space-y-4">
                    {/* Ícone */}
                    <div className={cn(
                      "w-16 h-16 rounded-xl flex items-center justify-center transition-colors",
                      colors.bg
                    )}>
                      <Icon className={cn("w-8 h-8", colors.text)} />
                    </div>

                    {/* Conteúdo */}
                    <div className="space-y-2">
                      <h4 className={cn(
                        "text-lg font-semibold",
                        isSelected ? "text-gray-900" : "text-gray-700"
                      )}>
                        {type.label}
                      </h4>
                      <p className={cn(
                        "text-sm",
                        isSelected ? "text-gray-700" : "text-gray-500"
                      )}>
                        {type.subtitle}
                      </p>
                    </div>

                    {/* Badge de selecionado */}
                    {isSelected && (
                      <Badge className={colors.badge}>
                        <CheckCircle2 className="w-3 h-3 mr-1" />
                        Selecionado
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Configurações específicas */}
      {selectedType && (
        <div className="animate-in slide-in-from-bottom-4 duration-500">
          {/* Configurações Twilio */}
          {selectedType === 'twilio' && (
            <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-blue-900">
                  <Cloud className="w-5 h-5" />
                  Configurações Twilio
                </CardTitle>
                <CardDescription className="text-blue-700">
                  Configure suas credenciais da API oficial do WhatsApp Business
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="twilio-sid" className="text-blue-900 font-medium">
                      Account SID
                    </Label>
                    <Input
                      id="twilio-sid"
                      value={settings.twilioAccountSid}
                      onChange={(e) => onSettingsChange({
                        ...settings,
                        twilioAccountSid: e.target.value
                      })}
                      placeholder="AC..."
                      className="border-blue-300 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="twilio-token" className="text-blue-900 font-medium">
                      Auth Token
                    </Label>
                    <Input
                      id="twilio-token"
                      type="password"
                      value={settings.twilioAuthToken}
                      onChange={(e) => onSettingsChange({
                        ...settings,
                        twilioAuthToken: e.target.value
                      })}
                      placeholder="Token de autenticação"
                      className="border-blue-300 focus:ring-blue-500"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="twilio-phone" className="text-blue-900 font-medium">
                    Número do WhatsApp Business
                  </Label>
                  <div className="flex">
                    <div className="flex items-center px-3 bg-blue-100 border border-r-0 border-blue-300 rounded-l-md">
                      <Phone className="w-4 h-4 text-blue-600" />
                    </div>
                    <Input
                      id="twilio-phone"
                      value={settings.twilioPhoneNumber}
                      onChange={(e) => onSettingsChange({
                        ...settings,
                        twilioPhoneNumber: e.target.value
                      })}
                      placeholder="+5511999999999"
                      className="rounded-l-none border-blue-300 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Configurações WAHA */}
          {selectedType === 'waha' && (
            <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-orange-900">
                  <QrCode className="w-5 h-5" />
                  Configurações WhatsApp Web
                </CardTitle>
                <CardDescription className="text-orange-700">
                  Configure a URL da API WAHA para conectar via QR Code
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="waha-url" className="text-orange-900 font-medium">
                    URL da API WAHA
                  </Label>
                  <Input
                    id="waha-url"
                    value={settings.wahaApiUrl}
                    onChange={(e) => onSettingsChange({
                      ...settings,
                      wahaApiUrl: e.target.value
                    })}
                    placeholder="http://localhost:8090"
                    className="border-orange-300 focus:ring-[#EC5702]"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="waha-key" className="text-orange-900 font-medium">
                    API Key (Opcional)
                  </Label>
                  <Input
                    id="waha-key"
                    type="password"
                    value={settings.wahaApiKey}
                    onChange={(e) => onSettingsChange({
                      ...settings,
                      wahaApiKey: e.target.value
                    })}
                    placeholder="Deixe em branco para WAHA Core"
                    className="border-orange-300 focus:ring-[#EC5702]"
                  />
                  <p className="text-sm text-orange-700">
                    Necessário apenas para versões Pro/Plus da WAHA
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="waha-webhook" className="text-orange-900 font-medium">
                    Webhook URL (Opcional)
                  </Label>
                  <Input
                    id="waha-webhook"
                    value={settings.wahaWebhookUrl || ''}
                    onChange={(e) => onSettingsChange({
                      ...settings,
                      wahaWebhookUrl: e.target.value
                    })}
                    placeholder="http://seu-servidor.com/webhook/waha"
                    className="border-orange-300 focus:ring-[#EC5702]"
                  />
                  <p className="text-sm text-orange-700">
                    Se vazio, usará polling. Configure apenas se o webhook for acessível externamente.
                  </p>
                  
                  {/* Webhook Status and Control */}
                  <div className="mt-2 space-y-2">
                    {/* Pause/Resume Button */}
                    {settings.wahaWebhookUrl && (
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant={settings.wahaWebhookPaused ? "default" : "outline"}
                          onClick={toggleWebhookPause}
                          className={cn(
                            settings.wahaWebhookPaused ? 
                              "bg-green-500 hover:bg-green-600 text-white" :
                              "text-orange-700 border-orange-300 hover:bg-orange-50"
                          )}
                        >
                          {settings.wahaWebhookPaused ? (
                            <>
                              <Power className="w-3 h-3 mr-1" />
                              Reativar Webhook
                            </>
                          ) : (
                            <>
                              <PowerOff className="w-3 h-3 mr-1" />
                              Pausar Webhook
                            </>
                          )}
                        </Button>
                        
                        <p className="text-xs text-orange-600">
                          {settings.wahaWebhookPaused ? 
                            "Webhook pausado - usando polling" :
                            "Webhook ativo - recepção em tempo real"
                          }
                        </p>
                      </div>
                    )}
                    
                    {/* Status Indicator */}
                    {webhookStatus && (
                      <div className={cn(
                        "p-2 rounded-md text-xs",
                        settings.wahaWebhookPaused ? 
                          "bg-blue-50 text-blue-700 border border-blue-200" :
                          webhookStatus.mode === 'webhook' ? 
                            webhookStatus.isMatching ? 
                              "bg-green-50 text-green-700 border border-green-200" :
                              "bg-yellow-50 text-yellow-700 border border-yellow-200"
                            : "bg-blue-50 text-blue-700 border border-blue-200"
                      )}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {settings.wahaWebhookPaused ? (
                              <>
                                <WifiOff className="w-3 h-3" />
                                <span>⏸️ Webhook pausado - usando polling</span>
                              </>
                            ) : webhookStatus.mode === 'webhook' ? (
                              webhookStatus.isMatching ? (
                                <>
                                  <CheckCircle2 className="w-3 h-3" />
                                  <span>✅ Webhook ativo: {webhookStatus.details.url}</span>
                                </>
                              ) : (
                                <>
                                  <AlertTriangle className="w-3 h-3" />
                                  <span>⚠️ Webhook diferente configurado: {webhookStatus.details.url}</span>
                                </>
                              )
                            ) : (
                              <>
                                <Wifi className="w-3 h-3" />
                                <span>📡 Usando polling (sem webhook configurado)</span>
                              </>
                            )}
                            {(isCheckingWebhook || isReconfiguringWebhook) && <Loader2 className="w-3 h-3 animate-spin ml-1" />}
                          </div>
                          
                          {/* Status Indicator */}
                          {(isCheckingWebhook || isReconfiguringWebhook) && (
                            <Loader2 className="w-4 h-4 animate-spin text-blue-500" />
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {/* WAHA Health Status */}
                <div className="mt-6 p-4 bg-white rounded-lg border border-orange-200">
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-orange-900 flex items-center gap-2">
                      <Cloud className="w-4 h-4" />
                      Status da API WAHA
                    </h4>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={checkWahaHealth}
                      disabled={wahaHealth.isChecking}
                      className="text-orange-700 border-orange-300 hover:bg-orange-50"
                    >
                      {wahaHealth.isChecking ? (
                        <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                      ) : (
                        <RefreshCw className="w-4 h-4 mr-1" />
                      )}
                      {wahaHealth.isChecking ? 'Verificando...' : 'Verificar'}
                    </Button>
                  </div>
                  
                  <div className="flex items-start gap-3">
                    <div className={`w-3 h-3 rounded-full mt-1 ${
                      wahaHealth.isHealthy ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    <div className="flex-1 space-y-1">
                      <p className={`text-sm font-medium ${
                        wahaHealth.isHealthy ? 'text-green-700' : 'text-red-700'
                      }`}>
                        {wahaHealth.message}
                      </p>
                      {wahaHealth.version && (
                        <p className="text-xs text-gray-600">
                          <strong>Versão:</strong> {wahaHealth.version}
                        </p>
                      )}
                      {wahaHealth.lastChecked && (
                        <p className="text-xs text-gray-500">
                          <strong>Última verificação:</strong> {new Date(wahaHealth.lastChecked).toLocaleString('pt-BR')}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {!wahaHealth.isHealthy && (
                    <div className="mt-3 p-3 bg-red-50 rounded-lg border border-red-200">
                      <p className="text-xs text-red-700">
                        <strong>Problema de conectividade:</strong> Verifique se a URL está correta e se o WAHA está rodando em: <code className="bg-red-100 px-1 rounded">{settings.wahaApiUrl}</code>
                      </p>
                    </div>
                  )}
                </div>
                
                {/* WhatsApp Connection Status */}
                <div className="mt-6 p-4 bg-white rounded-lg border border-orange-200">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium text-orange-900 flex items-center gap-2">
                      {whatsappStatus.isConnected ? (
                        <Wifi className="w-4 h-4 text-green-600" />
                      ) : (
                        <WifiOff className="w-4 h-4 text-gray-600" />
                      )}
                      Status da Conexão WhatsApp
                    </h4>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${
                        whatsappStatus.connectionStatus === 'connected' ? 'bg-green-500' :
                        whatsappStatus.connectionStatus === 'connecting' ? 'bg-yellow-500' :
                        whatsappStatus.connectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-400'
                      }`} />
                      <span className={`text-sm font-medium ${getConnectionStatusColor()}`}>
                        {getConnectionStatusText()}
                        {whatsappStatus.phoneNumber && ` - ${whatsappStatus.phoneNumber}`}
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {whatsappStatus.isConnected ? (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={disconnectWhatsApp}
                        disabled={isConnecting}
                        className="text-red-700 border-red-300 hover:bg-red-50"
                      >
                        {isConnecting ? (
                          <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                        ) : (
                          <PowerOff className="w-4 h-4 mr-1" />
                        )}
                        Desconectar
                      </Button>
                    ) : (
                      <Button
                        size="sm"
                        onClick={connectWhatsApp}
                        disabled={isConnecting || !wahaHealth.isHealthy}
                        className="bg-[#EC5702] hover:bg-[#d83900] text-white"
                      >
                        {isConnecting ? (
                          <Loader2 className="w-4 h-4 mr-1 animate-spin" />
                        ) : (
                          <Power className="w-4 h-4 mr-1" />
                        )}
                        {isConnecting ? 'Conectando...' : 'Conectar WhatsApp'}
                      </Button>
                    )}
                    
                    {!wahaHealth.isHealthy && (
                      <p className="text-xs text-orange-700">
                        Verifique a conectividade do WAHA primeiro
                      </p>
                    )}
                  </div>
                  
                  {/* QR Code Display */}
                  {(whatsappStatus.qrCode || whatsappStatus.isLoadingQR) && selectedType === 'waha' && (
                    <div className="mt-4 mb-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="text-center space-y-3">
                        <h5 className="font-medium text-blue-900 flex items-center justify-center gap-2">
                          <QrCode className="w-4 h-4" />
                          {whatsappStatus.isLoadingQR ? 'Gerando QR Code...' : 'Escaneie o QR Code'}
                        </h5>
                        
                        {whatsappStatus.isLoadingQR ? (
                          <div className="flex flex-col items-center space-y-3">
                            <Loader2 className="animate-spin text-blue-500 w-8 h-8" />
                            <p className="text-sm text-blue-600">Preparando conexão...</p>
                          </div>
                        ) : whatsappStatus.qrCode ? (
                          <div className="flex flex-col items-center space-y-3">
                            <img
                              src={`data:image/png;base64,${whatsappStatus.qrCode}`}
                              alt="QR Code WhatsApp"
                              className="w-48 h-48 border border-gray-300 rounded-lg shadow-sm"
                              onError={(e) => {
                                console.error('❌ Erro ao carregar QR Code:', e);
                              }}
                              onLoad={() => {
                                console.log('✅ QR Code carregado com sucesso!');
                              }}
                            />
                            <div className="text-center space-y-1">
                              <p className="text-sm text-blue-700 font-medium">
                                Abra o WhatsApp no seu celular
                              </p>
                              <p className="text-xs text-blue-600">
                                Vá em Dispositivos Conectados &gt; Conectar dispositivo
                              </p>
                              <p className="text-xs text-blue-500">
                                QR Code válido por 90 segundos
                              </p>
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Configurações Customer App */}
          {selectedType === 'customer-app' && (
            <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-purple-100">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-purple-900">
                  <TestTube2 className="w-5 h-5" />
                  Modo Simulador Ativo
                </CardTitle>
                <CardDescription className="text-purple-700">
                  Ambiente seguro para testes e treinamento
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center p-6 bg-purple-100 rounded-lg border border-purple-200">
                  <div className="text-center space-y-3">
                    <TestTube2 className="w-8 h-8 text-purple-600 mx-auto" />
                    <div>
                      <p className="text-purple-900 font-medium">
                        Modo Teste Ativo!
                      </p>
                      <p className="text-sm text-purple-700 mt-1">
                        Use o Customer App para simular conversas e treinar. 
                        Quando estiver confiante, escolha uma opção de WhatsApp real!
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
};

export default WhatsAppTab;