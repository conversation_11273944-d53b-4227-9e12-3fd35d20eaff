import React from 'react';
import { Conversation } from '../../types/index';

interface ConversationListProps {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  onSelectConversation: (conversation: Conversation) => void;
}

const ConversationList: React.FC<ConversationListProps> = ({
  conversations,
  activeConversation,
  onSelectConversation
}) => {
  const formatTime = (date: Date) => {
    const now = new Date();
    const messageDate = new Date(date);
    const diffInHours = (now.getTime() - messageDate.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return messageDate.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 168) { // 7 days
      return messageDate.toLocaleDateString('pt-BR', { weekday: 'short' });
    } else {
      return messageDate.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit'
      });
    }
  };

  const getLastMessagePreview = (conversation: Conversation) => {
    if (conversation.messages.length === 0) {
      return 'Conversa iniciada';
    }
    
    const lastMessage = conversation.messages[conversation.messages.length - 1];
    const preview = lastMessage.content.length > 45 
      ? lastMessage.content.substring(0, 45) + '...'
      : lastMessage.content;
    
    return lastMessage.sender === 'ai' ? `🤖 ${preview}` : preview;
  };

  const getConversationStatus = (conversation: Conversation) => {
    if (conversation.unreadCount > 0) return 'active';
    
    // Verificar se é realmente um cliente novo
    // Se tem customerName e não é apenas o telefone, provavelmente tem dados no sistema
    const hasCustomerData = conversation.customerName && 
      conversation.customerName !== conversation.customerPhone &&
      !conversation.customerName.startsWith('+') &&
      conversation.customerName !== 'Cliente desconhecido';
    
    // Se não tem mensagens MAS tem dados do cliente, não é "new"
    if (conversation.messages.length === 0) {
      return hasCustomerData ? 'recent' : 'new';
    }
    
    const lastMessageTime = new Date(conversation.lastMessage).getTime();
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    return lastMessageTime > oneHourAgo ? 'recent' : 'idle';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-trinks-500';
      case 'new': return 'bg-trinks-green-500';
      case 'recent': return 'bg-green-400';
      default: return 'bg-gray-300';
    }
  };

  const getStatusText = (status: string, conversation: Conversation) => {
    switch (status) {
      case 'active': return 'Nova mensagem';
      case 'new': return 'Cliente novo';
      case 'recent': {
        // Se tem dados do cliente mas não tem mensagens, está aguardando
        if (conversation.messages.length === 0) {
          const hasCustomerData = conversation.customerName && 
            conversation.customerName !== conversation.customerPhone &&
            !conversation.customerName.startsWith('+') &&
            conversation.customerName !== 'Cliente desconhecido';
          return hasCustomerData ? 'Cliente conhecido' : 'Ativo agora';
        }
        return 'Ativo agora';
      }
      default: return 'Aguardando';
    }
  };

  return (
    <div className="space-y-1 p-2">
      {conversations.map((conversation) => {
        const status = getConversationStatus(conversation);
        return (
          <button
            key={conversation.id}
            onClick={() => onSelectConversation(conversation)}
            className={`w-full text-left p-3 rounded-xl transition-all duration-200 group ${
              activeConversation?.id === conversation.id 
                ? 'bg-gradient-to-r from-trinks-50 to-orange-50 border-2 border-trinks-200 shadow-md' 
                : 'hover:bg-gray-50 hover:shadow-sm border-2 border-transparent'
            }`}
          >
            <div className="flex gap-3">
              {/* Avatar com status */}
              <div className="relative">
                <div className={`w-11 h-11 rounded-xl flex items-center justify-center text-white font-semibold shadow-sm ${
                  activeConversation?.id === conversation.id 
                    ? 'bg-gradient-to-br from-trinks-500 to-trinks-600' 
                    : 'bg-gradient-to-br from-gray-400 to-gray-500 group-hover:from-trinks-400 group-hover:to-trinks-500'
                } transition-all duration-200`}>
                  {(conversation.customerName || conversation.customerPhone || 'C').charAt(0).toUpperCase()}
                </div>
                
                {/* Indicador de status */}
                <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                  getStatusColor(status)
                } ${status === 'active' ? 'animate-pulse' : ''}`} />
              </div>

              {/* Informações da conversa */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className={`text-sm font-semibold truncate ${
                    activeConversation?.id === conversation.id 
                      ? 'text-trinks-900' 
                      : conversation.unreadCount > 0 
                        ? 'text-gray-900' 
                        : 'text-gray-700'
                  }`}>
                    {conversation.customerName && conversation.customerName !== conversation.customerPhone 
                      ? conversation.customerName 
                      : conversation.customerPhone || 'Cliente'}
                  </h3>
                  
                  <div className="flex items-center gap-1 ml-2">
                    {conversation.unreadCount > 0 && (
                      <span className="inline-flex items-center justify-center min-w-5 h-5 px-1.5 text-xs font-bold text-white bg-trinks-500 rounded-full">
                        {conversation.unreadCount > 99 ? '99+' : conversation.unreadCount}
                      </span>
                    )}
                    <span className="text-xs text-gray-500 flex-shrink-0">
                      {formatTime(conversation.lastMessage)}
                    </span>
                  </div>
                </div>

                <p className={`text-xs truncate mb-2 ${
                  conversation.unreadCount > 0 ? 'text-gray-700 font-medium' : 'text-gray-500'
                }`}>
                  {getLastMessagePreview(conversation)}
                </p>

                {/* Status da conversa */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    <div className={`w-1.5 h-1.5 rounded-full ${getStatusColor(status)}`} />
                    <span className="text-xs text-gray-500 font-medium">
                      {getStatusText(status, conversation)}
                    </span>
                  </div>
                  
                  {/* Tags ou informações extras */}
                  {status === 'new' && (
                    <div className="px-2 py-0.5 bg-trinks-green-100 text-trinks-green-700 rounded-md text-xs font-medium">
                      Novo
                    </div>
                  )}
                </div>
              </div>
            </div>
          </button>
        );
      })}
    </div>
  );
};

export default ConversationList;