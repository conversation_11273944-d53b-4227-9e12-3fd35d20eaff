import React, { useState } from 'react';

interface CustomerSearchProps {
  onSearchCustomer: (phone: string) => void;
}

const CustomerSearch: React.FC<CustomerSearchProps> = ({ onSearchCustomer }) => {
  const [phone, setPhone] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const formatPhone = (value: string) => {
    // Remove all non-numeric characters
    const numbers = value.replace(/\D/g, '');
    
    // Format as Brazilian phone number
    if (numbers.length <= 11) {
      if (numbers.length <= 2) {
        return numbers;
      } else if (numbers.length <= 7) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`;
      } else if (numbers.length <= 10) {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 6)}-${numbers.slice(6)}`;
      } else {
        return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 7)}-${numbers.slice(7, 11)}`;
      }
    }
    return value;
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhone(e.target.value);
    setPhone(formatted);
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phone.trim()) return;
    
    setIsSearching(true);
    
    // Extract numbers for API call
    const cleanPhone = phone.replace(/\D/g, '');
    const formattedForAPI = `+55${cleanPhone}`;
    
    try {
      onSearchCustomer(formattedForAPI);
      // Clear input after successful search
      setTimeout(() => {
        setPhone('');
        setIsSearching(false);
      }, 1000);
    } catch (error) {
      console.error('Error searching customer:', error);
      setIsSearching(false);
    }
  };

  return (
    <div className="space-y-3">
      <form onSubmit={handleSearch} className="space-y-2">
        <div className="relative">
          <input
            type="text"
            placeholder="(11) 99999-9999"
            value={phone}
            onChange={handlePhoneChange}
            maxLength={15}
            className="w-full pl-10 pr-4 py-2.5 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-trinks-500 focus:border-trinks-500 transition-all duration-200 text-sm bg-white"
          />
          <svg
            className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
            />
          </svg>
        </div>
        
        <button
          type="submit"
          disabled={!phone.trim() || isSearching}
          className={`w-full py-2.5 px-4 rounded-xl font-semibold transition-all duration-200 text-sm ${
            phone.trim() && !isSearching
              ? 'bg-gradient-to-r from-trinks-500 to-trinks-600 text-white hover:from-trinks-600 hover:to-trinks-700 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5'
              : 'bg-gray-100 text-gray-400 cursor-not-allowed border border-gray-200'
          }`}
        >
          {isSearching ? (
            <div className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Conectando...
            </div>
          ) : (
            <div className="flex items-center justify-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
              </svg>
              Conectar Cliente
            </div>
          )}
        </button>
      </form>
      
      <div className="border-t border-gray-100 pt-3">
        <div className="bg-gradient-to-r from-trinks-50 to-orange-50 border border-trinks-100 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <div className="w-5 h-5 bg-trinks-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <svg className="w-3 h-3 text-trinks-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="font-semibold text-trinks-900 text-sm mb-1">Como conectar</h4>
              <p className="text-xs text-trinks-700 leading-relaxed">
                Digite o telefone para buscar histórico ou aguarde mensagens chegarem automaticamente pelo WhatsApp.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerSearch;