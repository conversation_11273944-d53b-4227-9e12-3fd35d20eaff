import React, { useState } from 'react';
import { Conversation } from '../../types/index';
import ConversationList from './ConversationList';
import SettingsPanel from '../Settings/SettingsPanel';
import PromptManagement from '../PromptManagement/PromptManagement';

interface SidebarProps {
  conversations: Conversation[];
  activeConversation: Conversation | null;
  onSelectConversation: (conversation: Conversation) => void;
  onSearchCustomer: (phone: string) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  conversations,
  activeConversation,
  onSelectConversation,
  onSearchCustomer
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSettings, setShowSettings] = useState(false);
  const [showPrompts, setShowPrompts] = useState(false);
  const connectionStatus = 'connected'; // Can be made dynamic later with socket.io

  const filteredConversations = conversations.filter(conv =>
    (conv.customerName || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (conv.customerPhone || '').includes(searchQuery)
  );

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-gradient-to-r from-trinks-50 to-white border-b border-trinks-100 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100">
              <img 
                src="/trinks-simbolo.png" 
                alt="Trinks" 
                className="w-8 h-8 object-contain"
              />
            </div>
            <div className="ml-4">
              <h1 className="text-lg font-bold text-gray-900 tracking-tight">Trinks</h1>
              <p className="text-xs text-trinks-600 font-medium">Sua Parceira de Atendimento</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* Prompts Button */}
            <button 
              onClick={() => setShowPrompts(true)}
              className="p-2.5 hover:bg-white hover:shadow-md rounded-xl transition-all duration-200 group"
              title="Prompts da IA"
            >
              <svg className="w-5 h-5 text-gray-500 group-hover:text-trinks-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>

            {/* Settings Button */}
            <button 
              onClick={() => setShowSettings(true)}
              className="p-2.5 hover:bg-white hover:shadow-md rounded-xl transition-all duration-200 group"
              title="Configurações"
            >
              <svg className="w-5 h-5 text-gray-500 group-hover:text-trinks-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
            </button>
          </div>
        </div>
      </div>


      {/* Search Conversations */}
      <div className="px-4 py-3 bg-gray-50/50 border-b border-gray-100">
        <div className="mb-2">
          <h3 className="text-sm font-semibold text-gray-900">Todas as Conversas</h3>
          <p className="text-xs text-gray-500">Busque por empreendedor ou mensagem</p>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Buscar empreendedor ou conversa..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-trinks-500 focus:border-trinks-500 transition-all duration-200 text-sm placeholder:text-gray-400"
          />
          <svg
            className="absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto">
        {filteredConversations.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 px-6 text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-trinks-100 to-trinks-50 rounded-2xl flex items-center justify-center mb-4">
              <svg className="w-8 h-8 text-trinks-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
              </svg>
            </div>
            <h3 className="text-base font-semibold text-gray-900 mb-2">
              {conversations.length === 0 
                ? 'Pronto para transformar atendimentos?'
                : 'Nenhuma conversa encontrada'
              }
            </h3>
            <p className="text-sm text-gray-500 leading-relaxed max-w-xs">
              {conversations.length === 0 
                ? 'Seus empreendedores aparecerão aqui quando iniciarem uma conversa no WhatsApp'
                : 'Tente ajustar sua busca para encontrar a conversa'
              }
            </p>
            {conversations.length === 0 && (
              <div className="mt-6 p-3 bg-trinks-50 rounded-lg border border-trinks-100">
                <p className="text-xs text-trinks-700 font-medium">💡 Dica: Configure o WhatsApp nas configurações do sistema</p>
              </div>
            )}
          </div>
        ) : (
          <ConversationList
            conversations={filteredConversations}
            activeConversation={activeConversation}
            onSelectConversation={onSelectConversation}
          />
        )}
      </div>

      {/* Footer - Connection Status */}
      <div className="p-3 border-t border-gray-100 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-2 h-2 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500 animate-pulse' :
              connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`} />
            <span className="text-xs font-medium text-gray-700">
              {connectionStatus === 'connected' ? 'Conectado' :
               connectionStatus === 'connecting' ? 'Conectando...' :
               'Desconectado'}
            </span>
          </div>
          <div className="text-xs text-gray-500">
            {conversations.filter(c => c.status === 'active').length > 0 && (
              <span className="font-medium text-trinks-600">
                {conversations.filter(c => c.status === 'active').length} ativas
              </span>
            )}
          </div>
        </div>
        
        {/* Brand reinforcement */}
        <div className="mt-2 pt-2 border-t border-gray-50">
          <p className="text-xs text-center text-gray-400 font-medium">
            Transformando sonhos em negócios de sucesso ✨
          </p>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <SettingsPanel onClose={() => setShowSettings(false)} />
      )}

      {/* Prompt Management */}
      {showPrompts && (
        <PromptManagement onClose={() => setShowPrompts(false)} />
      )}
    </div>
  );
};

export default Sidebar;