import React, { useState } from 'react';

interface TrinksDebugInfo {
  apiUsed?: boolean;
  mockResponse?: boolean;
  endpoint?: string;
  method?: string;
  requestParams?: any;
  requestHeaders?: any;
  responseStatus?: number;
  responseHeaders?: any;
  responseTime?: number;
  found?: boolean;
  timestamp?: string;
  apiUrl?: string;
  error?: string;
  errorCode?: string;
  fallbackToMock?: boolean;
  realApiError?: any;
}

interface TrinksDebugPanelProps {
  debugInfo: TrinksDebugInfo;
  onClose: () => void;
}

const TrinksDebugPanel: React.FC<TrinksDebugPanelProps> = ({ debugInfo, onClose }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'request' | 'response' | 'error'>('overview');

  const formatJson = (obj: any) => {
    return JSON.stringify(obj, null, 2);
  };

  const formatTime = (ms: number) => {
    return `${ms}ms`;
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="border-b p-4 flex items-center justify-between bg-blue-50">
          <div className="flex items-center">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
              <span className="text-white text-sm font-bold">T</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-blue-900">Debug Trinks API</h3>
              <p className="text-sm text-blue-700">
                {debugInfo.apiUsed ? '🌐 API Real' : '🎭 Mock Data'} • 
                {debugInfo.found ? ' ✅ Encontrado' : ' ❌ Não encontrado'} • 
                {formatTime(debugInfo.responseTime || 0)}
              </p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8 px-4" aria-label="Tabs">
            {[
              { id: 'overview', name: 'Visão Geral', icon: '📊' },
              { id: 'request', name: 'Requisição', icon: '📤' },
              { id: 'response', name: 'Resposta', icon: '📥' },
              ...(debugInfo.error ? [{ id: 'error', name: 'Erro', icon: '❌' }] : [])
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {activeTab === 'overview' && (
            <div className="space-y-4">
              {/* Status Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className={`p-4 rounded-lg border-2 ${
                  debugInfo.apiUsed 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-yellow-50 border-yellow-200'
                }`}>
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">
                      {debugInfo.apiUsed ? '🌐' : '🎭'}
                    </span>
                    <div>
                      <div className="font-semibold text-sm">
                        {debugInfo.apiUsed ? 'API Real' : 'Mock Data'}
                      </div>
                      <div className="text-xs text-gray-600">
                        {debugInfo.apiUsed ? debugInfo.apiUrl : 'Dados simulados'}
                      </div>
                    </div>
                  </div>
                </div>

                <div className={`p-4 rounded-lg border-2 ${
                  debugInfo.found 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">
                      {debugInfo.found ? '✅' : '❌'}
                    </span>
                    <div>
                      <div className="font-semibold text-sm">
                        {debugInfo.found ? 'Cliente Encontrado' : 'Cliente Não Encontrado'}
                      </div>
                      <div className="text-xs text-gray-600">
                        Resultado da busca
                      </div>
                    </div>
                  </div>
                </div>

                <div className="p-4 rounded-lg border-2 bg-blue-50 border-blue-200">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">⏱️</span>
                    <div>
                      <div className="font-semibold text-sm">
                        {formatTime(debugInfo.responseTime || 0)}
                      </div>
                      <div className="text-xs text-gray-600">
                        Tempo de resposta
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Details */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Detalhes da Requisição</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex">
                    <span className="font-medium text-gray-700 w-24">Endpoint:</span>
                    <span className="text-gray-600">{debugInfo.endpoint}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium text-gray-700 w-24">Método:</span>
                    <span className="text-gray-600">{debugInfo.method}</span>
                  </div>
                  <div className="flex">
                    <span className="font-medium text-gray-700 w-24">Timestamp:</span>
                    <span className="text-gray-600">
                      {debugInfo.timestamp ? new Date(debugInfo.timestamp).toLocaleString('pt-BR') : 'N/A'}
                    </span>
                  </div>
                  {debugInfo.responseStatus && (
                    <div className="flex">
                      <span className="font-medium text-gray-700 w-24">Status:</span>
                      <span className={`text-sm px-2 py-1 rounded ${
                        debugInfo.responseStatus >= 200 && debugInfo.responseStatus < 300
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {debugInfo.responseStatus}
                      </span>
                    </div>
                  )}
                  {debugInfo.fallbackToMock && (
                    <div className="flex">
                      <span className="font-medium text-gray-700 w-24">Fallback:</span>
                      <span className="text-orange-600">Utilizou mock após erro na API</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'request' && (
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Parâmetros da Requisição</h4>
                <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-48">
                  {formatJson(debugInfo.requestParams)}
                </pre>
              </div>

              {debugInfo.requestHeaders && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Headers da Requisição</h4>
                  <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-48">
                    {formatJson(debugInfo.requestHeaders)}
                  </pre>
                </div>
              )}

              <div>
                <h4 className="font-medium text-gray-900 mb-2">URL Completa</h4>
                <code className="bg-blue-100 text-blue-800 p-3 rounded block text-sm break-all">
                  {debugInfo.apiUrl || 'Mock'}{debugInfo.endpoint}
                  {debugInfo.requestParams && Object.keys(debugInfo.requestParams).length > 0 
                    ? `?${new URLSearchParams(debugInfo.requestParams).toString()}`
                    : ''
                  }
                </code>
              </div>

              {debugInfo.endpoint?.includes('/v1/') && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                  <div className="flex items-start">
                    <span className="text-green-500 text-xl mr-2">🔗</span>
                    <div>
                      <h4 className="font-medium text-green-900 mb-1">API Trinks v1</h4>
                      <p className="text-green-800 text-sm">
                        {debugInfo.endpoint === '/v1/clientes' && 'Endpoint para buscar clientes por telefone e estabelecimento'}
                        {debugInfo.endpoint === '/v1/agendamentos' && 'Endpoint para buscar histórico de agendamentos do cliente'}
                      </p>
                      <p className="text-green-700 text-xs mt-1">
                        Estabelecimento ID: {debugInfo.requestParams?.id_estabelecimento || '188253'}
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'response' && (
            <div className="space-y-4">
              {debugInfo.responseHeaders && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Headers da Resposta</h4>
                  <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-auto max-h-48">
                    {formatJson(debugInfo.responseHeaders)}
                  </pre>
                </div>
              )}

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Status da Resposta</h4>
                <div className="flex items-center space-x-4">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                    debugInfo.responseStatus && debugInfo.responseStatus >= 200 && debugInfo.responseStatus < 300
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {debugInfo.responseStatus || (debugInfo.mockResponse ? 'Mock 200' : 'N/A')}
                  </span>
                  <span className="text-sm text-gray-600">
                    Tempo: {formatTime(debugInfo.responseTime || 0)}
                  </span>
                </div>
              </div>

              <div>
                <h4 className="font-medium text-gray-900 mb-2">Resultado</h4>
                <div className={`p-4 rounded-lg border-2 ${
                  debugInfo.found 
                    ? 'bg-green-50 border-green-200' 
                    : 'bg-red-50 border-red-200'
                }`}>
                  <div className="flex items-center">
                    <span className="text-2xl mr-3">
                      {debugInfo.found ? '✅' : '❌'}
                    </span>
                    <div>
                      <div className="font-medium">
                        {debugInfo.found ? 'Cliente encontrado' : 'Cliente não encontrado'}
                      </div>
                      <div className="text-sm text-gray-600 mt-1">
                        {debugInfo.found 
                          ? 'Dados do cliente foram retornados com sucesso'
                          : 'Nenhum cliente encontrado com este telefone'
                        }
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'error' && debugInfo.error && (
            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-start">
                  <span className="text-red-500 text-2xl mr-3">❌</span>
                  <div>
                    <h4 className="font-medium text-red-900 mb-2">Erro na API</h4>
                    <p className="text-red-800 text-sm mb-3">{debugInfo.error}</p>
                    {debugInfo.errorCode && (
                      <div className="text-xs text-red-700">
                        <strong>Código:</strong> {debugInfo.errorCode}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {debugInfo.fallbackToMock && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <span className="text-yellow-500 text-2xl mr-3">🔄</span>
                    <div>
                      <h4 className="font-medium text-yellow-900 mb-2">Fallback Aplicado</h4>
                      <p className="text-yellow-800 text-sm">
                        Após o erro na API real, o sistema automaticamente utilizou dados mock para manter a funcionalidade.
                      </p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t p-4 bg-gray-50">
          <div className="flex justify-between items-center">
            <div className="text-xs text-gray-500">
              Debug info gerado em {debugInfo.timestamp ? new Date(debugInfo.timestamp).toLocaleString('pt-BR') : 'N/A'}
            </div>
            <button 
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 text-sm"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrinksDebugPanel;