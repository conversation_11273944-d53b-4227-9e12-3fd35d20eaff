import React from 'react';
import { Service } from '../../types/index';

interface RecentHistoryProps {
  services: Service[];
}

const RecentHistory: React.FC<RecentHistoryProps> = ({ services }) => {
  const safeServices = services || [];
  
  // Filter completed services and get the 3 most recent
  const completedServices = safeServices
    .filter(service => 
      service.status && (
        service.status.toLowerCase().includes('finalizado') || 
        service.status.toLowerCase().includes('concluído') ||
        service.status.toLowerCase().includes('completed')
      )
    )
    .sort((a, b) => new Date(b.data).getTime() - new Date(a.data).getTime())
    .slice(0, 3);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return 'ontem';
    } else if (diffDays <= 7) {
      return `há ${diffDays} dias`;
    } else if (diffDays <= 30) {
      const weeks = Math.floor(diffDays / 7);
      return `há ${weeks} ${weeks === 1 ? 'semana' : 'semanas'}`;
    } else {
      const months = Math.floor(diffDays / 30);
      return `há ${months} ${months === 1 ? 'mês' : 'meses'}`;
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (completedServices.length === 0) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Últimos Atendimentos</h3>
      
      <div className="space-y-3">
        {completedServices.map((service, index) => (
          <div key={index} className="bg-gray-50 rounded-lg p-3">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-semibold text-gray-900 text-sm">{service.servico}</h4>
                  <span className="text-xs text-gray-500">•</span>
                  <span className="text-xs text-gray-600">{formatDate(service.data)}</span>
                </div>
                
                {service.profissional && (
                  <div className="flex items-center gap-1 text-xs text-gray-600">
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                    </svg>
                    <span>Atendido por {service.profissional}</span>
                  </div>
                )}
              </div>
              
              <div className="text-right">
                <div className="text-sm font-semibold text-gray-900">{formatCurrency(service.valor)}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {completedServices.length >= 3 && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            Mostrando os 3 atendimentos mais recentes
          </p>
        </div>
      )}
    </div>
  );
};

export default RecentHistory;