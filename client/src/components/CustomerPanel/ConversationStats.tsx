import React from 'react';
import { Conversation } from '../../types/index';

interface ConversationStatsProps {
  conversation: Conversation;
}

const ConversationStats: React.FC<ConversationStatsProps> = ({ conversation }) => {
  const formatDuration = (startTime: Date) => {
    const now = new Date();
    const start = new Date(startTime);
    const diffInMinutes = Math.floor((now.getTime() - start.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) {
      return 'Há poucos segundos';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} min atrás`;
    } else {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours}h ${diffInMinutes % 60}min atrás`;
    }
  };

  const userMessages = conversation.messages.filter(m => m.sender === 'user');
  const aiMessages = conversation.messages.filter(m => m.sender === 'ai');

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Estatísticas da Conversa</h3>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-blue-50 rounded-lg">
          <div className="text-2xl font-bold text-blue-600">
            {conversation.messages.length}
          </div>
          <div className="text-sm text-blue-800">Mensagens</div>
        </div>
        
        <div className="text-center p-3 bg-green-50 rounded-lg">
          <div className="text-2xl font-bold text-green-600">
            {userMessages.length}
          </div>
          <div className="text-sm text-green-800">Do Cliente</div>
        </div>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Iniciada:</span>
          <span className="font-medium text-gray-900">
            {conversation.messages.length > 0 
              ? formatDuration(conversation.messages[0].timestamp)
              : 'Agora'
            }
          </span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Última mensagem:</span>
          <span className="font-medium text-gray-900">
            {formatDuration(conversation.lastMessage)}
          </span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Mensagens da IA:</span>
          <span className="font-medium text-gray-900">
            {aiMessages.length}
          </span>
        </div>

        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Status:</span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <span className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1.5"></span>
            Ativa
          </span>
        </div>
      </div>

      {/* Conversation Flow Indicator */}
      <div className="mt-4 pt-4 border-t border-gray-200">
        <h4 className="text-sm font-medium text-gray-700 mb-2">Fluxo da Conversa</h4>
        <div className="space-y-2">
          {conversation.messages.slice(-3).map((message, index) => (
            <div key={message.id} className="flex items-center text-xs">
              <div className={`w-2 h-2 rounded-full mr-2 ${
                message.sender === 'user' ? 'bg-blue-400' : 'bg-green-400'
              }`}></div>
              <span className={`truncate ${
                message.sender === 'user' ? 'text-blue-700' : 'text-green-700'
              }`}>
                {message.sender === 'user' ? 'Cliente' : 'IA'}: {typeof message.content === 'string' ? message.content.substring(0, 30) + '...' : '[Audio Message]'}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ConversationStats;