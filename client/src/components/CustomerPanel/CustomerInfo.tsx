import React from 'react';
import { Customer } from '../../types/index';

interface CustomerInfoProps {
  customer: Customer;
}

const CustomerInfo: React.FC<CustomerInfoProps> = ({ customer }) => {
  // Only show if customer has preferences
  if (!customer.preferencias) {
    return null;
  }

  return (
    <div className="bg-gradient-to-r from-trinks-50 to-orange-50 border border-trinks-200 rounded-xl p-4">
      <div className="flex items-start gap-3">
        <div className="w-8 h-8 bg-trinks-100 rounded-lg flex items-center justify-center flex-shrink-0">
          <svg className="w-4 h-4 text-trinks-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </div>
        <div className="flex-1">
          <h4 className="text-sm font-bold text-trinks-900 mb-1">Preferências Especiais</h4>
          <p className="text-sm text-trinks-700 leading-relaxed">{customer.preferencias}</p>
        </div>
      </div>
    </div>
  );
};

export default CustomerInfo;