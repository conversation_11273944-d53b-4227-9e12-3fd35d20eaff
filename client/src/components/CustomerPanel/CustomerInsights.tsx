import React from 'react';
import { Service } from '../../types/index';

interface CustomerInsightsProps {
  services: Service[];
  customerName: string;
  isLoading?: boolean;
}

const CustomerInsights: React.FC<CustomerInsightsProps> = ({ services, customerName, isLoading = false }) => {
  const safeServices = services || [];
  
  // Se está carregando, mostra estado de loading
  if (isLoading) {
    return (
      <div className="bg-gradient-to-br from-trinks-50 via-orange-50 to-yellow-50 rounded-2xl p-6 shadow-lg border border-trinks-100/50 backdrop-blur-sm mb-6">
        <div className="text-center mb-4">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-trinks-200/50 shadow-sm">
            <span className="text-lg">⏳</span>
            <span className="text-sm font-semibold text-trinks-800">
              Carregando dados...
            </span>
          </div>
        </div>
      </div>
    );
  }
  
  if (safeServices.length === 0) {
    return (
      <div className="bg-gradient-to-br from-gray-50 via-blue-50 to-indigo-50 rounded-2xl p-6 shadow-lg border border-gray-100/50 backdrop-blur-sm mb-6">
        <div className="text-center mb-4">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-gray-200/50 shadow-sm">
            <span className="text-lg">⭐</span>
            <span className="text-sm font-semibold text-gray-800">
              Cliente Novo
            </span>
          </div>
        </div>
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Primeiro contato com {customerName}
          </p>
        </div>
      </div>
    );
  }

  // Calculate insights
  const totalServices = safeServices.length;
  const totalSpent = safeServices.reduce((sum, service) => sum + service.valor, 0);
  const averageSpent = totalSpent / totalServices;
  
  // Most frequent professional
  const professionalCounts = safeServices.reduce((acc, service) => {
    acc[service.profissional] = (acc[service.profissional] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  const favoriteProfessional = Object.entries(professionalCounts)
    .sort(([,a], [,b]) => b - a)[0]?.[0];
  
  // Most frequent service
  const serviceCounts = safeServices.reduce((acc, service) => {
    acc[service.servico] = (acc[service.servico] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);
  const favoriteService = Object.entries(serviceCounts)
    .sort(([,a], [,b]) => b - a)[0];
  
  // Calculate visit frequency (last 30 days)
  const now = new Date();
  const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
  const recentServices = safeServices.filter(service => 
    new Date(service.data) >= thirtyDaysAgo
  );
  
  // Last visit
  const lastVisit = safeServices[0]?.data; // Services are sorted by date
  const daysSinceLastVisit = lastVisit 
    ? Math.floor((now.getTime() - new Date(lastVisit).getTime()) / (1000 * 60 * 60 * 24))
    : null;

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm">
      {/* Clean Header */}
      <div className="flex items-center gap-3 mb-6">
        <div className="w-8 h-8 bg-gradient-to-br from-trinks-500 to-trinks-600 rounded-lg flex items-center justify-center">
          <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Perfil do Cliente</h3>
          <p className="text-xs text-gray-500">Estatísticas e informações-chave</p>
        </div>
      </div>

      {/* Main Stats Card - Humanized and cleaner */}
      <div className="bg-gradient-to-br from-trinks-50 to-orange-50 border border-trinks-200/40 rounded-xl p-5 mb-5">
        {/* Customer Category - More prominent */}
        <div className="text-center mb-4">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-white/80 backdrop-blur-sm rounded-full border border-trinks-200/50 shadow-sm">
            <span className="text-lg">
              {totalServices >= 10 ? '🏆' : totalServices >= 5 ? '💎' : totalServices >= 1 ? '🌟' : '⭐'}
            </span>
            <span className="text-sm font-semibold text-trinks-800">
              {totalServices >= 10 ? 'Cliente VIP' : totalServices >= 5 ? 'Cliente Regular' : totalServices >= 1 ? 'Cliente Ativo' : 'Cliente Novo'}
            </span>
          </div>
        </div>

        {/* Humanized Statistics */}
        {totalServices > 0 ? (
          <div className="text-center space-y-3">
            <div>
              <div className="text-3xl font-bold text-trinks-900 mb-1">{formatCurrency(totalSpent)}</div>
              <p className="text-sm text-trinks-700">
                {totalServices === 1 ? 
                  'investiu em 1 atendimento conosco' : 
                  `investiu em ${totalServices} atendimentos conosco`
                }
              </p>
            </div>
            
            {totalServices > 1 && (
              <div className="pt-3 border-t border-trinks-200/50">
                <p className="text-xs text-trinks-600">
                  Gasta em média <span className="font-semibold text-trinks-800">{formatCurrency(averageSpent)}</span> por visita
                </p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-2">
            <p className="text-sm text-trinks-700 mb-2">
              Novo cliente em potencial
            </p>
            <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-white/60 rounded-lg border border-trinks-200/50">
              <span className="text-xs">💡</span>
              <span className="text-xs font-medium text-trinks-800">Primeira oportunidade de conquistar</span>
            </div>
          </div>
        )}
      </div>

      {/* Additional Insights - Compact and modern */}
      {(favoriteProfessional || favoriteService || daysSinceLastVisit !== null) && (
        <div className="space-y-3">
          {/* Favorites Grid - Cleaner layout */}
          {(favoriteProfessional || favoriteService) && (
            <div className="grid gap-3">
              {favoriteProfessional && (
                <div className="bg-gradient-to-r from-blue-50 to-blue-50/50 border border-blue-200/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-blue-600 uppercase tracking-wide">Profissional Preferido</p>
                        <p className="text-sm font-semibold text-blue-900">{favoriteProfessional}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-900">{professionalCounts[favoriteProfessional]}</div>
                      <div className="text-xs text-blue-600">atendimento{professionalCounts[favoriteProfessional] !== 1 ? 's' : ''}</div>
                    </div>
                  </div>
                </div>
              )}

              {favoriteService && (
                <div className="bg-gradient-to-r from-orange-50 to-orange-50/50 border border-orange-200/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs font-medium text-orange-600 uppercase tracking-wide">Serviço Preferido</p>
                        <p className="text-sm font-semibold text-orange-900">{favoriteService[0]}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-orange-900">{favoriteService[1]}</div>
                      <div className="text-xs text-orange-600">{favoriteService[1] === 1 ? 'vez' : 'vezes'}</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Last Visit - More prominent */}
          {daysSinceLastVisit !== null && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-xs font-medium text-green-600 uppercase tracking-wide">Última Visita</p>
                    <p className="text-sm font-semibold text-green-900">
                      {daysSinceLastVisit === 0 ? 'Hoje' :
                       daysSinceLastVisit === 1 ? 'Ontem' :
                       `Há ${daysSinceLastVisit} dias`}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-900">{recentServices.length}</div>
                  <div className="text-xs text-green-600">nos últimos 30 dias</div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default CustomerInsights;