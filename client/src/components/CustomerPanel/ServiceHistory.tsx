import React from 'react';
import { Service } from '../../types/index';

interface ServiceHistoryProps {
  services: Service[];
}

const ServiceHistory: React.FC<ServiceHistoryProps> = ({ services }) => {
  // Garantir que services seja sempre um array
  const safeServices = services || [];
  
  const formatDate = (dateString: string) => {
    // Use date string comparison to avoid timezone issues
    const appointmentDate = dateString.split('T')[0]; // Get YYYY-MM-DD part
    
    // Get today's date in Brazil timezone consistently
    const today = new Date().toLocaleDateString('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).split('/').reverse().join('-'); // Convert DD/MM/YYYY to YYYY-MM-DD
    
    if (appointmentDate === today) {
      return 'Hoje';
    }
    
    // Parse the date for formatting
    const date = new Date(appointmentDate + 'T00:00:00'); // Ensure midnight local time
    return date.toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const totalSpent = safeServices.reduce((sum, service) => sum + service.valor, 0);

  // Separate completed services from future appointments - More flexible filtering
  const completedServices = safeServices.filter(service => {
    // If no status field, check if it's a past date
    if (!service.status) {
      const serviceDate = new Date(service.data);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return serviceDate < today && !service.isFuture;
    }
    
    // If status exists, check for completion indicators
    const status = service.status.toLowerCase();
    return status.includes('finalizado') || 
           status.includes('concluído') ||
           status.includes('completed') ||
           status.includes('realizado') ||
           service.isPast === true;
  });
  
  // Show active future appointments (confirmed, pending, etc. - not cancelled)
  const futureAppointments = safeServices.filter(service => {
    const isCompleted = completedServices.includes(service);
    const isCancelled = service.status && (
      service.status.toLowerCase().includes('cancelado') || 
      service.status.toLowerCase().includes('cancelled')
    );
    
    // Check if it's a future appointment
    const serviceDate = new Date(service.data);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const isFuture = service.isFuture === true || serviceDate >= today;
    
    return !isCompleted && !isCancelled && isFuture;
  });
  
  const cancelledAppointments = safeServices.filter(service => 
    service.status && service.status.toLowerCase().includes('cancelado')
  );

  if (safeServices.length === 0) {
    return (
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-medium text-gray-900 mb-3">Histórico de Serviços</h3>
        <div className="text-center py-6 text-gray-500">
          <div className="text-3xl mb-2">✂️</div>
          <p>Nenhum serviço anterior</p>
          <p className="text-sm mt-1">Este será o primeiro atendimento</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white border border-gray-100 rounded-xl p-5 shadow-sm">
      {/* Header with cleaner design */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gradient-to-br from-trinks-500 to-trinks-600 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Histórico de Serviços</h3>
            <p className="text-xs text-gray-500">Agendamentos e atendimentos do cliente</p>
          </div>
        </div>
        
        {/* Summary badge - only show future appointments */}
        {futureAppointments.length > 0 && (
          <div className="flex items-center gap-1 bg-blue-50 border border-blue-200 px-2 py-1 rounded-lg">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-xs font-medium text-blue-700">{futureAppointments.length} agendado{futureAppointments.length !== 1 ? 's' : ''}</span>
          </div>
        )}
      </div>


      {/* Show message if only future appointments */}
      {completedServices.length === 0 && futureAppointments.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-blue-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm font-medium text-blue-800">Agendamentos futuros</p>
              <p className="text-xs text-blue-700 mt-1">Cliente tem {futureAppointments.length} agendamento(s) marcado(s)</p>
            </div>
          </div>
        </div>
      )}
      
      {/* Show message about cancelled appointments */}
      {cancelledAppointments.length > 0 && futureAppointments.length === 0 && completedServices.length === 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
          <div className="flex items-center">
            <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
            </svg>
            <div>
              <p className="text-sm font-medium text-red-800">Apenas agendamentos cancelados</p>
              <p className="text-xs text-red-700 mt-1">Cliente tem {cancelledAppointments.length} agendamento(s) cancelado(s)</p>
            </div>
          </div>
        </div>
      )}

      {/* Future Appointments Section - Cleaner design */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h4 className="text-sm font-semibold text-blue-900 flex items-center gap-2">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            Próximos Agendamentos
          </h4>
          {futureAppointments.length > 0 && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
              {futureAppointments.length}
            </span>
          )}
        </div>
        
        {futureAppointments.length > 0 ? (
          <div className="grid gap-3">
            {futureAppointments.slice(0, 3).map((service, index) => (
              <div key={`future-${index}`} className="bg-gradient-to-r from-blue-50 to-blue-50/50 border border-blue-200/50 rounded-xl p-4 hover:from-blue-100 hover:to-blue-50 transition-all duration-200">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h5 className="font-semibold text-blue-900 text-sm mb-2">{service.servico}</h5>
                    <div className="flex items-center gap-4 text-xs text-blue-700">
                      <div className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="font-medium">{formatDate(service.data)}</span>
                      </div>
                      {service.profissional && (
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          <span>{service.profissional}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-right pl-4">
                    <div className="text-lg font-bold text-blue-900">{formatCurrency(service.valor)}</div>
                    {service.status && (
                      <span className="inline-block px-2 py-1 text-xs rounded-full font-medium bg-blue-200 text-blue-800 mt-1">
                        {service.status}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-xl p-6 text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h5 className="text-sm font-semibold text-blue-900 mb-2">Nenhum agendamento futuro</h5>
            <p className="text-xs text-blue-600">O cliente ainda não possui agendamentos marcados</p>
          </div>
        )}
      </div>

      {/* Past Services Section - Only show if there are completed services */}
      {completedServices.length > 0 && (
        <div className="mb-6">
          <div className="mb-4">
            <h4 className="text-sm font-semibold text-green-900 flex items-center gap-2">
              <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Atendimentos Realizados
            </h4>
          </div>
          <div className="grid gap-3">
            {completedServices.slice(0, 3).map((service, index) => (
              <div key={`past-${index}`} className="bg-gradient-to-r from-green-50 to-green-50/50 border border-green-200/50 rounded-xl p-4">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h5 className="font-semibold text-green-900 text-sm mb-2">{service.servico}</h5>
                    <div className="flex items-center gap-4 text-xs text-green-700">
                      <div className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="font-medium">{formatDate(service.data)}</span>
                      </div>
                      {service.profissional && (
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                          <span>{service.profissional}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="text-right pl-4">
                    <div className="text-lg font-bold text-green-900">{formatCurrency(service.valor)}</div>
                    <span className="inline-block px-2 py-1 text-xs rounded-full font-medium bg-green-200 text-green-800 mt-1">
                      Concluído
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Cancelled Appointments Section */}
      {cancelledAppointments.length > 0 && (
        <div className="mb-4">
          <div className="flex items-center mb-3">
            <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mr-2">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <h4 className="text-sm font-semibold text-red-900">Agendamentos Cancelados</h4>
            <span className="ml-2 bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
              {cancelledAppointments.length}
            </span>
          </div>
          <div className="space-y-2">
            {cancelledAppointments.slice(0, 2).map((service, index) => (
              <div key={`cancelled-${index}`} className="p-3 bg-red-50 border border-red-200 rounded-lg opacity-75">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center">
                      <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                      <h5 className="font-medium text-red-900 text-sm line-through">{service.servico}</h5>
                      <span className="ml-2 px-2 py-1 text-xs rounded-full font-medium bg-red-100 text-red-800">
                        Cancelado
                      </span>
                    </div>
                    <div className="flex items-center mt-1 text-xs text-red-600">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                      </svg>
                      {formatDate(service.data)}
                    </div>
                  </div>
                  <div className="text-right ml-3">
                    <div className="font-semibold text-red-900 text-sm line-through">{formatCurrency(service.valor)}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {(futureAppointments.length > 3 || completedServices.length > 3 || cancelledAppointments.length > 2) && (
        <div className="mt-4 text-center">
          <button className="text-trinks-600 hover:text-trinks-700 text-sm font-medium">
            Ver histórico completo ({safeServices.length} total)
          </button>
        </div>
      )}
    </div>
  );
};

export default ServiceHistory;