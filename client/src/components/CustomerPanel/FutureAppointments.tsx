import React from 'react';
import { Service } from '../../types/index';

interface FutureAppointmentsProps {
  services: Service[];
}

const FutureAppointments: React.FC<FutureAppointmentsProps> = ({ services }) => {
  const safeServices = services || [];
  
  // Show active future appointments (confirmed, pending, etc. - not cancelled)
  const futureAppointments = safeServices.filter(service => {
    const isCompleted = service.status && (
      service.status.toLowerCase().includes('finalizado') || 
      service.status.toLowerCase().includes('concluído') ||
      service.status.toLowerCase().includes('completed')
    );
    const isCancelled = service.status && (
      service.status.toLowerCase().includes('cancelado') || 
      service.status.toLowerCase().includes('cancelled')
    );
    // Compare dates in Brazil timezone
    const serviceDateStr = service.data.split('T')[0];
    const todayBR = new Date().toLocaleDateString('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).split('/').reverse().join('-');
    
    const isFuture = service.isFuture || serviceDateStr >= todayBR;
    
    return !isCompleted && !isCancelled && isFuture;
  });

  const formatDate = (dateString: string) => {
    // Parse appointment date 
    const appointmentDate = dateString.split('T')[0]; // YYYY-MM-DD
    
    // Get today's date in Brazil timezone consistently
    const nowInBrazil = new Date();
    const todayBR = nowInBrazil.toLocaleDateString('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      year: 'numeric',
      month: '2-digit', 
      day: '2-digit'
    }).split('/').reverse().join('-'); // Convert DD/MM/YYYY to YYYY-MM-DD
    
    // Get tomorrow's date in Brazil timezone
    const tomorrowDate = new Date(nowInBrazil);
    tomorrowDate.setDate(tomorrowDate.getDate() + 1);
    const tomorrowBR = tomorrowDate.toLocaleDateString('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).split('/').reverse().join('-');
    
    if (appointmentDate === todayBR) {
      return 'Hoje';
    }
    
    if (appointmentDate === tomorrowBR) {
      return 'Amanhã';
    }
    
    // Format the date for display
    const date = new Date(appointmentDate + 'T12:00:00'); // Use noon to avoid timezone issues
    return date.toLocaleDateString('pt-BR', {
      timeZone: 'America/Sao_Paulo',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  if (futureAppointments.length === 0) {
    return null;
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Próximos Agendamentos</h3>
        <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
          {futureAppointments.length} {futureAppointments.length === 1 ? 'agendamento' : 'agendamentos'}
        </span>
      </div>

      <div className="space-y-3">
        {futureAppointments.map((service, index) => (
          <div key={index} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-semibold text-blue-900 text-sm">{service.servico}</h4>
                  {service.status && (
                    <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
                      {service.status}
                    </span>
                  )}
                </div>
                <div className="text-xs text-blue-700 space-y-1">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                      </svg>
                      {formatDate(service.data)}
                    </span>
                    {service.profissional && (
                      <span className="flex items-center gap-1">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                        </svg>
                        {service.profissional}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-blue-900">{formatCurrency(service.valor)}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-3 pt-3 border-t border-blue-200">
        <div className="flex justify-between items-center text-sm">
          <span className="text-blue-700">Total dos agendamentos:</span>
          <span className="font-bold text-blue-900">
            {formatCurrency(futureAppointments.reduce((sum, service) => sum + service.valor, 0))}
          </span>
        </div>
      </div>
    </div>
  );
};

export default FutureAppointments;