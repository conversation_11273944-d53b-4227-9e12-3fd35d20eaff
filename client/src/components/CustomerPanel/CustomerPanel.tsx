import React, { useState } from 'react';
import { Customer, Conversation } from '../../types/index';
import CustomerInfo from './CustomerInfo';
import ServiceHistory from './ServiceHistory';

import CustomerInsights from './CustomerInsights';
import AppointmentScheduler from '../Scheduling/AppointmentScheduler';
import MoodIndicator from '../MoodIndicator/MoodIndicator';
import FutureAppointments from './FutureAppointments';
import RecentHistory from './RecentHistory';
import ContactInfo from './ContactInfo';

interface CustomerPanelProps {
  customer: Customer | null;
  conversation: Conversation | null;
}

const CustomerPanel: React.FC<CustomerPanelProps> = ({ customer, conversation }) => {
  const [activeTab, setActiveTab] = useState<'info' | 'scheduling'>('info');

  // Handle booking completion
  const handleBookingComplete = (booking: any) => {
    console.log('Appointment booked:', booking);
    // Could refresh customer data or show success message
    // For now, just switch back to info tab
    setActiveTab('info');
  };

  if (!conversation) {
    return (
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="bg-gradient-to-r from-trinks-600 to-trinks-700 text-white p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h2 className="text-lg font-bold">Perfil do Cliente</h2>
              <p className="text-sm text-orange-100 font-medium">Powered by A Trinks</p>
            </div>
          </div>
        </div>

        {/* Content when no conversation */}
        <div className="flex-1 p-4 space-y-4">
          
          {/* Empty State */}
          <div className="flex-1 flex items-center justify-center p-6">
            <div className="text-center max-w-xs">
              <div className="w-16 h-16 bg-gradient-to-br from-trinks-100 to-orange-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-trinks-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-base font-semibold text-gray-900 mb-2">
                Pronto para conhecer seu cliente?
              </h3>
              <p className="text-sm text-gray-500 leading-relaxed mb-4">
                Selecione uma conversa à esquerda para ver o perfil completo e histórico
              </p>
              <div className="p-3 bg-trinks-50 rounded-lg border border-trinks-100">
                <p className="text-xs text-trinks-700 font-medium">
                  💡 Dica: Use a busca para conectar com clientes específicos
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Header - Aligned with Trinks branding */}
      <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4" style={{background: 'linear-gradient(135deg, #EC5702 0%, #d83900 100%)'}}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
              <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h2 className="text-lg font-bold tracking-tight">Perfil do Cliente</h2>
              <p className="text-sm text-orange-100 font-medium">
                {customer ? '✨ Cliente da casa Trinks' : '📱 Novo contato'}
              </p>
            </div>
          </div>

          {customer && (
            <div className="text-right">
              <div className="text-xs text-orange-100/75 uppercase tracking-wide">Cliente ID</div>
              <div className="text-sm font-mono text-white font-semibold">
                #{String(customer.id).slice(-6).toUpperCase()}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Navigation Tabs - Improved UX with better visual hierarchy */}
      <div className="bg-white border-b border-gray-100 px-4">
        <nav className="flex space-x-1">
          <button
            onClick={() => setActiveTab('info')}
            className={`py-3 px-4 rounded-t-lg text-sm font-semibold transition-all duration-200 relative ${
              activeTab === 'info'
                ? 'text-orange-700 bg-orange-50 border-b-2 border-orange-500'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              Perfil do Cliente
            </div>
          </button>
          <button
            onClick={() => setActiveTab('scheduling')}
            className={`py-3 px-4 rounded-t-lg text-sm font-semibold transition-all duration-200 relative ${
              activeTab === 'scheduling'
                ? 'text-orange-700 bg-orange-50 border-b-2 border-orange-500'
                : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              Agendamentos
            </div>
          </button>
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {activeTab === 'info' ? (
          <div className="p-4 space-y-6">
            
            {/* Customer Name - Always show first */}
            {customer ? (
              <div className="bg-white border border-gray-100 rounded-xl p-4 shadow-sm">
                <div className="flex items-center gap-4">
                  {/* Avatar */}
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-trinks-500 to-trinks-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                      {(customer.nome || customer.telefone || 'C').charAt(0).toUpperCase()}
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-trinks-green-500 rounded-full border-2 border-white shadow-sm" />
                  </div>
                  
                  {/* Customer Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="text-xl font-bold text-gray-900 truncate mb-1">
                      {customer.nome || 'Cliente'}
                    </h3>
                    <div className="text-xs text-gray-500 mb-2">
                      Desde {new Date(customer.created_at || Date.now()).toLocaleDateString('pt-BR')}
                    </div>
                    
                    {/* Compact Preferences */}
                    {customer.preferencias && (
                      <div className="bg-gradient-to-r from-trinks-50/60 to-orange-50/60 border border-trinks-200/50 rounded-lg p-2">
                        <div className="flex items-start gap-2">
                          <svg className="w-3 h-3 text-trinks-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                          <div className="flex-1 min-w-0">
                            <p className="text-xs font-medium text-trinks-800 mb-0.5">Preferências:</p>
                            <p className="text-xs text-trinks-700 leading-relaxed truncate">{customer.preferencias}</p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ) : (
              <div className="bg-white border border-gray-100 rounded-xl p-4 shadow-sm">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-gray-400 to-gray-500 rounded-2xl flex items-center justify-center text-white text-2xl font-bold shadow-lg">
                    ?
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {conversation.customerName || conversation.customerPhone || 'Novo Cliente'}
                    </h3>
                    <div className="text-xs text-gray-500">
                      {conversation.customerName ? 'Carregando dados...' : 'Primeiro contato'}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Mood Indicator - Always show when there's a conversation */}
            <MoodIndicator 
              conversation={conversation}
              onEscalationAlert={(alert) => {
                console.log('🚨 Escalation alert in CustomerPanel:', alert);
                // Here you could show a toast notification, update UI, etc.
              }}
            />

            {/* Contact Info - Compact, always show */}
            {customer ? (
              <ContactInfo customer={customer} customerPhone={conversation.customerPhone} />
            ) : (
              <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 rounded-xl p-3 border border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-whatsapp-500 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                    </svg>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-gray-900">{conversation.customerPhone}</div>
                    <div className="text-xs text-gray-500">WhatsApp</div>
                  </div>
                </div>
              </div>
            )}
            
            {customer ? (
              <>
                {/* Future Appointments - Show first */}
                <FutureAppointments services={
                  customer.appointmentsData ? 
                    [...(customer.appointmentsData.past || []), ...(customer.appointmentsData.future || [])] 
                    : customer.ultimosServicos || []
                } />

                {/* Customer Insights */}
                <CustomerInsights 
                  services={
                    customer.appointmentsData ? 
                      [...(customer.appointmentsData.past || []), ...(customer.appointmentsData.future || [])] 
                      : customer.ultimosServicos || []
                  }
                  customerName={customer.nome}
                />

                {/* Recent History - Last 3 appointments */}
                <RecentHistory services={
                  customer.appointmentsData ? 
                    [...(customer.appointmentsData.past || []), ...(customer.appointmentsData.future || [])] 
                    : customer.ultimosServicos || []
                } />

                
                {/* Service History - Show all appointments including future ones */}
                <ServiceHistory services={
                  customer.appointmentsData ? 
                    [...(customer.appointmentsData.past || []), ...(customer.appointmentsData.future || [])] 
                    : customer.ultimosServicos || []
                } />
                

              </>
            ) : (
              <>
              {/* New Customer Info */}
              <div className="bg-gradient-to-r from-blue-50 to-gray-50 border border-gray-200 rounded-xl p-4 mb-6">
                <div className="flex items-start gap-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                    <svg className="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6z" />
                    </svg>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-sm font-bold text-gray-900 mb-1">
                      📱 {conversation.customerName || 'Aguardando dados...'}
                    </h3>
                    <p className="text-sm text-gray-700 leading-relaxed">
                      {conversation.customerName ? 'Carregando histórico do cliente...' : 'Este é um novo contato! Esta conversa será registrada no sistema de atendimento.'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Customer Status - Show for clients without full data */}
              <CustomerInsights 
                services={[]} 
                customerName={conversation.customerName || conversation.customerPhone || 'Cliente'}
                isLoading={!!conversation.customerName && !customer}
              />

              {/* Basic Contact Info */}
              <div className="bg-white border border-gray-200 rounded-lg p-4 mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-3">Contato</h3>
                <div className="space-y-3">
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <span className="text-sm text-gray-600">{conversation.customerPhone}</span>
                  </div>
                  <div className="flex items-center">
                    <svg className="w-5 h-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span className="text-sm text-gray-600">Nome não informado</span>
                  </div>
                </div>
              </div>


              </>
            )}
          </div>
        ) : (
          <div className="p-4">
            <AppointmentScheduler
              customerName={customer?.nome || 'Cliente'}
              customerPhone={conversation.customerPhone}
              onBookingComplete={handleBookingComplete}
            />
          </div>
        )}
      </div>

      {/* Quick Actions - Only button, no title */}
      {activeTab === 'info' && (
        <div className="border-t border-gray-200 p-4 bg-gradient-to-b from-orange-50 to-white">
          <button
            onClick={() => setActiveTab('scheduling')}
            className="w-full text-left px-4 py-3 text-sm bg-gradient-to-r from-orange-50 to-orange-100 border border-orange-200 rounded-lg hover:from-orange-100 hover:to-orange-200 transition-all duration-200 group"
          >
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <p className="font-semibold text-orange-900">Agendar Serviço</p>
                <p className="text-xs text-orange-700">Novo agendamento para a cliente</p>
              </div>
            </div>
          </button>
        </div>
      )}
    </div>
  );
};

export default CustomerPanel;