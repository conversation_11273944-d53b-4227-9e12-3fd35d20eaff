import React, { useState, useEffect } from 'react';
import socketService from '../../services/socket';
import { Conversation, MoodData, EscalationAlert } from '../../types/index';

interface MoodIndicatorProps {
  conversation: Conversation | null;
  onEscalationAlert?: (alert: EscalationAlert) => void;
}

const MoodIndicator: React.FC<MoodIndicatorProps> = ({ 
  conversation, 
  onEscalationAlert 
}) => {
  const [currentMood, setCurrentMood] = useState<MoodData | null>(null);
  const [showAlert, setShowAlert] = useState(false);
  const [alertData, setAlertData] = useState<EscalationAlert | null>(null);
  const [moodHistory, setMoodHistory] = useState<MoodData[]>([]);
  const [isExpanded, setIsExpanded] = useState(false);

  // Mood configurations aligned with backend
  const moodConfigs = {
    VERY_POSITIVE: {
      label: '<PERSON><PERSON> Positivo',
      description: 'Empreendedor super satisfeito! 🎉',
      color: '#10B981',
      bgColor: '#D1FAE5',
      icon: '😊'
    },
    POSITIVE: {
      label: 'Positivo', 
      description: 'Empreendedor contente',
      color: '#22C55E',
      bgColor: '#ECFDF5',
      icon: '🙂'
    },
    NEUTRAL: {
      label: 'Neutro',
      description: 'Estado emocional estável',
      color: '#6B7280',
      bgColor: '#F9FAFB',
      icon: '😐'
    },
    FRUSTRATED: {
      label: 'Frustrado',
      description: 'Precisa de atenção especial',
      color: '#F59E0B',
      bgColor: '#FEF3C7',
      icon: '😕'
    },
    ANGRY: {
      label: 'Irritado',
      description: 'Suporte imediato necessário',
      color: '#EF4444',
      bgColor: '#FEE2E2',
      icon: '😠'
    },
    VERY_ANGRY: {
      label: 'Muito Irritado',
      description: 'Situação crítica! ⚡',
      color: '#DC2626',
      bgColor: '#FEE2E2',
      icon: '😡'
    }
  };

  // Response suggestions based on mood
  const getResponseSuggestions = (mood: string) => {
    const suggestions = {
      VERY_POSITIVE: [
        '🎉 Corresponda à energia positiva!',
        '💪 Reforce como a Trinks está ajudando',
        '✨ Ofereça valor adicional quando apropriado'
      ],
      POSITIVE: [
        '😊 Mantenha o bom humor',
        '🤝 Seja receptiva e prestativa',
        '📈 Use o momento para fortalecer a parceria'
      ],
      NEUTRAL: [
        '🎯 Mantenha tom profissional da Trinks',
        '💼 Seja eficiente e prestativa',
        '🚀 Foque no crescimento do negócio'
      ],
      FRUSTRATED: [
        '🤗 "A gente entende sua preocupação"',
        '📋 Ofereça orientação clara passo-a-passo',
        '💪 "Vamos dar um up nessa situação?"'
      ],
      ANGRY: [
        '🤝 Use linguagem acolhedora da Trinks',
        '⚡ Ofereça solução imediata',
        '👥 "Vem, que a gente resolve isso juntos"'
      ],
      VERY_ANGRY: [
        '🚨 PRIORIDADE: Seja extremamente empática',
        '🤝 "A Trinks está aqui para resolver"',
        '⚡ Considere transferir para equipe especializada'
      ]
    };
    return suggestions[mood as keyof typeof suggestions] || [];
  };

  useEffect(() => {
    const socket = socketService.getSocket();
    console.log('🎭 [FRONTEND DEBUG] Socket instance:', socket ? 'available' : 'not available');
    console.log('🎭 [FRONTEND DEBUG] Socket connected:', socket?.connected);
    
    if (!socket) return;

    // Listen for mood updates
    const handleMoodUpdate = (data: {
      conversationId: string;
      customerPhone: string;
      moodAnalysis: MoodData;
    }) => {
      console.log('🎭 [FRONTEND DEBUG] Mood update event received:', data);
      console.log('🎭 [FRONTEND DEBUG] Current conversation phone:', conversation?.customerPhone);
      console.log('🎭 [FRONTEND DEBUG] Event customer phone:', data.customerPhone);
      
      // Match by customer phone instead of conversation ID for better accuracy
      if (!conversation || data.customerPhone !== conversation.customerPhone) {
        console.log('🎭 [FRONTEND DEBUG] Mood update ignored - customer phone mismatch');
        return;
      }
      
      console.log('🎭 [FRONTEND DEBUG] Processing mood update:', data.moodAnalysis);
      
      const newMood = {
        ...data.moodAnalysis,
        timestamp: new Date().toISOString()
      };
      
      console.log('🎭 [FRONTEND DEBUG] Setting new mood state:', newMood);
      setCurrentMood(newMood);
      
      // Add to history
      setMoodHistory(prev => {
        const newHistory = [...prev, newMood];
        console.log('🎭 [FRONTEND DEBUG] Updated mood history length:', newHistory.length);
        return newHistory.slice(-10); // Keep last 10 entries
      });
    };

    // Listen for escalation alerts
    const handleEscalationAlert = (alert: EscalationAlert) => {
      // Match by customer phone for escalation alerts as well
      if (!conversation || alert.customerPhone !== conversation.customerPhone) return;
      
      console.log('🚨 Escalation alert received:', alert);
      
      setAlertData(alert);
      setShowAlert(true);
      
      // Call parent callback
      if (onEscalationAlert) {
        onEscalationAlert(alert);
      }
      
      // Auto-hide alert after 10 seconds
      setTimeout(() => {
        setShowAlert(false);
      }, 10000);
    };

    console.log('🎭 [FRONTEND DEBUG] Registering socket event listeners for conversation:', conversation?.id);
    
    socket.on('customer_mood_update', handleMoodUpdate);
    socket.on('mood_escalation_alert', handleEscalationAlert);
    
    console.log('🎭 [FRONTEND DEBUG] Socket event listeners registered successfully');

    return () => {
      socket.off('customer_mood_update', handleMoodUpdate);
      socket.off('mood_escalation_alert', handleEscalationAlert);
    };
  }, [conversation?.id, onEscalationAlert]);

  // Reset mood when conversation changes
  useEffect(() => {
    if (!conversation) {
      setCurrentMood(null);
      setShowAlert(false);
      setMoodHistory([]);
    }
  }, [conversation?.id]);

  if (!conversation) {
    return null; // Don't show indicator if no conversation
  }

  // Use neutral state if no mood data yet
  const moodToShow = currentMood || {
    category: 'NEUTRAL',
    score: 0.0,
    confidence: 0.0,
    escalationRecommended: false,
    trend: 'stable'
  };

  const config = moodConfigs[moodToShow.category as keyof typeof moodConfigs];
  if (!config) return null;

  const handleEscalation = () => {
    if (!alertData) return;
    
    // Emit escalation event to backend
    const socket = socketService.getSocket();
    if (socket) {
      socket.emit('escalate_conversation', {
        conversationId: conversation.id,
        reason: 'mood_based_escalation',
        moodData: moodToShow,
        timestamp: new Date().toISOString()
      });
    }
    
    setShowAlert(false);
    
    // Show success feedback
    alert('🤝 Conversa escalada para equipe especializada Trinks!\n\nVamos dar atenção especial a este empreendedor.');
  };

  const handleDismissAlert = () => {
    setShowAlert(false);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'improving': return '📈';
      case 'declining': return '📉';
      case 'stable': return '➡️';
      default: return '📊';
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'improving': return '#10B981';
      case 'declining': return '#EF4444';
      case 'stable': return '#6B7280';
      default: return '#6B7280';
    }
  };

  return (
    <div className="bg-white border border-gray-100 rounded-xl shadow-sm overflow-hidden">
      {/* Escalation Alert - Redesigned */}
      {showAlert && alertData && (
        <div className="bg-gradient-to-r from-red-50 via-red-50 to-pink-50 border-b border-red-200/50 p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
              <span className="text-lg">🚨</span>
            </div>
            <div className="flex-1">
              <div className="font-semibold text-red-900 text-sm">Atenção Especial Necessária</div>
              <div className="text-xs text-red-700 mt-0.5">
                Cliente {config.label.toLowerCase()} precisa de suporte imediato
              </div>
            </div>
            <div className="flex gap-2">
              <button 
                className="px-3 py-1.5 bg-red-500 text-white text-xs font-semibold rounded-lg hover:bg-red-600 transition-all duration-200 shadow-sm"
                onClick={handleEscalation}
                title="Escalar para equipe especializada"
              >
                Escalar Agora
              </button>
              <button 
                className="p-1.5 text-red-400 hover:bg-red-100 rounded-lg transition-colors"
                onClick={handleDismissAlert}
                title="Dispensar alerta"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Beautiful Header with Gradient */}
      <div className="p-5">
        <div 
          className="flex items-center justify-between cursor-pointer group"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center gap-4">
            {/* Mood Icon with Gradient Background */}
            <div className="relative">
              <div 
                className="w-14 h-14 rounded-2xl flex items-center justify-center text-2xl shadow-lg transition-transform group-hover:scale-105"
                style={{ 
                  background: `linear-gradient(135deg, ${config.bgColor} 0%, ${config.color}20 100%)`,
                  border: `2px solid ${config.color}30`
                }}
              >
                {config.icon}
              </div>
              {/* Trend indicator as badge */}
              <div 
                className="absolute -top-1 -right-1 w-6 h-6 rounded-full flex items-center justify-center text-xs shadow-md"
                style={{ backgroundColor: getTrendColor(moodToShow.trend) }}
              >
                <span className="text-white text-xs">{getTrendIcon(moodToShow.trend)}</span>
              </div>
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-1">
                <h4 className="font-bold text-gray-900 text-base">{config.label}</h4>
                <div 
                  className="px-2 py-1 rounded-full text-xs font-medium"
                  style={{ 
                    backgroundColor: `${config.color}15`,
                    color: config.color
                  }}
                >
                  {Math.round(moodToShow.confidence * 100)}% confiança
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-2">{config.description}</p>
              
              {/* Confidence Bar - Always visible */}
              <div className="w-full bg-gray-100 rounded-full h-2 overflow-hidden">
                <div 
                  className="h-full rounded-full transition-all duration-700 ease-out"
                  style={{ 
                    width: `${moodToShow.confidence * 100}%`,
                    background: `linear-gradient(90deg, ${config.color} 0%, ${config.color}80 100%)`
                  }}
                ></div>
              </div>
            </div>
          </div>
          
          {/* Expand Arrow */}
          <div className="ml-4">
            <svg 
              className={`w-5 h-5 text-gray-400 transition-all duration-300 group-hover:text-gray-600 ${isExpanded ? 'rotate-180' : ''}`}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>

        {/* Expanded Content - Beautiful and organized */}
        {isExpanded && (
          <div className="mt-6 space-y-5 border-t border-gray-100 pt-5">
            
            {/* Score and Trend Stats */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-xl p-3">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Score Humor</div>
                <div className="text-lg font-bold" style={{ color: config.color }}>
                  {moodToShow.score.toFixed(1)}
                </div>
              </div>
              <div className="bg-gray-50 rounded-xl p-3">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Tendência</div>
                <div className="flex items-center gap-2">
                  <span 
                    className="text-lg"
                    style={{ color: getTrendColor(moodToShow.trend) }}
                  >
                    {getTrendIcon(moodToShow.trend)}
                  </span>
                  <span className="text-sm font-medium text-gray-700">
                    {moodToShow.trend === 'improving' && 'Melhorando'}
                    {moodToShow.trend === 'declining' && 'Piorando'}
                    {moodToShow.trend === 'stable' && 'Estável'}
                  </span>
                </div>
              </div>
            </div>

            {/* Enhanced Response Suggestions */}
            <div>
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-gradient-to-br from-orange-400 to-orange-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-xs">💡</span>
                </div>
                <h5 className="text-sm font-bold text-gray-900">Sugestões da Trinks</h5>
              </div>
              
              <div className="space-y-3">
                {getResponseSuggestions(moodToShow.category).map((suggestion, index) => (
                  <div 
                    key={index} 
                    className="bg-gradient-to-r from-trinks-50 to-orange-50 border border-trinks-200/40 rounded-lg p-3 hover:shadow-sm transition-shadow"
                  >
                    <p className="text-xs text-trinks-800 leading-relaxed">{suggestion}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Emotional Indicators - if available */}
            {moodToShow.emotionalIndicators && moodToShow.emotionalIndicators.length > 0 && (
              <div>
                <h5 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Indicadores Detectados</h5>
                <div className="flex flex-wrap gap-2">
                  {moodToShow.emotionalIndicators.map((indicator, index) => (
                    <span 
                      key={index}
                      className="px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs"
                    >
                      {indicator}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {/* Status Footer */}
            <div className="bg-gray-50 rounded-lg p-3 text-center">
              <p className="text-xs text-gray-500">
                {currentMood ? (
                  <span className="flex items-center justify-center gap-1">
                    <svg className="w-3 h-3 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    Atualizado há {Math.round((Date.now() - new Date(currentMood.timestamp || Date.now()).getTime()) / 1000)}s
                  </span>
                ) : (
                  <span className="flex items-center justify-center gap-1">
                    <svg className="w-3 h-3 text-yellow-500 animate-pulse" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                    </svg>
                    Aguardando mensagem do cliente...
                  </span>
                )}
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );

};

export default MoodIndicator;
