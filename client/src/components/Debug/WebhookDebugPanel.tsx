import React, { useState, useEffect } from 'react';
import { ConversationLogs } from './ConversationLogs';
import './WebhookDebugPanel.css';

interface DebugLog {
  id: number;
  timestamp: string;
  type: 'webhook' | 'polling' | 'api_request' | 'api_response' | 'connection';
  event: string;
  data: any;
}

interface WebhookDebugPanelProps {
  isVisible: boolean;
  onToggle: () => void;
}

const WebhookDebugPanel: React.FC<WebhookDebugPanelProps> = ({ isVisible, onToggle }) => {
  const [logs, setLogs] = useState<DebugLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [expandedLogs, setExpandedLogs] = useState<Set<number>>(new Set());
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [activeTab, setActiveTab] = useState<'webhooks' | 'conversations'>('webhooks');

  const fetchLogs = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/debug/logs?limit=10');
      const data = await response.json();
      
      if (data.success) {
        setLogs(data.logs);
      }
    } catch (error) {
      console.error('Error fetching debug logs:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = async () => {
    try {
      const response = await fetch('/api/debug/logs/clear', { method: 'POST' });
      const data = await response.json();
      
      if (data.success) {
        setLogs([]);
        setExpandedLogs(new Set());
      }
    } catch (error) {
      console.error('Error clearing debug logs:', error);
    }
  };

  const toggleExpanded = (logId: number) => {
    const newExpanded = new Set(expandedLogs);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedLogs(newExpanded);
  };

  const getTypeIcon = (type: string, event: string) => {
    switch (type) {
      case 'webhook':
        return <span className="text-blue-500">📡</span>;
      case 'api_request':
        return <span className="text-green-500">📤</span>;
      case 'api_response':
        return <span className="text-green-600">📥</span>;
      case 'polling':
        return <span className="text-yellow-500">🔄</span>;
      case 'connection':
        return <span className="text-purple-500">🔌</span>;
      default:
        return <span className="text-gray-500">⏰</span>;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'webhook':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'api_request':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'api_response':
        return 'bg-green-50 text-green-700 border-green-100';
      case 'polling':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'connection':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatData = (data: any) => {
    try {
      return JSON.stringify(data, null, 2);
    } catch {
      return String(data);
    }
  };

  useEffect(() => {
    if (isVisible) {
      fetchLogs();
    }
  }, [isVisible]);

  useEffect(() => {
    if (autoRefresh && isVisible) {
      const interval = setInterval(fetchLogs, 3000); // Refresh every 3 seconds
      return () => clearInterval(interval);
    }
  }, [autoRefresh, isVisible]);

  return (
    <>
      {/* Debug Toggle Button */}
      <button 
        className="debug-toggle-btn"
        onClick={onToggle}
        title={`${isVisible ? 'Hide' : 'Show'} Debug Panel`}
      >
        🐛
      </button>

      {/* Debug Panel */}
      {isVisible && (
        <div className="webhook-debug-panel">
          <div className="debug-header">
            <div className="debug-title">
              <h3>🐛 Debug Panel</h3>
              <div className="debug-tabs">
                <button 
                  className={`tab-btn ${activeTab === 'webhooks' ? 'active' : ''}`}
                  onClick={() => setActiveTab('webhooks')}
                >
                  Webhooks & API
                </button>
                <button 
                  className={`tab-btn ${activeTab === 'conversations' ? 'active' : ''}`}
                  onClick={() => setActiveTab('conversations')}
                >
                  Logs de Conversa
                </button>
              </div>
              {activeTab === 'webhooks' && <span className="log-count">{logs.length} logs</span>}
            </div>
        
        {activeTab === 'webhooks' && (
          <div className="debug-controls">
            <label className="auto-refresh-toggle">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
              Auto-refresh
            </label>
            
            <button
              onClick={fetchLogs}
              disabled={isLoading}
              className="refresh-btn"
              title="Refresh logs"
            >
              <span className={isLoading ? 'spinning' : ''}>🔄</span>
            </button>
            
            <button
              onClick={clearLogs}
              className="clear-btn"
              title="Clear logs"
            >
              🗑️
            </button>
          </div>
        )}
      </div>

      {/* Tab Content */}
      {activeTab === 'webhooks' && (
        <div className="debug-logs">
          {logs.length === 0 ? (
            <div className="no-logs">
              <p>Nenhum log de debug encontrado</p>
              <small>Webhooks e requisições da API aparecerão aqui</small>
            </div>
          ) : (
            logs.map((log) => (
              <div key={log.id} className="debug-log-item">
                <div 
                  className="log-header"
                  onClick={() => toggleExpanded(log.id)}
                >
                  <div className="log-info">
                    <div className="log-meta">
                      {getTypeIcon(log.type, log.event)}
                      <span className={`log-type ${getTypeColor(log.type)}`}>
                        {log.type}
                      </span>
                      <span className="log-event">{log.event}</span>
                      <span className="log-timestamp">
                        {formatTimestamp(log.timestamp)}
                      </span>
                    </div>
                    
                    {log.data.url && (
                      <div className="log-url">
                        {log.data.method && (
                          <span className="http-method">{log.data.method}</span>
                        )}
                        {log.data.url}
                        {log.data.status && (
                          <span className={`status-code ${
                            log.data.status >= 200 && log.data.status < 300 ? 'success' :
                            log.data.status >= 400 ? 'error' : 'info'
                          }`}>
                            {log.data.status}
                          </span>
                        )}
                      </div>
                    )}
                    
                    {log.data.provider && (
                      <div className="log-provider">
                        Provider: {log.data.provider}
                      </div>
                    )}
                  </div>
                  
                  <div className="expand-icon">
                    {expandedLogs.has(log.id) ? '🔽' : '▶️'}
                  </div>
                </div>
                
                {expandedLogs.has(log.id) && (
                  <div className="log-details">
                    <pre>{formatData(log.data)}</pre>
                  </div>
                )}
              </div>
            ))
          )}
        </div>
      )}

      {activeTab === 'conversations' && (
        <div className="conversation-logs-container">
          <ConversationLogs />
        </div>
      )}
      </div>
      )}
    </>
  );
};

export default WebhookDebugPanel;