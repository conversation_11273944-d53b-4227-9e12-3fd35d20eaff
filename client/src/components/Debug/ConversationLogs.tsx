import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON><PERSON>er, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Badge } from '../ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '../ui/tabs';
import { Separator } from '../ui/separator';
import { 
  RefreshCw, 
  Download, 
  Search, 
  Clock, 
  MessageSquare, 
  Activity,
  Phone,
  Eye,
  EyeOff,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

interface ConversationMeta {
  conversationId: string;
  phoneNumber: string;
  startedAt: string;
  endedAt?: string;
  messageCount: number;
  duration?: number;
}

interface Message {
  id: string;
  timestamp: string;
  sender: 'customer' | 'ai';
  content: string;
  type?: string;
  transcription?: string;
  stage?: string;
  context?: any;
}

interface DetailedEvent {
  id: string;
  timestamp: string;
  type: 'claude_api_call' | 'trinks_api_call' | 'error';
  prompt?: any;
  response?: any;
  endpoint?: string;
  params?: any;
  success?: boolean;
  error?: string;
}

interface ConversationData {
  metadata: any;
  messages: Message[];
  detailedEvents: DetailedEvent[];
}

interface ActiveConversation {
  phone: string;
  conversationId: string;
  startedAt: string;
  lastActivity: string;
  messageCount: number;
}

export const ConversationLogs: React.FC = () => {
  const [conversations, setConversations] = useState<ConversationMeta[]>([]);
  const [activeConversations, setActiveConversations] = useState<ActiveConversation[]>([]);
  const [selectedConversation, setSelectedConversation] = useState<ConversationData | null>(null);
  const [searchPhone, setSearchPhone] = useState('');
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [showDetailed, setShowDetailed] = useState(false);
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());

  // Carregar conversas ativas
  const loadActiveConversations = async () => {
    try {
      const response = await fetch('/api/logs/active');
      const data = await response.json();
      if (data.success) {
        setActiveConversations(data.data.activeConversations);
      }
    } catch (error) {
      console.error('Erro ao carregar conversas ativas:', error);
    }
  };

  // Carregar estatísticas
  const loadStats = async () => {
    try {
      const response = await fetch('/api/logs/stats');
      const data = await response.json();
      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error);
    }
  };

  // Buscar conversas por telefone
  const searchConversations = async () => {
    if (!searchPhone.trim()) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/logs/conversations?phone=${encodeURIComponent(searchPhone)}`);
      const data = await response.json();
      if (data.success) {
        setConversations(data.data.conversations);
      }
    } catch (error) {
      console.error('Erro ao buscar conversas:', error);
    } finally {
      setLoading(false);
    }
  };

  // Carregar detalhes de uma conversa
  const loadConversationDetails = async (phone: string, conversationId: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/logs/conversation/${encodeURIComponent(phone)}/${conversationId}?includeDetailed=true`);
      const data = await response.json();
      if (data.success) {
        setSelectedConversation(data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar detalhes da conversa:', error);
    } finally {
      setLoading(false);
    }
  };

  // Exportar conversa
  const exportConversation = async (phone: string, conversationId: string, format: 'json' | 'csv') => {
    try {
      const response = await fetch(`/api/logs/export/${encodeURIComponent(phone)}/${conversationId}?format=${format}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `conversation_${conversationId}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Erro ao exportar conversa:', error);
    }
  };

  // Toggle evento expandido
  const toggleEvent = (eventId: string) => {
    const newExpanded = new Set(expandedEvents);
    if (newExpanded.has(eventId)) {
      newExpanded.delete(eventId);
    } else {
      newExpanded.add(eventId);
    }
    setExpandedEvents(newExpanded);
  };

  // Formatar timestamp
  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString('pt-BR');
  };

  // Formatar duração
  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  useEffect(() => {
    loadActiveConversations();
    loadStats();
    
    // Refresh automático a cada 30 segundos
    const interval = setInterval(() => {
      loadActiveConversations();
      loadStats();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Debug de Conversas</h2>
        <Button 
          onClick={() => {
            loadActiveConversations();
            loadStats();
          }}
          variant="outline"
          size="sm"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Atualizar
        </Button>
      </div>

      {/* Estatísticas */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Conversas Ativas</p>
                  <p className="text-2xl font-bold">{stats.activeConversations}</p>
                </div>
                <Activity className="w-8 h-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Uptime</p>
                  <p className="text-2xl font-bold">{formatDuration(stats.uptime * 1000)}</p>
                </div>
                <Clock className="w-8 h-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Memória</p>
                  <p className="text-2xl font-bold">{Math.round(stats.memoryUsage.heapUsed / 1024 / 1024)}MB</p>
                </div>
                <MessageSquare className="w-8 h-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Timestamp</p>
                  <p className="text-sm font-medium">{formatTimestamp(stats.timestamp)}</p>
                </div>
                <Clock className="w-8 h-8 text-orange-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="active" className="w-full">
        <TabsList>
          <TabsTrigger value="active">Conversas Ativas</TabsTrigger>
          <TabsTrigger value="search">Buscar Histórico</TabsTrigger>
          <TabsTrigger value="details">Detalhes da Conversa</TabsTrigger>
        </TabsList>

        {/* Conversas Ativas */}
        <TabsContent value="active">
          <Card>
            <CardHeader>
              <CardTitle>Conversas Ativas ({activeConversations.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {activeConversations.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">Nenhuma conversa ativa no momento</p>
                ) : (
                  activeConversations.map(conv => (
                    <div key={conv.conversationId} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Phone className="w-4 h-4" />
                        <div>
                          <p className="font-medium">{conv.phone}</p>
                          <p className="text-sm text-muted-foreground">
                            {conv.messageCount} mensagens • Iniciada: {formatTimestamp(conv.startedAt)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">Ativa</Badge>
                        <Button
                          onClick={() => loadConversationDetails(conv.phone, conv.conversationId)}
                          variant="outline"
                          size="sm"
                        >
                          Ver Detalhes
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Buscar Histórico */}
        <TabsContent value="search">
          <Card>
            <CardHeader>
              <CardTitle>Buscar Conversas por Telefone</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex space-x-2 mb-4">
                <Input
                  placeholder="Ex: +5521999999999"
                  value={searchPhone}
                  onChange={(e) => setSearchPhone(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && searchConversations()}
                />
                <Button onClick={searchConversations} disabled={loading}>
                  <Search className="w-4 h-4 mr-2" />
                  Buscar
                </Button>
              </div>

              <div className="space-y-3">
                {conversations.length === 0 ? (
                  <p className="text-muted-foreground text-center py-8">
                    {searchPhone ? 'Nenhuma conversa encontrada para este número' : 'Digite um número para buscar conversas'}
                  </p>
                ) : (
                  conversations.map(conv => (
                    <div key={conv.conversationId} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">ID: {conv.conversationId.slice(0, 8)}...</p>
                        <p className="text-sm text-muted-foreground">
                          {conv.messageCount} mensagens • {formatTimestamp(conv.startedAt)}
                          {conv.duration && ` • Duração: ${formatDuration(conv.duration)}`}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          onClick={() => loadConversationDetails(conv.phoneNumber, conv.conversationId)}
                          variant="outline"
                          size="sm"
                        >
                          Ver Detalhes
                        </Button>
                        <Button
                          onClick={() => exportConversation(conv.phoneNumber, conv.conversationId, 'json')}
                          variant="outline"
                          size="sm"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Detalhes da Conversa */}
        <TabsContent value="details">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Detalhes da Conversa</CardTitle>
                {selectedConversation && (
                  <div className="flex items-center space-x-2">
                    <Button
                      onClick={() => setShowDetailed(!showDetailed)}
                      variant="outline"
                      size="sm"
                    >
                      {showDetailed ? <EyeOff className="w-4 h-4 mr-2" /> : <Eye className="w-4 h-4 mr-2" />}
                      {showDetailed ? 'Ocultar Técnico' : 'Mostrar Técnico'}
                    </Button>
                    <Button
                      onClick={() => exportConversation(
                        selectedConversation.metadata.phoneNumber, 
                        selectedConversation.metadata.conversationId, 
                        'json'
                      )}
                      variant="outline"
                      size="sm"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Exportar
                    </Button>
                  </div>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {!selectedConversation ? (
                <p className="text-muted-foreground text-center py-8">
                  Selecione uma conversa para ver os detalhes
                </p>
              ) : (
                <div className="space-y-6">
                  {/* Metadata */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted rounded-lg">
                    <div>
                      <p className="text-sm font-medium">Telefone</p>
                      <p className="text-sm text-muted-foreground">{selectedConversation.metadata.phoneNumber}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Mensagens</p>
                      <p className="text-sm text-muted-foreground">{selectedConversation.metadata.messageCount}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Chamadas IA</p>
                      <p className="text-sm text-muted-foreground">{selectedConversation.metadata.aiCallCount}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Chamadas API</p>
                      <p className="text-sm text-muted-foreground">{selectedConversation.metadata.trinksApiCallCount}</p>
                    </div>
                  </div>

                  {/* Mensagens */}
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Fluxo de Mensagens</h3>
                    <div className="space-y-3 max-h-96 overflow-y-auto">
                      {selectedConversation.messages.map(msg => (
                        <div 
                          key={msg.id} 
                          className={`p-3 rounded-lg ${
                            msg.sender === 'customer' 
                              ? 'bg-blue-50 border-l-4 border-blue-500 ml-8' 
                              : 'bg-green-50 border-l-4 border-green-500 mr-8'
                          }`}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <Badge variant={msg.sender === 'customer' ? 'default' : 'secondary'}>
                              {msg.sender === 'customer' ? 'Cliente' : 'IA'}
                            </Badge>
                            <span className="text-xs text-muted-foreground">{formatTimestamp(msg.timestamp)}</span>
                          </div>
                          <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                          {msg.stage && (
                            <Badge variant="outline" className="mt-2 text-xs">
                              {msg.stage}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Eventos Detalhados */}
                  {showDetailed && (
                    <div>
                      <h3 className="text-lg font-semibold mb-3">Eventos Técnicos</h3>
                      <div className="space-y-3 max-h-96 overflow-y-auto">
                        {selectedConversation.detailedEvents.map(event => (
                          <div key={event.id} className="border rounded-lg">
                            <div 
                              className="flex items-center justify-between p-3 cursor-pointer hover:bg-muted"
                              onClick={() => toggleEvent(event.id)}
                            >
                              <div className="flex items-center space-x-2">
                                {expandedEvents.has(event.id) ? 
                                  <ChevronDown className="w-4 h-4" /> : 
                                  <ChevronRight className="w-4 h-4" />
                                }
                                <Badge variant={
                                  event.type === 'claude_api_call' ? 'default' :
                                  event.type === 'trinks_api_call' ? 'secondary' : 'destructive'
                                }>
                                  {event.type}
                                </Badge>
                                <span className="text-sm font-medium">
                                  {event.endpoint || 'Claude API'}
                                </span>
                              </div>
                              <span className="text-xs text-muted-foreground">
                                {formatTimestamp(event.timestamp)}
                              </span>
                            </div>
                            
                            {expandedEvents.has(event.id) && (
                              <div className="p-3 border-t bg-muted/50">
                                <pre className="text-xs overflow-x-auto whitespace-pre-wrap">
                                  {JSON.stringify(event, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};