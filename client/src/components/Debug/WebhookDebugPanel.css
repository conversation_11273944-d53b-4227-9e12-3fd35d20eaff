.debug-toggle-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 20px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
  z-index: 1001;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.debug-toggle-btn:hover {
  background: #2563eb;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.webhook-debug-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40vh;
  background: white;
  border-top: 2px solid #e5e7eb;
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
  flex-shrink: 0;
}

.debug-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.debug-title h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #1f2937;
}

.log-count {
  background: #e5e7eb;
  color: #6b7280;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.debug-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auto-refresh-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;
}

.auto-refresh-toggle input {
  accent-color: #3b82f6;
}

.refresh-btn,
.clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.refresh-btn:hover,
.clear-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.clear-btn {
  color: #dc2626;
}

.clear-btn:hover {
  background: #fee2e2;
  border-color: #fca5a5;
}

.debug-logs {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.no-logs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #9ca3af;
  text-align: center;
}

.no-logs p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.no-logs small {
  margin-top: 4px;
  font-size: 12px;
}

.debug-log-item {
  margin-bottom: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  overflow: hidden;
}

.log-header {
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.log-header:hover {
  background: #f9fafb;
}

.log-info {
  flex: 1;
}

.log-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.log-type {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  border: 1px solid;
}

.log-event {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.log-timestamp {
  font-size: 11px;
  color: #6b7280;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  margin-left: auto;
}

.log-url {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #4b5563;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  margin: 4px 0;
}

.http-method {
  background: #ddd6fe;
  color: #6b21a8;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-code {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
  margin-left: auto;
}

.status-code.success {
  background: #d1fae5;
  color: #065f46;
}

.status-code.error {
  background: #fee2e2;
  color: #991b1b;
}

.status-code.info {
  background: #dbeafe;
  color: #1e40af;
}

.log-provider {
  font-size: 11px;
  color: #6b7280;
  margin-top: 2px;
}

.expand-icon {
  color: #9ca3af;
  margin-left: 12px;
  flex-shrink: 0;
}

.log-details {
  border-top: 1px solid #f3f4f6;
  background: #f9fafb;
  padding: 12px;
}

.log-details pre {
  margin: 0;
  font-size: 11px;
  color: #374151;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.4;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

/* Custom scrollbar */
.debug-logs::-webkit-scrollbar {
  width: 6px;
}

.debug-logs::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.debug-logs::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.debug-logs::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive */
@media (max-width: 768px) {
  .webhook-debug-panel {
    height: 50vh;
  }
  
  .debug-header {
    padding: 10px 12px;
  }
  
  .debug-title h3 {
    font-size: 13px;
  }
  
  .log-header {
    padding: 10px;
  }
  
  .log-meta {
    flex-wrap: wrap;
    gap: 6px;
  }
  
  .log-url {
    font-size: 11px;
    overflow-x: auto;
  }
}

/* Tab styles */
.debug-tabs {
  display: flex;
  gap: 8px;
  margin: 0 16px;
}

.tab-btn {
  background: none;
  border: none;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s;
}

.tab-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.tab-btn.active {
  background: #3b82f6;
  color: white;
}

.conversation-logs-container {
  height: calc(40vh - 60px);
  overflow-y: auto;
  background: white;
}

/* Mobile tab adjustments */
@media (max-width: 768px) {
  .debug-tabs {
    flex-direction: column;
    gap: 4px;
    margin: 0 8px;
  }
  
  .tab-btn {
    padding: 6px 12px;
    font-size: 11px;
    text-align: left;
  }
  
  .conversation-logs-container {
    height: calc(40vh - 80px);
  }
}