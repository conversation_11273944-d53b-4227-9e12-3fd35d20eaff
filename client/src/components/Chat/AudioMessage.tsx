import React, { useState, useRef, useEffect, useCallback } from 'react';

interface AudioMessageProps {
  audioUrl: string;
  duration: number;
  sender: 'user' | 'ai';
  isLoading?: boolean;
  transcription?: string;
  isProcessing?: boolean;
}

const AudioMessage: React.FC<AudioMessageProps> = ({
  audioUrl,
  duration,
  sender,
  isLoading = false,
  transcription,
  isProcessing = false
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [audioLoaded, setAudioLoaded] = useState(false);
  const [audioError, setAudioError] = useState<string | null>(null);
  const [actualDuration, setActualDuration] = useState(duration);

  const audioRef = useRef<HTMLAudioElement>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);

  // Process audio URL to handle different formats
  const processAudioUrl = useCallback((url: string): string => {
    if (!url) return '';

    // If it's already a proper URL (starts with http or /api), use it as is
    if (url.startsWith('http') || url.startsWith('/api/audio/')) {
      return url;
    }

    // If it's base64 data, create a blob URL
    if (url.startsWith('data:')) {
      return url;
    }

    // If it's just base64 without data prefix, add it
    if (url.length > 100 && !url.includes('http')) {
      return `data:audio/ogg;base64,${url}`;
    }

    return url;
  }, []);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio || !audioUrl) return;

    // Reset states
    setAudioError(null);
    setAudioLoaded(false);
    setIsPlaying(false);
    setCurrentTime(0);

    const processedUrl = processAudioUrl(audioUrl);

    const handleLoadedData = () => {
      setAudioLoaded(true);
      setActualDuration(audio.duration || duration);
      console.log('🎤 AudioMessage: Audio loaded successfully', {
        duration: audio.duration,
        url: processedUrl.substring(0, 50) + '...'
      });
    };

    const handleLoadedMetadata = () => {
      if (audio.duration && audio.duration !== Infinity) {
        setActualDuration(audio.duration);
      }
    };

    const handleTimeUpdate = () => setCurrentTime(audio.currentTime);

    const handleEnded = () => {
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = (e: any) => {
      const errorMsg = `Failed to load audio: ${e.target?.error?.message || 'Unknown error'}`;
      console.error('🎤 AudioMessage: Error loading audio:', {
        error: e.target?.error,
        url: processedUrl.substring(0, 100) + '...',
        networkState: audio.networkState,
        readyState: audio.readyState
      });
      setAudioError(errorMsg);
      setAudioLoaded(false);
    };

    const handleCanPlay = () => {
      console.log('🎤 AudioMessage: Audio can play');
    };

    audio.addEventListener('loadeddata', handleLoadedData);
    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);
    audio.addEventListener('canplay', handleCanPlay);

    // Set the processed URL
    audio.src = processedUrl;

    // Log audio details for debugging
    console.log('🎤 AudioMessage: Setting up audio element', {
      originalUrl: audioUrl.substring(0, 50) + '...',
      processedUrl: processedUrl.substring(0, 50) + '...',
      duration,
      sender,
      isLoading,
      isProcessing
    });

    return () => {
      audio.removeEventListener('loadeddata', handleLoadedData);
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('canplay', handleCanPlay);

      // Clean up blob URLs to prevent memory leaks
      if (processedUrl.startsWith('blob:')) {
        URL.revokeObjectURL(processedUrl);
      }
    };
  }, [audioUrl, processAudioUrl]);

  const togglePlay = async () => {
    const audio = audioRef.current;
    if (!audio || !audioLoaded || audioError) return;

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
      } else {
        await audio.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('🎤 AudioMessage: Error playing audio:', error);
      setAudioError('Failed to play audio');
    }
  };

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    const audio = audioRef.current;
    const progressBar = progressBarRef.current;
    if (!audio || !progressBar || !audioLoaded || actualDuration === 0) return;

    const rect = progressBar.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * actualDuration;

    audio.currentTime = Math.max(0, Math.min(newTime, actualDuration));
    setCurrentTime(audio.currentTime);
  };

  const formatTime = (seconds: number) => {
    if (!seconds || !isFinite(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = actualDuration > 0 ? (currentTime / actualDuration) * 100 : 0;

  // Determine the display state
  const isDisabled = !audioLoaded || !!audioError || isLoading;
  const showError = !!audioError && !isLoading && !isProcessing;

  return (
    <div className="space-y-2">
      {/* Audio Player */}
      <div className={`flex items-center space-x-3 p-3 rounded-2xl min-w-[200px] ${
        sender === 'user'
          ? 'bg-trinks-500 text-white'
          : 'bg-white border border-gray-200 text-gray-800'
      } ${showError ? 'border-red-300 bg-red-50' : ''}`}>
        <audio ref={audioRef} preload="metadata" />

        {/* Play/Pause Button */}
        <button
          onClick={togglePlay}
          disabled={isDisabled}
          className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center transition-all duration-200 ${
            sender === 'user'
              ? 'bg-white/20 hover:bg-white/30 text-white'
              : 'bg-trinks-100 hover:bg-trinks-200 text-trinks-600'
          } ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'} ${
            showError ? 'bg-red-100 text-red-600' : ''
          }`}
        >
          {isLoading || isProcessing ? (
            <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
            </svg>
          ) : showError ? (
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
            </svg>
          ) : isPlaying ? (
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <rect x="6" y="4" width="4" height="16" />
              <rect x="14" y="4" width="4" height="16" />
            </svg>
          ) : (
            <svg className="w-5 h-5 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z" />
            </svg>
          )}
        </button>

        {/* Waveform/Progress */}
        <div className="flex-1">
          {showError ? (
            <div className="text-xs text-red-600 px-2">
              {audioError}
            </div>
          ) : (
            <>
              <div
                ref={progressBarRef}
                onClick={handleSeek}
                className={`h-8 flex items-center justify-center rounded cursor-pointer transition-all duration-200 ${
                  sender === 'user' ? 'bg-white/10 hover:bg-white/20' : 'bg-gray-100 hover:bg-gray-200'
                } ${isDisabled ? 'cursor-not-allowed' : ''}`}
              >
                {/* Enhanced waveform visualization with progress */}
                <div className="flex items-center space-x-1 h-full px-2 relative w-full">
                  {/* Progress overlay */}
                  <div
                    className={`absolute left-0 top-0 h-full rounded transition-all duration-100 ${
                      sender === 'user' ? 'bg-white/20' : 'bg-trinks-200'
                    }`}
                    style={{ width: `${progressPercentage}%` }}
                  />

                  {/* Waveform bars */}
                  {[...Array(16)].map((_, i) => {
                    const barHeight = Math.sin(i * 0.5) * 30 + 40; // More natural waveform
                    const isActive = progressPercentage > (i * 6.25);
                    return (
                      <div
                        key={i}
                        className={`w-1 bg-current rounded-full transition-all duration-100 relative z-10 ${
                          isActive ? 'opacity-100' : 'opacity-30'
                        }`}
                        style={{
                          height: `${barHeight}%`,
                        }}
                      />
                    );
                  })}
                </div>
              </div>

              {/* Time Display */}
              <div className={`text-xs mt-1 flex justify-between ${
                sender === 'user' ? 'text-white/70' : 'text-gray-500'
              }`}>
                <span>{formatTime(currentTime)}</span>
                <span>{formatTime(actualDuration)}</span>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Transcription Display */}
      {(transcription || isProcessing) && (
        <div className={`p-3 rounded-lg text-sm max-w-xs ${
          sender === 'user'
            ? 'bg-trinks-100 text-trinks-800 border border-trinks-200'
            : 'bg-gray-50 text-gray-700 border border-gray-200'
        }`}>
          <div className="flex items-center space-x-2 mb-2">
            <svg className="w-4 h-4 opacity-75" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
            </svg>
            <span className="font-medium opacity-75">Transcrição:</span>
            {isProcessing && (
              <svg className="animate-spin w-3 h-3 opacity-50" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            )}
          </div>
          <div className="leading-relaxed">
            {isProcessing && !transcription ? (
              <div className="italic opacity-60">
                Processando transcrição...
              </div>
            ) : transcription ? (
              <div className="italic">
                "{transcription}"
              </div>
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioMessage;