import React, { useState } from 'react';

interface ConversationHistoryLoaderProps {
  phoneNumber: string;
  onLoadHistory: (phoneNumber: string, limit: number) => Promise<void>;
  isLoading?: boolean;
  hasHistory?: boolean;
}

const ConversationHistoryLoader: React.FC<ConversationHistoryLoaderProps> = ({
  phoneNumber,
  onLoadHistory,
  isLoading = false,
  hasHistory = true
}) => {
  const [loadingHistory, setLoadingHistory] = useState(false);

  const handleLoadHistory = async (limit: number) => {
    if (loadingHistory || isLoading) return;
    
    setLoadingHistory(true);
    try {
      await onLoadHistory(phoneNumber, limit);
    } catch (error) {
      console.error('Error loading conversation history:', error);
    } finally {
      setLoadingHistory(false);
    }
  };

  if (!hasHistory) {
    return null;
  }

  return (
    <div className="flex justify-center py-4 border-b border-gray-100">
      <div className="flex flex-col items-center space-y-2">
        <div className="text-xs text-gray-500 mb-2">
          📚 Histórico de conversas anteriores disponível
        </div>
        
        <div className="flex space-x-2">
          <button
            onClick={() => handleLoadHistory(10)}
            disabled={loadingHistory || isLoading}
            className="px-4 py-2 bg-trinks-100 hover:bg-trinks-200 text-trinks-700 text-sm rounded-full transition-all duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loadingHistory ? (
              <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            )}
            <span>Últimas 10 mensagens</span>
          </button>
          
          <button
            onClick={() => handleLoadHistory(25)}
            disabled={loadingHistory || isLoading}
            className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-all duration-200 flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loadingHistory ? (
              <svg className="animate-spin w-4 h-4" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            ) : (
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            )}
            <span>Últimas 25 mensagens</span>
          </button>
        </div>
        
        <div className="text-xs text-gray-400 text-center max-w-sm">
          Carregue mensagens anteriores desta conversa para ter mais contexto
        </div>
      </div>
    </div>
  );
};

export default ConversationHistoryLoader;
