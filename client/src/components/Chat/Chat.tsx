import React, { useState, useRef, useEffect } from 'react';
import { Conversation } from '../../types/index';
import MessageBubble from './MessageBubble';
import TypingIndicator from './TypingIndicator';
import AudioRecorder from './AudioRecorder';
import ConversationHistoryLoader from './ConversationHistoryLoader';

interface ChatProps {
  conversation: Conversation;
  onSendMessage: (message: string) => void;
  onSendAudio: (audioBlob: Blob, duration: number) => void;
  isTyping: boolean;
  onTransferToHuman?: () => void;
  isAiMode?: boolean;
  onLoadMoreWhatsAppHistory?: (phoneNumber: string) => void;
  onLoadConversationHistory?: (phoneNumber: string, limit: number) => Promise<void>;
}

const Chat: React.FC<ChatProps> = ({ conversation, onSendMessage, onSendAudio, isTyping, onTransferToHuman, isAiMode = true, onLoadMoreWhatsAppHistory, onLoadConversationHistory }) => {
  const [messageInput, setMessageInput] = useState('');
  const [visibleMessageCount, setVisibleMessageCount] = useState(10);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    
    // When new messages arrive, always show the latest ones
    if (conversation.messages.length > visibleMessageCount) {
      // If we're showing old messages and new ones arrive, keep showing the same amount
      // But if we're at the bottom (showing latest), show new messages
      const isAtBottom = visibleMessageCount >= conversation.messages.length - 5;
      if (isAtBottom) {
        setVisibleMessageCount(Math.max(10, conversation.messages.length));
      }
    }
    
    scrollToBottom();
  }, [conversation.messages, isTyping]);
  
  // Reset visible message count when conversation changes
  useEffect(() => {
    setVisibleMessageCount(10);
  }, [conversation.id]);

  useEffect(() => {
    // Focus input when conversation changes
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, [conversation.id]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (messageInput.trim() && !isTyping) {
      onSendMessage(messageInput.trim());
      setMessageInput('');
      
      // Return focus to input after sending
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.focus();
        }
      }, 100);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(e);
    }
  };

  const formatTime = (date: Date) => {
    return new Date(date).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const loadMoreMessages = () => {
    const newCount = Math.min(visibleMessageCount + 10, conversation.messages.length);
    setVisibleMessageCount(newCount);
  };

  // Get the messages to display (last N messages)
  const visibleMessages = conversation.messages.slice(-visibleMessageCount);
  const hasMoreMessages = conversation.messages.length > visibleMessageCount;
  
  // Check for WhatsApp messages
  const hasWhatsAppMessages = conversation.messages.some(msg => (msg as any).whatsappMessage);
  const whatsAppMessageCount = conversation.messages.filter(msg => (msg as any).whatsappMessage).length;
  const totalWhatsAppMessages = conversation.totalWhatsAppMessages || 0;
  const canLoadMoreWhatsApp = hasWhatsAppMessages && totalWhatsAppMessages > whatsAppMessageCount;

  const handleLoadMoreWhatsApp = () => {
    if (onLoadMoreWhatsAppHistory) {
      onLoadMoreWhatsAppHistory(conversation.customerPhone);
    }
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Chat Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center">
          <div className="w-12 h-12 bg-gradient-to-br from-trinks-400 to-trinks-600 rounded-full flex items-center justify-center text-white font-semibold text-lg shadow-sm">
            {(conversation.customerName || 'U').charAt(0).toUpperCase()}
          </div>
          <div className="ml-4 flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 text-lg truncate">
              {conversation.customerName && conversation.customerName !== conversation.customerPhone 
                ? conversation.customerName 
                : conversation.customerPhone || 'Cliente especial'}
            </h3>
            <div className="flex items-center space-x-2">
              <p className="text-sm text-gray-500 flex items-center shrink-0">
                <span className="w-2 h-2 bg-trinks-green-500 rounded-full mr-1"></span>
                Na Trinks
              </p>
              <div className={`px-3 py-1 rounded-full text-xs font-medium shrink-0 ${
                isAiMode 
                  ? 'bg-blue-100 text-blue-700' 
                  : 'bg-trinks-green-100 text-trinks-green-700'
              }`}>
                <span className="truncate">{isAiMode ? '💬 Atendimento Trinks ativo' : '✨ Especialista Trinks ao vivo'}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">{/* Empty div to maintain layout */}
            
            <button className="p-2 hover:bg-gray-200 rounded-full transition-colors">
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </button>
            <button className="p-2 hover:bg-gray-200 rounded-full transition-colors">
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <div className="flex-1 overflow-y-auto px-6 py-6 space-y-4"
           style={{
             backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f3f4f6' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40z'/%3E%3C/g%3E%3C/svg%3E")`,
           }}>
        {conversation.messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <div className="text-4xl mb-4">💄</div>
            <p className="text-lg font-medium text-trinks-500">Vem dar um up no seu atendimento!</p>
            <p className="text-sm text-gray-600">Pronto para receber seu cliente especial?</p>
          </div>
        ) : (
          <>
            {/* Conversation History Loader */}
            {onLoadConversationHistory && conversation.customerPhone && (
              <ConversationHistoryLoader
                phoneNumber={conversation.customerPhone}
                onLoadHistory={onLoadConversationHistory}
                isLoading={isTyping}
                hasHistory={true}
              />
            )}

            {/* Load More WhatsApp Button */}
            {canLoadMoreWhatsApp && (
              <div className="flex justify-center mb-4">
                <button
                  onClick={handleLoadMoreWhatsApp}
                  className="px-4 py-2 bg-green-100 hover:bg-green-200 text-green-700 text-sm rounded-full transition-colors duration-200 flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                  </svg>
                  <span>📱 Carregar mais do WhatsApp ({totalWhatsAppMessages - whatsAppMessageCount} anteriores)</span>
                </button>
              </div>
            )}
            
            {/* Load More Local Messages Button */}
            {hasMoreMessages && !canLoadMoreWhatsApp && (
              <div className="flex justify-center mb-4">
                <button
                  onClick={loadMoreMessages}
                  className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 text-sm rounded-full transition-colors duration-200 flex items-center space-x-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                  </svg>
                  <span>Carregar mais mensagens ({conversation.messages.length - visibleMessageCount} anteriores)</span>
                </button>
              </div>
            )}
            
            {visibleMessages.map((message) => (
              <MessageBubble
                key={message.id}
                message={message}
                time={formatTime(message.timestamp)}
              />
            ))}
            
            {isTyping && <TypingIndicator />}
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Message Input */}
      <div className="bg-white border-t border-gray-200 px-6 py-4">
        <form onSubmit={handleSendMessage} className="flex items-end space-x-3">
          <div className="flex-1">
            <input
              ref={inputRef}
              type="text"
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={isAiMode ? "A Trinks está cuidando do seu cliente..." : "Sua resposta carinhosa para o cliente..."}
              disabled={isTyping || isAiMode}
              className={`w-full px-5 py-3 border border-gray-200 rounded-full focus:outline-none focus:border-trinks-400 transition-all ${
                isTyping ? 'bg-gray-200 cursor-not-allowed' : 'bg-white hover:shadow-md focus:shadow-md'
              }`}
            />
          </div>
          
          {/* Audio Recorder */}
          <AudioRecorder 
            onAudioRecorded={onSendAudio}
            isDisabled={isTyping || isAiMode}
          />
          
          <button
            type="submit"
            disabled={!messageInput.trim() || isTyping || isAiMode}
            className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
              messageInput.trim() && !isTyping && !isAiMode
                ? 'bg-trinks-500 text-white hover:bg-trinks-600 transform hover:scale-105'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
              />
            </svg>
          </button>
        </form>
        
        {/* Minimalist Transfer Button */}
        <div className="mt-2 flex justify-center">
          {isAiMode ? (
            <button 
              onClick={onTransferToHuman}
              className="px-3 py-1 text-xs text-gray-600 hover:text-trinks-green-600 hover:bg-trinks-green-50 rounded-full transition-colors"
              title="Transferir para especialista Trinks"
            >
              ✨ Chamar especialista Trinks
            </button>
          ) : (
            <button 
              onClick={onTransferToHuman}
              className="px-3 py-1 text-xs text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-full transition-colors"
              title="Voltar para atendimento automático"
            >
              💬 Voltar para atendimento Trinks
            </button>
          )}
        </div>
        
        {isTyping && (
          <div className="mt-2 text-sm text-gray-500 flex items-center">
            <span className="animate-pulse">A Trinks está preparando uma resposta especial...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default Chat;