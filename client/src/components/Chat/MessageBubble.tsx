import React, { useState } from 'react';
import { Message } from '../../types/index';
import AudioMessage from './AudioMessage';
import DebugPanel from './DebugPanel';

interface MessageBubbleProps {
  message: Message;
  time: string;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({ message, time }) => {
  // WhatsApp UI convention for SALON perspective:
  // - Customer messages should be on LEFT (received from customer)
  // - Salon/AI messages should be on RIGHT (sent by salon/AI)
  // - System messages should be CENTERED
  const isCustomer = message.sender === 'user' || message.sender === 'customer';
  const isSalonOrAI = message.sender === 'ai' || message.sender === 'salon' || message.sender === 'admin';
  const isSystem = message.sender === 'system';
  const [showDebug, setShowDebug] = useState(false);
  
  // Show debug only in development mode
  const isDevelopment = process.env.NODE_ENV === 'development';

  

  // System messages are centered
  if (isSystem) {
    return (
      <div className="flex justify-center message-enter">
        <div className="bg-gray-100 text-gray-600 px-3 py-2 rounded-full text-xs max-w-xs text-center">
          {message.content}
        </div>
      </div>
    );
  }

  return (
    <div className={`flex ${isSalonOrAI ? 'justify-end' : 'justify-start'} message-enter`}>
      {message.type === 'audio' && !isSystem ? (
        <div className="max-w-xs lg:max-w-md">
          <AudioMessage
            audioUrl={message.audioUrl || ''}
            duration={message.audioDuration || 0}
            sender={message.sender as 'user' | 'ai'}
            isLoading={message.isProcessing}
            transcription={message.audioTranscription}
          />
          
          <div className={`text-xs mt-1 ${
            isSalonOrAI ? 'text-gray-600' : 'text-gray-500'
          } flex items-center ${isSalonOrAI ? 'justify-end' : 'justify-start'}`}>
            <span>{time}</span>
            {isSalonOrAI && (
              <span className="ml-1">
                {message.status === 'sent' && '✓'}
                {message.status === 'delivered' && '✓✓'}
                {message.status === 'read' && (
                  <span className="text-blue-500">✓✓</span>
                )}
              </span>
            )}
          </div>
        </div>
      ) : (
        <div className={`max-w-xs lg:max-w-md px-4 py-3 rounded-2xl ${
          isSalonOrAI
            ? 'bg-blue-500 text-white'
            : 'bg-gray-100 text-gray-800 border border-gray-200'
        }`}>
          <div className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </div>
          <div className={`text-xs mt-1 ${
            isSalonOrAI ? 'text-blue-100' : 'text-gray-500'
          } flex items-center justify-between`}>
            <div className="flex items-center">
              {/* Debug Button - only for AI messages in development */}
              {isDevelopment && isSalonOrAI && (
                <button
                  onClick={() => setShowDebug(true)}
                  className="mr-2 p-1 hover:bg-blue-600 rounded transition-colors"
                  title="Ver debug info da IA"
                >
                  <svg className="w-3 h-3 text-white hover:text-blue-100" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </button>
              )}
            </div>
            <div className="flex items-center">
              <span>{time}</span>
              {isSalonOrAI && (
                <span className="ml-1">
                  {message.status === 'sent' && '✓'}
                  {message.status === 'delivered' && '✓✓'}
                  {message.status === 'read' && (
                    <span className="text-blue-200">✓✓</span>
                  )}
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Debug Panel - only in development */}
      {isDevelopment && showDebug && (
        <DebugPanel 
          message={message} 
          onClose={() => setShowDebug(false)} 
        />
      )}
    </div>
  );
};

export default MessageBubble;