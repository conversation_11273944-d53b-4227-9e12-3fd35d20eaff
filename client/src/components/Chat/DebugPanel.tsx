import React, { useState } from 'react';
import { Message } from '../../types/index';

interface DebugPanelProps {
  message: Message;
  onClose: () => void;
}

const DebugPanel: React.FC<DebugPanelProps> = ({ message, onClose }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'prompt' | 'context' | 'response' | 'trinks' | 'audio'>('overview');
  
  if (!message.debugInfo) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-md">
          <h3 className="text-lg font-semibold mb-2">Debug Info</h3>
          <p className="text-gray-600">Nenhuma informação de debug disponível para esta mensagem.</p>
          <button 
            onClick={onClose}
            className="mt-4 bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
          >
            <PERSON><PERSON>r
          </button>
        </div>
      </div>
    );
  }

  const debug = message.debugInfo;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="border-b p-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold">Debug Info - IA Response</h3>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b">
          <nav className="flex space-x-8 px-4" aria-label="Tabs">
            {[
              { id: 'overview', name: 'Visão Geral' },
              { id: 'prompt', name: 'System Prompt' },
              { id: 'context', name: 'Contexto' },
              { id: 'response', name: 'Resposta Raw' },
              { id: 'trinks', name: 'API Trinks' },
              ...(debug.audioProcessed ? [{ id: 'audio', name: 'Áudio 🎤' }] : [])
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-trinks-500 text-trinks-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.name}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto p-4">
          {activeTab === 'overview' && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 p-3 rounded">
                  <h4 className="font-medium text-gray-900 mb-1">Status da IA</h4>
                  <div className="flex items-center space-x-4">
                    {/* Status principal */}
                    <div className="flex items-center">
                      <span className={`w-2 h-2 rounded-full mr-2 ${
                        debug.apiUsed ? 'bg-green-500' : 'bg-yellow-500'
                      }`}></span>
                      <span className="text-sm">
                        {debug.apiUsed ? 'Claude API' : 'Mock Response'}
                      </span>
                    </div>
                    
                    {/* Status de áudio (SE aplicável) */}
                    {debug.audioProcessed && (
                      <div className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${
                          debug.transcriptionSuccess ? 'bg-green-500' : 'bg-red-500'
                        }`}></span>
                        <span className="text-sm">
                          🎤 {debug.transcriptionEngine || 'N/A'}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                
                {debug.model && (
                  <div className="bg-gray-50 p-3 rounded">
                    <h4 className="font-medium text-gray-900 mb-1">Modelo</h4>
                    <span className="text-sm text-gray-600">{debug.model}</span>
                  </div>
                )}
              </div>

              {debug.lastMessage && (
                <div className="bg-blue-50 p-3 rounded">
                  <h4 className="font-medium text-gray-900 mb-1">Última Mensagem do Cliente</h4>
                  <p className="text-sm text-gray-600 italic">"{debug.lastMessage}"</p>
                </div>
              )}

              {debug.stage && (
                <div className="bg-purple-50 p-3 rounded">
                  <h4 className="font-medium text-gray-900 mb-1">Estágio da Conversa</h4>
                  <span className="text-sm text-gray-600">{debug.stage}</span>
                </div>
              )}

              {debug.customerData && (
                <div className="bg-green-50 p-3 rounded">
                  <h4 className="font-medium text-gray-900 mb-1">Dados do Cliente</h4>
                  <div className="text-sm text-gray-600">
                    <p><strong>Nome:</strong> {debug.customerData.nome}</p>
                    <p><strong>Tem Histórico:</strong> {debug.customerData.hasHistory ? 'Sim' : 'Não'}</p>
                  </div>
                </div>
              )}

              <div className="bg-gray-50 p-3 rounded">
                <h4 className="font-medium text-gray-900 mb-1">Resposta Gerada</h4>
                <p className="text-sm text-gray-600 whitespace-pre-wrap">"{message.content}"</p>
              </div>

              {debug.appointmentCreated && (
                <div className={`p-3 rounded ${
                  debug.appointmentCreated.success ? 'bg-green-50' : 'bg-red-50'
                }`}>
                  <h4 className="font-medium text-gray-900 mb-1">Status do Agendamento</h4>
                  <div className="flex items-center mb-2">
                    <span className={`w-2 h-2 rounded-full mr-2 ${
                      debug.appointmentCreated.success ? 'bg-green-500' : 'bg-red-500'
                    }`}></span>
                    <span className="text-sm font-medium">
                      {debug.appointmentCreated.success ? 'Criado com sucesso' : 'Falha na criação'}
                    </span>
                  </div>
                  {debug.appointmentCreated.success && debug.appointmentCreated.appointmentId && (
                    <p className="text-sm text-gray-600">
                      <strong>ID do Agendamento:</strong> {debug.appointmentCreated.appointmentId}
                    </p>
                  )}
                  {debug.appointmentCreated.customerCreated && (
                    <p className="text-sm text-gray-600">
                      <strong>Cliente:</strong> Criado automaticamente
                    </p>
                  )}
                  {debug.appointmentCreated.error && (
                    <p className="text-sm text-red-600">
                      <strong>Erro:</strong> {debug.appointmentCreated.error}
                    </p>
                  )}
                </div>
              )}
            </div>
          )}

          {activeTab === 'prompt' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">System Prompt Enviado para IA</h4>
              {debug.systemPrompt ? (
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto whitespace-pre-wrap">
                  {debug.systemPrompt}
                </pre>
              ) : (
                <p className="text-gray-500 italic">System prompt não disponível (resposta mock)</p>
              )}
            </div>
          )}

          {activeTab === 'context' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Contexto da Conversa</h4>
              {debug.messagesContext ? (
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
                  {JSON.stringify(debug.messagesContext, null, 2)}
                </pre>
              ) : (
                <p className="text-gray-500 italic">Contexto não disponível (resposta mock)</p>
              )}
            </div>
          )}

          {activeTab === 'response' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Resposta Raw da API</h4>
              {debug.rawResponse ? (
                <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto">
                  {JSON.stringify(debug.rawResponse, null, 2)}
                </pre>
              ) : (
                <p className="text-gray-500 italic">Resposta raw não disponível (resposta mock)</p>
              )}
            </div>
          )}

          {activeTab === 'trinks' && (
            <div>
              <h4 className="font-medium text-gray-900 mb-2">Requisições API Trinks</h4>
              {debug.trinksApiCalls && debug.trinksApiCalls.length > 0 ? (
                <div className="space-y-4">
                  {debug.trinksApiCalls.map((call: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4 bg-gray-50">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 rounded text-xs font-medium ${
                            call.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {call.method}
                          </span>
                          <span className="text-sm font-mono text-gray-600">{call.endpoint}</span>
                          {call.cacheInfo?.usedCache && (
                            <span className="px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 flex items-center">
                              💾 Cache
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          {call.responseTime && (
                            <span className="text-xs text-gray-500">{call.responseTime}ms</span>
                          )}
                          <span className={`w-2 h-2 rounded-full ${
                            call.success ? 'bg-green-500' : 'bg-red-500'
                          }`}></span>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-2">{call.purpose}</p>
                      
                      {call.cacheInfo && (
                        <div className="mb-2 p-2 bg-blue-50 border border-blue-200 rounded">
                          <h6 className="text-xs font-medium text-blue-700 mb-1 flex items-center">
                            <span className="mr-1">💾</span> Informações de Cache:
                          </h6>
                          <div className="text-xs space-y-1">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Usado Cache:</span>
                              <span className={call.cacheInfo.usedCache ? 'text-green-600 font-medium' : 'text-gray-600'}>
                                {call.cacheInfo.usedCache ? '✅ Sim' : '❌ Não'}
                              </span>
                            </div>
                            {call.cacheInfo.cacheAge !== null && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">Idade do Cache:</span>
                                <span className="text-gray-600">{call.cacheInfo.cacheAge} minutos</span>
                              </div>
                            )}
                            <div className="flex justify-between">
                              <span className="text-gray-600">Cache Válido:</span>
                              <span className={call.cacheInfo.cacheValid ? 'text-green-600' : 'text-red-600'}>
                                {call.cacheInfo.cacheValid ? '✅ Sim' : '❌ Não'}
                              </span>
                            </div>
                            {call.cacheInfo.cacheServices && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">Serviços em Cache:</span>
                                <span className="text-gray-600">{call.cacheInfo.cacheServices}</span>
                              </div>
                            )}
                            {call.cacheInfo.savedToCache && (
                              <div className="flex justify-between">
                                <span className="text-gray-600">Salvo no Cache:</span>
                                <span className="text-green-600 font-medium">✅ Sim</span>
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                      
                      {call.request && (
                        <div className="mb-2">
                          <h6 className="text-xs font-medium text-blue-700 mb-1 flex items-center">
                            <span className="mr-1">📤</span> Requisição:
                          </h6>
                          <pre className="bg-blue-50 p-2 rounded text-xs border border-blue-200 max-h-32 overflow-auto">
                            {JSON.stringify(call.request, null, 2)}
                          </pre>
                        </div>
                      )}
                      
                      {call.response && (
                        <div className="mb-2">
                          <h6 className={`text-xs font-medium mb-1 flex items-center ${
                            call.success ? 'text-green-700' : 'text-red-700'
                          }`}>
                            <span className="mr-1">{call.success ? '📥' : '❌'}</span> 
                            Resposta {call.response.status && `(${call.response.status})`}:
                          </h6>
                          <pre className={`p-2 rounded text-xs border max-h-32 overflow-auto ${
                            call.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                          }`}>
                            {typeof call.response === 'string' ? call.response : JSON.stringify(call.response, null, 2)}
                          </pre>
                        </div>
                      )}
                      
                      {call.parameters && (
                        <div className="mb-2">
                          <h6 className="text-xs font-medium text-gray-700 mb-1">Parâmetros (legacy):</h6>
                          <pre className="bg-white p-2 rounded text-xs border">
                            {JSON.stringify(call.parameters, null, 2)}
                          </pre>
                        </div>
                      )}
                      
                      <div className="grid grid-cols-2 gap-2 text-xs">
                        <div>
                          <span className="font-medium text-gray-700">Início:</span>
                          <span className="ml-1 text-gray-600">{call.startTime ? new Date(call.startTime).toLocaleTimeString() : 'N/A'}</span>
                        </div>
                        {call.endTime && (
                          <div>
                            <span className="font-medium text-gray-700">Fim:</span>
                            <span className="ml-1 text-gray-600">{new Date(call.endTime).toLocaleTimeString()}</span>
                          </div>
                        )}
                      </div>
                      
                      {call.resultCount !== undefined && (
                        <div className="mt-2">
                          <span className="text-xs font-medium text-gray-700">Resultados: </span>
                          <span className="text-xs text-gray-600">{call.resultCount} itens</span>
                        </div>
                      )}
                      
                      {call.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                          <span className="text-xs font-medium text-red-700">Erro: </span>
                          <span className="text-xs text-red-600">{call.error}</span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 italic">Nenhuma requisição para API Trinks foi feita durante a geração desta resposta.</p>
              )}
            </div>
          )}

          {activeTab === 'audio' && debug.audioProcessed && (
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                  <span className="mr-2">🎤</span>
                  Processamento de Áudio
                </h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Engine de Transcrição:</span>
                      <div className="mt-1">
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          debug.transcriptionEngine === 'openai-whisper' 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {debug.transcriptionEngine || 'N/A'}
                        </span>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Status da Transcrição:</span>
                      <div className="mt-1 flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${
                          debug.transcriptionSuccess ? 'bg-green-500' : 'bg-red-500'
                        }`}></span>
                        <span className={`text-sm font-medium ${
                          debug.transcriptionSuccess ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {debug.transcriptionSuccess ? 'Sucesso' : 'Falhou'}
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  {debug.transcriptionText && (
                    <div>
                      <span className="text-sm font-medium text-gray-700">Texto Transcrito:</span>
                      <div className="mt-2 p-3 bg-white border border-gray-200 rounded-lg">
                        <p className="text-sm text-gray-800 italic">
                          "{debug.transcriptionText}"
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {!debug.transcriptionSuccess && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                      <div className="flex items-start">
                        <span className="text-yellow-500 text-lg mr-2">⚠️</span>
                        <div>
                          <h5 className="font-medium text-yellow-900 mb-1">Transcrição Falhou</h5>
                          <p className="text-yellow-800 text-sm">
                            A transcrição do áudio não foi bem-sucedida. Verifique se a API do OpenAI está configurada corretamente.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t p-4 flex justify-end">
          <button 
            onClick={onClose}
            className="bg-gray-500 text-white px-4 py-2 rounded hover:bg-gray-600"
          >
            Fechar
          </button>
        </div>
      </div>
    </div>
  );
};

export default DebugPanel;