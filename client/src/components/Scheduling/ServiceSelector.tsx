import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, Sparkles } from 'lucide-react';
import { Service } from './AppointmentScheduler';

interface ServiceSelectorProps {
  services: Service[];
  selectedService: Service | null;
  onServiceChange: (service: Service | null) => void;
  loading?: boolean;
}

const ServiceSelector: React.FC<ServiceSelectorProps> = ({
  services,
  selectedService,
  onServiceChange,
  loading = false
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Sanitize service name for display (XSS prevention)
  const sanitizeName = (name: string) => {
    return name.replace(/[<>]/g, '');
  };

  // Format price for display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  // Format duration for display  
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0 && mins > 0) {
      return `${hours}h ${mins}min`;
    } else if (hours > 0) {
      return `${hours}h`;
    } else {
      return `${mins}min`;
    }
  };

  // Enhanced service descriptions with Trinks branding
  const getServiceDescription = (serviceName: string) => {
    const descriptions: Record<string, string> = {
      'corte feminino': 'Corte que realça sua beleza ✨',
      'corte masculino': 'Estilo que combina com você 💪',
      'coloração': 'Cor dos seus sonhos 🎨',
      'escova': 'Cabelo dos sonhos ✨',
      'manicure': 'Unhas perfeitas 💅',
      'pedicure': 'Pés bem cuidados 👣',
      'hidratação': 'Cabelos sedosos e brilhantes 💆‍♀️',
      'progressiva': 'Liso perfeito e duradouro 🌟',
      'mechas': 'Iluminação natural e moderna ☀️',
      'barba': 'Barba alinhada e estilosa 🧔'
    };

    const lowerName = serviceName.toLowerCase();
    for (const [key, description] of Object.entries(descriptions)) {
      if (lowerName.includes(key)) {
        return description;
      }
    }
    return 'Transformação especial para você ✨';
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <label 
        htmlFor="service-selector"
        className="block text-sm font-medium text-gray-700 mb-2"
      >
        <span className="flex items-center gap-2">
          <Sparkles className="h-4 w-4 text-trinks-600" />
          Que transformação vamos fazer hoje?
        </span>
      </label>

      <button
        id="service-selector"
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={loading || services.length === 0}
        className={`
          w-full px-4 py-3 text-left bg-white border rounded-lg
          shadow-sm transition-all duration-200
          ${isOpen ? 'border-trinks-500 ring-2 ring-trinks-200' : 'border-gray-300 hover:border-gray-400'}
          ${selectedService ? 'text-gray-900' : 'text-gray-500'}
          ${loading || services.length === 0 ? 'opacity-50 cursor-not-allowed' : ''}
        `}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label="Selecionar serviço"
      >
        <div className="flex items-center justify-between">
          <div className="flex-1">
            {loading ? (
              <span className="text-gray-400">Carregando serviços...</span>
            ) : services.length === 0 ? (
              <span className="text-gray-400">Nenhum serviço disponível</span>
            ) : selectedService ? (
              <div>
                <span className="font-medium">{sanitizeName(selectedService.nome)}</span>
                <div className="text-xs text-gray-500 mt-1">
                  {formatDuration(selectedService.duracao)} • {formatPrice(selectedService.preco)}
                </div>
              </div>
            ) : (
              <span>Escolha um serviço especial</span>
            )}
          </div>
          <ChevronDown 
            className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''} ${
              loading || services.length === 0 ? 'text-gray-300' : 'text-gray-600'
            }`}
          />
        </div>
      </button>

      {/* Dropdown menu */}
      {isOpen && services.length > 0 && (
        <div 
          className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-60 overflow-auto"
          role="listbox"
          aria-label="Lista de serviços"
        >
          {services.map((service) => (
            <button
              key={service.id}
              type="button"
              onClick={() => {
                onServiceChange(service);
                setIsOpen(false);
              }}
              className={`
                w-full px-4 py-3 text-left hover:bg-trinks-50 transition-colors
                ${selectedService?.id === service.id ? 'bg-trinks-100 border-r-4 border-trinks-600' : ''}
                border-b border-gray-100 last:border-b-0
                focus:outline-none focus:bg-trinks-50
              `}
              role="option"
              aria-selected={selectedService?.id === service.id}
            >
              <div className="font-medium text-gray-900">
                {sanitizeName(service.nome)}
              </div>
              <div className="text-xs text-gray-600 mt-1">
                {getServiceDescription(service.nome)}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {formatDuration(service.duracao)} • {formatPrice(service.preco)}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ServiceSelector;