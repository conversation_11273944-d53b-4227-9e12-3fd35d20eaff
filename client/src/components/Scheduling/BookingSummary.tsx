import React, { useMemo, useState } from 'react';
import { Check, X, AlertCircle, Shield, Sparkles } from 'lucide-react';
import { Service, TimeSlot } from './AppointmentScheduler';

interface BookingSummaryProps {
  service: Service;
  slot: TimeSlot;
  date: Date;
  customerName?: string;
  onConfirm: () => void;
  onCancel: () => void;
  isConfirming?: boolean;
}

const BookingSummary: React.FC<BookingSummaryProps> = ({
  service,
  slot,
  date,
  customerName,
  onConfirm,
  onCancel,
  isConfirming = false
}) => {
  const [consentGiven, setConsentGiven] = useState(false);
  const [showConsentError, setShowConsentError] = useState(false);

  // Calculate end time based on service duration
  const endTime = useMemo(() => {
    const [hours, minutes] = slot.time.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const endMinutes = startMinutes + service.duracao;
    const endHours = Math.floor(endMinutes / 60);
    const endMins = endMinutes % 60;
    return `${endHours.toString().padStart(2, '0')}:${endMins.toString().padStart(2, '0')}`;
  }, [slot.time, service.duracao]);

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  // Sanitize strings for display (XSS prevention)
  const sanitize = (text: string) => {
    return text.replace(/[<>]/g, '');
  };

  // Handle confirmation with consent validation
  const handleConfirm = () => {
    if (!consentGiven) {
      setShowConsentError(true);
      return;
    }
    
    // Log consent for LGPD compliance
    console.log('LGPD Consent given at:', new Date().toISOString());
    
    onConfirm();
  };

  return (
    <div className="bg-gradient-to-br from-orange-50 to-white border border-trinks-200 rounded-lg p-4 space-y-4 shadow-sm">
      {/* Header */}
      <div className="flex items-center gap-2 pb-3 border-b border-trinks-100">
        <Sparkles className="h-5 w-5 text-trinks-600" />
        <h4 className="font-medium text-trinks-900">
          Quase lá! Vamos confirmar seu momento especial
        </h4>
      </div>

      {/* Booking Details */}
      <div className="space-y-3 text-sm">
        {/* Customer */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 flex items-center gap-1">
            <span className="text-trinks-500">▸</span> Cliente:
          </span>
          <span className="font-medium text-gray-900">
            {sanitize(customerName || 'Cliente')}
          </span>
        </div>

        {/* Service */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 flex items-center gap-1">
            <span className="text-trinks-500">▸</span> Serviço:
          </span>
          <span className="font-medium text-gray-900">
            {sanitize(service.nome)}
          </span>
        </div>

        {/* Professional */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 flex items-center gap-1">
            <span className="text-trinks-500">▸</span> Profissional:
          </span>
          <span className="font-medium text-gray-900">
            {sanitize(slot.professional.nome)}
          </span>
        </div>

        {/* Date */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 flex items-center gap-1">
            <span className="text-trinks-500">▸</span> Data:
          </span>
          <span className="font-medium text-gray-900 text-xs">
            {formatDate(date)}
          </span>
        </div>

        {/* Time */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 flex items-center gap-1">
            <span className="text-trinks-500">▸</span> Horário:
          </span>
          <span className="font-medium text-gray-900">
            {slot.time} - {endTime}
          </span>
        </div>

        {/* Duration */}
        <div className="flex justify-between items-center">
          <span className="text-gray-600 flex items-center gap-1">
            <span className="text-trinks-500">▸</span> Duração:
          </span>
          <span className="font-medium text-gray-900">
            {service.duracao} minutos
          </span>
        </div>

        {/* Price */}
        <div className="flex justify-between items-center pt-2 border-t border-gray-100">
          <span className="text-gray-600 font-medium">Valor:</span>
          <span className="font-bold text-trinks-700 text-base">
            {formatPrice(service.preco)}
          </span>
        </div>
      </div>

      {/* LGPD Consent Checkbox */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
        <label className="flex items-start gap-3 cursor-pointer">
          <input
            type="checkbox"
            checked={consentGiven}
            onChange={(e) => {
              setConsentGiven(e.target.checked);
              setShowConsentError(false);
            }}
            className="mt-0.5 w-4 h-4 text-trinks-600 border-gray-300 rounded focus:ring-trinks-500"
            aria-label="Consentimento para processamento de dados"
          />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <Shield className="h-4 w-4 text-blue-600" />
              <span className="text-xs font-medium text-blue-900">
                Proteção de Dados (LGPD)
              </span>
            </div>
            <p className="text-xs text-blue-700 leading-relaxed">
              Autorizo o processamento dos meus dados pessoais para realização 
              deste agendamento, conforme a Política de Privacidade da Trinks.
            </p>
          </div>
        </label>
        
        {showConsentError && (
          <div className="mt-2 flex items-center gap-2 text-red-600">
            <AlertCircle className="h-4 w-4" />
            <span className="text-xs">
              É necessário consentir com o processamento de dados para continuar.
            </span>
          </div>
        )}
      </div>

      {/* Confirmation Message */}
      <div className="bg-gradient-to-r from-trinks-50 to-orange-50 rounded-lg p-3 text-center">
        <p className="text-sm text-trinks-800 font-medium">
          A Trinks te espera para essa transformação! 💖
        </p>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 pt-2">
        <button
          type="button"
          onClick={onCancel}
          disabled={isConfirming}
          className="flex-1 px-4 py-2.5 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-all duration-200 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          aria-label="Cancelar agendamento"
        >
          <span className="flex items-center justify-center gap-2">
            <X className="h-4 w-4" />
            Talvez depois
          </span>
        </button>
        
        <button
          type="button"
          onClick={handleConfirm}
          disabled={isConfirming || !consentGiven}
          className={`
            flex-1 px-4 py-2.5 rounded-lg font-medium transition-all duration-200
            ${consentGiven
              ? 'bg-gradient-to-r from-trinks-500 to-trinks-600 text-white hover:from-trinks-600 hover:to-trinks-700 shadow-md hover:shadow-lg transform hover:scale-105 active:scale-95'
              : 'bg-gray-200 text-gray-400 cursor-not-allowed'
            }
            disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none
          `}
          aria-label="Confirmar agendamento"
        >
          {isConfirming ? (
            <span className="flex items-center justify-center gap-2">
              <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                <circle 
                  className="opacity-25" 
                  cx="12" 
                  cy="12" 
                  r="10" 
                  stroke="currentColor" 
                  strokeWidth="4"
                />
                <path 
                  className="opacity-75" 
                  fill="currentColor" 
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                />
              </svg>
              Confirmando...
            </span>
          ) : (
            <span className="flex items-center justify-center gap-2">
              <Check className="h-4 w-4" />
              Vamos lá! ✨
            </span>
          )}
        </button>
      </div>

      {/* Security Notice */}
      <div className="text-center">
        <p className="text-xs text-gray-500">
          <Shield className="h-3 w-3 inline mr-1" />
          Seus dados estão protegidos e seguros
        </p>
      </div>
    </div>
  );
};

export default BookingSummary;