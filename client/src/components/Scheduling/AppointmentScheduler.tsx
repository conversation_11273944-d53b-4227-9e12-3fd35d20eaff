import React, { useState, useEffect } from 'react';
import { Calendar, Clock, User, Sparkles } from 'lucide-react';
import DatePicker from './DatePicker';
import ServiceSelector from './ServiceSelector';
import TimeSlotGrid from './TimeSlotGrid';
import BookingSummary from './BookingSummary';
import socketService from '../../services/socket';

export interface Service {
  id: string;
  nome: string;
  duracao: number;
  preco: number;
  descricao?: string;
}

export interface Professional {
  id: string;
  nome: string;
  horariosVagos: string[];
  totalSlots: number;
  intervalosVagos?: Array<{
    inicio: string;
    fim: string;
  }>;
}

export interface TimeSlot {
  time: string;
  professional: Professional;
  available: boolean;
}

interface AppointmentSchedulerProps {
  customerId?: string;
  customerName?: string;
  customerPhone?: string;
  onBookingComplete?: (booking: any) => void;
  className?: string;
}

const AppointmentScheduler: React.FC<AppointmentSchedulerProps> = ({
  customerId,
  customerName,
  customerPhone,
  onBookingComplete,
  className = ''
}) => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedService, setSelectedService] = useState<Service | null>(null);
  const [selectedSlot, setSelectedSlot] = useState<TimeSlot | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [professionals, setProfessionals] = useState<Professional[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isConfirming, setIsConfirming] = useState(false);

  // Load services on mount
  useEffect(() => {
    loadServices();
  }, []);

  // Load availability when date or service changes
  useEffect(() => {
    if (selectedService && selectedDate) {
      loadAvailability();
    }
  }, [selectedDate, selectedService]);

  const loadServices = async () => {
    setLoading(true);
    setError(null);
    
    try {
      socketService.emit('get_services', {});
      
      // Listen for response
      socketService.once('services_response', (response: any) => {
        if (response.success) {
          console.log('Services loaded:', response.data);
          setServices(response.data || []);
        } else {
          setError('Erro ao carregar serviços');
          console.error('Error loading services:', response.error);
        }
        setLoading(false);
      });
    } catch (err) {
      setError('Erro ao carregar serviços');
      setLoading(false);
      console.error('Error loading services:', err);
    }
  };

  const loadAvailability = async () => {
    setLoading(true);
    setError(null);
    setProfessionals([]);
    setSelectedSlot(null);
    
    try {
      const dateStr = selectedDate.toISOString().split('T')[0];
      
      // Include serviceId to filter availability by specific service
      const requestData = { 
        date: dateStr, 
        serviceId: selectedService?.id 
      };
      
      console.log('🔍 Frontend: Requesting availability with:', requestData);
      socketService.emit('get_availability', requestData);
      
      // Listen for response
      socketService.once('availability_response', (response: any) => {
        if (response.success && response.data) {
          console.log('Availability loaded:', response.data);
          
          // Transform the response data to our Professional format
          const profs = response.data.professionals || response.data || [];
          setProfessionals(profs.map((prof: any) => ({
            id: prof.id,
            nome: prof.nome,
            horariosVagos: prof.horariosVagos || [],
            totalSlots: prof.horariosVagos?.length || 0,
            intervalosVagos: prof.intervalosVagos
          })));
        } else {
          setError('Erro ao carregar horários disponíveis');
          console.error('Error loading availability:', response.error);
        }
        setLoading(false);
      });
    } catch (err) {
      setError('Erro ao carregar horários disponíveis');
      setLoading(false);
      console.error('Error loading availability:', err);
    }
  };

  const handleConfirmBooking = async () => {
    if (!selectedSlot || !selectedService) return;
    
    setIsConfirming(true);
    setError(null);
    
    try {
      const appointmentData = {
        servicoId: selectedService.id,
        profissionalId: selectedSlot.professional.id,
        data: selectedDate.toISOString().split('T')[0],
        hora: selectedSlot.time,
        valor: selectedService.preco,
        duracaoEmMinutos: selectedService.duracao,
        observacoes: 'Agendamento criado manualmente pela recepção'
      };
      
      const customerData = {
        id: customerId,
        nome: customerName || 'Cliente',
        telefone: customerPhone || ''
      };
      
      socketService.emit('create_manual_appointment', {
        appointmentData,
        customerData
      });
      
      // Listen for response
      socketService.once('appointment_created', (response: any) => {
        if (response.success) {
          console.log('Appointment created:', response.data);
          
          // Reset selections
          setSelectedSlot(null);
          setSelectedService(null);
          
          // Notify parent component
          if (onBookingComplete) {
            onBookingComplete(response.data);
          }
          
          // Show success message
          alert('Agendamento confirmado com sucesso! ✨');
        } else {
          setError('Erro ao criar agendamento: ' + response.error);
          console.error('Error creating appointment:', response.error);
        }
        setIsConfirming(false);
      });
    } catch (err) {
      setError('Erro ao criar agendamento');
      setIsConfirming(false);
      console.error('Error creating appointment:', err);
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Trinks branding */}
      <div className="pb-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-2">
          <Sparkles className="h-5 w-5 text-trinks-600" />
          <h3 className="font-semibold text-gray-900">Vem dar um up no seu visual!</h3>
        </div>
        <p className="text-sm text-gray-600">Agende o momento de transformação</p>
      </div>

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-sm text-red-700">{error}</p>
          <button 
            onClick={() => setError(null)}
            className="mt-2 text-sm text-red-600 hover:text-red-800 font-medium"
          >
            Tentar novamente
          </button>
        </div>
      )}

      {/* Date Selection */}
      <DatePicker 
        selectedDate={selectedDate}
        onDateChange={setSelectedDate}
      />

      {/* Service Selection */}
      <ServiceSelector
        services={services}
        selectedService={selectedService}
        onServiceChange={setSelectedService}
        loading={loading}
      />

      {/* Time Slots by Professional */}
      {selectedService && (
        <TimeSlotGrid
          professionals={professionals}
          selectedSlot={selectedSlot}
          onSlotSelect={setSelectedSlot}
          serviceDuration={selectedService.duracao}
          loading={loading}
        />
      )}

      {/* Booking Summary */}
      {selectedSlot && selectedService && (
        <BookingSummary
          service={selectedService}
          slot={selectedSlot}
          date={selectedDate}
          customerName={customerName}
          onConfirm={handleConfirmBooking}
          onCancel={() => setSelectedSlot(null)}
          isConfirming={isConfirming}
        />
      )}
    </div>
  );
};

export default AppointmentScheduler;