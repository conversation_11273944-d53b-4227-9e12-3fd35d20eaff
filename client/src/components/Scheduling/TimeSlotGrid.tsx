import React, { useMemo } from 'react';
import { Clock, User, Sparkles } from 'lucide-react';
import { Professional, TimeSlot } from './AppointmentScheduler';

interface TimeSlotGridProps {
  professionals: Professional[];
  selectedSlot: TimeSlot | null;
  onSlotSelect: (slot: TimeSlot) => void;
  serviceDuration: number;
  loading?: boolean;
}

const TimeSlotGrid: React.FC<TimeSlotGridProps> = ({
  professionals,
  selectedSlot,
  onSlotSelect,
  serviceDuration,
  loading = false
}) => {
  // Group time slots by professional
  const groupedSlots = useMemo(() => {
    const grouped: Record<string, { professional: Professional; slots: TimeSlot[] }> = {};
    
    professionals.forEach(prof => {
      if (prof.horariosVagos && prof.horariosVagos.length > 0) {
        grouped[prof.id] = {
          professional: prof,
          slots: prof.horariosVagos.map(time => ({
            time,
            professional: prof,
            available: true
          }))
        };
      }
    });
    
    return grouped;
  }, [professionals]);

  // Check if a time slot can accommodate the service duration
  const isSlotAvailable = (slot: TimeSlot, index: number, allSlots: TimeSlot[]) => {
    const [hours, minutes] = slot.time.split(':').map(Number);
    const slotMinutes = hours * 60 + minutes;
    const endMinutes = slotMinutes + serviceDuration;
    
    // Check if service would end after business hours (20:00)
    if (endMinutes > 20 * 60) {
      return false;
    }
    
    // Check if there are enough consecutive slots for the service duration
    const slotsNeeded = Math.ceil(serviceDuration / 15); // Assuming 15-minute slots
    for (let i = 1; i < slotsNeeded; i++) {
      if (!allSlots[index + i] || !allSlots[index + i].available) {
        return false;
      }
    }
    
    return true;
  };

  // Format time for display with accessibility
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    return `${hours}:${minutes}`;
  };

  // Get professional initials for avatar
  const getInitials = (name: string) => {
    const names = name.split(' ');
    if (names.length >= 2) {
      return `${names[0][0]}${names[names.length - 1][0]}`.toUpperCase();
    }
    return names[0].substring(0, 2).toUpperCase();
  };

  // Sanitize professional name (XSS prevention)
  const sanitizeName = (name: string) => {
    return name.replace(/[<>]/g, '');
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <h4 className="font-medium text-gray-900 flex items-center gap-2">
          <Clock className="h-4 w-4 text-trinks-600" />
          Carregando horários disponíveis...
        </h4>
        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="bg-gray-50 rounded-lg p-4 animate-pulse">
              <div className="h-8 w-32 bg-gray-200 rounded mb-3"></div>
              <div className="grid grid-cols-3 gap-2">
                {[1, 2, 3, 4, 5, 6].map(j => (
                  <div key={j} className="h-10 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (professionals.length === 0) {
    return (
      <div className="bg-orange-50 border border-orange-200 rounded-lg p-6 text-center">
        <Sparkles className="h-8 w-8 text-orange-400 mx-auto mb-3" />
        <p className="text-sm font-medium text-orange-900">
          Ops! Não encontramos horários disponíveis para esta data.
        </p>
        <p className="text-xs text-orange-700 mt-2">
          Que tal tentar outro dia? A Trinks tem muitas opções para você! ✨
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <h4 className="font-medium text-gray-900 flex items-center gap-2">
        <Clock className="h-4 w-4 text-trinks-600" />
        Quando você quer brilhar?
      </h4>

      {Object.keys(groupedSlots).length === 0 ? (
        <div className="bg-gray-50 rounded-lg p-4 text-center">
          <p className="text-sm text-gray-600">
            Nenhum profissional disponível para este serviço nesta data.
          </p>
        </div>
      ) : (
        Object.values(groupedSlots).map(({ professional, slots }) => (
          <div 
            key={professional.id} 
            className="bg-gradient-to-r from-orange-50 to-white rounded-lg p-4 border border-orange-100"
          >
            {/* Professional Header */}
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-trinks-500 to-trinks-600 rounded-full flex items-center justify-center text-white font-medium shadow-md">
                {getInitials(professional.nome)}
              </div>
              <div className="flex-1">
                <p className="font-medium text-gray-900">
                  {sanitizeName(professional.nome)}
                </p>
                <p className="text-xs text-gray-500">
                  {professional.totalSlots} horários disponíveis
                </p>
              </div>
              {professional.intervalosVagos && professional.intervalosVagos[0] && (
                <div className="text-xs text-gray-500">
                  {professional.intervalosVagos[0].inicio} - {professional.intervalosVagos[0].fim}
                </div>
              )}
            </div>

            {/* Time Slots Grid */}
            <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
              {slots.map((slot, index) => {
                const isAvailable = isSlotAvailable(slot, index, slots);
                const isSelected = selectedSlot?.time === slot.time && 
                                 selectedSlot?.professional.id === professional.id;
                
                return (
                  <button
                    key={`${professional.id}-${slot.time}`}
                    type="button"
                    onClick={() => isAvailable && onSlotSelect(slot)}
                    disabled={!isAvailable}
                    className={`
                      px-3 py-2 rounded-md text-sm font-medium transition-all duration-200
                      ${isSelected
                        ? 'bg-trinks-600 text-white shadow-md ring-2 ring-trinks-200 scale-105'
                        : isAvailable
                          ? 'bg-white text-gray-700 border border-gray-200 hover:border-trinks-300 hover:bg-trinks-50 hover:scale-105 active:scale-95'
                          : 'bg-gray-100 text-gray-400 cursor-not-allowed opacity-50'
                      }
                    `}
                    aria-label={`Horário ${formatTime(slot.time)} com ${sanitizeName(professional.nome)}`}
                    aria-pressed={isSelected}
                    aria-disabled={!isAvailable}
                  >
                    {formatTime(slot.time)}
                  </button>
                );
              })}
            </div>

            {/* No slots message */}
            {slots.length === 0 && (
              <p className="text-sm text-gray-500 text-center py-2">
                Sem horários disponíveis
              </p>
            )}
          </div>
        ))
      )}

      {/* Service duration info */}
      {Object.keys(groupedSlots).length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
          <p className="text-xs text-blue-700">
            <strong>Duração do serviço:</strong> {serviceDuration} minutos
          </p>
          <p className="text-xs text-blue-600 mt-1">
            Selecione um horário que acomode toda a duração do serviço.
          </p>
        </div>
      )}
    </div>
  );
};

export default TimeSlotGrid;