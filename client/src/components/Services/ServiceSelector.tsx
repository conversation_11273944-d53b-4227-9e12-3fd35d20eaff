import React, { useState, useEffect } from 'react';

interface Service {
  id: string;
  nome: string;
  duracao: number;
  preco: number;
  descricao?: string;
  categoria?: string;
}

interface ServiceSelectorProps {
  onServiceSelect: (service: Service) => void;
  selectedService?: Service | null;
}

const ServiceSelector: React.FC<ServiceSelectorProps> = ({ 
  onServiceSelect, 
  selectedService 
}) => {
  const [allServices, setAllServices] = useState<Service[]>([]);
  const [displayedServices, setDisplayedServices] = useState<Service[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAll, setShowAll] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadServices();
  }, []);

  useEffect(() => {
    filterServices();
  }, [allServices, searchTerm, showAll]);

  const loadServices = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/trinks/services');
      if (response.ok) {
        const services = await response.json();
        setAllServices(services);
      } else {
        console.error('Error loading services from API');
        // Fallback to default services
        setAllServices([
          { id: "1", nome: "Corte Feminino", duracao: 60, preco: 50.00 },
          { id: "2", nome: "Corte + Escova", duracao: 90, preco: 80.00 },
          { id: "3", nome: "Pintura + Corte", duracao: 180, preco: 150.00 },
          { id: "4", nome: "Hidratação", duracao: 90, preco: 60.00 },
          { id: "5", nome: "Corte Masculino", duracao: 30, preco: 35.00 }
        ]);
      }
    } catch (error) {
      console.error('Error fetching services:', error);
      // Fallback to default services
      setAllServices([
        { id: "1", nome: "Corte Feminino", duracao: 60, preco: 50.00 },
        { id: "2", nome: "Corte + Escova", duracao: 90, preco: 80.00 },
        { id: "3", nome: "Pintura + Corte", duracao: 180, preco: 150.00 },
        { id: "4", nome: "Hidratação", duracao: 90, preco: 60.00 },
        { id: "5", nome: "Corte Masculino", duracao: 30, preco: 35.00 }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const filterServices = () => {
    let filtered = allServices;

    // Filter by search term
    if (searchTerm.trim()) {
      filtered = filtered.filter(service =>
        service.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (service.descricao && service.descricao.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Show only first 5 by default, or all if showAll is true or there's a search
    if (!showAll && !searchTerm.trim()) {
      filtered = filtered.slice(0, 5);
    }

    setDisplayedServices(filtered);
  };

  const handleRefresh = async () => {
    await loadServices();
  };

  return (
    <div className="space-y-4">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Selecionar Serviço</h3>
        <button
          onClick={handleRefresh}
          disabled={loading}
          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? '🔄' : '🔄 Atualizar'}
        </button>
      </div>

      {/* Search input */}
      <div className="relative">
        <input
          type="text"
          placeholder="Buscar serviços..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        <svg
          className="absolute right-3 top-3 h-4 w-4 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>

      {/* Services list */}
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {loading ? (
          <div className="text-center py-4 text-gray-500">
            Carregando serviços...
          </div>
        ) : displayedServices.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            {searchTerm.trim() ? 'Nenhum serviço encontrado' : 'Nenhum serviço disponível'}
          </div>
        ) : (
          displayedServices.map((service) => (
            <div
              key={service.id}
              onClick={() => onServiceSelect(service)}
              className={`p-3 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                selectedService?.id === service.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200'
              }`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-gray-900">{service.nome}</h4>
                  {service.descricao && (
                    <p className="text-sm text-gray-600 mt-1">{service.descricao}</p>
                  )}
                  <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                    <span>⏱️ {service.duracao} min</span>
                    <span>💰 R$ {service.preco.toFixed(2)}</span>
                    {service.categoria && (
                      <span className="px-2 py-1 bg-gray-100 rounded text-xs">
                        {service.categoria}
                      </span>
                    )}
                  </div>
                </div>
                {selectedService?.id === service.id && (
                  <div className="text-blue-500">
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      {/* Show more/less toggle */}
      {!searchTerm.trim() && allServices.length > 5 && (
        <div className="text-center">
          <button
            onClick={() => setShowAll(!showAll)}
            className="text-blue-500 hover:text-blue-700 text-sm font-medium"
          >
            {showAll ? '📦 Mostrar menos' : `📦 Ver todos (${allServices.length - 5} mais)`}
          </button>
        </div>
      )}
    </div>
  );
};

export default ServiceSelector;