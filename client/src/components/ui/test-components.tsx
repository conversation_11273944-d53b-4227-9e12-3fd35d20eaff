// Teste rápido dos novos componentes shadcn/ui
import React from 'react';
import { Avatar, AvatarImage, AvatarFallback } from './avatar';
import { Badge } from './badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';
import { Progress } from './progress';
import { Alert, AlertDescription, AlertTitle } from './alert';
import { Skeleton } from './skeleton';
import { Button } from './button';

export function TestComponents() {
  return (
    <div className="p-8 space-y-6 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Teste dos Componentes shadcn/ui</h1>
      
      {/* Avatar */}
      <div className="space-y-2">
        <h3 className="font-semibold">Avatar</h3>
        <div className="flex items-center gap-2">
          <Avatar>
            <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
          <Avatar>
            <AvatarFallback>TR</AvatarFallback>
          </Avatar>
        </div>
      </div>

      {/* Badge */}
      <div className="space-y-2">
        <h3 className="font-semibold">Badge</h3>
        <div className="flex items-center gap-2">
          <Badge>Default</Badge>
          <Badge variant="ai">IA</Badge>
          <Badge variant="agent">Especialista</Badge>
          <Badge variant="whatsapp">WhatsApp</Badge>
          <Badge variant="secondary">Secundário</Badge>
        </div>
      </div>

      {/* Progress */}
      <div className="space-y-2">
        <h3 className="font-semibold">Progress</h3>
        <Progress value={60} className="w-full" />
      </div>

      {/* Alert */}
      <div className="space-y-2">
        <h3 className="font-semibold">Alert</h3>
        <Alert variant="success">
          <AlertTitle>Sucesso!</AlertTitle>
          <AlertDescription>
            Componente funcionando perfeitamente.
          </AlertDescription>
        </Alert>
      </div>

      {/* Skeleton */}
      <div className="space-y-2">
        <h3 className="font-semibold">Skeleton</h3>
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-[80%]" />
          <Skeleton className="h-4 w-[60%]" />
        </div>
      </div>

      {/* Tooltip */}
      <div className="space-y-2">
        <h3 className="font-semibold">Tooltip</h3>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button variant="outline">Hover para tooltip</Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Este é um tooltip funcionando!</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}