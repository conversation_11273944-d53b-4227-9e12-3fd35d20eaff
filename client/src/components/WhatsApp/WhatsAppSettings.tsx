import React, { useState, useEffect } from 'react';
import { FaW<PERSON>sapp, FaQrcode, FaCloud, FaPhone, FaKey, FaCheck, FaTimes, FaSpinner, FaCog } from 'react-icons/fa';
import socketService from '../../services/socket';

interface WhatsAppStatus {
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  connectionType: 'twilio' | 'waha' | null;
  qrCode: string | null;
  phoneNumber: string | null;
  isLoadingQR?: boolean;
}

interface WAHAHealthStatus {
  isHealthy: boolean;
  version: string | null;
  message: string;
  lastChecked: string | null;
  isChecking: boolean;
}

interface WhatsAppSettings {
  whatsappEnabled: boolean;
  whatsappType: 'twilio' | 'waha' | 'customer-app';
  twilioAccountSid: string;
  twilioAuthToken: string;
  twilioPhoneNumber: string;
  wahaApiUrl: string;
  wahaApiKey: string;
  establishmentId: string;
  wahaHealth?: {
    isHealthy: boolean;
    version: string | null;
    message: string;
  };
}

interface Props {
  onClose: () => void;
}

const WhatsAppSettingsModal: React.FC<Props> = ({ onClose }) => {
  const [settings, setSettings] = useState<WhatsAppSettings>({
    whatsappEnabled: false,
    whatsappType: 'waha',
    twilioAccountSid: '',
    twilioAuthToken: '',
    twilioPhoneNumber: '',
    wahaApiUrl: process.env.REACT_APP_WAHA_API_URL || 'http://localhost:8090',
    wahaApiKey: process.env.REACT_APP_WAHA_API_KEY || '',
    establishmentId: ''
  });

  const [status, setStatus] = useState<WhatsAppStatus>({
    isConnected: false,
    connectionStatus: 'disconnected',
    connectionType: null,
    qrCode: null,
    phoneNumber: null,
    isLoadingQR: false
  });

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [connectionLogs, setConnectionLogs] = useState<string[]>([]);
  const [wahaHealth, setWahaHealth] = useState<WAHAHealthStatus>({
    isHealthy: false,
    version: null,
    message: 'Não verificado',
    lastChecked: null,
    isChecking: false
  });
  const logsContainerRef = React.useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadSettings();
    checkStatus();
    checkWahaHealth();
    
    // Connect to socket if not connected
    if (!socketService.isConnected) {
      socketService.connect();
    }
    
    // Setup WhatsApp event listeners
    const handleQRCode = (data: { qr: string }) => {
      console.log('🎉 ===== QR CODE RECEIVED IN FRONTEND =====');
      console.log('📱 QR Code received in component:', data);
      console.log('📱 QR Code data type:', typeof data.qr);
      console.log('📱 QR Code length:', data.qr?.length);
      console.log('⏰ Timestamp:', new Date().toISOString());
      console.log('========================================');

      // Validate QR code data
      if (!data.qr || typeof data.qr !== 'string') {
        console.error('❌ Invalid QR code data received:', data);
        return;
      }

      const logMessage = `📱 QR Code recebido (${data.qr?.length} chars) - escaneie para conectar`;
      setConnectionLogs(prev => {
        const newLogs = [...prev, logMessage];
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });

      setStatus(prev => ({
        ...prev,
        qrCode: data.qr,
        connectionStatus: 'connecting',
        isLoadingQR: false
      }));

      console.log('✅ QR Code state updated in frontend');
    };
    
    const handleStatusUpdate = (data: { status: string; type?: string; number?: string; reason?: string; message?: string }) => {
      console.log('📊 WhatsApp status update in component:', data);
      
      let logMessage = `📊 Status: ${data.status}`;
      if (data.type) logMessage += ` (${data.type})`;
      if (data.number) logMessage += ` - ${data.number}`;
      if (data.message) logMessage += ` - ${data.message}`;
      if (data.reason) logMessage += ` - Motivo: ${data.reason}`;
      
      setConnectionLogs(prev => {
        const newLogs = [...prev, logMessage];
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });
      
      // Handle different status types
      if (data.status === 'connected') {
        setStatus({
          isConnected: true,
          connectionStatus: 'connected',
          connectionType: data.type as 'twilio' | 'waha' || 'waha',
          phoneNumber: data.number || null,
          qrCode: null,
          isLoadingQR: false
        });
      } else if (data.status === 'authenticated') {
        // Authenticated but not yet connected
        setStatus(prev => ({
          ...prev,
          connectionStatus: 'connecting',
          qrCode: null,
          isLoadingQR: false
        }));
      } else if (data.status === 'disconnected') {
        setStatus({
          isConnected: false,
          connectionStatus: 'disconnected',
          connectionType: null,
          phoneNumber: null,
          qrCode: null,
          isLoadingQR: false
        });
      } else if (data.status === 'error') {
        setStatus(prev => ({
          ...prev,
          isConnected: false,
          connectionStatus: 'error',
          isLoadingQR: false,
          qrCode: null
        }));
      } else {
        // Other status updates
        setStatus(prev => ({
          ...prev,
          connectionStatus: data.status as 'disconnected' | 'connecting' | 'connected' | 'error'
        }));
      }
      
      if (data.message) {
        setMessage({ 
          type: data.status === 'connected' || data.status === 'authenticated' ? 'success' : 'error', 
          text: data.message 
        });
        setTimeout(() => setMessage(null), 5000);
      }
    };
    
    // Remove only WhatsApp listeners (preserve other app listeners)
    if (socketService.isConnected) {
      const socket = (socketService as any).socket;
      if (socket) {
        socket.off('whatsapp_qr');
        socket.off('whatsapp_status');
        socket.off('whatsapp_log');
        console.log('🧹 Removed existing WhatsApp listeners');
      }
    }
    
    // Setup WhatsApp log handler
    const handleWhatsAppLog = (data: { message: string; timestamp: string }) => {
      console.log('📋 WhatsApp log received:', data);
      const logMessage = data.message;
      setConnectionLogs(prev => {
        const newLogs = [...prev, logMessage];
        // Keep only last 50 logs to prevent memory issues
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });
    };
    
    // Setup event listeners
    console.log('🔧 Setting up WhatsApp event listeners...');
    console.log('🔍 Socket connected:', socketService.isConnected);

    // Test QR handler
    const testQRHandler = (data: { qr: string }) => {
      console.log('🎯 QR HANDLER CALLED:', data);
      handleQRCode(data);
    };

    socketService.onWhatsAppQR(testQRHandler);
    console.log('✅ whatsapp_qr listener configured');

    socketService.onWhatsAppStatus(handleStatusUpdate);
    console.log('✅ whatsapp_status listener configured');

    socketService.onWhatsAppLog(handleWhatsAppLog);
    console.log('✅ whatsapp_log listener configured');
    
    // Re-setup listeners on reconnection
    const handleReconnect = () => {
      console.log('🔄 Socket reconnected, re-setting up WhatsApp listeners');
      socketService.onWhatsAppQR(testQRHandler);
      socketService.onWhatsAppStatus(handleStatusUpdate);
      socketService.onWhatsAppLog(handleWhatsAppLog);
      // Check status after reconnection
      setTimeout(checkStatus, 1000);
    };
    
    // Listen for reconnection events
    if (socketService.isConnected) {
      const socket = (socketService as any).socket;
      if (socket) {
        socket.on('reconnect', handleReconnect);
      }
    }
    
    // Cleanup on unmount
    return () => {
      // Clean up reconnect listener
      if (socketService.isConnected) {
        const socket = (socketService as any).socket;
        if (socket) {
          socket.off('reconnect', handleReconnect);
        }
      }
    };
  }, []);

  // Check WAHA health when settings change
  React.useEffect(() => {
    if (settings.whatsappType === 'waha') {
      checkWahaHealth();
    }
  }, [settings.whatsappType, settings.wahaApiUrl, settings.wahaApiKey]);

  // Auto-scroll logs to bottom when new logs are added
  React.useEffect(() => {
    if (logsContainerRef.current) {
      logsContainerRef.current.scrollTop = logsContainerRef.current.scrollHeight;
    }
  }, [connectionLogs]);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings({
          whatsappEnabled: data.whatsappEnabled || false,
          whatsappType: data.whatsappType || 'waha',
          twilioAccountSid: data.twilioAccountSid || '',
          twilioAuthToken: data.twilioAuthToken || '',
          twilioPhoneNumber: data.twilioPhoneNumber || '',
          wahaApiUrl: data.wahaApiUrl || process.env.REACT_APP_WAHA_API_URL || 'http://localhost:8090',
          wahaApiKey: data.wahaApiKey || process.env.REACT_APP_WAHA_API_KEY || '',
          establishmentId: data.establishmentId || data.trinksEstabelecimentoId || ''
        });
        
        // Update WAHA health from settings if available
        if (data.wahaHealth) {
          setWahaHealth({
            isHealthy: data.wahaHealth.isHealthy,
            version: data.wahaHealth.version,
            message: data.wahaHealth.message,
            lastChecked: new Date().toISOString(),
            isChecking: false
          });
        }
      }
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  };

  const checkStatus = async () => {
    try {
      const response = await fetch('/api/whatsapp/status');
      if (response.ok) {
        const data = await response.json();
        setStatus(data.status);
      }
    } catch (error) {
      console.error('Error checking status:', error);
    }
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/whatsapp/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Configurações salvas com sucesso!' });
        setTimeout(() => setMessage(null), 3000);
      } else {
        throw new Error('Erro ao salvar configurações');
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erro ao salvar configurações' });
      setTimeout(() => setMessage(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  const checkQRCodeStatus = async () => {
    try {
      const response = await fetch('/api/whatsapp/status');
      if (response.ok) {
        const data = await response.json();
        if (data.status && data.status.qrCode) {
          console.log('📱 QR Code found via HTTP fallback');
          setStatus(prev => ({
            ...prev,
            qrCode: data.status.qrCode,
            connectionStatus: 'connecting'
          }));
        }
      }
    } catch (error) {
      console.error('Error checking QR code status:', error);
    }
  };

  const connect = async () => {
    try {
      setLoading(true);
      setConnectionLogs([]);
      
      // Set loading state for QR code if it's a waha connection
      if (settings.whatsappType === 'waha') {
        setStatus(prev => ({ ...prev, isLoadingQR: true }));
        const connectionMessage = '🚀 Iniciando conexão WAHA API...';
        setConnectionLogs([connectionMessage]);
      }
      
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 90000); // 90 second timeout for WhatsApp initialization

      const response = await fetch('/api/whatsapp/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: settings.whatsappType,
          settings: settings
        }),
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        throw new Error(`Resposta inválida do servidor: ${text.substring(0, 100)}...`);
      }

      const data = await response.json();
      
      if (data.success) {
        setStatus(data.status);
        setMessage({ type: 'success', text: `WhatsApp conectado via ${settings.whatsappType}!` });
        
        const successLog = `✅ Conexão iniciada com sucesso via ${settings.whatsappType}`;
        setConnectionLogs(prev => {
          const newLogs = [...prev, successLog];
          return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
        });
        
        // For waha connections, start polling for QR code as fallback
        if (settings.whatsappType === 'waha') {
          setConnectionLogs(prev => {
            const newLogs = [...prev, '🔍 Aguardando QR Code...'];
            return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
          });
          
          // Check for QR code immediately and then every 2 seconds
          setTimeout(checkQRCodeStatus, 1000);
          const qrInterval = setInterval(() => {
            checkQRCodeStatus();
          }, 2000);
          
          // Also stop polling when connected
          const statusCheck = setInterval(() => {
            if (status.isConnected) {
              clearInterval(qrInterval);
              clearInterval(statusCheck);
              setConnectionLogs(prev => {
                const newLogs = [...prev, '✅ WhatsApp conectado com sucesso!'];
                return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
              });
            }
          }, 1000);
          
          // Stop polling after 75 seconds or when connected (increased from 30s)
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const timeoutId = setTimeout(() => {
            clearInterval(qrInterval);
            clearInterval(statusCheck);
            if (!status.qrCode && !status.isConnected) {
              setConnectionLogs(prev => {
                const newLogs = [...prev, '⏰ Timeout aguardando QR Code - tente reconectar'];
                return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
              });
              setStatus(prev => ({ ...prev, isLoadingQR: false }));
            }
          }, 75000);
        }
      } else {
        throw new Error(data.error || 'Erro ao conectar');
      }
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        const timeoutMessage = 'Timeout: Conexão WhatsApp demorou muito. Tente novamente.';
        setMessage({ type: 'error', text: timeoutMessage });
        setConnectionLogs(prev => {
          const newLogs = [...prev, `❌ ${timeoutMessage}`];
          return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
        });
      } else {
        const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
        setMessage({ type: 'error', text: `Erro ao conectar: ${errorMessage}` });
        setConnectionLogs(prev => {
          const newLogs = [...prev, `❌ Erro: ${errorMessage}`];
          return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
        });
      }
      
      setStatus(prev => ({ ...prev, isLoadingQR: false }));
    } finally {
      setLoading(false);
    }
  };

  const disconnect = async () => {
    try {
      setLoading(true);
      setConnectionLogs(prev => {
        const newLogs = [...prev, '🔌 Desconectando WhatsApp...'];
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });
      
      const response = await fetch('/api/whatsapp/disconnect', {
        method: 'POST',
      });

      if (response.ok) {
        setStatus({
          isConnected: false,
          connectionStatus: 'disconnected',
          connectionType: null,
          qrCode: null,
          phoneNumber: null,
          isLoadingQR: false
        });
        setMessage({ type: 'success', text: 'WhatsApp desconectado!' });
        setConnectionLogs(prev => {
          const newLogs = [...prev, '✅ WhatsApp desconectado com sucesso'];
          return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
        });
      } else {
        throw new Error('Erro ao desconectar');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setMessage({ type: 'error', text: `Erro ao desconectar: ${errorMessage}` });
      setConnectionLogs(prev => {
        const newLogs = [...prev, `❌ Erro ao desconectar: ${errorMessage}`];
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });
    } finally {
      setLoading(false);
    }
  };

  const clearSession = async () => {
    try {
      setLoading(true);
      setConnectionLogs(prev => {
        const newLogs = [...prev, '🧹 Limpando sessão do WhatsApp...'];
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });
      
      // Use socket to clear session
      socketService.emit('whatsapp_clear_session', {});
      
      // Wait for response
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          socketService.off('whatsapp_clear_session_response');
          reject(new Error('Timeout ao limpar sessão'));
        }, 10000);
        
        const handleResponse = (data: any) => {
          clearTimeout(timeout);
          socketService.off('whatsapp_clear_session_response', handleResponse);
          if (data.success) {
            resolve(data);
          } else {
            reject(new Error(data.error || 'Erro ao limpar sessão'));
          }
        };
        
        socketService.on('whatsapp_clear_session_response', handleResponse);
      });
      
      setStatus({
        isConnected: false,
        connectionStatus: 'disconnected',
        connectionType: null,
        qrCode: null,
        phoneNumber: null,
        isLoadingQR: false
      });
      
      setMessage({ type: 'success', text: 'Sessão limpa com sucesso!' });
      setConnectionLogs(prev => {
        const newLogs = [...prev, '✅ Sessão limpa com sucesso'];
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setMessage({ type: 'error', text: `Erro ao limpar sessão: ${errorMessage}` });
      setConnectionLogs(prev => {
        const newLogs = [...prev, `❌ Erro ao limpar sessão: ${errorMessage}`];
        return newLogs.length > 50 ? newLogs.slice(-50) : newLogs;
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = () => {
    switch (status.connectionStatus) {
      case 'connected': return 'text-green-500';
      case 'connecting': return 'text-yellow-500';
      case 'error': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusText = () => {
    switch (status.connectionStatus) {
      case 'connected': return 'Conectado';
      case 'connecting': return 'Conectando...';
      case 'error': return 'Erro';
      default: return 'Desconectado';
    }
  };

  const checkWahaHealth = async () => {
    if (settings.whatsappType !== 'waha') return;
    
    try {
      setWahaHealth(prev => ({ ...prev, isChecking: true }));
      
      const response = await fetch('/api/whatsapp/waha-health');
      if (response.ok) {
        const data = await response.json();
        setWahaHealth({
          isHealthy: data.isHealthy,
          version: data.version,
          message: data.message,
          lastChecked: data.lastChecked,
          isChecking: false
        });
      } else {
        throw new Error('Falha ao verificar saúde do WAHA');
      }
    } catch (error) {
      console.error('Error checking WAHA health:', error);
      setWahaHealth({
        isHealthy: false,
        version: null,
        message: '❌ Erro ao verificar conectividade',
        lastChecked: new Date().toISOString(),
        isChecking: false
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FaWhatsapp className="text-2xl text-green-500" />
            <h2 className="text-xl font-semibold text-gray-900">Configurações do WhatsApp</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status */}
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${
                status.connectionStatus === 'connected' ? 'bg-green-500' :
                status.connectionStatus === 'connecting' ? 'bg-yellow-500' :
                status.connectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-500'
              }`} />
              <div>
                <p className="font-medium text-gray-900">Status da Conexão</p>
                <p className={`text-sm ${getStatusColor()}`}>
                  {getStatusText()}
                  {status.connectionType && ` (${status.connectionType === 'twilio' ? 'Twilio' : status.connectionType === 'waha' ? 'WAHA' : status.connectionType})`}
                  {status.phoneNumber && ` - ${status.phoneNumber}`}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {status.isConnected ? (
                <button
                  onClick={disconnect}
                  disabled={loading}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? <FaSpinner className="animate-spin" /> : 'Desconectar'}
                </button>
              ) : (
                <>
                  <button
                    onClick={connect}
                    disabled={loading || !settings.whatsappEnabled}
                    className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {loading ? <FaSpinner className="animate-spin" /> : 'Conectar'}
                  </button>
                  {settings.whatsappType === 'waha' && !status.isConnected && (
                    <button
                      onClick={clearSession}
                      disabled={loading}
                      className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      title="Limpar sessão salva e forçar novo QR Code"
                    >
                      {loading ? <FaSpinner className="animate-spin" /> : '🧹 Limpar Sessão'}
                    </button>
                  )}
                </>
              )}
            </div>
          </div>

          {/* QR Code or Loading */}
          {settings.whatsappType === 'waha' && (status.qrCode || status.isLoadingQR) && (
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2 mb-3">
                <FaQrcode className="text-blue-500" />
                <h3 className="font-medium text-blue-900">
                  {status.isLoadingQR ? 'Gerando QR Code...' : 'Escaneie o QR Code'}
                </h3>
              </div>
              <p className="text-sm text-blue-700 mb-3">
                {status.isLoadingQR
                  ? 'Aguarde enquanto o QR Code está sendo gerado...'
                  : 'Abra o WhatsApp no seu celular e escaneie este QR Code em Dispositivos Conectados'
                }
              </p>
              <div className="flex justify-center">
                {status.isLoadingQR ? (
                  <div className="flex flex-col items-center space-y-4">
                    <FaSpinner className="animate-spin text-blue-500 text-4xl" />
                    <p className="text-sm text-blue-600">Preparando conexão...</p>
                  </div>
                ) : status.qrCode ? (
                  <div className="flex flex-col items-center space-y-2">
                    <img
                      src={`data:image/png;base64,${status.qrCode}`}
                      alt="QR Code WhatsApp"
                      className="max-w-xs border border-gray-300 rounded-lg shadow-sm"
                      onError={(e) => {
                        console.error('❌ Erro ao carregar QR Code:', e);
                        console.log('📱 QR Code data:', status.qrCode?.substring(0, 100) + '...');
                      }}
                      onLoad={() => {
                        console.log('✅ QR Code carregado com sucesso!');
                      }}
                    />
                    <p className="text-xs text-blue-600 text-center">
                      QR Code válido por 90 segundos
                    </p>
                  </div>
                ) : null}
              </div>
            </div>
          )}

          {/* Connection Logs */}
          {connectionLogs.length > 0 && (
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                  <span>📋</span>
                  <span>Log de Conexão</span>
                  <span className="text-xs text-gray-500">({connectionLogs.length})</span>
                </h3>
                <button
                  onClick={() => setConnectionLogs([])}
                  className="text-xs text-gray-500 hover:text-gray-700 underline"
                >
                  Limpar
                </button>
              </div>
              <div 
                ref={logsContainerRef}
                className="max-h-40 overflow-y-auto space-y-1 bg-white p-2 rounded border scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
                style={{ fontFamily: 'Monaco, Consolas, "Courier New", monospace' }}
              >
                {connectionLogs.map((log, index) => {
                  const timestamp = new Date().toLocaleTimeString('pt-BR', { 
                    hour12: false,
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                  });
                  
                  return (
                    <div key={index} className="text-xs text-gray-700 p-1 hover:bg-gray-50 rounded border-l-2 border-transparent hover:border-blue-300 transition-colors">
                      <span className="text-gray-400 mr-2">[{timestamp}]</span>
                      <span>{log}</span>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Debug Info */}
          {process.env.NODE_ENV === 'development' && (
            <div className="p-3 bg-gray-100 rounded-lg text-xs font-mono">
              <p><strong>Socket Connected:</strong> {socketService.isConnected ? '✅' : '❌'}</p>
              <p><strong>Status:</strong> {status.connectionStatus}</p>
              <p><strong>Type:</strong> {status.connectionType || 'none'}</p>
              <p><strong>QR Code:</strong> {status.qrCode ? `Yes (${status.qrCode.length} chars)` : status.isLoadingQR ? 'Loading...' : 'No'}</p>
              <p><strong>Phone:</strong> {status.phoneNumber || 'none'}</p>
            </div>
          )}

          {/* Message */}
          {message && (
            <div className={`p-4 rounded-lg ${
              message.type === 'success' ? 'bg-green-50 text-green-700 border border-green-200' :
              'bg-red-50 text-red-700 border border-red-200'
            }`}>
              <div className="flex items-center space-x-2">
                {message.type === 'success' ? <FaCheck /> : <FaTimes />}
                <p>{message.text}</p>
              </div>
            </div>
          )}

          {/* Settings Form */}
          <div className="space-y-4">
            {/* Enable WhatsApp */}
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="whatsappEnabled"
                checked={settings.whatsappEnabled}
                onChange={(e) => setSettings(prev => ({ ...prev, whatsappEnabled: e.target.checked }))}
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
              />
              <label htmlFor="whatsappEnabled" className="text-sm font-medium text-gray-700">
                Ativar integração com WhatsApp
              </label>
            </div>

            {settings.whatsappEnabled && (
              <>
                {/* Connection Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Como seu empreendimento vai se conectar com os clientes
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* WAHA Option */}
                    <button
                      type="button"
                      onClick={() => setSettings(prev => ({ ...prev, whatsappType: 'waha' }))}
                      className={`p-6 border-2 rounded-xl transition-all duration-200 hover:shadow-md ${
                        settings.whatsappType === 'waha'
                          ? 'border-[#EC5702] bg-gradient-to-br from-orange-50 to-orange-100 text-[#EC5702] shadow-sm'
                          : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-3">
                        <div className={`p-3 rounded-full ${settings.whatsappType === 'waha' ? 'bg-[#EC5702] text-white' : 'bg-gray-100 text-gray-600'}`}>
                          <FaWhatsapp className="text-xl" />
                        </div>
                        <div className="text-center">
                          <h3 className="font-semibold text-base">WhatsApp Web</h3>
                          <p className="text-sm opacity-75 mt-1">QR Code • Ideal para começar</p>
                          {settings.whatsappType === 'waha' && (
                            <div className="mt-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[#EC5702] text-white">
                                <FaCheck className="mr-1 text-xs" />
                                Recomendado
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </button>

                    {/* Twilio Option */}
                    <button
                      type="button"
                      onClick={() => setSettings(prev => ({ ...prev, whatsappType: 'twilio' }))}
                      className={`p-6 border-2 rounded-xl transition-all duration-200 hover:shadow-md ${
                        settings.whatsappType === 'twilio'
                          ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-blue-100 text-blue-700 shadow-sm'
                          : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-3">
                        <div className={`p-3 rounded-full ${settings.whatsappType === 'twilio' ? 'bg-blue-500 text-white' : 'bg-gray-100 text-gray-600'}`}>
                          <FaCloud className="text-xl" />
                        </div>
                        <div className="text-center">
                          <h3 className="font-semibold text-base">API Oficial</h3>
                          <p className="text-sm opacity-75 mt-1">Twilio • Para alto volume</p>
                        </div>
                      </div>
                    </button>

                    {/* Customer App Test Mode */}
                    <button
                      type="button"
                      onClick={() => setSettings(prev => ({ ...prev, whatsappType: 'customer-app' }))}
                      className={`p-6 border-2 rounded-xl transition-all duration-200 hover:shadow-md ${
                        settings.whatsappType === 'customer-app'
                          ? 'border-purple-500 bg-gradient-to-br from-purple-50 to-purple-100 text-purple-700 shadow-sm'
                          : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-3">
                        <div className={`p-3 rounded-full ${settings.whatsappType === 'customer-app' ? 'bg-purple-500 text-white' : 'bg-gray-100 text-gray-600'}`}>
                          <FaCog className="text-xl" />
                        </div>
                        <div className="text-center">
                          <h3 className="font-semibold text-base">Modo Teste</h3>
                          <p className="text-sm opacity-75 mt-1">Customer App • Desenvolvimento</p>
                          {settings.whatsappType === 'customer-app' && (
                            <div className="mt-2">
                              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-purple-500 text-white">
                                <FaCog className="mr-1 text-xs" />
                                Teste
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    </button>
                  </div>
                  
                  {/* Description based on selected type */}
                  <div className="mt-4 p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-lg border border-gray-200">
                    <div className="flex items-start space-x-3">
                      {settings.whatsappType === 'waha' && (
                        <>
                          <FaWhatsapp className="text-[#EC5702] mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-medium text-gray-900 mb-1">WhatsApp Web para seu empreendimento</h4>
                            <p className="text-sm text-gray-700 leading-relaxed">
                              Conecte seu WhatsApp pessoal ou comercial usando QR Code. Perfeito para empreendedores 
                              que estão começando e querem dar um up no atendimento sem complicação.
                            </p>
                          </div>
                        </>
                      )}
                      {settings.whatsappType === 'twilio' && (
                        <>
                          <FaCloud className="text-blue-500 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-medium text-gray-900 mb-1">API Oficial para escalar seu negócio</h4>
                            <p className="text-sm text-gray-700 leading-relaxed">
                              Integração profissional via Twilio para empreendimentos que já cresceram e precisam 
                              de um atendimento de alto volume com total confiabilidade.
                            </p>
                          </div>
                        </>
                      )}
                      {settings.whatsappType === 'customer-app' && (
                        <>
                          <FaCog className="text-purple-500 mt-0.5 flex-shrink-0" />
                          <div>
                            <h4 className="font-medium text-gray-900 mb-1">Ambiente de testes para desenvolvimento</h4>
                            <p className="text-sm text-gray-700 leading-relaxed">
                              Teste todas as funcionalidades sem precisar conectar seu WhatsApp real. 
                              Ideal para experimentar o sistema e treinar sua equipe antes de ir ao vivo.
                            </p>
                            <div className="mt-2">
                              <button 
                                onClick={() => window.open('http://localhost:3002', '_blank')}
                                className="text-xs text-purple-600 hover:text-purple-800 underline"
                              >
                                🚀 Abrir Customer App de teste
                              </button>
                            </div>
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Twilio Settings */}
                {settings.whatsappType === 'twilio' && (
                  <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                      <FaCloud className="text-blue-500" />
                      <span>Configurações do Twilio</span>
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Account SID
                      </label>
                      <div className="relative">
                        <FaKey className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          value={settings.twilioAccountSid}
                          onChange={(e) => setSettings(prev => ({ ...prev, twilioAccountSid: e.target.value }))}
                          className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="AC..."
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Auth Token
                      </label>
                      <div className="relative">
                        <FaKey className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="password"
                          value={settings.twilioAuthToken}
                          onChange={(e) => setSettings(prev => ({ ...prev, twilioAuthToken: e.target.value }))}
                          className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="Token de autenticação"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Número do Twilio
                      </label>
                      <div className="relative">
                        <FaPhone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          value={settings.twilioPhoneNumber}
                          onChange={(e) => setSettings(prev => ({ ...prev, twilioPhoneNumber: e.target.value }))}
                          className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="+5511999999999"
                        />
                      </div>
                    </div>
                  </div>
                )}

                {/* Customer App Settings */}
                {settings.whatsappType === 'customer-app' && (
                  <div className="space-y-4 p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl border border-purple-200">
                    <h3 className="font-semibold text-gray-900 flex items-center space-x-3">
                      <div className="p-2 bg-purple-500 rounded-lg">
                        <FaCog className="text-white text-lg" />
                      </div>
                      <span>Modo Teste - Customer App</span>
                    </h3>
                    
                    <div className="bg-white rounded-lg p-4 border border-purple-200">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <FaCog className="text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900 mb-2">Como funciona o modo teste</h4>
                          <div className="space-y-2 text-sm text-gray-700">
                            <div className="flex items-center space-x-2">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                              <span>Simule conversas como se fosse um cliente real</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                              <span>Teste a análise de humor em tempo real</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <div className="w-1.5 h-1.5 bg-purple-500 rounded-full"></div>
                              <span>Experimente todos os recursos sem WhatsApp real</span>
                            </div>
                          </div>
                          
                          <div className="flex space-x-3 mt-4">
                            <button
                              onClick={() => window.open('http://localhost:3002', '_blank')}
                              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm font-medium"
                            >
                              🚀 Abrir Customer App
                            </button>
                            <button
                              onClick={() => window.open('http://localhost:3000', '_blank')}
                              className="px-4 py-2 border border-purple-300 text-purple-700 rounded-lg hover:bg-purple-50 transition-colors text-sm font-medium"
                            >
                              📊 Ver no CRM
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gradient-to-r from-[#EC5702]/10 to-orange-100/50 rounded-lg p-4 border border-orange-200">
                      <div className="flex items-start space-x-3">
                        <div className="text-[#EC5702] mt-0.5">
                          <FaCheck className="text-sm" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 mb-1">Pronto para testar!</h4>
                          <p className="text-sm text-gray-700">
                            O modo teste já está configurado e funcionando. Experimente criar conversas 
                            e veja como a Trinks cuida do relacionamento com seus clientes.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* WAHA Settings */}
                {settings.whatsappType === 'waha' && (
                  <div className="space-y-4 p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl border border-orange-200">
                    <h3 className="font-semibold text-gray-900 flex items-center space-x-3">
                      <div className="p-2 bg-[#EC5702] rounded-lg">
                        <FaWhatsapp className="text-white text-lg" />
                      </div>
                      <span>Configurações do WhatsApp Web</span>
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        URL da API WAHA
                      </label>
                      <div className="relative">
                        <FaCloud className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="text"
                          value={settings.wahaApiUrl}
                          onChange={(e) => setSettings(prev => ({ ...prev, wahaApiUrl: e.target.value }))}
                          className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder="http://localhost:8090"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        API Key (Opcional para WAHA Core)
                      </label>
                      <div className="relative">
                        <FaKey className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                        <input
                          type="password"
                          value={settings.wahaApiKey}
                          onChange={(e) => setSettings(prev => ({ ...prev, wahaApiKey: e.target.value }))}
                          className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                          placeholder={settings.wahaApiKey && settings.wahaApiKey.length > 0 ? '••••••••••••••••' : 'Deixe em branco para WAHA Core'}
                        />
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        API Key é necessária apenas para WAHA Pro/Plus. Deixe em branco para WAHA Core.
                      </p>
                    </div>

                    {/* WAHA Health Status */}
                    <div className="mt-4 p-4 bg-white rounded-lg border border-orange-200">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900 flex items-center space-x-2">
                          <FaCloud className="text-orange-500" />
                          <span>Status da API WAHA</span>
                        </h4>
                        <button
                          onClick={checkWahaHealth}
                          disabled={wahaHealth.isChecking}
                          className="px-3 py-1 text-xs bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          {wahaHealth.isChecking ? (
                            <><FaSpinner className="animate-spin mr-1" /> Verificando...</>
                          ) : (
                            'Verificar Agora'
                          )}
                        </button>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          wahaHealth.isHealthy ? 'bg-green-500' : 'bg-red-500'
                        }`} />
                        <div className="flex-1">
                          <p className={`text-sm font-medium ${
                            wahaHealth.isHealthy ? 'text-green-700' : 'text-red-700'
                          }`}>
                            {wahaHealth.message}
                          </p>
                          {wahaHealth.version && (
                            <p className="text-xs text-gray-600 mt-1">
                              Versão: {wahaHealth.version}
                            </p>
                          )}
                          {wahaHealth.lastChecked && (
                            <p className="text-xs text-gray-500 mt-1">
                              Última verificação: {new Date(wahaHealth.lastChecked).toLocaleString('pt-BR')}
                            </p>
                          )}
                        </div>
                      </div>
                      
                      {!wahaHealth.isHealthy && (
                        <div className="mt-3 p-3 bg-red-50 rounded-lg border border-red-200">
                          <p className="text-xs text-red-700">
                            <strong>Problema de conectividade:</strong> Verifique se a URL e API Key estão corretas.
                            O WAHA deve estar rodando e acessível em: <code className="bg-red-100 px-1 rounded">{settings.wahaApiUrl}</code>
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                )}


                {/* Business Number - Auto-detected for WAHA */}
                {settings.whatsappType === 'waha' && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center">
                      <FaWhatsapp className="text-blue-500 mr-2" />
                      <div>
                        <p className="text-sm font-medium text-blue-800">
                          Número do WhatsApp
                        </p>
                        <p className="text-xs text-blue-600">
                          {status.phoneNumber
                            ? `Conectado: ${status.phoneNumber}`
                            : 'Será detectado automaticamente após conectar via QR Code'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancelar
          </button>
          <button
            onClick={saveSettings}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            {loading ? <FaSpinner className="animate-spin" /> : <FaCheck />}
            <span>Salvar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppSettingsModal;