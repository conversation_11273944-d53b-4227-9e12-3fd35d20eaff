import React, { useState, useEffect } from 'react';
import { FaWhatsapp, FaCog, FaQrcode, FaCloud, FaExclamationTriangle } from 'react-icons/fa';
import WhatsAppSettingsModal from './WhatsAppSettings';
import socketService from '../../services/socket';

interface WhatsAppStatus {
  isConnected: boolean;
  connectionStatus: 'disconnected' | 'connecting' | 'connected' | 'error';
  connectionType: 'twilio' | 'waha' | null;
  qrCode: string | null;
  phoneNumber: string | null;
}

interface WAHAHealthStatus {
  isHealthy: boolean;
  version: string | null;
  message: string;
  lastChecked: string | null;
  isChecking: boolean;
}

const WhatsAppStatusComponent: React.FC = () => {
  const [status, setStatus] = useState<WhatsAppStatus>({
    isConnected: false,
    connectionStatus: 'disconnected',
    connectionType: null,
    qrCode: null,
    phoneNumber: null
  });

  const [showSettings, setShowSettings] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  
  const [wahaHealth, setWahaHealth] = useState<WAHAHealthStatus>({
    isHealthy: false,
    version: null,
    message: '⏳ Verificando conectividade...',
    lastChecked: null,
    isChecking: true
  });

  useEffect(() => {
    // Check status on component mount
    checkStatus();
    checkWAHAHealth();

    // Set up interval to check status periodically
    const interval = setInterval(checkStatus, 5000);
    
    // Set up interval to check WAHA health less frequently
    const wahaInterval = setInterval(checkWAHAHealth, 30000); // Every 30 seconds

    // Listen to real-time socket events for immediate updates
    const handleStatusUpdate = (data: any) => {
      console.log('📡 WhatsApp status update received:', data);
      setStatus(data);
    };

    const handleQRCode = (data: { qr: string }) => {
      console.log('📱 QR Code received via socket:', data.qr ? 'QR Code available' : 'No QR Code');
      setStatus(prev => ({ ...prev, qrCode: data.qr }));
    };

    const handleLog = (data: { message: string; timestamp: string }) => {
      console.log('📝 WhatsApp log:', data.message);
    };

    // Subscribe to socket events
    socketService.onWhatsAppStatus(handleStatusUpdate);
    socketService.onWhatsAppQR(handleQRCode);
    socketService.onWhatsAppLog(handleLog);

    return () => {
      clearInterval(interval);
      clearInterval(wahaInterval);
      // Clean up socket listeners
      socketService.off('whatsapp_status', handleStatusUpdate);
      socketService.off('whatsapp_qr', handleQRCode);
      socketService.off('whatsapp_log', handleLog);
    };
  }, []);

  const checkStatus = async () => {
    try {
      const response = await fetch('/api/whatsapp/status');
      if (response.ok) {
        const data = await response.json();
        setStatus(data.status);
      }
    } catch (error) {
      console.error('Error checking WhatsApp status:', error);
    }
  };

  const checkWAHAHealth = async () => {
    setWahaHealth(prev => ({ ...prev, isChecking: true }));
    
    try {
      const response = await fetch('/api/whatsapp/waha-health');
      if (response.ok) {
        const data = await response.json();
        setWahaHealth({
          isHealthy: data.isHealthy,
          version: data.version,
          message: data.message,
          lastChecked: data.lastChecked,
          isChecking: false
        });
        console.log('🔍 WAHA Health check:', data);
      } else {
        setWahaHealth({
          isHealthy: false,
          version: null,
          message: '❌ Erro ao verificar status da API WAHA',
          lastChecked: new Date().toISOString(),
          isChecking: false
        });
      }
    } catch (error) {
      console.error('Error checking WAHA health:', error);
      setWahaHealth({
        isHealthy: false,
        version: null,
        message: '❌ Falha na comunicação com API WAHA',
        lastChecked: new Date().toISOString(),
        isChecking: false
      });
    }
  };

  const getStatusIcon = () => {
    switch (status.connectionStatus) {
      case 'connected':
        return <FaWhatsapp className="text-green-500" />;
      case 'connecting':
        return <FaWhatsapp className="text-yellow-500 animate-pulse" />;
      case 'error':
        return <FaExclamationTriangle className="text-red-500" />;
      default:
        return <FaWhatsapp className="text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (status.connectionStatus) {
      case 'connected':
        const typeLabel = status.connectionType === 'waha' ? '(WAHA)' : '(Twilio)';
        return `WhatsApp Conectado ${typeLabel}`;
      case 'connecting':
        return 'Conectando ao WhatsApp...';
      case 'error':
        return 'Erro na conexão WhatsApp';
      default:
        return 'WhatsApp Desconectado';
    }
  };

  const getStatusColor = () => {
    switch (status.connectionStatus) {
      case 'connected': return 'text-green-600';
      case 'connecting': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getBgColor = () => {
    switch (status.connectionStatus) {
      case 'connected': return 'bg-green-50 border-green-200';
      case 'connecting': return 'bg-yellow-50 border-yellow-200';
      case 'error': return 'bg-red-50 border-red-200';
      default: return 'bg-gray-50 border-gray-200';
    }
  };

  const getWAHAHealthColor = () => {
    if (wahaHealth.isChecking) return 'text-yellow-600';
    return wahaHealth.isHealthy ? 'text-green-600' : 'text-red-600';
  };

  const getWAHAHealthIcon = () => {
    if (wahaHealth.isChecking) {
      return <FaCloud className="text-yellow-500 animate-pulse" />;
    }
    return wahaHealth.isHealthy 
      ? <FaCloud className="text-green-500" />
      : <FaExclamationTriangle className="text-red-500" />;
  };

  return (
    <>
      <div className={`p-4 rounded-lg border ${getBgColor()} transition-colors`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="text-xl">
              {getStatusIcon()}
            </div>
            <div>
              <h3 className="font-medium text-gray-900">WhatsApp</h3>
              <p className={`text-sm ${getStatusColor()}`}>
                {getStatusText()}
                {status.phoneNumber && (
                  <span className="block text-xs text-gray-500 mt-1">
                    {status.phoneNumber}
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* QR Code button (only for WAHA when connecting) */}
            {status.qrCode && status.connectionType === 'waha' && (
              <button
                onClick={() => setShowQRCode(true)}
                className="p-2 text-blue-500 hover:bg-blue-100 rounded-lg transition-colors"
                title="Ver QR Code"
              >
                <FaQrcode />
              </button>
            )}

            {/* Settings button */}
            <button
              onClick={() => setShowSettings(true)}
              className="p-2 text-gray-500 hover:bg-gray-100 rounded-lg transition-colors"
              title="Configurações do WhatsApp"
            >
              <FaCog />
            </button>
          </div>
        </div>

        {/* Additional connection info */}
        {status.connectionType && (
          <div className="mt-3 flex items-center space-x-4 text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <FaCloud />
              <span>
                {status.connectionType === 'waha' ? 'WAHA API' : 'Twilio Business API'}
              </span>
            </div>
          </div>
        )}

        {/* WAHA Health Status */}
        {status.connectionType === 'waha' && (
          <div className="mt-3 p-2 rounded-md bg-gray-50 border border-gray-200">
            <div className="flex items-center space-x-2 text-xs">
              {getWAHAHealthIcon()}
              <span className={`font-medium ${getWAHAHealthColor()}`}>
                {wahaHealth.message}
              </span>
            </div>
            {wahaHealth.lastChecked && (
              <div className="text-xs text-gray-400 mt-1">
                Última verificação: {new Date(wahaHealth.lastChecked).toLocaleTimeString('pt-BR')}
              </div>
            )}
          </div>
        )}

        {/* Quick actions for error state */}
        {status.connectionStatus === 'error' && (
          <div className="mt-3">
            <button
              onClick={() => setShowSettings(true)}
              className="text-xs text-red-600 hover:text-red-700 underline"
            >
              Verificar configurações
            </button>
          </div>
        )}
      </div>

      {/* QR Code Modal */}
      {showQRCode && status.qrCode && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">QR Code WhatsApp</h3>
              <button
                onClick={() => setShowQRCode(false)}
                className="text-gray-400 hover:text-gray-500"
              >
                ×
              </button>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Escaneie este QR Code com seu WhatsApp
              </p>
              <img 
                src={`data:image/png;base64,${status.qrCode}`} 
                alt="QR Code" 
                className="mx-auto max-w-full"
              />
              <p className="text-xs text-gray-500 mt-4">
                Abra o WhatsApp → Menu → Dispositivos conectados → Conectar dispositivo
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Settings Modal */}
      {showSettings && (
        <WhatsAppSettingsModal onClose={() => setShowSettings(false)} />
      )}
    </>
  );
};

export default WhatsAppStatusComponent;