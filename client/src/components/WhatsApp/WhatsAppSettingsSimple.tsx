import React, { useState } from 'react';
import { FaWhatsapp, FaTimes } from 'react-icons/fa';

interface Props {
  onClose: () => void;
}

const WhatsAppSettingsSimple: React.FC<Props> = ({ onClose }) => {
  const [loading, setLoading] = useState(false);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FaWhatsapp className="text-2xl text-green-500" />
            <h2 className="text-xl font-semibold text-gray-900">Configurações do WhatsApp</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          <div className="text-center">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Configurações do WhatsApp (Versão Simplificada)
            </h3>
            <p className="text-gray-600">
              Esta é uma versão simplificada para debug. O componente completo será restaurado após resolver o problema.
            </p>
          </div>

          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-sm text-yellow-800">
              🚧 <strong>Modo Debug:</strong> Se você está vendo esta tela, significa que o componente original 
              estava causando problemas de renderização.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Fechar
          </button>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppSettingsSimple;