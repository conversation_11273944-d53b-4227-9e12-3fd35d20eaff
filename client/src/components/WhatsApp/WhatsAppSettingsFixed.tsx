import React, { useState, useEffect } from 'react';
import { FaWhatsapp, FaQrcode, FaCloud, FaPhone, FaKey, FaCheck, FaTimes, FaSpinner, FaCog } from 'react-icons/fa';

interface WhatsAppSettings {
  whatsappEnabled: boolean;
  whatsappType: 'twilio' | 'waha' | 'customer-app';
  twilioAccountSid: string;
  twilioAuthToken: string;
  twilioPhoneNumber: string;
  wahaApiUrl: string;
  wahaApiKey: string;
  establishmentId: string;
}

interface Props {
  onClose: () => void;
}

const WhatsAppSettingsFixed: React.FC<Props> = ({ onClose }) => {
  const [settings, setSettings] = useState<WhatsAppSettings>({
    whatsappEnabled: false,
    whatsappType: 'waha',
    twilioAccountSid: '',
    twilioAuthToken: '',
    twilioPhoneNumber: '',
    wahaApiUrl: process.env.REACT_APP_WAHA_API_URL || 'http://localhost:8090',
    wahaApiKey: process.env.REACT_APP_WAHA_API_KEY || '',
    establishmentId: ''
  });

  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const response = await fetch('/api/settings');
      if (response.ok) {
        const data = await response.json();
        setSettings({
          whatsappEnabled: data.whatsappEnabled || false,
          whatsappType: data.whatsappType || 'waha',
          twilioAccountSid: data.twilioAccountSid || '',
          twilioAuthToken: data.twilioAuthToken || '',
          twilioPhoneNumber: data.twilioPhoneNumber || '',
          wahaApiUrl: data.wahaApiUrl || process.env.REACT_APP_WAHA_API_URL || 'http://localhost:8090',
          wahaApiKey: data.wahaApiKey || process.env.REACT_APP_WAHA_API_KEY || '',
          establishmentId: data.establishmentId || data.trinksEstabelecimentoId || ''
        });
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      setMessage({ type: 'error', text: 'Erro ao carregar configurações' });
    }
  };

  const saveSettings = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/whatsapp/settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Configurações salvas com sucesso!' });
        setTimeout(() => setMessage(null), 3000);
      } else {
        throw new Error('Erro ao salvar configurações');
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Erro ao salvar configurações' });
      setTimeout(() => setMessage(null), 3000);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <FaWhatsapp className="text-2xl text-green-500" />
            <h2 className="text-xl font-semibold text-gray-900">Configurações do WhatsApp</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 transition-colors"
          >
            <FaTimes className="text-xl" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Message */}
          {message && (
            <div className={`p-4 rounded-lg ${
              message.type === 'success' ? 'bg-green-50 text-green-700 border border-green-200' :
              'bg-red-50 text-red-700 border border-red-200'
            }`}>
              <div className="flex items-center space-x-2">
                {message.type === 'success' ? <FaCheck /> : <FaTimes />}
                <p>{message.text}</p>
              </div>
            </div>
          )}

          {/* Settings Form */}
          <div className="space-y-4">
            {/* Enable WhatsApp */}
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="whatsappEnabled"
                checked={settings.whatsappEnabled}
                onChange={(e) => setSettings(prev => ({ ...prev, whatsappEnabled: e.target.checked }))}
                className="w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500"
              />
              <label htmlFor="whatsappEnabled" className="text-sm font-medium text-gray-700">
                Ativar integração com WhatsApp
              </label>
            </div>

            {settings.whatsappEnabled && (
              <>
                {/* Connection Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Tipo de Conexão
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* WAHA Option */}
                    <button
                      type="button"
                      onClick={() => setSettings(prev => ({ ...prev, whatsappType: 'waha' }))}
                      className={`p-4 border-2 rounded-lg transition-colors ${
                        settings.whatsappType === 'waha'
                          ? 'border-green-500 bg-green-50 text-green-700'
                          : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-2">
                        <FaWhatsapp className="text-2xl" />
                        <span className="font-medium">WhatsApp Web</span>
                        <span className="text-sm opacity-75">QR Code</span>
                      </div>
                    </button>

                    {/* Twilio Option */}
                    <button
                      type="button"
                      onClick={() => setSettings(prev => ({ ...prev, whatsappType: 'twilio' }))}
                      className={`p-4 border-2 rounded-lg transition-colors ${
                        settings.whatsappType === 'twilio'
                          ? 'border-blue-500 bg-blue-50 text-blue-700'
                          : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-2">
                        <FaCloud className="text-2xl" />
                        <span className="font-medium">API Oficial</span>
                        <span className="text-sm opacity-75">Twilio</span>
                      </div>
                    </button>

                    {/* Customer App Test Mode */}
                    <button
                      type="button"
                      onClick={() => setSettings(prev => ({ ...prev, whatsappType: 'customer-app' }))}
                      className={`p-4 border-2 rounded-lg transition-colors ${
                        settings.whatsappType === 'customer-app'
                          ? 'border-purple-500 bg-purple-50 text-purple-700'
                          : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <div className="flex flex-col items-center space-y-2">
                        <FaCog className="text-2xl" />
                        <span className="font-medium">Modo Teste</span>
                        <span className="text-sm opacity-75">Customer App</span>
                      </div>
                    </button>
                  </div>
                </div>

                {/* Twilio Settings */}
                {settings.whatsappType === 'twilio' && (
                  <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                    <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                      <FaCloud className="text-blue-500" />
                      <span>Configurações do Twilio</span>
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Account SID
                      </label>
                      <input
                        type="text"
                        value={settings.twilioAccountSid}
                        onChange={(e) => setSettings(prev => ({ ...prev, twilioAccountSid: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="AC..."
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Auth Token
                      </label>
                      <input
                        type="password"
                        value={settings.twilioAuthToken}
                        onChange={(e) => setSettings(prev => ({ ...prev, twilioAuthToken: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="Token de autenticação"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Número do Twilio
                      </label>
                      <input
                        type="text"
                        value={settings.twilioPhoneNumber}
                        onChange={(e) => setSettings(prev => ({ ...prev, twilioPhoneNumber: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="+5511999999999"
                      />
                    </div>
                  </div>
                )}

                {/* WAHA Settings */}
                {settings.whatsappType === 'waha' && (
                  <div className="space-y-4 p-4 bg-green-50 rounded-lg">
                    <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                      <FaWhatsapp className="text-green-500" />
                      <span>Configurações do WhatsApp Web</span>
                    </h3>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        URL da API WAHA
                      </label>
                      <input
                        type="text"
                        value={settings.wahaApiUrl}
                        onChange={(e) => setSettings(prev => ({ ...prev, wahaApiUrl: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="http://localhost:8090"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        API Key (Opcional)
                      </label>
                      <input
                        type="password"
                        value={settings.wahaApiKey}
                        onChange={(e) => setSettings(prev => ({ ...prev, wahaApiKey: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                        placeholder="Deixe em branco para WAHA Core"
                      />
                    </div>
                  </div>
                )}

                {/* Customer App Settings */}
                {settings.whatsappType === 'customer-app' && (
                  <div className="space-y-4 p-4 bg-purple-50 rounded-lg">
                    <h3 className="font-medium text-gray-900 flex items-center space-x-2">
                      <FaCog className="text-purple-500" />
                      <span>Modo Teste</span>
                    </h3>
                    
                    <p className="text-sm text-gray-700">
                      Use o Customer App para simular conversas de clientes.
                    </p>
                    
                    <button
                      onClick={() => window.open('http://localhost:3002', '_blank')}
                      className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors text-sm"
                    >
                      🚀 Abrir Customer App
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
          >
            Cancelar
          </button>
          <button
            onClick={saveSettings}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
          >
            {loading ? <FaSpinner className="animate-spin" /> : <FaCheck />}
            <span>Salvar</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default WhatsAppSettingsFixed;