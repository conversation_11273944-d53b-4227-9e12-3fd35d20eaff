// Test fixtures and mock data
import { Conversation, Message, Customer } from '../../types';

export const mockCustomer: Customer = {
  id: 'customer-123',
  nome: '<PERSON>',
  email: '<EMAIL>',
  telefone: '+5511999999999',
  endereco: {
    rua: '<PERSON>ua das <PERSON>, 123',
    cidade: 'São Paulo',
    cep: '01234-567',
    estado: 'SP'
  },
  dataNascimento: '1990-05-15',
  genero: 'masculino',
  observacoes: 'Cliente VIP',
  tags: ['VIP', 'Frequente'],
  ultimaVisita: new Date('2024-01-15'),
  totalGasto: 1500.00,
  servicosFavoritos: ['Corte Masculino', 'Barba'],
  frequenciaVisitas: 'mensal',
  avaliacaoMedia: 4.8,
  preferenciaHorario: 'manhã',
  profissionalPreferido: 'Carlos',
  metodoPagamentoPreferido: 'pix',
  comunicacaoPreferida: 'whatsapp',
  status: 'ativo',
  dataInscricao: new Date('2023-06-01'),
  fonteCadastro: 'whatsapp'
};

export const mockTextMessage: Message = {
  id: 'msg-001',
  content: 'Olá! Gostaria de agendar um corte.',
  sender: 'user',
  timestamp: new Date('2024-01-15T10:00:00Z'),
  status: 'sent',
  type: 'text'
};

export const mockAiMessage: Message = {
  id: 'msg-002',
  content: 'Olá João! Claro, vou ajudar você a agendar. Que tipo de serviço você gostaria?',
  sender: 'ai',
  timestamp: new Date('2024-01-15T10:01:00Z'),
  status: 'sent',
  type: 'text'
};

export const mockAudioMessage: Message = {
  id: 'msg-003',
  content: 'Mensagem de áudio',
  sender: 'user',
  timestamp: new Date('2024-01-15T10:02:00Z'),
  status: 'sent',
  type: 'audio',
  audioUrl: 'blob:mocked-audio-url',
  audioDuration: 15,
  audioTranscription: 'Quero agendar para amanhã às 14h',
  isProcessing: false
};

export const mockConversation: Conversation = {
  id: 'conv-123',
  customerPhone: '+5511999999999',
  customerName: 'João Silva',
  messages: [mockTextMessage, mockAiMessage, mockAudioMessage],
  lastMessage: new Date('2024-01-15T10:02:00Z'),
  unreadCount: 0,
  whatsappHistoryLoaded: true,
  totalWhatsAppMessages: 25
};

export const mockConversations: Conversation[] = [
  mockConversation,
  {
    id: 'conv-124',
    customerPhone: '+5511888888888',
    customerName: 'Maria Santos',
    messages: [
      {
        id: 'msg-004',
        content: 'Oi, preciso cancelar meu agendamento',
        sender: 'user',
        timestamp: new Date('2024-01-15T09:30:00Z'),
        status: 'sent',
        type: 'text'
      }
    ],
    lastMessage: new Date('2024-01-15T09:30:00Z'),
    unreadCount: 1
  },
  {
    id: 'conv-125',
    customerPhone: '+5511777777777',
    customerName: '+5511777777777', // New customer - shows phone
    messages: [],
    lastMessage: new Date('2024-01-15T09:00:00Z'),
    unreadCount: 0
  }
];

// WebSocket event fixtures
export const mockWebSocketEvents = {
  messageReceived: {
    conversationId: 'conv-123',
    message: mockTextMessage
  },
  customerFound: {
    customer: mockCustomer,
    conversationId: 'conv-123'
  },
  customerNotFound: {
    phone: '+5511777777777',
    conversationId: 'conv-125'
  },
  newConversation: mockConversation,
  conversationUpdated: {
    conversationId: 'conv-123',
    conversation: {
      ...mockConversation,
      messages: [...mockConversation.messages, {
        id: 'msg-new',
        content: 'Nova mensagem',
        sender: 'user',
        timestamp: new Date(),
        status: 'sent',
        type: 'text'
      }]
    }
  },
  aiTypingStart: { conversationId: 'conv-123' },
  aiTypingStop: { conversationId: 'conv-123' },
  aiModeToggled: { conversationId: 'conv-123', isAiMode: false },
  whatsappHistoryLoaded: {
    conversationId: 'conv-123',
    history: [mockTextMessage],
    totalHistoryMessages: 50
  }
};

// Audio test data
export const mockAudioBlob = new Blob(['fake audio data'], { type: 'audio/wav' });
export const mockAudioDuration = 30;

// Error scenarios
export const mockErrors = {
  connectionError: new Error('Failed to connect to server'),
  sendMessageError: new Error('Failed to send message'),
  audioUploadError: new Error('Failed to upload audio'),
  customerSearchError: new Error('Customer search failed')
};