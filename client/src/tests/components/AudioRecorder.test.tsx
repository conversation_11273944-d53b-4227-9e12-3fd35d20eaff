// Unit tests for AudioRecorder component
import React from 'react';
import { screen, waitFor, act } from '@testing-library/react';
import { render, mockMediaRecorder, mockGetUserMedia } from '../utils/test-utils';
import { mockAudioBlob } from '../fixtures/test-data';
import AudioRecorder from '../../components/Chat/AudioRecorder';

const mockProps = {
  onAudioRecorded: jest.fn(),
  disabled: false,
};

describe('AudioRecorder Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset MediaRecorder mock
    mockMediaRecorder.state = 'inactive';
    mockMediaRecorder.start.mockClear();
    mockMediaRecorder.stop.mockClear();
    
    // Reset getUserMedia mock
    mockGetUserMedia.mockClear();
  });

  it('should render record button in initial state', () => {
    render(<AudioRecorder {...mockProps} />);
    
    expect(screen.getByRole('button', { name: /gravar áudio/i })).toBeInTheDocument();
  });

  it('should start recording when record button is clicked', async () => {
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    const recordButton = screen.getByRole('button', { name: /gravar áudio/i });
    
    await user.click(recordButton);
    
    await waitFor(() => {
      expect(mockGetUserMedia).toHaveBeenCalledWith({ audio: true });
    });
  });

  it('should show recording state when recording', async () => {
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    const recordButton = screen.getByRole('button');
    await user.click(recordButton);
    
    // Simulate recording state
    act(() => {
      mockMediaRecorder.state = 'recording';
    });
    
    await waitFor(() => {
      expect(screen.getByText(/gravando/i)).toBeInTheDocument();
    });
  });

  it('should stop recording when stop button is clicked', async () => {
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    // Start recording first
    const recordButton = screen.getByRole('button');
    await user.click(recordButton);
    
    // Simulate recording state
    act(() => {
      mockMediaRecorder.state = 'recording';
    });
    
    await waitFor(() => {
      expect(screen.getByText(/gravando/i)).toBeInTheDocument();
    });
    
    // Click stop
    const stopButton = screen.getByRole('button', { name: /parar gravação/i });
    await user.click(stopButton);
    
    expect(mockMediaRecorder.stop).toHaveBeenCalled();
  });

  it('should handle recording completion and call onAudioRecorded', async () => {
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    // Start recording
    const recordButton = screen.getByRole('button');
    await user.click(recordButton);
    
    // Simulate recording completion
    act(() => {
      mockMediaRecorder.state = 'recording';
      
      // Simulate dataavailable event
      const mockEvent = {
        data: mockAudioBlob,
      };
      
      const dataavailableCallback = mockMediaRecorder.addEventListener.mock.calls
        .find(call => call[0] === 'dataavailable')?.[1];
      
      if (dataavailableCallback) {
        dataavailableCallback(mockEvent);
      }
      
      // Simulate stop event
      const stopCallback = mockMediaRecorder.addEventListener.mock.calls
        .find(call => call[0] === 'stop')?.[1];
      
      if (stopCallback) {
        stopCallback();
      }
    });
    
    await waitFor(() => {
      expect(mockProps.onAudioRecorded).toHaveBeenCalledWith(
        expect.any(Blob),
        expect.any(Number)
      );
    });
  });

  it('should handle microphone permission denied', async () => {
    // Mock getUserMedia to reject
    mockGetUserMedia.mockRejectedValueOnce(new Error('Permission denied'));
    
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    const recordButton = screen.getByRole('button');
    await user.click(recordButton);
    
    await waitFor(() => {
      expect(screen.getByText(/erro ao acessar microfone/i)).toBeInTheDocument();
    });
  });

  it('should be disabled when disabled prop is true', () => {
    render(<AudioRecorder {...mockProps} disabled={true} />);
    
    const recordButton = screen.getByRole('button');
    expect(recordButton).toBeDisabled();
  });

  it('should show recording duration timer', async () => {
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    // Start recording
    const recordButton = screen.getByRole('button');
    await user.click(recordButton);
    
    // Simulate recording state
    act(() => {
      mockMediaRecorder.state = 'recording';
    });
    
    await waitFor(() => {
      expect(screen.getByText(/00:00/)).toBeInTheDocument();
    });
    
    // Fast forward time to check timer updates
    act(() => {
      jest.advanceTimersByTime(2000);
    });
    
    await waitFor(() => {
      expect(screen.getByText(/00:02/)).toBeInTheDocument();
    });
  });

  it('should handle maximum recording duration', async () => {
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    // Start recording
    const recordButton = screen.getByRole('button');
    await user.click(recordButton);
    
    // Simulate max duration reached
    act(() => {
      mockMediaRecorder.state = 'recording';
      jest.advanceTimersByTime(120000); // 2 minutes
    });
    
    await waitFor(() => {
      expect(mockMediaRecorder.stop).toHaveBeenCalled();
    });
  });

  it('should clean up resources on unmount', () => {
    const { unmount } = render(<AudioRecorder {...mockProps} />);
    
    unmount();
    
    // Should clean up any ongoing recordings or timers
    // This is implementation dependent
  });

  it('should handle recording errors gracefully', async () => {
    const { user } = render(<AudioRecorder {...mockProps} />);
    
    // Start recording
    const recordButton = screen.getByRole('button');
    await user.click(recordButton);
    
    // Simulate recording error
    act(() => {
      const errorCallback = mockMediaRecorder.addEventListener.mock.calls
        .find(call => call[0] === 'error')?.[1];
      
      if (errorCallback) {
        errorCallback(new Error('Recording failed'));
      }
    });
    
    await waitFor(() => {
      expect(screen.getByText(/erro na gravação/i)).toBeInTheDocument();
    });
  });
});