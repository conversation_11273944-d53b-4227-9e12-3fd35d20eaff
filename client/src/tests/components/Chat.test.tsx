// Unit tests for Chat component
import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { render } from '../utils/test-utils';
import { mockConversation, mockAudioBlob } from '../fixtures/test-data';
import Chat from '../../components/Chat/Chat';

const mockProps = {
  conversation: mockConversation,
  onSendMessage: jest.fn(),
  onSendAudio: jest.fn(),
  isTyping: false,
  onTransferToHuman: jest.fn(),
  isAiMode: true,
  onLoadMoreWhatsAppHistory: jest.fn(),
  onLoadConversationHistory: jest.fn(),
};

describe('Chat Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render chat header with customer name', () => {
    render(<Chat {...mockProps} />);
    
    expect(screen.getByText('<PERSON>')).toBeInTheDocument();
  });

  it('should display messages in the conversation', () => {
    render(<Chat {...mockProps} />);
    
    expect(screen.getByText('Olá! Gostaria de agendar um corte.')).toBeInTheDocument();
    expect(screen.getByText('Olá João! Claro, vou ajudar você a agendar. Que tipo de serviço você gostaria?')).toBeInTheDocument();
  });

  it('should show typing indicator when AI is typing', () => {
    render(<Chat {...mockProps} isTyping={true} />);
    
    // Look for typing indicator
    expect(screen.getByTestId('typing-indicator')).toBeInTheDocument();
  });

  it('should handle text message sending', async () => {
    const { user } = render(<Chat {...mockProps} />);
    
    // Find message input
    const messageInput = screen.getByPlaceholderText(/digite sua mensagem/i);
    const sendButton = screen.getByRole('button', { name: /enviar/i });
    
    // Type and send message
    await user.type(messageInput, 'Test message');
    await user.click(sendButton);
    
    expect(mockProps.onSendMessage).toHaveBeenCalledWith('Test message');
  });

  it('should handle AI mode toggle', async () => {
    const { user } = render(<Chat {...mockProps} />);
    
    // Find transfer to human button
    const transferButton = screen.getByRole('button', { name: /transferir para humano/i });
    
    await user.click(transferButton);
    
    expect(mockProps.onTransferToHuman).toHaveBeenCalled();
  });

  it('should display AI mode indicator correctly', () => {
    render(<Chat {...mockProps} isAiMode={true} />);
    
    expect(screen.getByText(/modo ia ativo/i)).toBeInTheDocument();
  });

  it('should display human mode indicator correctly', () => {
    render(<Chat {...mockProps} isAiMode={false} />);
    
    expect(screen.getByText(/atendimento humano/i)).toBeInTheDocument();
  });

  it('should handle audio message recording', async () => {
    const { user } = render(<Chat {...mockProps} />);
    
    // Find audio record button
    const recordButton = screen.getByRole('button', { name: /gravar áudio/i });
    
    await user.click(recordButton);
    
    // Should start recording
    expect(screen.getByText(/gravando/i)).toBeInTheDocument();
  });

  it('should handle WhatsApp history loading', async () => {
    const { user } = render(<Chat {...mockProps} />);
    
    // Find load more button (if exists)
    const loadMoreButton = screen.queryByRole('button', { name: /carregar mais/i });
    
    if (loadMoreButton) {
      await user.click(loadMoreButton);
      expect(mockProps.onLoadMoreWhatsAppHistory).toHaveBeenCalled();
    }
  });

  it('should display audio messages correctly', () => {
    render(<Chat {...mockProps} />);
    
    // Check for audio message
    expect(screen.getByText('Mensagem de áudio')).toBeInTheDocument();
    // Check for audio controls
    expect(screen.getByRole('button', { name: /reproduzir áudio/i })).toBeInTheDocument();
  });

  it('should handle empty conversation', () => {
    const emptyConversation = {
      ...mockConversation,
      messages: []
    };
    
    render(<Chat {...mockProps} conversation={emptyConversation} />);
    
    expect(screen.getByText('João Silva')).toBeInTheDocument();
    // Should show empty state or just the input area
  });

  it('should handle message input keyboard shortcuts', async () => {
    const { user } = render(<Chat {...mockProps} />);
    
    const messageInput = screen.getByPlaceholderText(/digite sua mensagem/i);
    
    // Type message and press Enter
    await user.type(messageInput, 'Test message{enter}');
    
    expect(mockProps.onSendMessage).toHaveBeenCalledWith('Test message');
  });

  it('should prevent sending empty messages', async () => {
    const { user } = render(<Chat {...mockProps} />);
    
    const sendButton = screen.getByRole('button', { name: /enviar/i });
    
    // Try to send empty message
    await user.click(sendButton);
    
    expect(mockProps.onSendMessage).not.toHaveBeenCalled();
  });

  it('should show message status indicators', () => {
    render(<Chat {...mockProps} />);
    
    // Check for message status (sent, delivered, etc.)
    // This depends on your MessageBubble component implementation
    const messages = screen.getAllByTestId(/message-/);
    expect(messages.length).toBeGreaterThan(0);
  });
});