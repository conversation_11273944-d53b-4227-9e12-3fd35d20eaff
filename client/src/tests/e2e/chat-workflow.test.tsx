// End-to-end tests for complete chat workflow
import React from 'react';
import { screen, waitFor, act } from '@testing-library/react';
import { render, simulateSocketEvent, expectSocketMethodToBeCalled } from '../utils/test-utils';
import { mockConversations, mockCustomer, mockTextMessage, mockAudioMessage } from '../fixtures/test-data';
import { mockSocketService } from '../mocks/socket.mock';
import App from '../../App';

describe('E2E: Complete Chat Workflow', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should handle complete new customer conversation flow', async () => {
    const { user } = render(<App />);
    
    // 1. App starts and connects
    await waitFor(() => {
      expect(screen.getByText('Conectado')).toBeInTheDocument();
    });
    
    // 2. Search for new customer (not found)
    const searchInput = screen.getByPlaceholderText(/buscar por telefone/i);
    await user.type(searchInput, '+5511777777777');
    await user.press('Enter');
    
    expectSocketMethodToBeCalled('searchCustomer', '+5511777777777');
    
    // 3. Customer not found - create new conversation
    act(() => {
      simulateSocketEvent('customer_not_found', {
        phone: '+5511777777777',
        conversationId: 'conv-new-123'
      });
    });
    
    await waitFor(() => {
      expect(screen.getByText('+5511777777777')).toBeInTheDocument();
    });
    
    // 4. New message arrives from customer
    act(() => {
      simulateSocketEvent('message_received', {
        conversationId: 'conv-new-123',
        message: {
          id: 'msg-new-001',
          content: 'Olá, gostaria de agendar um horário',
          sender: 'user',
          timestamp: new Date(),
          status: 'sent',
          type: 'text'
        }
      });
    });
    
    await waitFor(() => {
      expect(screen.getByText('Olá, gostaria de agendar um horário')).toBeInTheDocument();
    });
    
    // 5. AI responds
    act(() => {
      simulateSocketEvent('ai_typing_start', { conversationId: 'conv-new-123' });
    });
    
    // Wait for typing indicator
    await waitFor(() => {
      expect(screen.getByTestId('typing-indicator')).toBeInTheDocument();
    });
    
    // AI stops typing and sends message
    act(() => {
      simulateSocketEvent('ai_typing_stop', { conversationId: 'conv-new-123' });
      simulateSocketEvent('message_received', {
        conversationId: 'conv-new-123',
        message: {
          id: 'msg-ai-001',
          content: 'Olá! Claro, vou ajudar você a agendar. Que tipo de serviço você precisa?',
          sender: 'ai',
          timestamp: new Date(),
          status: 'sent',
          type: 'text'
        }
      });
    });
    
    await waitFor(() => {
      expect(screen.getByText(/vou ajudar você a agendar/)).toBeInTheDocument();
    });
  });

  it('should handle existing customer conversation with full history', async () => {
    const { user } = render(<App />);
    
    // 1. Search for existing customer
    const searchInput = screen.getByPlaceholderText(/buscar por telefone/i);
    await user.type(searchInput, '+5511999999999');
    await user.press('Enter');
    
    // 2. Customer found with full conversation
    act(() => {
      simulateSocketEvent('customer_found', {
        customer: mockCustomer,
        conversationId: 'conv-123'
      });
      
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
      expect(screen.getByText(mockCustomer.email)).toBeInTheDocument();
    });
    
    // 3. Load WhatsApp history
    act(() => {
      simulateSocketEvent('whatsapp_history_loaded', {
        conversationId: 'conv-123',
        history: [
          {
            id: 'history-001',
            content: 'Mensagem antiga do WhatsApp',
            sender: 'user',
            timestamp: new Date(Date.now() - 86400000), // 1 day ago
            status: 'sent',
            type: 'text'
          }
        ],
        totalHistoryMessages: 25
      });
    });
    
    await waitFor(() => {
      expect(screen.getByText('Mensagem antiga do WhatsApp')).toBeInTheDocument();
    });
    
    // 4. Send a new message
    const messageInput = screen.getByPlaceholderText(/digite sua mensagem/i);
    await user.type(messageInput, 'Nova mensagem do atendente');
    await user.press('Enter');
    
    expectSocketMethodToBeCalled('sendMessage', 'conv-123', 'Nova mensagem do atendente', '+5511999999999');
  });

  it('should handle audio message complete workflow', async () => {
    const { user } = render(<App />);
    
    // Setup existing conversation
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    // Select conversation
    await user.click(screen.getByText('João Silva'));
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
    });
    
    // 1. Start audio recording
    const recordButton = screen.getByRole('button', { name: /gravar áudio/i });
    await user.click(recordButton);
    
    await waitFor(() => {
      expect(screen.getByText(/gravando/i)).toBeInTheDocument();
    });
    
    // 2. Stop recording
    const stopButton = screen.getByRole('button', { name: /parar gravação/i });
    await user.click(stopButton);
    
    // 3. Audio message should be sent
    await waitFor(() => {
      expectSocketMethodToBeCalled('sendAudio');
    });
    
    // 4. Simulate audio message processing
    act(() => {
      simulateSocketEvent('message_received', {
        conversationId: 'conv-123',
        message: {
          ...mockAudioMessage,
          isProcessing: true
        }
      });
    });
    
    // 5. Audio processing completes
    act(() => {
      simulateSocketEvent('message_updated', {
        conversationId: 'conv-123',
        message: {
          ...mockAudioMessage,
          isProcessing: false,
          audioTranscription: 'Transcrição do áudio processado'
        }
      });
    });
    
    await waitFor(() => {
      expect(screen.getByText('Transcrição do áudio processado')).toBeInTheDocument();
    });
  });

  it('should handle AI mode toggle workflow', async () => {
    const { user } = render(<App />);
    
    // Setup conversation
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    // Select conversation
    await user.click(screen.getByText('João Silva'));
    
    await waitFor(() => {
      expect(screen.getByText(/modo ia ativo/i)).toBeInTheDocument();
    });
    
    // Toggle to human mode
    const transferButton = screen.getByRole('button', { name: /transferir para humano/i });
    await user.click(transferButton);
    
    expectSocketMethodToBeCalled('toggleAiMode', 'conv-123', false);
    
    // Simulate mode change response
    act(() => {
      simulateSocketEvent('ai_mode_toggled', {
        conversationId: 'conv-123',
        isAiMode: false
      });
    });
    
    await waitFor(() => {
      expect(screen.getByText(/atendimento humano/i)).toBeInTheDocument();
    });
    
    // Toggle back to AI mode
    const aiButton = screen.getByRole('button', { name: /ativar ia/i });
    await user.click(aiButton);
    
    expectSocketMethodToBeCalled('toggleAiMode', 'conv-123', true);
  });

  it('should handle multiple conversations workflow', async () => {
    const { user } = render(<App />);
    
    // Add multiple conversations
    act(() => {
      mockConversations.forEach(conv => {
        simulateSocketEvent('new_conversation', conv);
      });
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
      expect(screen.getByText('Maria Santos')).toBeInTheDocument();
    });
    
    // Select first conversation
    await user.click(screen.getByText('João Silva'));
    
    await waitFor(() => {
      expect(screen.getByText('Olá! Gostaria de agendar um corte.')).toBeInTheDocument();
    });
    
    // Switch to second conversation
    await user.click(screen.getByText('Maria Santos'));
    
    await waitFor(() => {
      expect(screen.getByText('Oi, preciso cancelar meu agendamento')).toBeInTheDocument();
    });
    
    // Send message in second conversation
    const messageInput = screen.getByPlaceholderText(/digite sua mensagem/i);
    await user.type(messageInput, 'Claro, vou ajudar com o cancelamento');
    await user.press('Enter');
    
    expectSocketMethodToBeCalled('sendMessage', 'conv-124', 'Claro, vou ajudar com o cancelamento', '+5511888888888');
  });

  it('should handle error scenarios gracefully', async () => {
    const { user } = render(<App />);
    
    // Simulate connection error
    act(() => {
      simulateSocketEvent('disconnect');
    });
    
    await waitFor(() => {
      expect(screen.getByText('Desconectado')).toBeInTheDocument();
    });
    
    // Try to send message while disconnected
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    await user.click(screen.getByText('João Silva'));
    
    const messageInput = screen.getByPlaceholderText(/digite sua mensagem/i);
    await user.type(messageInput, 'Test message');
    await user.press('Enter');
    
    // Should still attempt to send (socket service handles the error)
    expectSocketMethodToBeCalled('sendMessage');
    
    // Reconnect
    act(() => {
      simulateSocketEvent('connect');
    });
    
    await waitFor(() => {
      expect(screen.getByText('Conectado')).toBeInTheDocument();
    });
  });
});