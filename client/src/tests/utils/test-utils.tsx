// Testing utilities and custom render functions
import React, { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { mockSocketService } from '../mocks/socket.mock';

// Mock the socket service module
jest.mock('../../services/socket', () => ({
  __esModule: true,
  default: mockSocketService,
}));

// Custom render function with providers
const AllTheProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div data-testid="test-wrapper">
      {children}
    </div>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => {
  const user = userEvent.setup();
  
  return {
    user,
    ...render(ui, { wrapper: AllTheProviders, ...options }),
  };
};

// Utility functions for testing
export const waitForSocketConnection = () => {
  return new Promise(resolve => {
    if (mockSocketService.isConnected()) {
      resolve(true);
      return;
    }
    
    mockSocketService.on('connect', () => resolve(true));
    mockSocketService.connect();
  });
};

export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 100));
};

// Mock audio recording
export const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  pause: jest.fn(),
  resume: jest.fn(),
  state: 'inactive' as RecordingState,
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
};

// Mock getUserMedia
export const mockGetUserMedia = jest.fn().mockImplementation(() =>
  Promise.resolve({
    getTracks: () => [
      {
        stop: jest.fn(),
        kind: 'audio',
        enabled: true,
      },
    ],
    getAudioTracks: () => [
      {
        stop: jest.fn(),
        kind: 'audio',
        enabled: true,
      },
    ],
  })
);

// Setup global mocks
beforeEach(() => {
  // Reset all mocks
  jest.clearAllMocks();
  
  // Mock MediaRecorder
  global.MediaRecorder = jest.fn().mockImplementation(() => mockMediaRecorder);
  
  // Mock getUserMedia
  Object.defineProperty(navigator, 'mediaDevices', {
    writable: true,
    value: {
      getUserMedia: mockGetUserMedia,
      enumerateDevices: jest.fn().mockResolvedValue([]),
    },
  });
  
  // Reset socket service state
  mockSocketService.removeAllListeners();
});

// Custom assertions
export const expectSocketMethodToBeCalled = (method: string, ...args: any[]) => {
  expect((mockSocketService as any)[method]).toHaveBeenCalledWith(...args);
};

export const expectMessageInConversation = (messages: any[], messageContent: string) => {
  const message = messages.find(m => m.content === messageContent);
  expect(message).toBeDefined();
  return message;
};

export const simulateSocketEvent = (event: string, data?: any) => {
  mockSocketService.emit(event, data);
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { customRender as render };
export { userEvent };