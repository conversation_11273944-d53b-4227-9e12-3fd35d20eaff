// Integration tests for the main App component
import React from 'react';
import { screen, waitFor, act } from '@testing-library/react';
import { render, expectSocketMethodToBeCalled, simulateSocketEvent } from '../utils/test-utils';
import { mockSocketService } from '../mocks/socket.mock';
import { mockConversations, mockCustomer, mockWebSocketEvents } from '../fixtures/test-data';
import App from '../../App';

describe('App Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the main app layout', () => {
    render(<App />);
    
    // Check for main layout elements
    expect(screen.getByText('Vem dar um up no seu atendimento!')).toBeInTheDocument();
    expect(screen.getByText('Conectado')).toBeInTheDocument();
  });

  it('should establish socket connection on mount', async () => {
    render(<App />);
    
    await waitFor(() => {
      expect(mockSocketService.connect).toHaveBeenCalled();
    });
  });

  it('should display conversations when they arrive', async () => {
    render(<App />);
    
    // Simulate socket connection
    act(() => {
      simulateSocketEvent('connect');
    });
    
    // Simulate new conversation
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
    });
  });

  it('should handle message reception and display', async () => {
    render(<App />);
    
    // Setup conversation first
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    // Simulate message received
    act(() => {
      simulateSocketEvent('message_received', mockWebSocketEvents.messageReceived);
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
    });
  });

  it('should handle customer search and display results', async () => {
    render(<App />);
    
    // Simulate customer found
    act(() => {
      simulateSocketEvent('customer_found', mockWebSocketEvents.customerFound);
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
    });
  });

  it('should handle conversation selection', async () => {
    const { user } = render(<App />);
    
    // Add a conversation
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
    });
    
    // Click on conversation
    const conversationElement = screen.getByText('João Silva');
    await user.click(conversationElement);
    
    // Should trigger customer search
    expect(mockSocketService.searchCustomer).toHaveBeenCalledWith('+5511999999999');
  });

  it('should handle typing indicators', async () => {
    render(<App />);
    
    // Setup conversation and select it
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    // Simulate typing start
    act(() => {
      simulateSocketEvent('ai_typing_start', { conversationId: 'conv-123' });
    });
    
    // Note: Typing indicator would only show if conversation is active
    // This test validates the event handling
    expect(mockSocketService.onAITypingStart).toHaveBeenCalled();
  });

  it('should handle AI mode toggling', async () => {
    render(<App />);
    
    // Setup conversation
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    // Simulate AI mode toggle
    act(() => {
      simulateSocketEvent('ai_mode_toggled', mockWebSocketEvents.aiModeToggled);
    });
    
    // The app should handle this event
    expect(mockSocketService.onAiModeToggled).toHaveBeenCalled();
  });

  it('should handle audio message processing', async () => {
    render(<App />);
    
    // Setup conversation with audio message
    const conversationWithAudio = {
      ...mockConversations[0],
      messages: [
        ...mockConversations[0].messages,
        {
          id: 'audio-msg',
          content: 'Mensagem de áudio',
          sender: 'user' as const,
          timestamp: new Date(),
          status: 'sent' as const,
          type: 'audio' as const,
          audioUrl: 'blob:mock-url',
          audioDuration: 30,
          isProcessing: true
        }
      ]
    };
    
    act(() => {
      simulateSocketEvent('new_conversation', conversationWithAudio);
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
    });
  });

  it('should handle WhatsApp history loading', async () => {
    render(<App />);
    
    // Setup conversation
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    // Simulate WhatsApp history loaded
    act(() => {
      simulateSocketEvent('whatsapp_history_loaded', mockWebSocketEvents.whatsappHistoryLoaded);
    });
    
    expect(mockSocketService.onWhatsAppHistoryLoaded).toHaveBeenCalled();
  });

  it('should handle conversation updates', async () => {
    render(<App />);
    
    // Setup initial conversation
    act(() => {
      simulateSocketEvent('new_conversation', mockConversations[0]);
    });
    
    // Update conversation
    act(() => {
      simulateSocketEvent('conversation_updated', mockWebSocketEvents.conversationUpdated);
    });
    
    await waitFor(() => {
      expect(screen.getByText('João Silva')).toBeInTheDocument();
    });
  });

  it('should handle new customer (phone only) scenarios', async () => {
    render(<App />);
    
    // Simulate customer not found
    act(() => {
      simulateSocketEvent('customer_not_found', mockWebSocketEvents.customerNotFound);
    });
    
    await waitFor(() => {
      expect(screen.getByText('+5511777777777')).toBeInTheDocument();
    });
  });

  it('should handle errors gracefully', async () => {
    render(<App />);
    
    // Simulate error
    act(() => {
      simulateSocketEvent('error', { message: 'Connection failed' });
    });
    
    expect(mockSocketService.onError).toHaveBeenCalled();
  });

  it('should cleanup socket listeners on unmount', () => {
    const { unmount } = render(<App />);
    
    unmount();
    
    expect(mockSocketService.removeAllListeners).toHaveBeenCalled();
    expect(mockSocketService.disconnect).toHaveBeenCalled();
  });
});