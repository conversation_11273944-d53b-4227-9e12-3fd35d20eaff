// Socket.IO Mock for Testing
import { Conversation, Message, Customer } from '../../types';

export class MockSocketService {
  private listeners: Map<string, Function[]> = new Map();
  private connected = false;

  // Mock implementation of socket service methods
  connect = jest.fn().mockImplementation(() => {
    this.connected = true;
    this.emit('connect');
    return this;
  });

  disconnect = jest.fn().mockImplementation(() => {
    this.connected = false;
    this.emit('disconnect');
  });

  isConnected = jest.fn().mockImplementation(() => this.connected);

  // Event listeners
  on = jest.fn().mockImplementation((event: string, callback: Function) => {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, []);
    }
    this.listeners.get(event)!.push(callback);
  });

  off = jest.fn().mockImplementation((event: string, callback?: Function) => {
    if (!callback) {
      this.listeners.delete(event);
      return;
    }
    
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      const index = eventListeners.indexOf(callback);
      if (index > -1) {
        eventListeners.splice(index, 1);
      }
    }
  });

  removeAllListeners = jest.fn().mockImplementation(() => {
    this.listeners.clear();
  });

  // Emit events (for testing)
  emit = jest.fn().mockImplementation((event: string, data?: any) => {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => callback(data));
    }
  });

  // Business logic methods
  searchCustomer = jest.fn();
  sendMessage = jest.fn();
  sendAudio = jest.fn();
  selectConversation = jest.fn();
  toggleAiMode = jest.fn();
  loadMoreWhatsAppHistory = jest.fn();
  loadConversationHistory = jest.fn();

  // Event listener methods
  onMessageReceived = jest.fn().mockImplementation((callback: Function) => {
    this.on('message_received', callback);
  });

  onMessageSent = jest.fn().mockImplementation((callback: Function) => {
    this.on('message_sent', callback);
  });

  onMessageUpdated = jest.fn().mockImplementation((callback: Function) => {
    this.on('message_updated', callback);
  });

  onCustomerFound = jest.fn().mockImplementation((callback: Function) => {
    this.on('customer_found', callback);
  });

  onCustomerNotFound = jest.fn().mockImplementation((callback: Function) => {
    this.on('customer_not_found', callback);
  });

  onNewConversation = jest.fn().mockImplementation((callback: Function) => {
    this.on('new_conversation', callback);
  });

  onConversationUpdated = jest.fn().mockImplementation((callback: Function) => {
    this.on('conversation_updated', callback);
  });

  onAITypingStart = jest.fn().mockImplementation((callback: Function) => {
    this.on('ai_typing_start', callback);
  });

  onAITypingStop = jest.fn().mockImplementation((callback: Function) => {
    this.on('ai_typing_stop', callback);
  });

  onAiModeToggled = jest.fn().mockImplementation((callback: Function) => {
    this.on('ai_mode_toggled', callback);
  });

  onWhatsAppHistoryLoaded = jest.fn().mockImplementation((callback: Function) => {
    this.on('whatsapp_history_loaded', callback);
  });

  onConversationHistoryResponse = jest.fn().mockImplementation((callback: Function) => {
    this.on('conversation_history_response', callback);
  });

  onError = jest.fn().mockImplementation((callback: Function) => {
    this.on('error', callback);
  });

  // Test helpers
  simulateMessage(conversationId: string, message: Message) {
    this.emit('message_received', { conversationId, message });
  }

  simulateCustomerFound(customer: Customer, conversationId: string) {
    this.emit('customer_found', { customer, conversationId });
  }

  simulateNewConversation(conversation: Conversation) {
    this.emit('new_conversation', conversation);
  }

  simulateTyping(conversationId: string, start = true) {
    this.emit(start ? 'ai_typing_start' : 'ai_typing_stop', { conversationId });
  }
}

export const mockSocketService = new MockSocketService();