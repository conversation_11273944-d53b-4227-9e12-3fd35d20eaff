import React from 'react';
import { render, screen } from '@testing-library/react';
import App from './App';

// Mock do serviço de socket
jest.mock('./services/socket', () => ({
  __esModule: true,
  default: {
    connect: jest.fn().mockReturnValue({
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
    }),
    disconnect: jest.fn(),
    removeAllListeners: jest.fn(),
    onMessageReceived: jest.fn(),
    onMessageSent: jest.fn(),
    onMessageUpdated: jest.fn(),
    onCustomerFound: jest.fn(),
    onCustomerNotFound: jest.fn(),
    onNewConversation: jest.fn(),
    onConversationUpdated: jest.fn(),
    onAITypingStart: jest.fn(),
    onAITypingStop: jest.fn(),
    onAiModeToggled: jest.fn(),
    onWhatsAppHistoryLoaded: jest.fn(),
    onConversationHistoryResponse: jest.fn(),
    onError: jest.fn(),
  },
}));

test('renders Trinks welcome message', () => {
  render(<App />);
  const welcomeElement = screen.getByText(/Vem dar um up no seu atendimento/i);
  expect(welcomeElement).toBeInTheDocument();
});