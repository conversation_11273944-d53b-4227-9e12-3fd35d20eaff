# Trinks IA - Sistema de Atendimento Inteligente

## 📋 Visão Geral do Projeto

Sistema completo de atendimento inteligente para salões de beleza com:
- Frontend React/TypeScript com interface WhatsApp-like
- Backend Node.js/Express com IA conversacional
- Integração com API Trinks para agendamentos
- Comunicação em tempo real via WebSocket
- Sistema de estados para fluxos conversacionais


## 🏗️ Arquitetura do Sistema

### Estrutura de Diretórios
```
especializados
├── client/              # Frontend React (Aplicação do Salão)
├── customer-app/        # Frontend React (Simulador de Cliente)  
├── server/              # Backend Node.js/Express
│   ├── backup/         # Código legado não utilizado (NÃO MODIFICAR)
│   ├── core/           # Sistema central (Estado, Cache, RAG)
│   ├── routes/         # Rotas da API REST
│   └── services/       # Serviços (IA, WhatsApp, Trinks)
├── shared/             # Tipos TypeScript compartilhados
└── prompts/            # Templates de prompts para IA
```

## 🔧 Configuração e Desenvolvimento

### 📂 Repositório GitHub
- **URL**: https://github.com/fbichara/atendimento-inteligente
- **Branch Principal**: main
- **Clone**: `git clone https://github.com/fbichara/atendimento-inteligente.git`

### Variáveis de Ambiente Obrigatórias
- **ANTHROPIC_API_KEY**: Chave da API Claude (obrigatória para IA real)
- **TRINKS_API_KEY**: Chave da API Trinks para agendamentos
- **TRINKS_ESTABLISHMENT_ID**: ID do estabelecimento na Trinks
- **DATABASE_URL**: Connection string PostgreSQL para cache persistente (opcional)
- **PORT**: Porta do servidor (padrão: 3001)

### Scripts de Desenvolvimento
- `npm run dev`: Inicia servidor em modo desenvolvimento
- `npm run lint`: Verifica qualidade do código
- `npm run security-audit`: Auditoria de segurança

### 🧪 Testes Abrangentes da API Trinks - OBRIGATÓRIO ✅
**REGRA**: Sempre realizar testes completos usando chaves reais do projeto para validar estabilidade.

#### Comando de Teste Completo:
```bash
TRINKS_API_KEY=i0fFPhCnix6JuqiOWAIZNaJNHZx89zst9zfSH22c TRINKS_ESTABLISHMENT_ID=188253 node -e "
const trinksService = require('./services/trinks');
// [script de teste abrangente - ver implementação em docs/TESTING.md]
"
```

#### Últimos Resultados Validados:
- ✅ **90% taxa de sucesso** (9/10 métodos funcionando)
- ✅ **154 serviços** carregados corretamente
- ✅ **12 profissionais** obtidos com sucesso
- ✅ **58 slots disponíveis** (bug de endpoint corrigido)
- ✅ **Sistema estável para produção**

### Comandos para Executar o Sistema
```bash
# Clone do repositório
git clone https://github.com/fbichara/atendimento-inteligente.git
cd atendimento-inteligente

# Terminal 1 - Servidor
cd server && npm run dev

# Terminal 2 - App do Salão  
cd client && npm start

# Terminal 3 - App do Cliente (testes)
cd customer-app && npm start
```

### 🔄 Workflow Git
Ver documentação completa: **[docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)**

## 🤖 Sistema de IA

### Integração Claude AI
- **OBRIGATÓRIO**: Configure ANTHROPIC_API_KEY no .env para IA real
- Sem a chave: Sistema usa respostas mock simuladas
- Com a chave: Respostas inteligentes e contextuais do Claude
- Modelo recomendado: claude-sonnet-4-20250514

### Sistema de IA Unificado
- **services/ai.js**: Lógica principal de IA com cache inteligente
- **Cache de Cliente**: TTL de 15 minutos para otimização
- **Cache de Disponibilidade**: TTL de 5 minutos para performance
- **Lazy Loading**: Carregamento inteligente apenas quando necessário
- **Prompts Humanizados**: Linguagem brasileira autêntica


### ⚡ SISTEMA CRÍTICO: Verificação de Disponibilidade
**REGRA FUNDAMENTAL**: O sistema NUNCA deve dizer "vou verificar" ou "aguarde". Sempre busca dados ANTES de responder.

#### Fluxo de Disponibilidade:
1. **Detecção Automática**: Identifica data + profissional na conversa
2. **Busca Imediata**: Consulta API Trinks em tempo real
3. **Resposta Direta**: Informa disponibilidade sem "verificar"

API da Trinks

documentação api trinks https://trinks.readme.io/reference/introducao e swagger da doc. https://api.trinks.com/swagger/index.html     


#### Endpoints Utilizados:
- `/v1/profissionais`: Buscar ID do profissional por nome
- `/v1/agendamentos/profissionais/{data}`: Slots específicos do profissional
- `/v1/agendamentos/profissionais/data`: Disponibilidade geral

#### Funções Principais:
- `handleProgressiveAvailability()`: Detecta quando buscar disponibilidade
- `fetchSpecificProfessionalSlots()`: Busca slots de profissional específico
- `getProfessionalIdByName()`: Converte nome em ID via API (SEM HARDCODE)

### Fluxo de Estados Conversacionais
1. **reception**: Recepção inicial e identificação
2. **scheduling**: Processo de agendamento
3. **confirmation**: Confirmação de dados
4. **cancellation**: Cancelamento/reagendamento
5. **completed**: Conversa finalizada

## 🔗 Integrações Externas

### API Resources
- Postman da Evolution API: https://www.postman.com/agenciadgcode/evolution-api/collection/jn0bbzv/evolution-api-v2-2-2

### Trinks API (Produção)
- **Base URL**: https://api.trinks.com
- **API Key**: i0fFPhCnix6JuqiOWAIZNaJNHZx89zst9zfSH22c
- **Establishment ID**: 188253

### WhatsApp Integration
- Evolution API para integração WhatsApp Business
- WAHA Provider para desenvolvimento
- Suporte a áudio, imagens e documentos

## 🎨 Frontend e UX

### Design System
- Baseado em shadcn/ui components
- Tailwind CSS para estilização
- Interface responsiva WhatsApp-like
- Modo escuro/claro (theme switching)

### Componentes Principais
- **Chat**: Interface de conversa principal
- **CustomerPanel**: CRM com dados do cliente
- **Sidebar**: Lista de conversas ativas
- **AudioRecorder**: Gravação de mensagens de voz


## 📊 Monitoramento e Logs
Sistema completo de logs e métricas de IA implementado.

### 📋 Logs Otimizados por Padrão ✅
- **Modo Padrão**: Logs conciso (1-5KB) mantendo informações essenciais
- **Modo Verbose**: `CONCISE_LOGS=false` para debug completo (100-500KB)
- **Informações Mantidas**: Mensagens usuário, respostas IA, prompts, erros, fallbacks
- **Redução**: ~99% menos dados mantendo 100% da informação relevante

**Ver detalhes**: **[docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)**

## 🔒 Segurança e Guardrails

### Validações de Entrada
- Express-validator para sanitização
- CORS configurado
- Helmet para headers de segurança

### Guardrails de IA
- Filtros de conteúdo inadequado
- Limitação de contexto conversacional
- Fallbacks para falhas de IA
- Auditoria de interações

## 📋 Diretrizes de Desenvolvimento

### Princípios Gerais
- Sempre usar IA para análise contextual, evitar hardcoding
- Todas as funcionalidades devem ter fallbacks
- Configurações críticas documentadas no README e código
- Seguir padrões de Clean Architecture

### Manutenção do CLAUDE.md
- Sempre adicionar diretrizes importantes solicitadas pelo usuário
- Documentar mudanças significativas na arquitetura
- Manter configurações de produção atualizadas
- Incluir novos cenários de teste relevantes

### Padrões de Código
- TypeScript strict mode habilitado
- ESLint + Prettier para formatação
- Commits semânticos (feat:, fix:, refactor:, docs:)
- Testes obrigatórios para novas features
- Git workflow: feature branches → main
- Pull requests obrigatórios para mudanças críticas

### Performance e Escalabilidade
Otimizações implementadas para produção.
**Ver detalhes**: **[docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)**



### Configurações de Produção
```env
NODE_ENV=production
ANTHROPIC_API_KEY=<chave_producao>
TRINKS_API_KEY=i0fFPhCnix6JuqiOWAIZNaJNHZx89zst9zfSH22c
TRINKS_ESTABLISHMENT_ID=188253
```


## 🎨 SISTEMA DE TEMPLATES CONFIGURÁVEIS ✅

> **📚 Documentação Detalhada:**
> - **[docs/TEMPLATES.md](docs/TEMPLATES.md)** - Exemplos e casos de uso
> - **[docs/ARCHITECTURE.md](docs/ARCHITECTURE.md)** - Implementação técnica
> - **[docs/TESTING.md](docs/TESTING.md)** - Scripts e validação
> - **[docs/DEPLOYMENT.md](docs/DEPLOYMENT.md)** - Deploy e monitoramento

### 🏆 IMPLEMENTAÇÃO COMPLETA
- **❌ ZERO HARDCODE**: Nenhum prompt fixo no código
- **🎨 PERSONALIZAÇÃO TOTAL**: Cada salão define seu tom de voz  
- **💾 CACHE INTELIGENTE**: PostgreSQL + Memory fallback
- **🌐 API REST**: Gerenciamento completo via endpoints

## 🗺️ SISTEMA DE MAPEAMENTO PROFISSIONAL → SERVIÇOS ✅

### 📋 **Funcionalidade**
Sistema completo que mapeia quais serviços cada profissional pode realizar, eliminando respostas genéricas como "não consigo ver quais profissionais fazem cada serviço".

### ⚙️ **Implementação**
- **Cache Especializado**: `server/utils/professionalServicesMapping.js`
- **Busca Automática**: `/v1/profissionais` + `/v1/profissionais/{id}/servicos` para cada profissional
- **Atualização Periódica**: Cache se renova automaticamente a cada 6 horas
- **Fallback Robusto**: Sistema antigo como backup se novo falhar

### 🔧 **Estrutura do Cache**
```javascript
{
  "782094": {
    "id": "782094",
    "nome": "João da Silva",
    "apelido": "João", 
    "servicos": [
      { "id": 11866299, "nome": "BARBA 15", "preco": 30, "duracaoEmMinutos": 15 },
      { "id": 11866300, "nome": "BARBA 50", "preco": 50, "duracaoEmMinutos": 45 }
    ]
  }
}
```

### 📡 **Endpoints de Debug**
- **GET** `/api/debug/professional-services` - Visualizar cache completo
- **POST** `/api/debug/professional-services/update` - Forçar atualização
- **GET** `/api/debug/professional/joão/services` - Buscar serviços de profissional específico

### ✅ **Resultado Esperado**
- **Pergunta**: "o joão faz barba 50?"
- **Resposta IA**: "Sim! O João faz BARBA 50 sim! São 45 minutinhos por R$50. Quer agendar com ele?"
- **Antes**: "não consigo ver exatamente quais profissionais fazem cada serviço específico"



## 🚨 REGRAS CRÍTICAS DE IMPLEMENTAÇÃO

### ❌ PROIBIÇÕES ABSOLUTAS
- **HARDCODE**: ✅ ELIMINADO - Sistema 100% configurável
- **"VOU VERIFICAR"**: Sistema NUNCA pode usar essas expressões:
  - "vou verificar" / "vou consultar" / "vou checar"
  - "aguarde" / "um momento" / "deixe-me verificar"
  - "rapidinho" / "só um minutinho" / "deixa eu ver"

### ✅ COMPORTAMENTO OBRIGATÓRIO
- **BUSCA ANTES DE RESPONDER**: Sempre consultar API antes da resposta
- **DADOS VIA API**: Profissionais, serviços e disponibilidade sempre da API Trinks
- **RESPOSTA DIRETA**: "Ágata está livre às 15h e 16h!" (não "vou verificar a Ágata")
- **TEMPLATES PERSONALIZADOS**: ✅ Cada salão tem seu tom único
- **CONFIRMAÇÃO OBRIGATÓRIA**: Sistema SEMPRE solicita confirmação antes de criar agendamentos
- **FLUXO DE ESTADOS**: Gerenciamento rigoroso de estados de conversa sem conflitos
- **MAPEAMENTO PROFISSIONAL → SERVIÇOS**: ✅ Sistema sempre sabe quais serviços cada profissional oferece

## 🔄 FLUXOS DE CONFIRMAÇÃO OBRIGATÓRIOS

### 📅 AGENDAMENTO - Fluxo Completo ✅
```
1. Coleta de dados → 2. Validação → 3. CONFIRMAÇÃO → 4. Criação
```
- **REGRA**: Sistema NUNCA cria agendamentos automaticamente
- **ESTÁGIO**: `awaiting_appointment_confirmation` 
- **MENSAGEM**: Via cache semântico (`appointment_request_confirm`)
- **CONFIRMAÇÃO**: Via cache semântico (`appointment_confirm`)
- **FALLBACK**: Análise por regras quando IA indisponível

### 🚫 CANCELAMENTO - Fluxo Unificado ✅
```
1. Detecção → 2. Identificação → 3. CONFIRMAÇÃO → 4. Execução
```
- **REGRA**: Sistema NUNCA cancela agendamentos automaticamente  
- **PREVENÇÃO**: Conflitos entre fluxos antigos/novos eliminados
- **VERIFICAÇÃO**: `awaitingConfirmation` antes de executar fluxo
- **ESTÁGIO**: `awaiting_cancellation_confirmation` (único ponto)

### 🤖 INTEGRAÇÃO LANGGRAPH OBRIGATÓRIA
- **DETECÇÃO**: Sempre usar LangGraph para análise de intenção
- **CACHE**: Sistema semântico igual para agendamento e cancelamento
- **RESPOSTA**: Mensagens diretas quando apropriado (sem IA desnecessária)
- **ESTADO**: Gerenciamento rigoroso via StateManager

### 📋 REGRAS DE DESENVOLVIMENTO CRÍTICAS
- Não pode ter referência hardcoded a profissionais, serviços ou coisas que deixam fixas a análise - nos prompts. Apenas pode ter exemplos.
- Sempre que um código for modificado, deve verificar se há necessidade de alterar outros lugares na plataforma. Em qualquer projeto daqui. Ex: debuginfo que é a janela do frontend de client entre outros. Nunca usar hardcode de informações da api trinks.
- **CONFIRMAÇÃO É LEI**: Todo agendamento e cancelamento DEVE passar por confirmação
- **PREVENÇÃO DE CONFLITOS**: Verificar estados antes de definir novos estágios
- **CACHE SEMÂNTICO**: Usar sempre para mensagens de confirmação/cancelamento

## 📱 SISTEMA DE HISTÓRICO E CACHE OBRIGATÓRIO ✅

### 🔄 Cache Configurável
- **Flag**: `DISABLE_RESPONSE_CACHE=true` (padrão: desabilitado)
- **Comportamento**: Cache desabilitado força geração nova toda vez
- **Performance**: ↓ Mais lento, ↑ Maior custo, ✅ Sempre atual
- **Localização**: `server/cache/HumanizedResponseCache.js`

### 📜 Histórico Obrigatório - REGRA FUNDAMENTAL
- **TODAS** as chamadas IA devem incluir últimas 10 mensagens
- **ai.js**: `conversationContext` sempre presente via `buildConversationContext()`
- **LangGraph**: Histórico transferido via `IntentGraphAdapter` (conversation completa)
- **Templates**: Placeholder `{conversationHistory}` obrigatório
- **Tomadas de decisão**: Contexto completo sempre disponível

### 📏 Mensagens Curtas Obrigatório
- **Regra**: Máximo 1-2 frases por resposta
- **Simulação**: Conversa humana natural brasileira
- **Templates**: Instrução explícita "MÁXIMO 1-2 frases curtas"
- **generateSimpleResponse**: Instrução de brevidade automática
- **Objetivo**: Evitar respostas robóticas longas

### 🚫 Zero Fallbacks - Política de Falha
- **Mock responses**: ❌ Completamente removidos
- **IA indisponível**: Sistema deve falhar explicitamente
- **Debugging**: Facilita identificação de problemas
- **Produção**: Força configuração correta da IA
- **Funções afetadas**: 
  - `generateSimpleResponse()`: Throw error se sem IA
  - `extractCancellationResponse()`: Throw error se sem IA
  - `extractAppointmentResponse()`: Throw error se sem IA
  - `buildSystemPrompt()`: Throw error se falhar
  - `IntentClassificationNode`: Throw error se sem IA

### 🔗 Sincronização LangGraph-AI.js
- **Transferência Completa**: `IntentGraphAdapter` passa `conversation` completa
- **Histórico Sincronizado**: StateManager recebe todas as mensagens do ai.js
- **Log de Verificação**: Sistema mostra quantas mensagens foram transferidas
- **Contexto Unificado**: Mesmas últimas 10 mensagens em ambos os sistemas

## 🧪 TESTES DE VALIDAÇÃO ✅

### 📋 Diretrizes de Teste Obrigatórias
**IMPORTANTE**: Sempre realizar testes completos usando as chaves de API reais do projeto:

#### 🔑 Chaves de API para Testes
```bash
# Use sempre estas chaves para testes completos
TRINKS_API_KEY=i0fFPhCnix6JuqiOWAIZNaJNHZx89zst9zfSH22c
TRINKS_ESTABLISHMENT_ID=188253
```

#### 🧪 Comandos de Teste Essenciais

**1. Teste do Sistema de Disponibilidade:**
```bash
TRINKS_API_KEY=i0fFPhCnix6JuqiOWAIZNaJNHZx89zst9zfSH22c TRINKS_ESTABLISHMENT_ID=188253 node -e "
const aiService = require('./services/ai');
const conversation = {
  schedulingData: {
    professional: { name: 'João', id: '782094' },
    service: { name: 'BARBA 15', id: '11866299' },
    date: '2025-09-06',
    time: '14:00'
  },
  messages: [{ role: 'user', content: 'Quero fazer barba com o João amanhã às 14h' }]
};

(async () => {
  const trinksApiCalls = [];
  await aiService.buildSystemPrompt({ nome: 'Fernando', telefone: '5521998217917' }, trinksApiCalls, conversation);
  console.log('✅ Sistema automático de disponibilidade funcionando!');
})();
"
```

**2. Teste do Cache de Disponibilidade com Filtro de Serviço:**
```bash
node -e "
const availabilityCache = require('./utils/availabilityCache');
availabilityCache.set('João', '2025-09-06', { slots: ['10:00', '14:00'] }, '11866299');
const result = availabilityCache.get('João', '2025-09-06', '11866299');
console.log('✅ Cache com filtro de serviço:', result ? 'funcionando' : 'falhou');
"
```

### 🎯 Validação Obrigatória
- **Taxa de Confirmação**: 100% (nenhum agendamento sem confirmação)
- **Conflitos de Estado**: 0% (prevenção implementada)
- **Cache Hit Rate**: >50% (templates reutilizados)
- **Integração LangGraph**: 100% (detecção de intenção)
- **Disponibilidade Automática**: 100% (busca baseada em schedulingData)

**Scripts e cenários detalhados**: **[docs/TESTING.md](docs/TESTING.md)**

## 🚨 SISTEMA DE LOGS DE ERRO COMPLETO ✅

### 📋 **Funcionalidade**
Sistema avançado que cria arquivos de log individuais para cada erro e fallback, seguindo o padrão dos logs de mensagem.

### ⚙️ **Implementação**
- **Arquivo principal**: `server/utils/ErrorLogger.js`
- **Pasta de logs**: `server/logs/errors/`
- **Formato**: `error-{YYYY-MM-DD}-{timestamp}-{uuid}.log`
- **Categorias**: 8 tipos diferentes (IA_ERROR, TRINKS_ERROR, API_ERROR, etc.)
- **Sanitização**: Remove automaticamente dados sensíveis (passwords, tokens, API keys)

### 🔧 **Integração Completa**
- **AI Service** (`ai.js`): Erros de Claude/OpenAI com contexto completo
- **Trinks Service** (`trinks.js`): Falhas de API com dados de debug
- **Cache System** (`HumanizedResponseCache.js`): Erros de cache e geração
- **LangGraph Nodes**: Falhas de processamento de intenção
- **Session Linking**: Referência cruzada com logs de mensagem via sessionId

### 🔧 **Uso Automático**
```javascript
// Wrapper automático - recomendado
const result = await errorLogger.executeWithLogging(
  async () => await operacao(),
  ERROR_CATEGORIES.API_ERROR,
  { contexto: 'dados relevantes' }
);

// Log manual quando necessário
await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, context);
```

### 📡 **Endpoints de Debug**
- **GET** `/api/debug/error-stats?days=7` - Estatísticas de erro
- **POST** `/api/debug/test-error-log` - Teste de logging (desenvolvimento)

### ✅ **Resultado**
- **Rastreabilidade completa** de todos os erros e fallbacks
- **Zero configuração adicional** - funciona automaticamente
- **Segurança garantida** - dados sensíveis sempre mascarados
- **Performance otimizada** - logs assíncronos não bloqueiam operações

### 📋 REGRAS DE DESENVOLVIMENTO CRÍTICAS

#### 🚫 ANTI-HARDCODE - REGRAS FUNDAMENTAIS
- **JAMAIS HARDCODE INFORMAÇÕES**: Nomes de profissionais, serviços, datas, horários ou qualquer dado dinâmico
- **SEMPRE USE IA PARA ANÁLISE**: Interpretação de contexto, extração de dados, identificação de padrões
- **PROMPTS DINÂMICOS**: Toda informação contextual deve ser passada via prompts, não via código
- **ZERO REGEX FIXO**: Não usar regex para detectar serviços/profissionais específicos
- **IA FIRST**: Priorizar inteligência artificial sobre lógica hardcoded sempre
- **CONTEXTO VIA PROMPT**: Histórico, preferências, dados sempre via prompt para IA analisar

#### 🎯 EXEMPLOS DO QUE NUNCA FAZER:
❌ `if (text.includes('massagem'))` - Hardcode de serviço
❌ `const profissionais = ['João', 'Ágata']` - Hardcode de nomes
❌ `pattern: /\b(jailson)\b/gi` - Regex hardcoded
❌ `service: 'barba'` - Mapeamento fixo
❌ Funções com 100+ linhas de regex para detectar padrões

#### ✅ EXEMPLOS DO QUE SEMPRE FAZER:
✅ "Analise esta conversa e identifique serviços mencionados" - Prompt para IA
✅ Passar dados via API Trinks dinamicamente
✅ Usar contexto histórico via prompt
✅ Deixar IA decidir com base em contexto completo
✅ Prompt: "Baseado no histórico, que informações o cliente já forneceu?"

#### 🌎 CONTEXTO GEOGRÁFICO E TEMPORAL OBRIGATÓRIO
- **SEMPRE incluir**: Data, hora, região, timezone em TODOS os prompts para IA
- **Formato padrão**: Rio de Janeiro, Brasil, America/Sao_Paulo timezone
- **Contexto cultural**: Português carioca, expressões brasileiras
- **Período do dia**: Manhã, tarde, noite para adaptar cumprimentos

## 🕐 REFERÊNCIAS TEMPORAIS INTELIGENTES

Sistema separa agendamentos em dois contextos distintos:

### 📋 **Separação de Contextos**
- **🔚 Atendimentos Realizados Hoje**: Para follow-up e contextualização
- **📅 Próximos Agendamentos**: Realmente no futuro

### 🎯 **REGRA FUNDAMENTAL**
IA deve usar linguagem temporal apropriada baseada no contexto recebido:

#### **Para Agendamentos Finalizados Hoje:**
- **Recém-finalizado (< 1h)**: "Você acabou de sair daqui!", "Vi que você acabou de fazer..."
- **Algumas horas atrás**: "Como foi o [serviço] de hoje?", "Gostou do resultado?"
- **Contexto**: Demonstrar que sabe que o cliente esteve no salão

#### **Para Próximos Agendamentos:**
- **Em breve (< 1h)**: "Tá chegando a hora!", "Daqui a pouquinho você tem..."
- **Amanhã**: "Te vejo amanhã para...", "Amanhã você tem..."
- **Mais distante**: Mencionar normalmente a data

### ⚡ **Implementação**
- **Sistema NÃO calcula tempos relativos** - apenas separa os contextos
- **IA decide dinamicamente** a melhor linguagem baseada no horário atual
- **Zero hardcode temporal** - toda lógica via prompt contextual
- **Mantém naturalidade** da conversa sem soar robótico

#### 🚨 REGRAS ESPECÍFICAS DO SISTEMA
- **LOGS OBRIGATÓRIOS**: Todo erro/fallback deve gerar log individual
- **CONTEXTO SEMPRE**: Incluir dados relevantes para debugging
- **CATEGORIZAÇÃO CORRETA**: Usar categoria apropriada para cada tipo de erro
- **SANITIZAÇÃO AUTOMÁTICA**: Sistema já remove dados sensíveis
- **CONFIRMAÇÃO É LEI**: Todo agendamento e cancelamento DEVE passar por confirmação
- **PREVENÇÃO DE CONFLITOS**: Verificar estados antes de definir novos estágios
- **CACHE SEMÂNTICO**: Usar sempre para mensagens de confirmação/cancelamento
- **REFERÊNCIAS TEMPORAIS**: IA usa contextos separados para decidir linguagem apropriada
- Sempre remova código legado quando há refactoring
- Nunca hardcode informações da API Trinks
- Documentação API Trinks: https://trinks.readme.io/reference/introducao
- Todas as documentações MD que são geradas devem estar na pasta docs da raiz do projeto.
- Sempre passar o eslint depois de realizar alterações em arquivos nodejs