export interface Customer {
  id: string;
  nome: string;
  telefone: string;
  email: string;
  ultimosServicos: Service[];
  preferencias: string;
}

export interface Service {
  data: string;
  servico: string;
  profissional: string;
  valor: number;
}

export interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai' | 'system';
  timestamp: Date;
  status: 'sent' | 'delivered' | 'read';
  type?: 'text' | 'audio';
  audioUrl?: string;
  audioDuration?: number;
  isProcessing?: boolean;
  debugInfo?: {
    apiUsed?: boolean;
    mockResponse?: boolean;
    systemPrompt?: string;
    messagesContext?: any[];
    rawResponse?: any;
    model?: string;
    lastMessage?: string;
    stage?: string;
    customerData?: any;
  };
}

export interface Conversation {
  id: string;
  customerPhone: string;
  customerName: string;
  messages: Message[];
  lastMessage: Date;
  unreadCount: number;
}

export interface TimeSlot {
  hora: string;
  profissional: string;
  disponivel: boolean;
}

export interface AvailabilityResponse {
  data: string;
  horarios: TimeSlot[];
}

export interface AppointmentRequest {
  cliente_id: string;
  data: string;
  hora: string;
  servico: string;
  profissional: string;
}