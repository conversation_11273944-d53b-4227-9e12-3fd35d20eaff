/**
 * IntentGraphAdapter - Adaptador de Integração
 * 
 * Responsabilidades:
 * - Integrar IntentGraph com o sistema ai.js existente
 * - Manter interface compatível
 * - Converter dados entre formatos
 * - Fornecer fallback para sistema antigo
 */

const IntentGraph = require('../graphs/IntentGraph');

class IntentGraphAdapter {
  constructor() {
    this.intentGraph = new IntentGraph();
    this.isEnabled = true;
    
    // Mapeamento de estágios entre sistemas
    this.stageMapping = {
      // LangGraph -> Sistema Atual
      'greeting': 'greeting',
      'intent_classification': 'scheduling',
      'context_validation': 'scheduling', 
      'intent_confirmation': 'scheduling',
      'scheduling': 'scheduling',
      'confirmation': 'confirmation',
      'completed': 'completed',
      'error': 'error'
    };
  }

  /**
   * 🔥 NOVO: Detecta intenção completa (não apenas scheduling) via IA
   * Usado para evitar loops de confirmação - retorna tipo específico de intenção
   * SEMPRE usa IA - contexto completo das mensagens
   */
  async detectFullIntentWithAI(conversation) {
    if (!this.isEnabled) {
      throw new Error('IntentGraphAdapter está desabilitado - IA obrigatória para detecção');
    }
    
    try {
      const customerPhone = conversation?.customerPhone || 'unknown';
      const lastMessage = this.getLastUserMessage(conversation?.messages || []);

      console.log(`🔗 LangGraph: Classificação COMPLETA de intenção (${conversation?.messages?.length || 0} mensagens)`);
      
      // Processar com IntentGraph passando conversation completa para análise de intenção
      const result = await this.intentGraph.process(customerPhone, lastMessage, conversation);
      
      if (!result?.success) {
        throw new Error('IntentGraph falhou na classificação de intenção');
      }

      // Extrair intenção detectada
      const intentType = (result.intent && result.intent.type) 
        || (result.context && result.context.intent && result.context.intent.type) 
        || 'unknown';
        
      const confidence = (result.intent && result.intent.confidence) 
        || (result.context && result.context.intent && result.context.intent.confidence) 
        || 0;
        
      const context = (result.intent && result.intent.context) 
        || (result.context && result.context.intent && result.context.intent.context) 
        || {};

      console.log(`🤖 LangGraph Intenção Completa: ${intentType} (${confidence})`);
      
      return {
        intent: intentType,
        confidence: confidence, 
        context: context,
        reasoning: result.debugInfo?.reasoning || 'Análise via LangGraph/IA'
      };
      
    } catch (error) {
      console.error('❌ Erro em detectFullIntentWithAI:', error.message);
      throw error; // Re-throw para forçar falha sem fallbacks
    }
  }

  /**
   * Método principal que substitui detectSchedulingIntentWithAI() - IMPLEMENTAÇÃO EXCLUSIVA
   */
  async detectSchedulingIntentWithAI(conversation) {
    if (!this.isEnabled) return false;
    try {
      const customerPhone = conversation?.customerPhone || 'unknown';
      const lastMessage = this.getLastUserMessage(conversation?.messages || []);

      console.log(`🔗 LangGraph: Transferindo histórico completo (${conversation?.messages?.length || 0} mensagens)`);
      
      // Processar com IntentGraph passando conversation completa para manter histórico
      const result = await this.intentGraph.process(customerPhone, lastMessage, conversation);
      if (!result?.success) {
        console.warn('IntentGraph processing returned unsuccessful result');
        return false;
      }

      // Determinar intenção detectada
      const hasSchedulingIntent = this.hasSchedulingIntent(result);

      // Log detalhado
      const detectedType = (result.intent && result.intent.type) || (result.context && result.context.intent && result.context.intent.type) || 'unknown';
      const detectedConfidence = (result.intent && result.intent.confidence) || (result.context && result.context.intent && result.context.intent.confidence) || 0;
      console.log(`🤖 LangGraph Intent Detection: ${hasSchedulingIntent}`);
      console.log(`📊 Detected Intent: ${detectedType}`);
      console.log(`🔍 Intent Confidence: ${detectedConfidence}`);

      return hasSchedulingIntent;
    } catch (error) {
      console.error('❌ Error in IntentGraphAdapter.detectSchedulingIntentWithAI:', error.message);
      return false;
    }
  }

  /**
   * Método principal que substitui processMessage() parcialmente
   */
  async processMessageWithGraph(customerPhone, message, customerData = null, messageType = 'text') {
    try {
      if (!this.isEnabled) {
        return null; // Indicar para usar sistema antigo
      }
      
      // Preparar estado inicial baseado nos dados existentes
      const initialState = this.prepareInitialState(customerPhone, customerData);
      
      // Processar com IntentGraph
      const result = await this.intentGraph.process(customerPhone, message, initialState);
      
      if (!result.success) {
        return null; // Fallback para sistema antigo
      }
      
      // Converter para formato esperado pelo ai.js
      return this.convertToAIServiceFormat(result, customerData);
      
    } catch (error) {
      console.error('❌ Error processing message with IntentGraph:', error.message);
      return null; // Fallback para sistema antigo
    }
  }

  /**
   * Prepara estado inicial baseado nos dados existentes do sistema
   */
  prepareInitialState(customerPhone, customerData) {
    return {
      customerPhone,
      currentStage: 'greeting',
      messages: [], // Será preenchido pelo IntentGraph
      context: {
        customerData: customerData ? {
          nome: customerData.nome,
          telefone: customerData.telefone,
          hasHistory: !!(customerData.ultimosServicos?.length)
        } : null
      },
      schedulingData: {
        professional: { id: null, name: null },
        service: { id: null, name: null, price: null },
        date: null,
        time: null,
        extractedAt: null,
        source: 'ai',
        isComplete: false
      }
    };
  }

  /**
   * Converte resultado do IntentGraph para formato do ai.js
   */
  convertToAIServiceFormat(graphResult, customerData) {
    const mappedStage = this.stageMapping[graphResult.stage] || 'scheduling';
    
    return {
      response: graphResult.response,
      stage: mappedStage,
      context: {
        ...graphResult.context,
        langGraphUsed: true,
        originalStage: graphResult.stage
      },
      debugInfo: {
        ...graphResult.debugInfo,
        adapter: 'IntentGraphAdapter',
        conversionTimestamp: new Date()
      }
    };
  }

  /**
   * Determina se há intenção de agendamento baseada no resultado
   */
  hasSchedulingIntent(graphResult) {
    const intentType = (graphResult.intent && graphResult.intent.type)
      || (graphResult.context && graphResult.context.intent && graphResult.context.intent.type)
      || null;
    
    // Considerar como "scheduling intent" se:
    // 1. Intenção explícita de agendamento
    // 2. Cancelamento (precisa buscar agendamentos)
    // 3. Reagendamento (precisa buscar agendamentos)
    // 4. Consulta sobre disponibilidade
    
    const schedulingIntents = ['scheduling', 'cancellation', 'rescheduling', 'inquiry'];
    
    if (intentType && schedulingIntents.includes(intentType)) {
      return true;
    }
    
    // Verificar confidence da intenção
    const confidence = (graphResult.intent && graphResult.intent.confidence)
      || (graphResult.context && graphResult.context.intent && graphResult.context.intent.confidence)
      || 0;
    
    // Se confidence baixa, ser conservador
    if (confidence < 0.6) {
      return false;
    }
    
    return intentType === 'scheduling';
  }

  // MÉTODO REMOVIDO: fallbackIntentDetection - não usar mais fallbacks

  /**
   * Obtém última mensagem do usuário
   */
  getLastUserMessage(messages) {
    if (!messages || messages.length === 0) return '';
    
    const userMessages = messages.filter(msg => msg.role === 'user');
    return userMessages[userMessages.length - 1]?.content || '';
  }

  /**
   * Habilita/desabilita o adaptador
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    console.log(`🔄 IntentGraphAdapter ${enabled ? 'habilitado' : 'desabilitado'}`);
  }

  /**
   * Obtém estado atual de um cliente via IntentGraph
   */
  getCustomerState(customerPhone) {
    return this.intentGraph.stateManager.getState(customerPhone);
  }

  /**
   * Debug de um cliente específico
   */
  debugCustomer(customerPhone) {
    return this.intentGraph.debugCustomerFlow(customerPhone);
  }

  /**
   * Health check do adaptador
   */
  healthCheck() {
    const health = {
      adapterStatus: this.isEnabled ? 'enabled' : 'disabled',
      graphHealth: this.intentGraph.healthCheck(),
      integrationStatus: 'operational'
    };
    
    // Verificar se há problemas críticos
    if (health.graphHealth.status === 'unhealthy') {
      health.integrationStatus = 'degraded';
      health.recommendation = 'Consider disabling IntentGraph temporarily';
    }
    
    return health;
  }

  /**
   * Métricas do adaptador
   */
  getMetrics() {
    return {
      adapterInfo: {
        enabled: this.isEnabled,
        version: '1.0.0',
        integrationPoints: ['detectSchedulingIntentWithAI', 'processMessageWithGraph']
      },
      graphMetrics: this.intentGraph.getMetrics(),
      performanceComparison: this.getPerformanceComparison()
    };
  }

  /**
   * Compara performance entre sistemas (placeholder)
   */
  getPerformanceComparison() {
    return {
      note: 'Performance comparison requires running both systems simultaneously',
      avgExecutionTime: this.intentGraph.executionStats.averageExecutionTime,
      successRate: this.intentGraph.executionStats.totalExecutions > 0 ?
        this.intentGraph.executionStats.successfulExecutions / this.intentGraph.executionStats.totalExecutions : 0
    };
  }

  /**
   * Reset estado de cliente
   */
  resetCustomer(customerPhone) {
    this.intentGraph.resetCustomerState(customerPhone);
  }

  /**
   * Cleanup automático de estados antigos
   */
  cleanup(maxAge = 24 * 60 * 60 * 1000) { // 24 horas
    const stats = this.intentGraph.stateManager.getStats();
    console.log(`🧹 Cleanup: ${stats.activeStates} estados ativos`);
    
    // Implementar lógica de cleanup baseada em idade
    // Por ora, apenas log
  }

  /**
   * Exporta dados para análise
   */
  exportData(options = {}) {
    return {
      adapterInfo: {
        version: '1.0.0',
        enabled: this.isEnabled,
        exportTimestamp: new Date()
      },
      graphData: this.intentGraph.exportData(options),
      metrics: this.getMetrics(),
      healthCheck: this.healthCheck()
    };
  }

  /**
   * Migração gradual - permite comparação lado a lado
   */
  async processWithComparison(customerPhone, message, customerData, originalMethod) {
    const comparison = {
      timestamp: new Date(),
      customerPhone,
      message
    };
    
    try {
      // Processar com IntentGraph
      const startTime = Date.now();
      const graphResult = await this.processMessageWithGraph(customerPhone, message, customerData);
      comparison.langGraph = {
        duration: Date.now() - startTime,
        success: !!graphResult,
        result: graphResult
      };
      
      // Processar com sistema original
      const originalStartTime = Date.now();
      const originalResult = await originalMethod(customerPhone, message, customerData);
      comparison.original = {
        duration: Date.now() - originalStartTime,
        success: !!originalResult,
        result: originalResult
      };
      
      // Log comparação
      console.log('🔍 System Comparison:', {
        langGraph: `${comparison.langGraph.duration}ms ${comparison.langGraph.success ? '✅' : '❌'}`,
        original: `${comparison.original.duration}ms ${comparison.original.success ? '✅' : '❌'}`
      });
      
      // Retornar resultado preferido (LangGraph se disponível, senão original)
      return graphResult || originalResult;
      
    } catch (error) {
      console.error('❌ Comparison failed:', error.message);
      return await originalMethod(customerPhone, message, customerData);
    }
  }
}

module.exports = IntentGraphAdapter;
