/**
 * SchedulingGraph - Grafo Principal de Agendamento
 * 
 * Orquestra o fluxo completo de agendamento:
 * customer_identification → service_selection → professional_selection → availability_check → schedule_confirmation
 * 
 * Características:
 * - Fluxo linear com validações em cada etapa
 * - Rollback automático em caso de falhas
 * - Cache inteligente compartilhado entre nós
 * - Recuperação de estado para conversas interrompidas
 * - Integração com API Trinks para dados reais
 */

const { StateGraph, END } = require('@langchain/langgraph');
const { BaseMessage, HumanMessage, AIMessage } = require('@langchain/core/messages');

// Importar nós do grafo
const CustomerIdentificationNode = require('../nodes/scheduling/CustomerIdentificationNode');
const ServiceSelectionNode = require('../nodes/scheduling/ServiceSelectionNode');
const ProfessionalSelectionNode = require('../nodes/scheduling/ProfessionalSelectionNode');
const AvailabilityCheckNode = require('../nodes/scheduling/AvailabilityCheckNode');
const ScheduleConfirmationNode = require('../nodes/scheduling/ScheduleConfirmationNode');

class SchedulingGraph {
  constructor(stateManager) {
    this.stateManager = stateManager;
    
    // Inicializar nós
    this.nodes = {
      customerIdentification: new CustomerIdentificationNode(),
      serviceSelection: new ServiceSelectionNode(),
      professionalSelection: new ProfessionalSelectionNode(),
      availabilityCheck: new AvailabilityCheckNode(),
      scheduleConfirmation: new ScheduleConfirmationNode()
    };
    
    // Configurar grafo
    this.graph = this.buildGraph();
    this.compiledGraph = this.graph.compile();
    
    // Estatísticas do grafo
    this.stats = {
      totalExecutions: 0,
      successfulSchedules: 0,
      failedSchedules: 0,
      avgExecutionTime: 0,
      nodeExecutionCounts: {},
      mostCommonFailurePoints: {}
    };
  }

  /**
   * Constrói o grafo com todos os nós e transições
   */
  buildGraph() {
    // Definir estado inicial
    const initialState = {
      messages: [],
      customerPhone: '',
      currentStage: 'customer_identification',
      context: {},
      schedulingData: {},
      debugInfo: {
        nodeExecutions: [],
        startTime: null,
        endTime: null
      }
    };

    // Criar grafo
    const graph = new StateGraph({
      channels: {
        messages: {
          reducer: (x, y) => x.concat(y),
          default: () => []
        },
        customerPhone: {
          reducer: (x, y) => y ?? x,
          default: () => ''
        },
        currentStage: {
          reducer: (x, y) => y ?? x,
          default: () => 'customer_identification'
        },
        context: {
          reducer: (x, y) => ({ ...x, ...y }),
          default: () => ({})
        },
        schedulingData: {
          reducer: (x, y) => ({ ...x, ...y }),
          default: () => ({})
        },
        debugInfo: {
          reducer: (x, y) => ({ ...x, ...y }),
          default: () => ({
            nodeExecutions: [],
            startTime: null,
            endTime: null
          })
        }
      }
    });

    // Adicionar nós ao grafo
    graph.addNode('customer_identification', this.createNodeWrapper('customerIdentification'));
    graph.addNode('service_selection', this.createNodeWrapper('serviceSelection'));
    graph.addNode('professional_selection', this.createNodeWrapper('professionalSelection'));
    graph.addNode('availability_check', this.createNodeWrapper('availabilityCheck'));
    graph.addNode('schedule_confirmation', this.createNodeWrapper('scheduleConfirmation'));

    // Definir ponto de entrada
    graph.addEdge('__start__', 'customer_identification');

    // Definir transições condicionais
    graph.addConditionalEdges(
      'customer_identification',
      this.routeFromCustomerIdentification.bind(this)
    );

    graph.addConditionalEdges(
      'service_selection',
      this.routeFromServiceSelection.bind(this)
    );

    graph.addConditionalEdges(
      'professional_selection',
      this.routeFromProfessionalSelection.bind(this)
    );

    graph.addConditionalEdges(
      'availability_check',
      this.routeFromAvailabilityCheck.bind(this)
    );

    graph.addConditionalEdges(
      'schedule_confirmation',
      this.routeFromScheduleConfirmation.bind(this)
    );

    return graph;
  }

  /**
   * Cria wrapper para execução de nós com logging e erro handling
   */
  createNodeWrapper(nodeName) {
    return async (state) => {
      const node = this.nodes[nodeName];
      const startTime = Date.now();
      
      try {
        // Log início da execução
        this.logNodeExecution(nodeName, 'start', state);
        
        // Executar nó
        const result = await node.execute({
          customerPhone: state.customerPhone,
          state: state
        });
        
        const endTime = Date.now();
        const executionTime = endTime - startTime;
        
        // Atualizar estatísticas
        this.updateNodeStats(nodeName, result.success, executionTime);
        
        // Log resultado
        this.logNodeExecution(nodeName, 'complete', state, result, executionTime);
        
        // Preparar atualizações do estado
        const stateUpdates = {
          currentStage: result.nextStage || state.currentStage,
          debugInfo: {
            ...state.debugInfo,
            nodeExecutions: [
              ...(state.debugInfo.nodeExecutions || []),
              {
                nodeName,
                success: result.success,
                executionTime,
                timestamp: new Date().toISOString(),
                nextStage: result.nextStage
              }
            ]
          }
        };
        
        // Aplicar updates específicos do nó
        if (result.updates) {
          Object.assign(stateUpdates, result.updates);
        }
        
        // Salvar resultado no contexto para próximos nós
        stateUpdates.context = {
          ...state.context,
          ...stateUpdates.context,
          [`${nodeName}Result`]: result.result
        };
        
        return stateUpdates;
        
      } catch (error) {
        const endTime = Date.now();
        const executionTime = endTime - startTime;
        
        // Log erro
        this.logNodeExecution(nodeName, 'error', state, { error: error.message }, executionTime);
        
        // Atualizar estatísticas de erro
        this.updateNodeStats(nodeName, false, executionTime, error);
        
        // Retornar estado com erro
        return {
          currentStage: 'error',
          context: {
            ...state.context,
            error: {
              nodeName,
              message: error.message,
              timestamp: new Date().toISOString()
            }
          }
        };
      }
    };
  }

  /**
   * Roteamento condicional a partir de customer_identification
   */
  routeFromCustomerIdentification(state) {
    const result = state.context.customerIdentificationResult;
    
    if (!result || !result.success) {
      return 'customer_identification'; // Tentar novamente ou sair
    }
    
    return 'service_selection';
  }

  /**
   * Roteamento condicional a partir de service_selection
   */
  routeFromServiceSelection(state) {
    const result = state.context.serviceSelectionResult;
    
    if (!result || !result.success) {
      return 'service_selection'; // Tentar novamente
    }
    
    return 'professional_selection';
  }

  /**
   * Roteamento condicional a partir de professional_selection
   */
  routeFromProfessionalSelection(state) {
    const result = state.context.professionalSelectionResult;
    
    if (!result || !result.success) {
      return 'professional_selection'; // Tentar novamente
    }
    
    return 'availability_check';
  }

  /**
   * Roteamento condicional a partir de availability_check
   */
  routeFromAvailabilityCheck(state) {
    const result = state.context.availabilityCheckResult;
    
    if (!result || !result.success) {
      return 'availability_check'; // Tentar novamente
    }
    
    if (!result.hasAvailability) {
      return END; // Sem horários disponíveis - finalizar
    }
    
    return 'schedule_confirmation';
  }

  /**
   * Roteamento condicional a partir de schedule_confirmation
   */
  routeFromScheduleConfirmation(state) {
    const result = state.context.scheduleConfirmationResult;
    
    if (!result) {
      return 'schedule_confirmation'; // Tentar novamente
    }
    
    if (result.success) {
      return END; // Agendamento criado com sucesso
    }
    
    // Analisar tipo de erro para decidir próximo passo
    if (result.error?.code === 'TIME_CONFLICT') {
      return 'availability_check'; // Buscar novos horários
    }
    
    return END; // Para outros erros, finalizar
  }

  /**
   * Processa mensagem através do grafo
   */
  async process(customerPhone, message, existingState = null) {
    const startTime = Date.now();
    
    try {
      // Inicializar ou recuperar estado
      let initialState = existingState || this.stateManager.getState(customerPhone);
      
      if (!initialState) {
        initialState = {
          messages: [],
          customerPhone: customerPhone,
          currentStage: 'customer_identification',
          context: {},
          schedulingData: {},
          debugInfo: {
            nodeExecutions: [],
            startTime: startTime,
            endTime: null
          }
        };
      }
      
      // Adicionar nova mensagem
      initialState.messages.push(new HumanMessage(message));
      initialState.debugInfo.startTime = startTime;
      
      // Executar grafo
      const result = await this.compiledGraph.invoke(initialState);
      
      const endTime = Date.now();
      result.debugInfo.endTime = endTime;
      result.debugInfo.totalExecutionTime = endTime - startTime;
      
      // Salvar estado atualizado
      this.stateManager.setState(customerPhone, result);
      
      // Atualizar estatísticas gerais
      this.updateGeneralStats(result, endTime - startTime);
      
      // Determinar resposta final
      const response = this.generateResponse(result);
      
      return {
        success: result.currentStage !== 'error',
        response: response,
        stage: result.currentStage,
        debugInfo: result.debugInfo,
        schedulingData: result.schedulingData,
        context: result.context
      };
      
    } catch (error) {
      const endTime = Date.now();
      
      console.error('Erro no SchedulingGraph:', error);
      
      // Atualizar estatísticas de erro
      this.stats.failedSchedules++;
      this.stats.totalExecutions++;
      
      return {
        success: false,
        response: 'Desculpe, tivemos um problema interno. Pode tentar novamente?',
        stage: 'error',
        error: error.message,
        debugInfo: {
          error: error.message,
          executionTime: endTime - startTime
        }
      };
    }
  }

  /**
   * Gera resposta final baseada no estado
   */
  generateResponse(state) {
    // Se chegou ao fim com sucesso
    if (state.currentStage === 'completed') {
      const confirmationResult = state.context.scheduleConfirmationResult;
      if (confirmationResult && confirmationResult.confirmationMessage) {
        return confirmationResult.confirmationMessage;
      }
      return 'Agendamento confirmado com sucesso! ✅';
    }
    
    // Se terminou sem disponibilidade
    const availabilityResult = state.context.availabilityCheckResult;
    if (availabilityResult && !availabilityResult.hasAvailability) {
      return 'Infelizmente não temos horários disponíveis para os próximos dias. Que tal tentar outro profissional ou serviço?';
    }
    
    // Se houve erro em algum nó
    if (state.context.error) {
      return `Tivemos um probleminha: ${state.context.error.message}. Pode tentar novamente?`;
    }
    
    // Resposta padrão
    return 'Estou processando seu agendamento. Um momento...';
  }

  /**
   * Log de execução de nós
   */
  logNodeExecution(nodeName, phase, state, result = null, executionTime = null) {
    const logData = {
      node: nodeName,
      phase: phase,
      customerPhone: state.customerPhone,
      currentStage: state.currentStage,
      timestamp: new Date().toISOString()
    };
    
    if (result) {
      logData.success = result.success || !result.error;
      logData.nextStage = result.nextStage;
    }
    
    if (executionTime) {
      logData.executionTime = executionTime;
    }
    
    console.log(`[SchedulingGraph] ${JSON.stringify(logData)}`);
  }

  /**
   * Atualiza estatísticas de nó específico
   */
  updateNodeStats(nodeName, success, executionTime, error = null) {
    if (!this.stats.nodeExecutionCounts[nodeName]) {
      this.stats.nodeExecutionCounts[nodeName] = {
        total: 0,
        successful: 0,
        failed: 0,
        totalTime: 0,
        avgTime: 0
      };
    }
    
    const nodeStats = this.stats.nodeExecutionCounts[nodeName];
    nodeStats.total++;
    nodeStats.totalTime += executionTime;
    nodeStats.avgTime = nodeStats.totalTime / nodeStats.total;
    
    if (success) {
      nodeStats.successful++;
    } else {
      nodeStats.failed++;
      
      // Registrar ponto de falha
      if (!this.stats.mostCommonFailurePoints[nodeName]) {
        this.stats.mostCommonFailurePoints[nodeName] = 0;
      }
      this.stats.mostCommonFailurePoints[nodeName]++;
    }
  }

  /**
   * Atualiza estatísticas gerais do grafo
   */
  updateGeneralStats(state, executionTime) {
    this.stats.totalExecutions++;
    
    if (state.currentStage === 'completed') {
      this.stats.successfulSchedules++;
    } else {
      this.stats.failedSchedules++;
    }
    
    // Atualizar tempo médio de execução
    this.stats.avgExecutionTime = (
      (this.stats.avgExecutionTime * (this.stats.totalExecutions - 1)) + executionTime
    ) / this.stats.totalExecutions;
  }

  /**
   * Obtém estatísticas completas do grafo
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.totalExecutions > 0 
        ? (this.stats.successfulSchedules / this.stats.totalExecutions) * 100 
        : 0,
      nodeStats: Object.entries(this.stats.nodeExecutionCounts).map(([nodeName, stats]) => ({
        nodeName,
        ...stats,
        successRate: stats.total > 0 ? (stats.successful / stats.total) * 100 : 0
      }))
    };
  }

  /**
   * Reset de estatísticas (útil para testes)
   */
  resetStats() {
    this.stats = {
      totalExecutions: 0,
      successfulSchedules: 0,
      failedSchedules: 0,
      avgExecutionTime: 0,
      nodeExecutionCounts: {},
      mostCommonFailurePoints: {}
    };
  }

  /**
   * Obtém estado atual de um cliente
   */
  getCustomerState(customerPhone) {
    return this.stateManager.getState(customerPhone);
  }

  /**
   * Limpa estado de um cliente (útil para reiniciar conversa)
   */
  clearCustomerState(customerPhone) {
    this.stateManager.clearState(customerPhone);
  }

  /**
   * Valida configuração do grafo
   */
  validateConfiguration() {
    const validation = {
      isValid: true,
      issues: []
    };
    
    // Verificar se todos os nós estão disponíveis
    const requiredNodes = [
      'customerIdentification',
      'serviceSelection', 
      'professionalSelection',
      'availabilityCheck',
      'scheduleConfirmation'
    ];
    
    for (const nodeName of requiredNodes) {
      if (!this.nodes[nodeName]) {
        validation.isValid = false;
        validation.issues.push(`Nó ausente: ${nodeName}`);
      }
    }
    
    // Verificar se StateManager está disponível
    if (!this.stateManager) {
      validation.isValid = false;
      validation.issues.push('StateManager não configurado');
    }
    
    return validation;
  }
}

module.exports = SchedulingGraph;