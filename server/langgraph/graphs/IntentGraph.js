/**
 * IntentGraph - Grafo Principal de Análise de Intenções
 * 
 * Orquestra todo o fluxo de:
 * 1. Classificação de intenções
 * 2. Validação de contexto
 * 3. Confirmação e coleta de dados faltantes
 * 
 * Substitui completamente:
 * - detectSchedulingIntentWithAI()
 * - handleProgressiveAvailability() (parcialmente)
 */

const StateManager = require('../core/StateManager');
const GraphLogger = require('../core/GraphLogger');
const IntentClassificationNode = require('../nodes/intent/IntentClassificationNode');
const ContextValidationNode = require('../nodes/intent/ContextValidationNode');
const IntentConfirmationNode = require('../nodes/intent/IntentConfirmationNode');

class IntentGraph {
  constructor() {
    this.graphId = 'intent_analysis_graph';
    this.stateManager = new StateManager();
    this.logger = new GraphLogger();
    
    // Inicializar nós
    this.nodes = {
      intentClassification: new IntentClassificationNode(),
      contextValidation: new ContextValidationNode(),
      intentConfirmation: new IntentConfirmationNode()
    };
    
    // Definir fluxo do grafo
    this.nodeFlow = {
      'START': 'intentClassification',
      'intentClassification': this.determinePostClassificationNode.bind(this),
      'contextValidation': this.determinePostValidationNode.bind(this),
      'intentConfirmation': 'END'
    };
    
    // Métricas do grafo
    this.executionStats = {
      totalExecutions: 0,
      successfulExecutions: 0,
      averageExecutionTime: 0
    };
  }

  /**
   * Processo principal do grafo
   */
  async process(customerPhone, message, currentState = null) {
    const startTime = Date.now();
    
    try {
      // Inicializar ou obter estado
      let state = this.stateManager.getState(customerPhone);
      // Adotar estado externo se fornecido e ainda não existir no StateManager
      if (!state && currentState) {
        state = this.stateManager.initializeState(customerPhone);
        // Adotar contexto e dados de agendamento básicos
        state = this.stateManager.updateState(customerPhone, {
          context: currentState.context || {},
          schedulingData: currentState.schedulingData || state.schedulingData
        });
        // Repopular mensagens do estado externo (normalizando)
        const extMessages = Array.isArray(currentState.messages) ? currentState.messages : [];
        console.log(`📝 LangGraph: Sincronizando ${extMessages.length} mensagens do ai.js para StateManager`);
        for (const m of extMessages) {
          this.stateManager.addMessage(customerPhone, {
            role: m.role === 'assistant' ? 'assistant' : 'user',
            content: typeof m.content === 'string' ? m.content : String(m.content ?? ''),
            type: m.type || 'text'
          });
        }
      }
      // Se ainda não existir, inicializar do zero
      if (!state) {
        state = this.stateManager.initializeState(customerPhone);
      }
      
      // Adicionar nova mensagem ao estado
      if (message) {
        state = this.stateManager.addMessage(customerPhone, {
          role: 'user',
          content: message,
          type: 'text'
        });
      }
      
      // Iniciar trace de execução
      const traceId = this.logger.startGraphExecution(this.graphId, customerPhone, state);
      
      // Executar fluxo do grafo
      const result = await this.executeFlow(customerPhone, state, traceId);
      
      // Finalizar trace
      const finalState = this.stateManager.getState(customerPhone);
      this.logger.endGraphExecution(traceId, finalState, 'completed');
      
      // Atualizar métricas
      this.updateExecutionStats(Date.now() - startTime, true);
      
      return {
        success: true,
        response: result.response,
        stage: result.stage,
        context: result.context,
        debugInfo: {
          traceId,
          executionTime: Date.now() - startTime,
          nodesExecuted: result.nodesExecuted,
          graphId: this.graphId
        }
      };
      
    } catch (error) {
      console.error(`❌ Erro no IntentGraph: ${error.message}`);
      
      // Atualizar métricas de erro
      this.updateExecutionStats(Date.now() - startTime, false);
      
      return {
        success: false,
        response: 'Desculpe, tive um problema para processar sua mensagem. Pode tentar novamente?',
        stage: 'error',
        context: {},
        error: {
          message: error.message,
          graphId: this.graphId
        }
      };
    }
  }

  /**
   * Executa o fluxo do grafo
   */
  async executeFlow(customerPhone, initialState, traceId) {
    let currentNode = 'START';
    let state = initialState;
    let finalResponse = null;
    let nodesExecuted = [];
    
    // Loop de execução dos nós
    while (currentNode !== 'END') {
      // Determinar próximo nó
      const nextNode = typeof this.nodeFlow[currentNode] === 'function' 
        ? this.nodeFlow[currentNode](state)
        : this.nodeFlow[currentNode];
      
      if (!nextNode || nextNode === 'END') {
        break;
      }
      
      // Executar nó
      const nodeResult = await this.executeNode(nextNode, customerPhone, state, traceId);
      
      if (!nodeResult.success && nodeResult.error?.recoverable === false) {
        throw new Error(`Fatal error in node ${nextNode}: ${nodeResult.error.message}`);
      }
      
      // Atualizar estado com resultado do nó
      if (nodeResult.success && nodeResult.updates) {
        state = this.stateManager.updateState(customerPhone, nodeResult.updates);
      }
      
      // Atualizar estágio se fornecido
      if (nodeResult.nextStage) {
        const fromStage = state.currentStage;
        state = this.stateManager.transitionStage(
          customerPhone, 
          nodeResult.nextStage, 
          `Transição via ${nextNode}`
        );
        // Logar transição de estágio
        try {
          this.logger.logStageTransition(traceId, fromStage, nodeResult.nextStage, nextNode, 'via node result');
        } catch (_) { /* noop */ }
      }
      
      // Registrar histórico de nó executado
      this.stateManager.addNodeToHistory(customerPhone, nextNode);
      nodesExecuted.push({
        nodeId: nextNode,
        success: nodeResult.success,
        executionTime: nodeResult.metadata?.executionTime
      });
      
      // Capturar resposta se disponível
      if (nodeResult.result?.response) {
        finalResponse = nodeResult.result.response;
      }
      
      // Determinar próximo nó baseado no resultado
      currentNode = this.determineNextNodeFromResult(nextNode, nodeResult, state);
    }
    
    return {
      response: finalResponse || this.generateFallbackResponse(state),
      stage: state.currentStage,
      context: state.context,
      intent: state.intent,
      nodesExecuted
    };
  }

  /**
   * Executa um nó específico
   */
  async executeNode(nodeId, customerPhone, state, traceId) {
    const node = this.nodes[nodeId];
    if (!node) {
      throw new Error(`Node ${nodeId} not found`);
    }
    
    // Preparar input para o nó
    const input = {
      customerPhone,
      state,
      message: state.messages[state.messages.length - 1]?.content,
      metadata: {
        traceId,
        graphId: this.graphId
      }
    };
    
    // Executar nó
    const result = await node.run(input);
    
    // Log resultado do nó
    this.logger.logNodeExecution(traceId, nodeId, input, result);
    
    return result;
  }

  /**
   * Determina próximo nó após classificação de intenção
   */
  determinePostClassificationNode(state) {
    const intent = state.intent;
    
    // Se não há intenção ou confidence muito baixa, ir para confirmação
    if (!intent || intent.confidence < 0.6) {
      return 'intentConfirmation';
    }
    
    // Para intenções claras, validar contexto
    return 'contextValidation';
  }

  /**
   * Determina próximo nó após validação de contexto
   */
  determinePostValidationNode(state) {
    const validation = state.context?.validation;
    
    if (!validation) {
      return 'intentConfirmation';
    }
    
    // Se contexto está completo e válido, finalizar
    if (validation.isComplete && validation.businessValidation?.isValid) {
      return 'END';
    }
    
    // Se há problemas, ir para confirmação/coleta
    return 'intentConfirmation';
  }

  /**
   * Determina próximo nó baseado no resultado do nó atual
   */
  determineNextNodeFromResult(currentNodeId, nodeResult, state) {
    // Se resultado indica estágio específico, usar fluxo padrão
    if (nodeResult.nextStage) {
      switch (nodeResult.nextStage) {
        case 'intent_classification':
          return 'intentClassification';
        case 'context_validation':
          return 'contextValidation';
        case 'intent_confirmation':
          return 'intentConfirmation';
        case 'scheduling':
        case 'completed':
          return 'END';
        default:
          return 'END';
      }
    }
    
    // Se há erro não recuperável, finalizar
    if (!nodeResult.success && !nodeResult.error?.recoverable) {
      return 'END';
    }
    
    // Usar fluxo padrão baseado no nó atual
    return this.nodeFlow[currentNodeId] || 'END';
  }

  /**
   * Gera resposta fallback quando nenhum nó gerou resposta
   */
  generateFallbackResponse(state) {
    const intent = state.intent?.type;
    
    switch (intent) {
      case 'scheduling':
        return 'Vou te ajudar com o agendamento! Me diga que serviço você precisa.';
      case 'cancellation':
        return 'Vou te ajudar com o cancelamento. Qual agendamento você quer cancelar?';
      case 'inquiry':
        return 'Que informação você precisa? Posso ajudar com horários, preços ou serviços.';
      case 'greeting':
        return 'Oi! Tudo bem? Como posso ajudar você hoje? 😊';
      default:
        return 'Oi! Como posso ajudar você hoje?';
    }
  }

  /**
   * Atualiza estatísticas de execução
   */
  updateExecutionStats(executionTime, success) {
    this.executionStats.totalExecutions++;
    
    if (success) {
      this.executionStats.successfulExecutions++;
    }
    
    // Calcular média móvel do tempo de execução
    const currentAvg = this.executionStats.averageExecutionTime;
    const totalExecs = this.executionStats.totalExecutions;
    
    this.executionStats.averageExecutionTime = 
      (currentAvg * (totalExecs - 1) + executionTime) / totalExecs;
  }

  /**
   * Obtém métricas completas do grafo
   */
  getMetrics() {
    return {
      graphId: this.graphId,
      executionStats: this.executionStats,
      nodeMetrics: Object.fromEntries(
        Object.entries(this.nodes).map(([nodeId, node]) => [
          nodeId,
          node.getMetrics()
        ])
      ),
      stateManagerStats: this.stateManager.getStats(),
      loggerMetrics: this.logger.getGraphMetrics(this.graphId)
    };
  }

  /**
   * Debug: Visualizar fluxo de um cliente específico
   */
  debugCustomerFlow(customerPhone) {
    const state = this.stateManager.getState(customerPhone);
    const traces = this.logger.getCustomerTraces(customerPhone);
    
    console.log(`🔍 DEBUG - Fluxo do Cliente ${customerPhone}`);
    console.log('='.repeat(50));
    
    if (state) {
      console.log('📊 Estado Atual:');
      console.log(`  Estágio: ${state.currentStage}`);
      console.log(`  Intenção: ${state.intent?.type} (${(state.intent?.confidence * 100).toFixed(0)}%)`);
      console.log(`  Mensagens: ${state.messages.length}`);
      console.log(`  Contexto válido: ${state.context?.validation?.isComplete ? 'Sim' : 'Não'}`);
    }
    
    if (traces.length > 0) {
      console.log('\n🛤️ Execuções Recentes:');
      traces.slice(0, 3).forEach((trace, index) => {
        const duration = trace.endTime - trace.startTime;
        console.log(`  ${index + 1}. ${trace.status} em ${duration}ms (${trace.nodes.length} nós)`);
      });
    }
    
    if (state?.metadata?.nodeHistory) {
      console.log('\n📈 Histórico de Nós:');
      state.metadata.nodeHistory.slice(-5).forEach((entry, index) => {
        console.log(`  ${index + 1}. ${entry}`);
      });
    }
  }

  /**
   * Reset do estado de um cliente (limpeza)
   */
  resetCustomerState(customerPhone) {
    this.stateManager.clearState(customerPhone);
    console.log(`🧹 Estado resetado para ${customerPhone}`);
  }

  /**
   * Health check do grafo
   */
  healthCheck() {
    const health = {
      status: 'healthy',
      issues: [],
      metrics: this.getMetrics()
    };
    
    // Verificar nós
    for (const [nodeId, node] of Object.entries(this.nodes)) {
      const nodeMetrics = node.getMetrics();
      
      if (nodeMetrics.errorRate > 0.5) {
        health.status = 'degraded';
        health.issues.push(`High error rate in ${nodeId}: ${(nodeMetrics.errorRate * 100).toFixed(0)}%`);
      }
      
      if (nodeMetrics.averageExecutionTime > 10000) {
        health.status = 'degraded';
        health.issues.push(`Slow execution in ${nodeId}: ${nodeMetrics.averageExecutionTime.toFixed(0)}ms avg`);
      }
    }
    
    // Verificar estado geral
    if (this.executionStats.successfulExecutions === 0) {
      health.status = 'unhealthy';
      health.issues.push('No successful executions recorded');
    }
    
    const successRate = this.executionStats.successfulExecutions / this.executionStats.totalExecutions;
    if (successRate < 0.8) {
      health.status = 'degraded';
      health.issues.push(`Low success rate: ${(successRate * 100).toFixed(0)}%`);
    }
    
    return health;
  }

  /**
   * Exporta dados do grafo para análise
   */
  exportData(options = {}) {
    return {
      graphId: this.graphId,
      metrics: this.getMetrics(),
      loggerData: this.logger.exportData(options),
      healthCheck: this.healthCheck(),
      exportTimestamp: new Date()
    };
  }
}

module.exports = IntentGraph;
