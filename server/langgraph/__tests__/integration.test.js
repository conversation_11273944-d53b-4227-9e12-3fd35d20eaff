/**
 * Testes de Integração para LangGraph - Fase 1
 * 
 * Valida funcionamento básico da integração
 */

const IntentGraph = require('../graphs/IntentGraph');
const IntentGraphAdapter = require('../adapters/IntentGraphAdapter');

describe('LangGraph Integration - Fase 1', () => {
  let intentGraph;
  let adapter;
  
  beforeEach(() => {
    intentGraph = new IntentGraph();
    adapter = new IntentGraphAdapter();
  });

  describe('IntentGraph Basic Functionality', () => {
    test('deve inicializar sem erros', () => {
      expect(intentGraph).toBeDefined();
      expect(intentGraph.graphId).toBe('intent_analysis_graph');
      expect(intentGraph.nodes).toBeDefined();
      expect(intentGraph.stateManager).toBeDefined();
    });

    test('deve ter todos os nós necessários', () => {
      expect(intentGraph.nodes.intentClassification).toBeDefined();
      expect(intentGraph.nodes.contextValidation).toBeDefined();
      expect(intentGraph.nodes.intentConfirmation).toBeDefined();
    });

    test('deve processar mensagem básica de agendamento', async () => {
      const customerPhone = '+5521999999999';
      const message = 'Quero agendar uma barba';
      
      const result = await intentGraph.process(customerPhone, message);
      
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.response).toBeDefined();
      expect(typeof result.response).toBe('string');
      expect(result.debugInfo.traceId).toBeDefined();
    }, 30000); // 30s timeout para IA

    test('deve processar mensagem de saudação', async () => {
      const customerPhone = '+5521888888888';
      const message = 'Oi';
      
      const result = await intentGraph.process(customerPhone, message);
      
      expect(result).toBeDefined();
      expect(result.success).toBe(true);
      expect(result.response).toBeDefined();
      expect(result.stage).toBeDefined();
    }, 30000);

    test('deve manter estado entre mensagens', async () => {
      const customerPhone = '+5521777777777';
      
      // Primeira mensagem
      const result1 = await intentGraph.process(customerPhone, 'Oi');
      expect(result1.success).toBe(true);
      
      // Segunda mensagem
      const result2 = await intentGraph.process(customerPhone, 'Quero agendar');
      expect(result2.success).toBe(true);
      
      // Estado deve existir
      const state = intentGraph.stateManager.getState(customerPhone);
      expect(state).toBeDefined();
      expect(state.messages.length).toBeGreaterThanOrEqual(2);
    }, 30000);
  });

  describe('IntentGraphAdapter Integration', () => {
    test('deve inicializar adapter sem erros', () => {
      expect(adapter).toBeDefined();
      expect(adapter.intentGraph).toBeDefined();
      expect(adapter.isEnabled).toBe(true);
    });

    test('deve detectar intenção de agendamento', async () => {
      const conversation = {
        customerPhone: '+5521666666666',
        messages: [
          { role: 'user', content: 'Quero agendar uma barba amanhã' }
        ]
      };
      
      const hasIntent = await adapter.detectSchedulingIntentWithAI(conversation);
      
      expect(typeof hasIntent).toBe('boolean');
      // Pode ser true ou false dependendo da IA, mas deve responder
    }, 30000);

    test('deve retornar false para saudação simples', async () => {
      const conversation = {
        customerPhone: '+5521555555555',
        messages: [
          { role: 'user', content: 'Oi' }
        ]
      };
      
      const hasIntent = await adapter.detectSchedulingIntentWithAI(conversation);
      
      expect(typeof hasIntent).toBe('boolean');
      // Saudação simples não deve ter intenção de agendamento
      expect(hasIntent).toBe(false);
    }, 30000);

    test('deve ter fallback funcional', async () => {
      // Desabilitar adapter para testar fallback
      adapter.setEnabled(false);
      
      const conversation = {
        customerPhone: '+5521444444444',
        messages: [
          { role: 'user', content: 'Quero agendar' }
        ]
      };
      
      const hasIntent = await adapter.detectSchedulingIntentWithAI(conversation);
      
      expect(typeof hasIntent).toBe('boolean');
      // Deve funcionar mesmo desabilitado (fallback)
      
      // Reabilitar para outros testes
      adapter.setEnabled(true);
    });
  });

  describe('Error Handling', () => {
    test('deve lidar com mensagens vazias', async () => {
      const customerPhone = '+5521333333333';
      const message = '';
      
      const result = await intentGraph.process(customerPhone, message);
      
      expect(result).toBeDefined();
      expect(result.success).toBeDefined();
      // Deve retornar algo, mesmo com mensagem vazia
    });

    test('deve lidar com conversas inválidas', async () => {
      const conversation = null;
      
      const hasIntent = await adapter.detectSchedulingIntentWithAI(conversation);
      
      expect(typeof hasIntent).toBe('boolean');
      expect(hasIntent).toBe(false);
    });

    test('deve ter health check funcional', () => {
      const health = adapter.healthCheck();
      
      expect(health).toBeDefined();
      expect(health.adapterStatus).toBeDefined();
      expect(health.graphHealth).toBeDefined();
      expect(health.integrationStatus).toBeDefined();
    });
  });

  describe('Performance Metrics', () => {
    test('deve coletar métricas básicas', () => {
      const metrics = adapter.getMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.adapterInfo).toBeDefined();
      expect(metrics.graphMetrics).toBeDefined();
      expect(metrics.adapterInfo.enabled).toBe(true);
    });

    test('deve ter métricas dos nós', () => {
      const graphMetrics = intentGraph.getMetrics();
      
      expect(graphMetrics).toBeDefined();
      expect(graphMetrics.nodeMetrics).toBeDefined();
      expect(graphMetrics.nodeMetrics.intentClassification).toBeDefined();
      expect(graphMetrics.nodeMetrics.contextValidation).toBeDefined();
      expect(graphMetrics.nodeMetrics.intentConfirmation).toBeDefined();
    });
  });

  describe('State Management', () => {
    test('deve criar e gerenciar estados', () => {
      const customerPhone = '+5521222222222';
      
      // Estado inicial não deve existir
      let state = intentGraph.stateManager.getState(customerPhone);
      expect(state).toBeUndefined();
      
      // Inicializar estado
      state = intentGraph.stateManager.initializeState(customerPhone);
      expect(state).toBeDefined();
      expect(state.customerPhone).toBe(customerPhone);
      expect(state.currentStage).toBe('greeting');
      
      // Deve conseguir recuperar estado
      const retrievedState = intentGraph.stateManager.getState(customerPhone);
      expect(retrievedState).toEqual(state);
    });

    test('deve validar transições de estágio', () => {
      const customerPhone = '+5521111111111';
      
      intentGraph.stateManager.initializeState(customerPhone);
      
      // Transição válida: greeting -> intent_classification
      expect(() => {
        intentGraph.stateManager.transitionStage(customerPhone, 'intent_classification', 'Teste');
      }).not.toThrow();
      
      // Transição inválida deve lançar erro
      expect(() => {
        intentGraph.stateManager.transitionStage(customerPhone, 'completed', 'Inválida');
      }).toThrow();
    });
  });

  afterEach(() => {
    // Cleanup após cada teste
    if (intentGraph && intentGraph.stateManager) {
      // Limpar estados de teste
      const testPhones = [
        '+5521999999999', '+5521888888888', '+5521777777777',
        '+5521666666666', '+5521555555555', '+5521444444444',
        '+5521333333333', '+5521222222222', '+5521111111111'
      ];
      
      testPhones.forEach(phone => {
        try {
          intentGraph.stateManager.clearState(phone);
        } catch (error) {
          // Ignorar erros de limpeza
        }
      });
    }
  });
});