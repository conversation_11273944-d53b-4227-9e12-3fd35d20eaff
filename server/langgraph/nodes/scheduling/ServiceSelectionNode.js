/**
 * ServiceSelectionNode - Nó de Seleção de Serviços
 * 
 * Responsabilidades:
 * - Buscar serviços disponíveis via API Trinks
 * - Mapear serviços mencionados pelo cliente
 * - Validar disponibilidade de serviços
 * - Sugerir serviços baseado no contexto
 * - Cache de serviços para performance
 */

const BaseNode = require('../../core/BaseNode');

class ServiceSelectionNode extends BaseNode {
  constructor() {
    super('service_selection', {
      timeout: 8000, // 8s para selecionar serviço
      retryAttempts: 2
    });
    
    // Configuração da API Trinks
    this.trinksConfig = {
      baseUrl: process.env.TRINKS_API_BASE_URL || 'https://api.trinks.com',
      apiKey: process.env.TRINKS_API_KEY,
      establishmentId: process.env.TRINKS_ESTABLISHMENT_ID
    };
    
    // Cache de serviços
    this.serviceCache = new Map();
    this.cacheExpiration = 30 * 60 * 1000; // 30 minutos
    
    // Mapeamento de palavras-chave para serviços
    this.serviceKeywords = {
      'corte': ['corte', 'cabelo', 'cortar'],
      'barba': ['barba', 'fazer a barba'],
      'unha': ['unha', 'manicure', 'pedicure'],
      'sobrancelha': ['sobrancelha', 'design', 'design de sobrancelha'],
      'escova': ['escova', 'escova progressiva'],
      'tintura': ['tintura', 'coloracao', 'coloração', 'pintar'],
      'hidratacao': ['hidratacao', 'hidratação', 'tratamento'],
      'penteado': ['penteado', 'penteados']
    };
  }

  /**
   * Executa seleção de serviço
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Buscar serviços disponíveis
      const availableServices = await this.getAvailableServices();
      
      // Analisar serviço mencionado pelo cliente
      const serviceAnalysis = this.analyzeRequestedService(state);
      
      // Mapear serviço solicitado
      const serviceMapping = this.mapRequestedService(serviceAnalysis, availableServices);
      
      // Validar seleção de serviço
      const validation = this.validateServiceSelection(serviceMapping, availableServices);
      
      if (validation.isValid) {
        return this.buildSuccessResponse(serviceMapping.selectedService, state, serviceAnalysis);
      } else {
        return this.buildValidationErrorResponse(validation, availableServices, state);
      }
      
    } catch (error) {
      return {
        success: false,
        result: {
          selectedService: null,
          availableServices: [],
          error: error.message
        },
        nextStage: 'intent_confirmation',
        updates: {},
        error: {
          message: error.message,
          code: 'SERVICE_SELECTION_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Busca serviços disponíveis via API ou cache
   */
  async getAvailableServices() {
    // Verificar cache primeiro
    const cacheKey = 'available_services';
    const cachedServices = this.getCachedServices(cacheKey);
    
    if (cachedServices) {
      return cachedServices;
    }

    let services;
    if (this.trinksConfig.apiKey) {
      services = await this.fetchServicesFromAPI();
    } else {
      services = this.generateMockServices();
    }
    
    // Processar e normalizar serviços
    const processedServices = this.processServices(services);
    
    // Salvar no cache
    this.setCachedServices(cacheKey, processedServices);
    
    return processedServices;
  }

  /**
   * Busca serviços via API Trinks
   */
  async fetchServicesFromAPI() {
    const response = await this.callExternalService('trinks_api', async () => {
      const url = `${this.trinksConfig.baseUrl}/v1/servicos`;
      
      const requestOptions = {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.trinksConfig.apiKey}`,
          'Content-Type': 'application/json',
          'X-Establishment-ID': this.trinksConfig.establishmentId
        }
      };
      
      const queryParams = new URLSearchParams({
        estabelecimento_id: this.trinksConfig.establishmentId,
        ativo: 'true' // Apenas serviços ativos
      });
      
      const fullUrl = `${url}?${queryParams.toString()}`;
      const apiResponse = await fetch(fullUrl, requestOptions);
      
      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status} - ${apiResponse.statusText}`);
      }
      
      return await apiResponse.json();
    });
    
    return response?.data || response?.servicos || response || [];
  }

  /**
   * Gera serviços mock para desenvolvimento
   */
  generateMockServices() {
    return [
      {
        id: 'serv_001',
        nome: 'Corte Masculino',
        descricao: 'Corte de cabelo masculino tradicional',
        preco: 30.00,
        duracao: 30,
        ativo: true,
        categoria: 'Corte'
      },
      {
        id: 'serv_002',
        nome: 'Barba 15',
        descricao: 'Serviço de barba completo',
        preco: 25.00,
        duracao: 15,
        ativo: true,
        categoria: 'Barba'
      },
      {
        id: 'serv_003',
        nome: 'Unha',
        descricao: 'Manicure completa',
        preco: 40.00,
        duracao: 45,
        ativo: true,
        categoria: 'Unha'
      },
      {
        id: 'serv_004',
        nome: 'Design de Sobrancelha',
        descricao: 'Design e modelagem de sobrancelhas',
        preco: 35.00,
        duracao: 20,
        ativo: true,
        categoria: 'Sobrancelha'
      },
      {
        id: 'serv_005',
        nome: 'Escova Progressiva',
        descricao: 'Tratamento capilar com escova',
        preco: 80.00,
        duracao: 120,
        ativo: true,
        categoria: 'Tratamento'
      }
    ];
  }

  /**
   * Processa e normaliza dados de serviços
   */
  processServices(rawServices) {
    return rawServices.map(service => ({
      id: service.id,
      name: service.nome || service.name,
      description: service.descricao || service.description || '',
      price: parseFloat(service.preco || service.price || 0),
      duration: parseInt(service.duracao || service.duration || 30),
      category: service.categoria || service.category || 'Geral',
      isActive: service.ativo !== false && service.active !== false,
      keywords: this.generateServiceKeywords(service.nome || service.name)
    })).filter(service => service.isActive);
  }

  /**
   * Gera palavras-chave para um serviço
   */
  generateServiceKeywords(serviceName) {
    const name = serviceName.toLowerCase();
    const keywords = [name];
    
    // Adicionar variações baseadas no nome
    for (const [category, categoryKeywords] of Object.entries(this.serviceKeywords)) {
      if (categoryKeywords.some(keyword => name.includes(keyword))) {
        keywords.push(...categoryKeywords);
      }
    }
    
    return [...new Set(keywords)]; // Remove duplicatas
  }

  /**
   * Analisa serviço solicitado pelo cliente
   */
  analyzeRequestedService(state) {
    const analysis = {
      mentionedService: null,
      confidence: 0,
      source: 'none',
      extractedKeywords: []
    };
    
    // 1. Verificar contexto extraído pela IA
    if (state.intent?.extractedContext?.mentioned_service) {
      analysis.mentionedService = state.intent.extractedContext.mentioned_service;
      analysis.source = 'ai_extraction';
      analysis.confidence = 0.8;
    }
    
    // 2. Verificar dados de scheduling salvos
    if (state.schedulingData?.service?.name) {
      analysis.mentionedService = state.schedulingData.service.name;
      analysis.source = 'scheduling_data';
      analysis.confidence = 0.9;
    }
    
    // 3. Analisar mensagens recentes
    if (!analysis.mentionedService) {
      const recentAnalysis = this.analyzeRecentMessages(state.messages);
      if (recentAnalysis.service) {
        analysis.mentionedService = recentAnalysis.service;
        analysis.source = 'message_analysis';
        analysis.confidence = recentAnalysis.confidence;
        analysis.extractedKeywords = recentAnalysis.keywords;
      }
    }
    
    return analysis;
  }

  /**
   * Analisa mensagens recentes em busca de serviços
   */
  analyzeRecentMessages(messages) {
    if (!messages || messages.length === 0) {
      return { service: null, confidence: 0, keywords: [] };
    }
    
    // Pegar últimas mensagens do usuário
    const userMessages = messages
      .filter(msg => msg.role === 'user')
      .slice(-3)
      .map(msg => msg.content.toLowerCase());
    
    const combinedText = userMessages.join(' ');
    const foundKeywords = [];
    
    // Buscar palavras-chave de serviços
    for (const [category, keywords] of Object.entries(this.serviceKeywords)) {
      for (const keyword of keywords) {
        if (combinedText.includes(keyword)) {
          foundKeywords.push({ category, keyword, confidence: 0.7 });
        }
      }
    }
    
    if (foundKeywords.length > 0) {
      // Pegar a categoria mais mencionada
      const categoryCount = {};
      foundKeywords.forEach(item => {
        categoryCount[item.category] = (categoryCount[item.category] || 0) + 1;
      });
      
      const mostMentioned = Object.entries(categoryCount)
        .sort(([,a], [,b]) => b - a)[0];
      
      return {
        service: mostMentioned[0],
        confidence: Math.min(0.8, 0.5 + (mostMentioned[1] * 0.1)),
        keywords: foundKeywords
      };
    }
    
    return { service: null, confidence: 0, keywords: [] };
  }

  /**
   * Mapeia serviço solicitado para serviços disponíveis
   */
  mapRequestedService(serviceAnalysis, availableServices) {
    const mapping = {
      requestedService: serviceAnalysis.mentionedService,
      selectedService: null,
      confidence: serviceAnalysis.confidence,
      matchType: 'none',
      alternatives: []
    };
    
    if (!serviceAnalysis.mentionedService) {
      return mapping;
    }
    
    const requested = serviceAnalysis.mentionedService.toLowerCase();
    
    // 1. Buscar correspondência exata no nome
    let exactMatch = availableServices.find(service => 
      service.name.toLowerCase() === requested
    );
    
    if (exactMatch) {
      mapping.selectedService = exactMatch;
      mapping.matchType = 'exact';
      mapping.confidence = 0.95;
      return mapping;
    }
    
    // 2. Buscar correspondência parcial no nome
    let partialMatch = availableServices.find(service => 
      service.name.toLowerCase().includes(requested) || 
      requested.includes(service.name.toLowerCase())
    );
    
    if (partialMatch) {
      mapping.selectedService = partialMatch;
      mapping.matchType = 'partial';
      mapping.confidence = Math.max(0.8, serviceAnalysis.confidence);
      return mapping;
    }
    
    // 3. Buscar por palavras-chave
    let keywordMatches = availableServices.filter(service =>
      service.keywords.some(keyword => 
        keyword.includes(requested) || requested.includes(keyword)
      )
    );
    
    if (keywordMatches.length > 0) {
      // Pegar o melhor match por palavras-chave
      mapping.selectedService = keywordMatches[0];
      mapping.matchType = 'keyword';
      mapping.confidence = Math.max(0.6, serviceAnalysis.confidence * 0.8);
      mapping.alternatives = keywordMatches.slice(1, 4); // Até 3 alternativas
      return mapping;
    }
    
    // 4. Sugerir serviços populares como alternativas
    mapping.alternatives = availableServices
      .sort((a, b) => a.price - b.price) // Ordenar por preço
      .slice(0, 3);
    
    return mapping;
  }

  /**
   * Valida seleção de serviço
   */
  validateServiceSelection(serviceMapping, availableServices) {
    const validation = {
      isValid: false,
      issues: [],
      suggestions: []
    };
    
    if (serviceMapping.selectedService) {
      // Verificar se serviço está ativo
      if (!serviceMapping.selectedService.isActive) {
        validation.issues.push('Serviço não está disponível no momento');
        validation.suggestions.push('Escolher outro serviço da nossa lista');
      } else {
        validation.isValid = true;
      }
    } else if (serviceMapping.requestedService) {
      // Serviço mencionado mas não encontrado
      validation.issues.push(`Serviço "${serviceMapping.requestedService}" não encontrado`);
      
      if (serviceMapping.alternatives.length > 0) {
        validation.suggestions.push('Serviços similares disponíveis');
      } else {
        validation.suggestions.push('Escolher um serviço da nossa lista');
      }
    } else {
      // Nenhum serviço mencionado
      validation.issues.push('Tipo de serviço não especificado');
      validation.suggestions.push('Informar qual serviço deseja agendar');
    }
    
    return validation;
  }

  /**
   * Constrói resposta de sucesso
   */
  buildSuccessResponse(selectedService, state, serviceAnalysis) {
    return {
      success: true,
      result: {
        selectedService,
        serviceFound: true,
        matchType: 'confirmed',
        confidence: 0.9
      },
      nextStage: 'professional_selection',
      updates: {
        context: {
          ...state.context,
          selectedService: selectedService,
          schedulingData: {
            ...(state.context?.schedulingData || {}),
            service: {
              id: selectedService.id,
              name: selectedService.name,
              price: selectedService.price,
              duration: selectedService.duration
            }
          }
        }
      },
      metadata: {
        debugInfo: {
          serviceId: selectedService.id,
          serviceName: selectedService.name,
          price: selectedService.price,
          duration: selectedService.duration,
          analysisSource: serviceAnalysis.source,
          originalRequest: serviceAnalysis.mentionedService
        }
      }
    };
  }

  /**
   * Constrói resposta de erro de validação
   */
  buildValidationErrorResponse(validation, availableServices, state) {
    return {
      success: false,
      result: {
        selectedService: null,
        serviceFound: false,
        issues: validation.issues,
        suggestions: validation.suggestions,
        availableServices: availableServices.slice(0, 5), // Top 5 serviços
        needsUserInput: true
      },
      nextStage: 'intent_confirmation', // Pedir mais informações
      updates: {
        context: {
          ...state.context,
          serviceSelection: {
            issues: validation.issues,
            availableServices: availableServices
          }
        }
      },
      metadata: {
        debugInfo: {
          validationFailed: true,
          issueCount: validation.issues.length,
          suggestionCount: validation.suggestions.length,
          availableServiceCount: availableServices.length
        }
      }
    };
  }

  /**
   * Cache de serviços
   */
  getCachedServices(cacheKey) {
    const cached = this.serviceCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp < this.cacheExpiration)) {
      return cached.data;
    }
    return null;
  }

  setCachedServices(cacheKey, data) {
    this.serviceCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Obtém estatísticas de seleção de serviços
   */
  getServiceStats() {
    return {
      ...this.getMetrics(),
      cacheSize: this.serviceCache.size,
      exactMatches: this.exactMatchCount || 0,
      partialMatches: this.partialMatchCount || 0,
      keywordMatches: this.keywordMatchCount || 0,
      noMatches: this.noMatchCount || 0
    };
  }
}

module.exports = ServiceSelectionNode;