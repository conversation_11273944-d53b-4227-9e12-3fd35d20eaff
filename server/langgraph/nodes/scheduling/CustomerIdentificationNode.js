/**
 * CustomerIdentificationNode - Nó de Identificação de Cliente
 * 
 * Responsabilidades:
 * - Identificar cliente existente por telefone
 * - Criar novo cliente se não existir
 * - Validar dados do cliente
 * - Integração com API Trinks para dados reais
 * - Cache de dados do cliente
 */

const BaseNode = require('../../core/BaseNode');

class CustomerIdentificationNode extends BaseNode {
  constructor() {
    super('customer_identification', {
      timeout: 10000, // 10s para identificar/criar cliente
      retryAttempts: 2
    });
    
    // Configuração da API Trinks
    this.trinksConfig = {
      baseUrl: process.env.TRINKS_API_BASE_URL || 'https://api.trinks.com',
      apiKey: process.env.TRINKS_API_KEY,
      establishmentId: process.env.TRINKS_ESTABLISHMENT_ID
    };
    
    // Cache de clientes
    this.customerCache = new Map();
    this.cacheExpiration = 15 * 60 * 1000; // 15 minutos
  }

  /**
   * Executa identificação/criação de cliente
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Verificar cache primeiro
      const cacheKey = this.generateCacheKey(customerPhone);
      const cachedCustomer = this.getCachedCustomer(cacheKey);
      
      if (cachedCustomer) {
        this.logExecution('DEBUG', customerPhone, {
          action: 'cache_hit',
          customerId: cachedCustomer.id
        });
        
        return this.buildSuccessResponse(cachedCustomer, state, true);
      }

      // Buscar cliente na API
      let customer;
      if (this.trinksConfig.apiKey) {
        customer = await this.findOrCreateCustomerAPI(customerPhone, state);
      } else {
        // Fallback para dados mock
        customer = this.generateMockCustomer(customerPhone, state);
      }
      
      // Validar dados do cliente
      const validatedCustomer = this.validateCustomerData(customer);
      
      // Salvar no cache
      this.setCachedCustomer(cacheKey, validatedCustomer);
      
      return this.buildSuccessResponse(validatedCustomer, state, false);
      
    } catch (error) {
      return {
        success: false,
        result: {
          customer: null,
          isExistingCustomer: false,
          error: error.message
        },
        nextStage: 'intent_confirmation', // Permitir continuar sem dados do cliente
        updates: {},
        error: {
          message: error.message,
          code: 'CUSTOMER_IDENTIFICATION_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Busca ou cria cliente via API Trinks
   */
  async findOrCreateCustomerAPI(customerPhone, state) {
    // Primeiro, tentar buscar cliente existente
    let customer = await this.findExistingCustomer(customerPhone);
    
    if (customer) {
      this.logExecution('INFO', customerPhone, {
        action: 'customer_found',
        customerId: customer.id,
        customerName: customer.nome
      });
      
      return {
        ...customer,
        isExisting: true,
        source: 'api_existing'
      };
    }
    
    // Se não encontrou, criar novo cliente
    const extractedName = this.extractCustomerName(state);
    
    if (!extractedName) {
      // Não tem nome para criar cliente - retornar erro que será tratado
      throw new Error('Nome do cliente necessário para criar nova conta');
    }
    
    customer = await this.createNewCustomer(customerPhone, extractedName);
    
    this.logExecution('INFO', customerPhone, {
      action: 'customer_created',
      customerId: customer.id,
      customerName: customer.nome
    });
    
    return {
      ...customer,
      isExisting: false,
      source: 'api_created'
    };
  }

  /**
   * Busca cliente existente na API
   */
  async findExistingCustomer(customerPhone) {
    const normalizedPhone = this.normalizePhone(customerPhone);
    
    try {
      const response = await this.callExternalService('trinks_api', async () => {
        const url = `${this.trinksConfig.baseUrl}/v1/clientes/buscar`;
        
        const requestOptions = {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${this.trinksConfig.apiKey}`,
            'Content-Type': 'application/json',
            'X-Establishment-ID': this.trinksConfig.establishmentId
          }
        };
        
        const queryParams = new URLSearchParams({
          telefone: normalizedPhone,
          estabelecimento_id: this.trinksConfig.establishmentId
        });
        
        const fullUrl = `${url}?${queryParams.toString()}`;
        const apiResponse = await fetch(fullUrl, requestOptions);
        
        if (apiResponse.status === 404) {
          return null; // Cliente não encontrado
        }
        
        if (!apiResponse.ok) {
          throw new Error(`API Error: ${apiResponse.status} - ${apiResponse.statusText}`);
        }
        
        return await apiResponse.json();
      });
      
      return response?.data || response?.cliente || response;
      
    } catch (error) {
      if (error.message.includes('404')) {
        return null; // Cliente não encontrado
      }
      throw error;
    }
  }

  /**
   * Cria novo cliente na API
   */
  async createNewCustomer(customerPhone, customerName) {
    const normalizedPhone = this.normalizePhone(customerPhone);
    
    const customerData = {
      nome: customerName,
      telefone: normalizedPhone,
      email: this.generateEmailFromPhone(normalizedPhone), // Email fictício baseado no telefone
      estabelecimento_id: this.trinksConfig.establishmentId,
      observacoes: 'Cliente criado automaticamente via IA'
    };
    
    const response = await this.callExternalService('trinks_api', async () => {
      const url = `${this.trinksConfig.baseUrl}/v1/clientes`;
      
      const requestOptions = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.trinksConfig.apiKey}`,
          'Content-Type': 'application/json',
          'X-Establishment-ID': this.trinksConfig.establishmentId
        },
        body: JSON.stringify(customerData)
      };
      
      const apiResponse = await fetch(url, requestOptions);
      
      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status} - ${apiResponse.statusText}`);
      }
      
      return await apiResponse.json();
    });
    
    return response?.data || response?.cliente || response;
  }

  /**
   * Extrai nome do cliente do estado da conversa
   */
  extractCustomerName(state) {
    // 1. Verificar se já tem nome no contexto
    if (state.context?.customerName) {
      return state.context.customerName;
    }
    
    // 2. Verificar se foi extraído pela IA na intenção
    if (state.intent?.extractedContext?.customer_name) {
      return state.intent.extractedContext.customer_name;
    }
    
    // 3. Tentar extrair das últimas mensagens
    const recentMessages = state.messages?.slice(-5) || [];
    const userMessages = recentMessages.filter(msg => msg.role === 'user');
    
    for (const message of userMessages.reverse()) {
      const extractedName = this.extractNameFromMessage(message.content);
      if (extractedName) {
        return extractedName;
      }
    }
    
    return null;
  }

  /**
   * Extrai nome de uma mensagem usando padrões
   */
  extractNameFromMessage(message) {
    const text = message.toLowerCase();
    
    // Padrões para detectar apresentação do nome
    const patterns = [
      /(?:sou|eu sou|me chamo|meu nome é|eu me chamo)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)/i,
      /(?:^|\s)([A-Z][a-z]{2,}(?:\s+[A-Z][a-z]{2,})*?)(?:\s|$)/,
      /(?:nome|chamo)\s+([A-Z][a-z]+)/i
    ];
    
    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match && match[1]) {
        const name = match[1].trim();
        
        // Validar se parece um nome real
        if (this.isValidName(name)) {
          return this.capitalizeName(name);
        }
      }
    }
    
    return null;
  }

  /**
   * Valida se parece um nome real
   */
  isValidName(name) {
    // Não pode ser muito curto
    if (name.length < 2) return false;
    
    // Não pode conter números
    if (/\d/.test(name)) return false;
    
    // Não pode ser palavras comuns
    const commonWords = ['quero', 'para', 'com', 'hoje', 'amanha', 'agora', 'favor', 'obrigado'];
    if (commonWords.includes(name.toLowerCase())) return false;
    
    return true;
  }

  /**
   * Capitaliza nome corretamente
   */
  capitalizeName(name) {
    return name.split(' ')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1).toLowerCase())
      .join(' ');
  }

  /**
   * Normaliza telefone
   */
  normalizePhone(phone) {
    const numbersOnly = phone.replace(/\D/g, '');
    
    // Se começar com +55, remover
    if (numbersOnly.startsWith('55') && numbersOnly.length === 13) {
      return numbersOnly.substring(2);
    }
    
    return numbersOnly;
  }

  /**
   * Gera email fictício baseado no telefone
   */
  generateEmailFromPhone(phone) {
    return `cliente_${phone}@sistema.trinks.com`;
  }

  /**
   * Gera cliente mock para desenvolvimento
   */
  generateMockCustomer(customerPhone, state) {
    const normalizedPhone = this.normalizePhone(customerPhone);
    const phoneHash = this.hashPhone(normalizedPhone);
    
    // Extrair nome se possível
    const extractedName = this.extractCustomerName(state);
    
    const mockNames = ['Ana Silva', 'João Santos', 'Maria Oliveira', 'Carlos Pereira', 'Fernanda Costa'];
    const mockName = extractedName || mockNames[phoneHash % mockNames.length];
    
    return {
      id: phoneHash,
      nome: mockName,
      telefone: normalizedPhone,
      email: this.generateEmailFromPhone(normalizedPhone),
      dataCadastro: new Date().toISOString(),
      observacoes: 'Cliente mock para desenvolvimento',
      isExisting: phoneHash % 3 === 0, // 1/3 dos clientes são "existentes"
      source: 'mock'
    };
  }

  /**
   * Hash simples do telefone
   */
  hashPhone(phone) {
    let hash = 0;
    for (let i = 0; i < phone.length; i++) {
      const char = phone.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash) % 100000; // ID entre 1-100000
  }

  /**
   * Valida dados do cliente
   */
  validateCustomerData(customer) {
    const validated = { ...customer };
    
    // Garantir campos obrigatórios
    validated.id = validated.id || validated.cliente_id || this.hashPhone(customer.telefone);
    validated.nome = validated.nome || validated.name || 'Cliente';
    validated.telefone = validated.telefone || validated.phone;
    validated.email = validated.email || this.generateEmailFromPhone(validated.telefone);
    
    // Normalizar telefone
    validated.telefone = this.normalizePhone(validated.telefone);
    
    // Adicionar metadados
    validated.validatedAt = new Date();
    validated.isValid = !!(validated.id && validated.nome && validated.telefone);
    
    return validated;
  }

  /**
   * Constrói resposta de sucesso
   */
  buildSuccessResponse(customer, state, fromCache) {
    return {
      success: true,
      result: {
        customer,
        isExistingCustomer: customer.isExisting || false,
        customerFound: true,
        source: customer.source || 'unknown',
        fromCache
      },
      nextStage: 'service_selection',
      updates: {
        context: {
          ...state.context,
          customer: customer,
          customerPhone: customer.telefone,
          customerName: customer.nome,
          isExistingCustomer: customer.isExisting || false
        }
      },
      metadata: {
        debugInfo: {
          customerId: customer.id,
          customerName: customer.nome,
          isExisting: customer.isExisting || false,
          source: customer.source || 'unknown',
          fromCache,
          validationPassed: customer.isValid
        }
      }
    };
  }

  /**
   * Cache de clientes
   */
  generateCacheKey(phone) {
    return `customer_${this.normalizePhone(phone)}`;
  }

  getCachedCustomer(cacheKey) {
    const cached = this.customerCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp < this.cacheExpiration)) {
      return cached.data;
    }
    return null;
  }

  setCachedCustomer(cacheKey, data) {
    this.customerCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    // Limpar cache antigo
    if (this.customerCache.size > 100) {
      const oldestKey = this.customerCache.keys().next().value;
      this.customerCache.delete(oldestKey);
    }
  }

  /**
   * Obtém estatísticas de identificação de clientes
   */
  getCustomerStats() {
    return {
      ...this.getMetrics(),
      cacheSize: this.customerCache.size,
      cacheHitRate: this.calculateCacheHitRate(),
      newCustomersCreated: this.newCustomerCount || 0,
      existingCustomersFound: this.existingCustomerCount || 0
    };
  }

  calculateCacheHitRate() {
    const totalRequests = this.executionCount || 1;
    const cacheHits = this.cacheHitCount || 0;
    return (cacheHits / totalRequests) * 100;
  }
}

module.exports = CustomerIdentificationNode;