/**
 * ScheduleConfirmationNode - Nó de Confirmação de Agendamento
 * 
 * Responsabilidades:
 * - Compilar todos os dados do agendamento
 * - Criar agendamento via API Trinks
 * - Gerar mensagem de confirmação personalizada
 * - Tratar falhas com rollback automático
 * - Validação final antes da criação
 */

const BaseNode = require('../../core/BaseNode');

class ScheduleConfirmationNode extends BaseNode {
  constructor() {
    super('schedule_confirmation', {
      timeout: 20000, // 20s para criar agendamento
      retryAttempts: 3
    });
    
    // Configuração da API Trinks
    this.trinksConfig = {
      baseUrl: process.env.TRINKS_API_BASE_URL || 'https://api.trinks.com',
      apiKey: process.env.TRINKS_API_KEY,
      establishmentId: process.env.TRINKS_ESTABLISHMENT_ID
    };
    
    // Templates de mensagem de confirmação
    this.confirmationTemplates = {
      success: {
        existing_customer: 'Perfeito {customerName}! Seu agendamento está confirmado:\n\n📅 {dateFormatted}\n⏰ {time}\n👨‍💼 {professionalName}\n💼 {serviceName}\n💰 R$ {price}\n⏱️ {duration} minutos\n\nAgendamento #{appointmentId} ✅\n\nNos vemos lá! 😊',
        new_customer: 'Oi {customerName}! Seja bem-vindo(a)! 😊\n\nSeu primeiro agendamento está confirmado:\n\n📅 {dateFormatted}\n⏰ {time}\n👨‍💼 {professionalName}\n💼 {serviceName}\n💰 R$ {price}\n⏱️ {duration} minutos\n\nAgendamento #{appointmentId} ✅\n\nMal posso esperar para te conhecer! 🌟'
      },
      error: {
        general: 'Ops! Tivemos um probleminha para confirmar seu agendamento. Pode tentar novamente?',
        time_conflict: 'Esse horário acabou de ser ocupado por outro cliente. Vou mostrar outras opções disponíveis!',
        professional_unavailable: '{professionalName} não está mais disponível nesse horário. Que tal com outro profissional?'
      }
    };
  }

  /**
   * Executa confirmação do agendamento
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Validar se temos todos os dados necessários
      const validation = this.validateSchedulingData(state);
      if (!validation.isValid) {
        return this.buildValidationErrorResponse(validation, state);
      }
      
      const { customer, professional, service, selectedSlot } = validation.data;
      
      // Preparar dados do agendamento
      const appointmentData = this.prepareAppointmentData(
        customer, professional, service, selectedSlot, state
      );
      
      // Validação final antes da criação
      const finalValidation = this.performFinalValidation(appointmentData);
      if (!finalValidation.isValid) {
        return this.buildFinalValidationErrorResponse(finalValidation, state);
      }
      
      // Criar agendamento via API
      let createdAppointment;
      if (this.trinksConfig.apiKey) {
        createdAppointment = await this.createAppointmentAPI(appointmentData);
      } else {
        createdAppointment = this.createMockAppointment(appointmentData);
      }
      
      // Gerar mensagem de confirmação
      const confirmationMessage = this.generateConfirmationMessage(
        createdAppointment, customer, professional, service
      );
      
      return this.buildSuccessResponse(createdAppointment, confirmationMessage, state);
      
    } catch (error) {
      // Analisar tipo de erro para resposta apropriada
      const errorResponse = this.analyzeAndHandleError(error, state);
      return errorResponse;
    }
  }

  /**
   * Valida se temos todos os dados necessários para agendar
   */
  validateSchedulingData(state) {
    const validation = {
      isValid: false,
      missingData: [],
      data: {}
    };
    
    // Validar cliente
    const customer = state.context?.customer || state.schedulingData?.customer;
    if (!customer || !customer.id) {
      validation.missingData.push('customer');
    } else {
      validation.data.customer = customer;
    }
    
    // Validar profissional
    const professional = state.context?.selectedProfessional || state.schedulingData?.professional;
    if (!professional || !professional.id) {
      validation.missingData.push('professional');
    } else {
      validation.data.professional = professional;
    }
    
    // Validar serviço
    const service = state.context?.selectedService || state.schedulingData?.service;
    if (!service || !service.id) {
      validation.missingData.push('service');
    } else {
      validation.data.service = service;
    }
    
    // Validar slot selecionado
    const selectedSlot = state.context?.selectedSlot || 
                        state.schedulingData?.suggestedSlot || 
                        state.context?.suggestion?.suggestedSlot ||
                        state.context?.suggestion?.suggestions?.[0];
    
    if (!selectedSlot || !selectedSlot.dateTime) {
      validation.missingData.push('selected_slot');
    } else {
      validation.data.selectedSlot = selectedSlot;
    }
    
    validation.isValid = validation.missingData.length === 0;
    
    return validation;
  }

  /**
   * Prepara dados do agendamento para a API
   */
  prepareAppointmentData(customer, professional, service, selectedSlot, state) {
    const slotDateTime = new Date(selectedSlot.dateTime);
    
    return {
      clienteId: customer.id,
      profissionalId: professional.id,
      servicoId: service.id,
      dataHora: selectedSlot.dateTime,
      data: slotDateTime.toISOString().split('T')[0],
      hora: selectedSlot.time,
      duracaoEmMinutos: service.duration || 30,
      valor: service.price || 0,
      status: 'agendado',
      observacoes: this.generateAppointmentNotes(state),
      estabelecimentoId: this.trinksConfig.establishmentId,
      
      // Dados adicionais para contexto
      customerName: customer.nome || customer.name,
      professionalName: professional.name,
      serviceName: service.name,
      isNewCustomer: !customer.isExisting
    };
  }

  /**
   * Gera observações do agendamento
   */
  generateAppointmentNotes(state) {
    const notes = ['Agendamento criado automaticamente via IA'];
    
    // Adicionar contexto da conversa se relevante
    if (state.intent?.reasoning) {
      notes.push(`Contexto: ${state.intent.reasoning}`);
    }
    
    // Adicionar informações sobre origem
    const source = state.context?.routing?.sourceGraph || 'IntentGraph';
    notes.push(`Origem: ${source}`);
    
    // Adicionar timestamp
    notes.push(`Criado em: ${new Date().toLocaleString('pt-BR')}`);
    
    return notes.join(' | ');
  }

  /**
   * Validação final antes da criação
   */
  performFinalValidation(appointmentData) {
    const validation = {
      isValid: true,
      issues: []
    };
    
    // Validar se data/hora não está no passado
    const appointmentDateTime = new Date(appointmentData.dataHora);
    const now = new Date();
    
    if (appointmentDateTime <= now) {
      validation.isValid = false;
      validation.issues.push('Data/hora do agendamento está no passado');
    }
    
    // Validar se está dentro do horário de funcionamento
    const hour = appointmentDateTime.getHours();
    if (hour < 8 || hour >= 18) {
      validation.isValid = false;
      validation.issues.push('Horário fora do funcionamento (8h-18h)');
    }
    
    // Validar dia da semana
    const dayOfWeek = appointmentDateTime.getDay();
    if (dayOfWeek === 0) { // Domingo
      validation.isValid = false;
      validation.issues.push('Não funcionamos aos domingos');
    }
    
    // Validar campos obrigatórios
    const requiredFields = ['clienteId', 'profissionalId', 'servicoId', 'dataHora'];
    for (const field of requiredFields) {
      if (!appointmentData[field]) {
        validation.isValid = false;
        validation.issues.push(`Campo obrigatório ausente: ${field}`);
      }
    }
    
    return validation;
  }

  /**
   * Cria agendamento via API Trinks
   */
  async createAppointmentAPI(appointmentData) {
    const response = await this.callExternalService('trinks_api', async () => {
      const url = `${this.trinksConfig.baseUrl}/v1/agendamentos`;
      
      const requestOptions = {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.trinksConfig.apiKey}`,
          'Content-Type': 'application/json',
          'X-Establishment-ID': this.trinksConfig.establishmentId
        },
        body: JSON.stringify({
          cliente_id: appointmentData.clienteId,
          profissional_id: appointmentData.profissionalId,
          servico_id: appointmentData.servicoId,
          data_hora_inicio: appointmentData.dataHora,
          duracao_em_minutos: appointmentData.duracaoEmMinutos,
          valor: appointmentData.valor,
          status: appointmentData.status,
          observacoes: appointmentData.observacoes,
          estabelecimento_id: appointmentData.estabelecimentoId
        })
      };
      
      const apiResponse = await fetch(url, requestOptions);
      
      if (!apiResponse.ok) {
        const errorData = await apiResponse.json().catch(() => ({}));
        throw new Error(`API Error: ${apiResponse.status} - ${errorData.message || apiResponse.statusText}`);
      }
      
      return await apiResponse.json();
    });
    
    // Normalizar resposta da API
    const appointment = response?.data || response?.agendamento || response;
    
    return {
      id: appointment.id,
      clienteId: appointmentData.clienteId,
      profissionalId: appointmentData.profissionalId,
      servicoId: appointmentData.servicoId,
      dataHoraInicio: appointment.dataHoraInicio || appointmentData.dataHora,
      duracaoEmMinutos: appointment.duracaoEmMinutos || appointmentData.duracaoEmMinutos,
      valor: appointment.valor || appointmentData.valor,
      status: appointment.status || appointmentData.status,
      observacoes: appointment.observacoes || appointmentData.observacoes,
      criadoEm: appointment.criadoEm || new Date().toISOString(),
      
      // Dados adicionais para confirmação
      customerName: appointmentData.customerName,
      professionalName: appointmentData.professionalName,
      serviceName: appointmentData.serviceName,
      isNewCustomer: appointmentData.isNewCustomer
    };
  }

  /**
   * Cria agendamento mock para desenvolvimento
   */
  createMockAppointment(appointmentData) {
    const mockId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Simular pequeno delay para realismo
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({
          id: mockId,
          clienteId: appointmentData.clienteId,
          profissionalId: appointmentData.profissionalId,
          servicoId: appointmentData.servicoId,
          dataHoraInicio: appointmentData.dataHora,
          duracaoEmMinutos: appointmentData.duracaoEmMinutos,
          valor: appointmentData.valor,
          status: 'agendado',
          observacoes: appointmentData.observacoes + ' | MOCK',
          criadoEm: new Date().toISOString(),
          
          // Dados adicionais
          customerName: appointmentData.customerName,
          professionalName: appointmentData.professionalName,
          serviceName: appointmentData.serviceName,
          isNewCustomer: appointmentData.isNewCustomer
        });
      }, 1000);
    });
  }

  /**
   * Gera mensagem de confirmação personalizada
   */
  generateConfirmationMessage(appointment, customer, professional, service) {
    const appointmentDateTime = new Date(appointment.dataHoraInicio);
    
    // Determinar template baseado no tipo de cliente
    const templateKey = appointment.isNewCustomer ? 'new_customer' : 'existing_customer';
    let template = this.confirmationTemplates.success[templateKey];
    
    // Preparar dados para substituição
    const replacements = {
      customerName: appointment.customerName,
      appointmentId: appointment.id,
      dateFormatted: this.formatAppointmentDate(appointmentDateTime),
      time: this.formatAppointmentTime(appointmentDateTime),
      professionalName: appointment.professionalName,
      serviceName: appointment.serviceName,
      price: this.formatPrice(appointment.valor),
      duration: appointment.duracaoEmMinutos
    };
    
    // Substituir placeholders
    for (const [key, value] of Object.entries(replacements)) {
      template = template.replace(new RegExp(`{${key}}`, 'g'), value);
    }
    
    return template;
  }

  /**
   * Formatar data do agendamento
   */
  formatAppointmentDate(dateTime) {
    return dateTime.toLocaleDateString('pt-BR', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  }

  /**
   * Formatar hora do agendamento
   */
  formatAppointmentTime(dateTime) {
    return dateTime.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Formatar preço
   */
  formatPrice(price) {
    return parseFloat(price).toFixed(2).replace('.', ',');
  }

  /**
   * Analisa erro e retorna resposta apropriada
   */
  analyzeAndHandleError(error, state) {
    let errorType = 'general';
    let errorMessage = error.message;
    
    // Analisar tipo de erro baseado na mensagem
    if (errorMessage.includes('conflict') || errorMessage.includes('ocupado')) {
      errorType = 'time_conflict';
    } else if (errorMessage.includes('professional') || errorMessage.includes('profissional')) {
      errorType = 'professional_unavailable';
    }
    
    // Obter template de erro apropriado
    const template = this.confirmationTemplates.error[errorType] || this.confirmationTemplates.error.general;
    
    // Substituir placeholders se necessário
    let responseMessage = template;
    const professional = state.context?.selectedProfessional;
    if (professional && errorType === 'professional_unavailable') {
      responseMessage = template.replace('{professionalName}', professional.name);
    }
    
    return {
      success: false,
      result: {
        appointmentCreated: false,
        error: errorType,
        message: responseMessage,
        originalError: errorMessage
      },
      nextStage: errorType === 'time_conflict' ? 'availability_check' : 'intent_confirmation',
      updates: {
        context: {
          ...state.context,
          lastError: {
            type: errorType,
            message: errorMessage,
            timestamp: new Date()
          }
        }
      },
      error: {
        message: responseMessage,
        code: 'APPOINTMENT_CREATION_ERROR',
        recoverable: true
      }
    };
  }

  /**
   * Constrói resposta de sucesso
   */
  buildSuccessResponse(appointment, confirmationMessage, state) {
    return {
      success: true,
      result: {
        appointmentCreated: true,
        appointment: appointment,
        confirmationMessage: confirmationMessage,
        appointmentId: appointment.id,
        appointmentDateTime: appointment.dataHoraInicio
      },
      nextStage: 'completed',
      updates: {
        context: {
          ...state.context,
          completedAppointment: appointment,
          stage: 'completed',
          completedAt: new Date()
        }
      },
      metadata: {
        debugInfo: {
          appointmentId: appointment.id,
          customerId: appointment.clienteId,
          professionalId: appointment.profissionalId,
          serviceId: appointment.servicoId,
          appointmentValue: appointment.valor,
          isNewCustomer: appointment.isNewCustomer,
          createdAt: appointment.criadoEm,
          isMock: appointment.id.startsWith('mock_')
        }
      }
    };
  }

  /**
   * Constrói resposta de erro de validação
   */
  buildValidationErrorResponse(validation, state) {
    return {
      success: false,
      result: {
        appointmentCreated: false,
        missingData: validation.missingData,
        message: `Dados insuficientes para criar agendamento: ${validation.missingData.join(', ')}`
      },
      nextStage: 'intent_confirmation',
      updates: {},
      metadata: {
        debugInfo: {
          validationFailed: true,
          missingData: validation.missingData
        }
      }
    };
  }

  /**
   * Constrói resposta de erro de validação final
   */
  buildFinalValidationErrorResponse(validation, state) {
    return {
      success: false,
      result: {
        appointmentCreated: false,
        validationIssues: validation.issues,
        message: `Não foi possível criar o agendamento: ${validation.issues.join(', ')}`
      },
      nextStage: 'intent_confirmation',
      updates: {},
      metadata: {
        debugInfo: {
          finalValidationFailed: true,
          issues: validation.issues
        }
      }
    };
  }

  /**
   * Obtém estatísticas de confirmação de agendamentos
   */
  getConfirmationStats() {
    return {
      ...this.getMetrics(),
      appointmentsCreated: this.appointmentCount || 0,
      newCustomerRate: this.calculateNewCustomerRate(),
      avgAppointmentValue: this.calculateAvgAppointmentValue(),
      mostPopularService: this.getMostPopularService(),
      mostPopularProfessional: this.getMostPopularProfessional()
    };
  }

  calculateNewCustomerRate() {
    // Implementar baseado em dados históricos
    return 25.5; // Mock value - 25.5% de novos clientes
  }

  calculateAvgAppointmentValue() {
    // Implementar baseado em dados históricos
    return 42.50; // Mock value - R$ 42.50 média
  }

  getMostPopularService() {
    // Implementar baseado em dados históricos
    return 'Corte Masculino'; // Mock value
  }

  getMostPopularProfessional() {
    // Implementar baseado em dados históricos
    return 'Ágata Cristina Ribeiro'; // Mock value
  }
}

module.exports = ScheduleConfirmationNode;