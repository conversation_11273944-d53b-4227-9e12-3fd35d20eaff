/**
 * ProfessionalSelectionNode - Nó de Seleção de Profissionais
 * 
 * Responsabilidades:
 * - Buscar profissionais disponíveis via API Trinks
 * - Mapear profissionais mencionados pelo cliente
 * - Validar se profissional pode fazer o serviço selecionado
 * - Sugerir profissionais baseado em disponibilidade e preferências
 * - Cache de profissionais para performance
 */

const BaseNode = require('../../core/BaseNode');

class ProfessionalSelectionNode extends BaseNode {
  constructor() {
    super('professional_selection', {
      timeout: 10000, // 10s para selecionar profissional
      retryAttempts: 2
    });
    
    // Configuração da API Trinks
    this.trinksConfig = {
      baseUrl: process.env.TRINKS_API_BASE_URL || 'https://api.trinks.com',
      apiKey: process.env.TRINKS_API_KEY,
      establishmentId: process.env.TRINKS_ESTABLISHMENT_ID
    };
    
    // Cache de profissionais
    this.professionalCache = new Map();
    this.cacheExpiration = 15 * 60 * 1000; // 15 minutos
    
    // Estratégias de seleção quando não especificado
    this.selectionStrategies = {
      FIRST_AVAILABLE: 'first_available',    // Primeiro disponível
      LEAST_BUSY: 'least_busy',             // Menos ocupado
      BEST_RATED: 'best_rated',             // Melhor avaliação
      RANDOM: 'random'                      // Aleatório
    };
  }

  /**
   * Executa seleção de profissional
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Obter serviço selecionado
      const selectedService = state.context?.selectedService || state.schedulingData?.service;
      if (!selectedService) {
        throw new Error('Serviço deve ser selecionado antes do profissional');
      }
      
      // Buscar profissionais que fazem o serviço
      const availableProfessionals = await this.getProfessionalsForService(selectedService.id);
      
      // Analisar profissional mencionado pelo cliente
      const professionalAnalysis = this.analyzeMentionedProfessional(state);
      
      // Mapear profissional solicitado
      const professionalMapping = this.mapRequestedProfessional(professionalAnalysis, availableProfessionals);
      
      // Validar seleção
      const validation = this.validateProfessionalSelection(professionalMapping, availableProfessionals);
      
      if (validation.isValid) {
        return this.buildSuccessResponse(professionalMapping.selectedProfessional, selectedService, state);
      } else {
        return this.buildValidationErrorResponse(validation, availableProfessionals, state);
      }
      
    } catch (error) {
      return {
        success: false,
        result: {
          selectedProfessional: null,
          availableProfessionals: [],
          error: error.message
        },
        nextStage: 'intent_confirmation',
        updates: {},
        error: {
          message: error.message,
          code: 'PROFESSIONAL_SELECTION_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Busca profissionais que fazem o serviço específico
   */
  async getProfessionalsForService(serviceId) {
    // Verificar cache primeiro
    const cacheKey = `professionals_service_${serviceId}`;
    const cachedProfessionals = this.getCachedProfessionals(cacheKey);
    
    if (cachedProfessionals) {
      return cachedProfessionals;
    }

    let professionals;
    if (this.trinksConfig.apiKey) {
      professionals = await this.fetchProfessionalsFromAPI(serviceId);
    } else {
      professionals = this.generateMockProfessionals(serviceId);
    }
    
    // Processar e normalizar profissionais
    const processedProfessionals = this.processProfessionals(professionals, serviceId);
    
    // Salvar no cache
    this.setCachedProfessionals(cacheKey, processedProfessionals);
    
    return processedProfessionals;
  }

  /**
   * Busca profissionais via API Trinks
   */
  async fetchProfessionalsFromAPI(serviceId) {
    const response = await this.callExternalService('trinks_api', async () => {
      const url = `${this.trinksConfig.baseUrl}/v1/profissionais`;
      
      const requestOptions = {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.trinksConfig.apiKey}`,
          'Content-Type': 'application/json',
          'X-Establishment-ID': this.trinksConfig.establishmentId
        }
      };
      
      const queryParams = new URLSearchParams({
        estabelecimento_id: this.trinksConfig.establishmentId,
        servico_id: serviceId,
        ativo: 'true'
      });
      
      const fullUrl = `${url}?${queryParams.toString()}`;
      const apiResponse = await fetch(fullUrl, requestOptions);
      
      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status} - ${apiResponse.statusText}`);
      }
      
      return await apiResponse.json();
    });
    
    return response?.data || response?.profissionais || response || [];
  }

  /**
   * Gera profissionais mock para desenvolvimento
   */
  generateMockProfessionals(serviceId) {
    const mockProfessionals = [
      {
        id: 'prof_001',
        nome: 'Ágata Cristina Ribeiro',
        especialidades: ['Corte', 'Barba', 'Unha'],
        ativo: true,
        avaliacao: 4.8,
        experiencia: 5
      },
      {
        id: 'prof_002',
        nome: 'João Santos Silva',
        especialidades: ['Corte', 'Barba'],
        ativo: true,
        avaliacao: 4.5,
        experiencia: 3
      },
      {
        id: 'prof_003',
        nome: 'Maria Oliveira Costa',
        especialidades: ['Unha', 'Sobrancelha'],
        ativo: true,
        avaliacao: 4.9,
        experiencia: 7
      },
      {
        id: 'prof_004',
        nome: 'Carlos Pereira',
        especialidades: ['Corte', 'Tratamento'],
        ativo: true,
        avaliacao: 4.3,
        experiencia: 2
      }
    ];
    
    // Filtrar por especialidade baseado no serviceId (simulação)
    const serviceSpecialties = {
      'serv_001': 'Corte',
      'serv_002': 'Barba', 
      'serv_003': 'Unha',
      'serv_004': 'Sobrancelha',
      'serv_005': 'Tratamento'
    };
    
    const requiredSpecialty = serviceSpecialties[serviceId] || 'Corte';
    
    return mockProfessionals.filter(prof => 
      prof.especialidades.includes(requiredSpecialty)
    );
  }

  /**
   * Processa e normaliza dados de profissionais
   */
  processProfessionals(rawProfessionals, serviceId) {
    return rawProfessionals.map(prof => ({
      id: prof.id,
      name: prof.nome || prof.name,
      specialties: prof.especialidades || prof.specialties || [],
      rating: parseFloat(prof.avaliacao || prof.rating || 4.0),
      experience: parseInt(prof.experiencia || prof.experience || 1),
      isActive: prof.ativo !== false && prof.active !== false,
      canProvideService: this.canProvideService(prof, serviceId),
      keywords: this.generateProfessionalKeywords(prof.nome || prof.name)
    })).filter(prof => prof.isActive && prof.canProvideService);
  }

  /**
   * Verifica se profissional pode fazer o serviço
   */
  canProvideService(professional, serviceId) {
    // Em implementação real, verificaria na API
    // Por ora, assumir que todos podem fazer todos os serviços
    return true;
  }

  /**
   * Gera palavras-chave para um profissional
   */
  generateProfessionalKeywords(professionalName) {
    const name = professionalName.toLowerCase();
    const keywords = [name];
    
    // Adicionar primeiro nome
    const firstName = name.split(' ')[0];
    if (firstName.length > 2) {
      keywords.push(firstName);
    }
    
    // Adicionar variações comuns
    keywords.push(
      name.replace(/\s+/g, ''),  // Sem espaços
      firstName + ' ' + name.split(' ').slice(-1)[0] // Primeiro + último nome
    );
    
    return [...new Set(keywords)];
  }

  /**
   * Analisa profissional mencionado pelo cliente
   */
  analyzeMentionedProfessional(state) {
    const analysis = {
      mentionedProfessional: null,
      confidence: 0,
      source: 'none',
      extractedKeywords: []
    };
    
    // 1. Verificar contexto extraído pela IA
    if (state.intent?.extractedContext?.mentioned_professional) {
      analysis.mentionedProfessional = state.intent.extractedContext.mentioned_professional;
      analysis.source = 'ai_extraction';
      analysis.confidence = 0.8;
    }
    
    // 2. Verificar dados de scheduling salvos
    if (state.schedulingData?.professional?.name) {
      analysis.mentionedProfessional = state.schedulingData.professional.name;
      analysis.source = 'scheduling_data';
      analysis.confidence = 0.9;
    }
    
    // 3. Analisar mensagens recentes
    if (!analysis.mentionedProfessional) {
      const recentAnalysis = this.analyzeRecentMessagesForProfessional(state.messages);
      if (recentAnalysis.professional) {
        analysis.mentionedProfessional = recentAnalysis.professional;
        analysis.source = 'message_analysis';
        analysis.confidence = recentAnalysis.confidence;
        analysis.extractedKeywords = recentAnalysis.keywords;
      }
    }
    
    return analysis;
  }

  /**
   * Analisa mensagens recentes em busca de profissionais
   */
  analyzeRecentMessagesForProfessional(messages) {
    if (!messages || messages.length === 0) {
      return { professional: null, confidence: 0, keywords: [] };
    }
    
    // Pegar últimas mensagens do usuário
    const userMessages = messages
      .filter(msg => msg.role === 'user')
      .slice(-3)
      .map(msg => msg.content);
    
    const combinedText = userMessages.join(' ');
    
    // Buscar padrões de nomes (palavras capitalizadas)
    const namePattern = /\b([A-Z][a-záãàéêíóôúç]+(?:\s+[A-Z][a-záãàéêíóôúç]+)*)\b/g;
    const matches = combinedText.match(namePattern);
    
    if (matches) {
      // Filtrar nomes comuns que não são profissionais
      const commonWords = ['Com', 'Para', 'Por', 'Que', 'Como', 'Quando', 'Onde', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'];
      const potentialNames = matches.filter(name => 
        !commonWords.includes(name) && name.length > 2
      );
      
      if (potentialNames.length > 0) {
        return {
          professional: potentialNames[0], // Pegar primeiro nome encontrado
          confidence: 0.7,
          keywords: potentialNames
        };
      }
    }
    
    return { professional: null, confidence: 0, keywords: [] };
  }

  /**
   * Mapeia profissional solicitado para profissionais disponíveis
   */
  mapRequestedProfessional(professionalAnalysis, availableProfessionals) {
    const mapping = {
      requestedProfessional: professionalAnalysis.mentionedProfessional,
      selectedProfessional: null,
      confidence: professionalAnalysis.confidence,
      matchType: 'none',
      alternatives: []
    };
    
    if (!professionalAnalysis.mentionedProfessional && availableProfessionals.length > 0) {
      // Nenhum profissional especificado - usar estratégia de seleção automática
      mapping.selectedProfessional = this.selectProfessionalByStrategy(
        availableProfessionals, 
        this.selectionStrategies.FIRST_AVAILABLE
      );
      mapping.matchType = 'auto_selected';
      mapping.confidence = 0.8;
      return mapping;
    }
    
    if (!professionalAnalysis.mentionedProfessional) {
      return mapping;
    }
    
    const requested = professionalAnalysis.mentionedProfessional.toLowerCase();
    
    // 1. Buscar correspondência exata no nome
    let exactMatch = availableProfessionals.find(prof => 
      prof.name.toLowerCase() === requested
    );
    
    if (exactMatch) {
      mapping.selectedProfessional = exactMatch;
      mapping.matchType = 'exact';
      mapping.confidence = 0.95;
      return mapping;
    }
    
    // 2. Buscar por primeiro nome
    let firstNameMatch = availableProfessionals.find(prof => 
      prof.name.toLowerCase().split(' ')[0] === requested.split(' ')[0]
    );
    
    if (firstNameMatch) {
      mapping.selectedProfessional = firstNameMatch;
      mapping.matchType = 'first_name';
      mapping.confidence = 0.85;
      return mapping;
    }
    
    // 3. Buscar correspondência parcial
    let partialMatches = availableProfessionals.filter(prof =>
      prof.keywords.some(keyword => 
        keyword.includes(requested) || requested.includes(keyword)
      )
    );
    
    if (partialMatches.length > 0) {
      mapping.selectedProfessional = partialMatches[0];
      mapping.matchType = 'partial';
      mapping.confidence = 0.7;
      mapping.alternatives = partialMatches.slice(1, 3);
      return mapping;
    }
    
    // 4. Não encontrou - sugerir alternativas
    mapping.alternatives = availableProfessionals.slice(0, 3);
    
    return mapping;
  }

  /**
   * Seleciona profissional por estratégia quando não especificado
   */
  selectProfessionalByStrategy(professionals, strategy) {
    switch (strategy) {
      case this.selectionStrategies.FIRST_AVAILABLE:
        return professionals[0];
        
      case this.selectionStrategies.BEST_RATED:
        return professionals.sort((a, b) => b.rating - a.rating)[0];
        
      case this.selectionStrategies.LEAST_BUSY:
        // Por ora, usar primeiro da lista (em implementação real, verificaria agenda)
        return professionals[0];
        
      case this.selectionStrategies.RANDOM:
        return professionals[Math.floor(Math.random() * professionals.length)];
        
      default:
        return professionals[0];
    }
  }

  /**
   * Valida seleção de profissional
   */
  validateProfessionalSelection(professionalMapping, availableProfessionals) {
    const validation = {
      isValid: false,
      issues: [],
      suggestions: []
    };
    
    if (professionalMapping.selectedProfessional) {
      // Verificar se profissional está ativo
      if (!professionalMapping.selectedProfessional.isActive) {
        validation.issues.push('Profissional não está disponível no momento');
        validation.suggestions.push('Escolher outro profissional');
      } else {
        validation.isValid = true;
      }
    } else if (professionalMapping.requestedProfessional) {
      // Profissional mencionado mas não encontrado
      validation.issues.push(`Profissional "${professionalMapping.requestedProfessional}" não encontrado`);
      
      if (professionalMapping.alternatives.length > 0) {
        validation.suggestions.push('Profissionais disponíveis sugeridos');
      } else {
        validation.suggestions.push('Escolher um profissional da nossa equipe');
      }
    } else if (availableProfessionals.length === 0) {
      // Nenhum profissional disponível para o serviço
      validation.issues.push('Nenhum profissional disponível para este serviço no momento');
      validation.suggestions.push('Tentar outro horário ou serviço');
    } else {
      // Caso não deveria acontecer se a lógica estiver correta
      validation.issues.push('Erro interno na seleção de profissional');
      validation.suggestions.push('Tentar novamente');
    }
    
    return validation;
  }

  /**
   * Constrói resposta de sucesso
   */
  buildSuccessResponse(selectedProfessional, selectedService, state) {
    return {
      success: true,
      result: {
        selectedProfessional,
        professionalFound: true,
        matchType: 'confirmed',
        confidence: 0.9
      },
      nextStage: 'availability_check',
      updates: {
        context: {
          ...state.context,
          selectedProfessional: selectedProfessional,
          schedulingData: {
            ...(state.context?.schedulingData || {}),
            professional: {
              id: selectedProfessional.id,
              name: selectedProfessional.name,
              rating: selectedProfessional.rating
            }
          }
        }
      },
      metadata: {
        debugInfo: {
          professionalId: selectedProfessional.id,
          professionalName: selectedProfessional.name,
          rating: selectedProfessional.rating,
          experience: selectedProfessional.experience,
          serviceCompatible: selectedProfessional.canProvideService
        }
      }
    };
  }

  /**
   * Constrói resposta de erro de validação
   */
  buildValidationErrorResponse(validation, availableProfessionals, state) {
    return {
      success: false,
      result: {
        selectedProfessional: null,
        professionalFound: false,
        issues: validation.issues,
        suggestions: validation.suggestions,
        availableProfessionals: availableProfessionals.slice(0, 3), // Top 3 profissionais
        needsUserInput: true
      },
      nextStage: 'intent_confirmation', // Pedir mais informações
      updates: {
        context: {
          ...state.context,
          professionalSelection: {
            issues: validation.issues,
            availableProfessionals: availableProfessionals
          }
        }
      },
      metadata: {
        debugInfo: {
          validationFailed: true,
          issueCount: validation.issues.length,
          availableProfessionalCount: availableProfessionals.length
        }
      }
    };
  }

  /**
   * Cache de profissionais
   */
  getCachedProfessionals(cacheKey) {
    const cached = this.professionalCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp < this.cacheExpiration)) {
      return cached.data;
    }
    return null;
  }

  setCachedProfessionals(cacheKey, data) {
    this.professionalCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    // Limpar cache antigo
    if (this.professionalCache.size > 50) {
      const oldestKey = this.professionalCache.keys().next().value;
      this.professionalCache.delete(oldestKey);
    }
  }

  /**
   * Obtém estatísticas de seleção de profissionais
   */
  getProfessionalStats() {
    return {
      ...this.getMetrics(),
      cacheSize: this.professionalCache.size,
      exactMatches: this.exactMatchCount || 0,
      firstNameMatches: this.firstNameMatchCount || 0,
      partialMatches: this.partialMatchCount || 0,
      autoSelections: this.autoSelectionCount || 0
    };
  }
}

module.exports = ProfessionalSelectionNode;