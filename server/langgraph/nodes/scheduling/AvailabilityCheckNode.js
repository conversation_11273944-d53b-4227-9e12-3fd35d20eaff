/**
 * AvailabilityCheckNode - Nó de Verificação de Disponibilidade
 * 
 * SISTEMA CRÍTICO: Este nó implementa a regra fundamental do sistema:
 * "NUNCA dizer 'vou verificar' - sempre buscar dados ANTES de responder"
 * 
 * Responsabilidades:
 * - Buscar disponibilidade real via API Trinks
 * - Mapear referências temporais (hoje, amanhã, horários específicos)
 * - Sugerir horários alternativos quando solicitado não está disponível
 * - Cache inteligente para performance
 * - Validação de regras de negócio (funcionamento, antecedência)
 */

const BaseNode = require('../../core/BaseNode');

class AvailabilityCheckNode extends BaseNode {
  constructor() {
    super('availability_check', {
      timeout: 15000, // 15s para buscar disponibilidade
      retryAttempts: 3
    });
    
    // Configuração da API Trinks
    this.trinksConfig = {
      baseUrl: process.env.TRINKS_API_BASE_URL || 'https://api.trinks.com',
      apiKey: process.env.TRINKS_API_KEY,
      establishmentId: process.env.TRINKS_ESTABLISHMENT_ID
    };
    
    // Cache de disponibilidade
    this.availabilityCache = new Map();
    this.cacheExpiration = 5 * 60 * 1000; // 5 minutos (disponibilidade muda rápido)
    
    // Regras de funcionamento
    this.businessRules = {
      workingDays: [1, 2, 3, 4, 5, 6], // Segunda a Sábado (0 = Domingo)
      workingHours: {
        start: '08:00',
        end: '18:00'
      },
      slotDuration: 30, // Slots de 30 minutos
      minimumAdvance: 60 * 60 * 1000 // 1 hora em milliseconds
    };
    
    // Mapeamento de referências temporais
    this.temporalMapping = {
      'hoje': 0,
      'amanha': 1,
      'amanhã': 1,
      'depois de amanha': 2,
      'depois de amanhã': 2,
      'segunda': 'next_monday',
      'terca': 'next_tuesday',
      'terça': 'next_tuesday',
      'quarta': 'next_wednesday',
      'quinta': 'next_thursday',
      'sexta': 'next_friday',
      'sabado': 'next_saturday',
      'sábado': 'next_saturday',
      'domingo': 'next_sunday'
    };
  }

  /**
   * Executa verificação de disponibilidade
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Validar dados necessários
      const validationResult = this.validateRequiredData(state);
      if (!validationResult.isValid) {
        return this.buildValidationErrorResponse(validationResult, state);
      }
      
      const { professional, service } = validationResult.data;
      
      // Analisar quando o cliente quer agendar
      const timeAnalysis = this.analyzeRequestedTime(state);
      
      // Buscar disponibilidade real
      const availabilityData = await this.fetchAvailability(
        professional.id, 
        service.id,
        service.duration,
        timeAnalysis
      );
      
      // Processar e validar horários
      const processedAvailability = this.processAvailabilityData(
        availabilityData, 
        timeAnalysis,
        service.duration
      );
      
      // Determinar melhor sugestão
      const suggestion = this.determineBestSuggestion(processedAvailability, timeAnalysis);
      
      return this.buildSuccessResponse(processedAvailability, suggestion, timeAnalysis, state);
      
    } catch (error) {
      return {
        success: false,
        result: {
          hasAvailability: false,
          suggestedSlots: [],
          error: error.message
        },
        nextStage: 'intent_confirmation',
        updates: {},
        error: {
          message: error.message,
          code: 'AVAILABILITY_CHECK_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Valida se temos os dados necessários
   */
  validateRequiredData(state) {
    const professional = state.context?.selectedProfessional || state.schedulingData?.professional;
    const service = state.context?.selectedService || state.schedulingData?.service;
    
    if (!professional) {
      return {
        isValid: false,
        error: 'Profissional deve ser selecionado antes de verificar disponibilidade',
        missingData: ['professional']
      };
    }
    
    if (!service) {
      return {
        isValid: false,
        error: 'Serviço deve ser selecionado antes de verificar disponibilidade',
        missingData: ['service']
      };
    }
    
    return {
      isValid: true,
      data: { professional, service }
    };
  }

  /**
   * Analisa quando o cliente quer agendar
   */
  analyzeRequestedTime(state) {
    const analysis = {
      specificDate: null,
      specificTime: null,
      relativeDate: null,
      timeOfDay: null,
      isFlexible: false,
      source: 'none'
    };
    
    // 1. Verificar contexto extraído pela IA
    if (state.intent?.extractedContext?.mentioned_date) {
      analysis.relativeDate = state.intent.extractedContext.mentioned_date;
      analysis.source = 'ai_extraction';
    }
    
    if (state.intent?.extractedContext?.mentioned_time) {
      analysis.specificTime = state.intent.extractedContext.mentioned_time;
      analysis.source = 'ai_extraction';
    }
    
    // 2. Verificar dados de scheduling
    if (state.schedulingData?.date) {
      analysis.specificDate = state.schedulingData.date;
      analysis.source = 'scheduling_data';
    }
    
    if (state.schedulingData?.time) {
      analysis.specificTime = state.schedulingData.time;
      analysis.source = 'scheduling_data';
    }
    
    // 3. Analisar mensagens recentes se não temos informação
    if (analysis.source === 'none') {
      const messageAnalysis = this.analyzeMessagesForTime(state.messages);
      analysis.relativeDate = messageAnalysis.relativeDate;
      analysis.specificTime = messageAnalysis.specificTime;
      analysis.timeOfDay = messageAnalysis.timeOfDay;
      analysis.isFlexible = messageAnalysis.isFlexible;
      analysis.source = 'message_analysis';
    }
    
    // 4. Converter para datas específicas
    analysis.targetDates = this.convertToTargetDates(analysis);
    
    return analysis;
  }

  /**
   * Analisa mensagens em busca de referências temporais
   */
  analyzeMessagesForTime(messages) {
    if (!messages || messages.length === 0) {
      return { relativeDate: null, specificTime: null, timeOfDay: null, isFlexible: true };
    }
    
    const userMessages = messages
      .filter(msg => msg.role === 'user')
      .slice(-3)
      .map(msg => msg.content.toLowerCase());
    
    const combinedText = userMessages.join(' ');
    
    const analysis = {
      relativeDate: null,
      specificTime: null,
      timeOfDay: null,
      isFlexible: false
    };
    
    // Buscar referências de data
    for (const [keyword, mapping] of Object.entries(this.temporalMapping)) {
      if (combinedText.includes(keyword)) {
        analysis.relativeDate = keyword;
        break;
      }
    }
    
    // Buscar horários específicos
    const timePatterns = [
      /(\d{1,2})[:h](\d{2})?/,           // 14:30 ou 14h30
      /(\d{1,2})\s*horas?/,             // 14 horas
      /às\s+(\d{1,2})[:h]?(\d{2})?/     // às 14:30
    ];
    
    for (const pattern of timePatterns) {
      const match = combinedText.match(pattern);
      if (match) {
        const hour = match[1];
        const minute = match[2] || '00';
        analysis.specificTime = `${hour}:${minute}`;
        break;
      }
    }
    
    // Buscar períodos do dia
    if (combinedText.includes('manhã')) {
      analysis.timeOfDay = 'morning';
    } else if (combinedText.includes('tarde')) {
      analysis.timeOfDay = 'afternoon';
    } else if (combinedText.includes('noite')) {
      analysis.timeOfDay = 'evening';
    }
    
    // Detectar flexibilidade
    const flexibilityKeywords = ['qualquer', 'tanto faz', 'pode ser', 'flexivel', 'flexível', 'disponível'];
    analysis.isFlexible = flexibilityKeywords.some(keyword => combinedText.includes(keyword));
    
    return analysis;
  }

  /**
   * Converte referências temporais para datas específicas
   */
  convertToTargetDates(analysis) {
    const dates = [];
    const today = new Date();
    
    if (analysis.specificDate) {
      // Data específica já fornecida
      dates.push(new Date(analysis.specificDate));
    } else if (analysis.relativeDate) {
      // Converter referência relativa
      const mapping = this.temporalMapping[analysis.relativeDate];
      
      if (typeof mapping === 'number') {
        // Dias relativos (hoje = 0, amanhã = 1, etc)
        const targetDate = new Date(today);
        targetDate.setDate(today.getDate() + mapping);
        dates.push(targetDate);
      } else if (typeof mapping === 'string' && mapping.startsWith('next_')) {
        // Próximo dia da semana
        const dayName = mapping.replace('next_', '');
        const targetDate = this.getNextWeekday(dayName);
        dates.push(targetDate);
      }
    } else {
      // Sem data específica - sugerir próximos 3 dias úteis
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        
        if (this.isWorkingDay(date)) {
          dates.push(date);
          if (dates.length >= 3) break;
        }
      }
    }
    
    return dates;
  }

  /**
   * Obtém próximo dia da semana específico
   */
  getNextWeekday(dayName) {
    const dayMap = {
      'sunday': 0, 'monday': 1, 'tuesday': 2, 'wednesday': 3,
      'thursday': 4, 'friday': 5, 'saturday': 6
    };
    
    const today = new Date();
    const targetDay = dayMap[dayName];
    const daysUntilTarget = (targetDay - today.getDay() + 7) % 7;
    
    const targetDate = new Date(today);
    targetDate.setDate(today.getDate() + (daysUntilTarget || 7)); // Se é hoje, pegar próxima semana
    
    return targetDate;
  }

  /**
   * Verifica se é dia útil
   */
  isWorkingDay(date) {
    return this.businessRules.workingDays.includes(date.getDay());
  }

  /**
   * Busca disponibilidade via API ou cache
   */
  async fetchAvailability(professionalId, serviceId, serviceDuration, timeAnalysis) {
    const cacheKey = this.generateAvailabilityCacheKey(professionalId, serviceId, timeAnalysis.targetDates);
    const cachedAvailability = this.getCachedAvailability(cacheKey);
    
    if (cachedAvailability) {
      return cachedAvailability;
    }

    let availability;
    if (this.trinksConfig.apiKey) {
      availability = await this.fetchAvailabilityFromAPI(professionalId, serviceId, timeAnalysis.targetDates, serviceDuration);
    } else {
      availability = this.generateMockAvailability(professionalId, serviceId, timeAnalysis.targetDates, serviceDuration);
    }
    
    // Salvar no cache
    this.setCachedAvailability(cacheKey, availability);
    
    return availability;
  }

  /**
   * Busca disponibilidade via API Trinks
   */
  async fetchAvailabilityFromAPI(professionalId, serviceId, targetDates, serviceDuration) {
    const availabilityData = [];
    
    // Buscar disponibilidade para cada data
    for (const date of targetDates) {
      const dateStr = date.toISOString().split('T')[0];
      
      try {
        const dayAvailability = await this.callExternalService('trinks_api', async () => {
          const url = `${this.trinksConfig.baseUrl}/v1/agendamentos/disponibilidade`;
          
          const requestOptions = {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${this.trinksConfig.apiKey}`,
              'Content-Type': 'application/json',
              'X-Establishment-ID': this.trinksConfig.establishmentId
            }
          };
          
          const queryParams = new URLSearchParams({
            profissional_id: professionalId,
            servico_id: serviceId,
            data: dateStr,
            duracao: serviceDuration,
            estabelecimento_id: this.trinksConfig.establishmentId
          });
          
          const fullUrl = `${url}?${queryParams.toString()}`;
          const apiResponse = await fetch(fullUrl, requestOptions);
          
          if (!apiResponse.ok) {
            console.warn(`Disponibilidade não encontrada para ${dateStr}: ${apiResponse.status}`);
            return null;
          }
          
          return await apiResponse.json();
        });
        
        if (dayAvailability) {
          availabilityData.push({
            date: dateStr,
            slots: dayAvailability.data || dayAvailability.horarios || []
          });
        }
        
      } catch (error) {
        console.warn(`Erro ao buscar disponibilidade para ${dateStr}:`, error.message);
        // Continuar com outras datas
      }
    }
    
    return availabilityData;
  }

  /**
   * Gera disponibilidade mock para desenvolvimento
   */
  generateMockAvailability(professionalId, serviceId, targetDates, serviceDuration) {
    const availabilityData = [];
    
    for (const date of targetDates) {
      const dateStr = date.toISOString().split('T')[0];
      const slots = [];
      
      // Gerar slots baseado no hash do profissional e data
      const seed = this.hashString(professionalId + dateStr);
      const slotsCount = 4 + (seed % 6); // 4-9 slots por dia
      
      // Gerar horários entre 8h e 17h
      const startHour = 8;
      const endHour = 17;
      const totalMinutes = (endHour - startHour) * 60;
      const interval = Math.floor(totalMinutes / slotsCount);
      
      for (let i = 0; i < slotsCount; i++) {
        const minutes = startHour * 60 + (i * interval) + ((seed + i) % 30);
        const hour = Math.floor(minutes / 60);
        const minute = minutes % 60;
        
        if (hour < endHour) {
          slots.push({
            horario: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
            disponivel: (seed + i) % 3 !== 0, // ~66% dos slots disponíveis
            duracao: serviceDuration
          });
        }
      }
      
      availabilityData.push({
        date: dateStr,
        slots: slots.filter(slot => slot.disponivel)
      });
    }
    
    return availabilityData;
  }

  /**
   * Hash simples para gerar dados consistentes
   */
  hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    return Math.abs(hash);
  }

  /**
   * Processa dados de disponibilidade
   */
  processAvailabilityData(availabilityData, timeAnalysis, serviceDuration) {
    const now = new Date();
    const processed = {
      availableDates: [],
      totalSlots: 0,
      hasAvailability: false,
      requestedTimeAvailable: false,
      alternativeSuggestions: []
    };
    
    for (const dayData of availabilityData) {
      const date = new Date(dayData.date);
      const isToday = this.isSameDay(date, now);
      
      const processedSlots = dayData.slots
        .filter(slot => this.isSlotValid(slot, date, serviceDuration, isToday))
        .map(slot => this.normalizeSlot(slot, date))
        .sort((a, b) => new Date(a.dateTime) - new Date(b.dateTime));
      
      if (processedSlots.length > 0) {
        processed.availableDates.push({
          date: dayData.date,
          dateFormatted: this.formatDate(date),
          isToday,
          isTomorrow: this.isSameDay(date, new Date(now.getTime() + 24 * 60 * 60 * 1000)),
          slots: processedSlots,
          slotCount: processedSlots.length
        });
        
        processed.totalSlots += processedSlots.length;
      }
    }
    
    processed.hasAvailability = processed.totalSlots > 0;
    
    // Verificar se horário solicitado está disponível
    if (timeAnalysis.specificTime) {
      processed.requestedTimeAvailable = this.checkRequestedTimeAvailable(
        processed.availableDates, 
        timeAnalysis.specificTime
      );
    }
    
    return processed;
  }

  /**
   * Verifica se slot é válido (não passou, respeita antecedência)
   */
  isSlotValid(slot, date, serviceDuration, isToday) {
    if (!isToday) return true;
    
    // Para hoje, verificar se não passou e respeita antecedência mínima
    const slotDateTime = new Date(date);
    const [hour, minute] = slot.horario.split(':');
    slotDateTime.setHours(parseInt(hour), parseInt(minute), 0, 0);
    
    const now = new Date();
    return slotDateTime.getTime() > (now.getTime() + this.businessRules.minimumAdvance);
  }

  /**
   * Normaliza slot para formato padrão
   */
  normalizeSlot(slot, date) {
    const [hour, minute] = slot.horario.split(':');
    const slotDateTime = new Date(date);
    slotDateTime.setHours(parseInt(hour), parseInt(minute), 0, 0);
    
    return {
      time: slot.horario,
      dateTime: slotDateTime.toISOString(),
      displayTime: this.formatTime(slot.horario),
      duration: slot.duracao || 30,
      available: true
    };
  }

  /**
   * Verifica se horário solicitado está disponível
   */
  checkRequestedTimeAvailable(availableDates, requestedTime) {
    for (const dateData of availableDates) {
      if (dateData.slots.some(slot => slot.time === requestedTime)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Determina melhor sugestão baseado na análise
   */
  determineBestSuggestion(availabilityData, timeAnalysis) {
    if (!availabilityData.hasAvailability) {
      return {
        type: 'no_availability',
        message: 'Não há horários disponíveis para os próximos dias.',
        suggestions: []
      };
    }
    
    // Se horário específico foi solicitado
    if (timeAnalysis.specificTime) {
      if (availabilityData.requestedTimeAvailable) {
        return {
          type: 'requested_time_available',
          message: `${timeAnalysis.specificTime} está disponível!`,
          suggestedSlot: this.findRequestedTimeSlot(availabilityData.availableDates, timeAnalysis.specificTime)
        };
      } else {
        return {
          type: 'requested_time_unavailable',
          message: `${timeAnalysis.specificTime} não está disponível, mas temos outras opções:`,
          alternatives: this.getAlternativeSlots(availabilityData.availableDates, timeAnalysis.specificTime, 3)
        };
      }
    }
    
    // Se período do dia foi especificado
    if (timeAnalysis.timeOfDay) {
      const periodSlots = this.getSlotsByPeriod(availabilityData.availableDates, timeAnalysis.timeOfDay);
      if (periodSlots.length > 0) {
        return {
          type: 'period_available',
          message: `Temos horários disponíveis para a ${this.translatePeriod(timeAnalysis.timeOfDay)}:`,
          suggestions: periodSlots.slice(0, 3)
        };
      }
    }
    
    // Sugestões gerais
    const bestSlots = this.getBestSlots(availabilityData.availableDates, 3);
    return {
      type: 'general_suggestions',
      message: 'Temos estes horários disponíveis:',
      suggestions: bestSlots
    };
  }

  /**
   * Encontra slot do horário solicitado
   */
  findRequestedTimeSlot(availableDates, requestedTime) {
    for (const dateData of availableDates) {
      const slot = dateData.slots.find(s => s.time === requestedTime);
      if (slot) {
        return {
          ...slot,
          date: dateData.date,
          dateFormatted: dateData.dateFormatted
        };
      }
    }
    return null;
  }

  /**
   * Obtém slots alternativos próximos ao horário solicitado
   */
  getAlternativeSlots(availableDates, requestedTime, count) {
    const [reqHour, reqMinute] = requestedTime.split(':').map(Number);
    const requestedMinutes = reqHour * 60 + reqMinute;
    
    const alternatives = [];
    
    for (const dateData of availableDates) {
      for (const slot of dateData.slots) {
        const [slotHour, slotMinute] = slot.time.split(':').map(Number);
        const slotMinutes = slotHour * 60 + slotMinute;
        const difference = Math.abs(slotMinutes - requestedMinutes);
        
        alternatives.push({
          ...slot,
          date: dateData.date,
          dateFormatted: dateData.dateFormatted,
          difference
        });
      }
    }
    
    return alternatives
      .sort((a, b) => a.difference - b.difference)
      .slice(0, count);
  }

  /**
   * Obtém slots por período do dia
   */
  getSlotsByPeriod(availableDates, period) {
    const periodRanges = {
      'morning': [6, 12],
      'afternoon': [12, 18],
      'evening': [18, 22]
    };
    
    const [startHour, endHour] = periodRanges[period] || [8, 18];
    const slots = [];
    
    for (const dateData of availableDates) {
      for (const slot of dateData.slots) {
        const [hour] = slot.time.split(':').map(Number);
        if (hour >= startHour && hour < endHour) {
          slots.push({
            ...slot,
            date: dateData.date,
            dateFormatted: dateData.dateFormatted
          });
        }
      }
    }
    
    return slots;
  }

  /**
   * Obtém melhores slots (mais próximos no tempo)
   */
  getBestSlots(availableDates, count) {
    const allSlots = [];
    
    for (const dateData of availableDates) {
      for (const slot of dateData.slots) {
        allSlots.push({
          ...slot,
          date: dateData.date,
          dateFormatted: dateData.dateFormatted
        });
      }
    }
    
    return allSlots
      .sort((a, b) => new Date(a.dateTime) - new Date(b.dateTime))
      .slice(0, count);
  }

  /**
   * Traduz período do dia
   */
  translatePeriod(period) {
    const translations = {
      'morning': 'manhã',
      'afternoon': 'tarde',
      'evening': 'noite'
    };
    return translations[period] || period;
  }

  /**
   * Constrói resposta de sucesso
   */
  buildSuccessResponse(availabilityData, suggestion, timeAnalysis, state) {
    return {
      success: true,
      result: {
        hasAvailability: availabilityData.hasAvailability,
        totalSlots: availabilityData.totalSlots,
        availableDates: availabilityData.availableDates,
        suggestion: suggestion,
        requestedTime: timeAnalysis.specificTime,
        requestedTimeAvailable: availabilityData.requestedTimeAvailable
      },
      nextStage: availabilityData.hasAvailability ? 'schedule_confirmation' : 'intent_confirmation',
      updates: {
        context: {
          ...state.context,
          availability: availabilityData,
          suggestion: suggestion,
          schedulingData: {
            ...(state.context?.schedulingData || {}),
            availability: availabilityData,
            suggestedSlot: suggestion.suggestedSlot || suggestion.suggestions?.[0]
          }
        }
      },
      metadata: {
        debugInfo: {
          totalSlots: availabilityData.totalSlots,
          datesChecked: availabilityData.availableDates.length,
          suggestionType: suggestion.type,
          requestedTime: timeAnalysis.specificTime,
          cacheUsed: false,
          processingTime: Date.now()
        }
      }
    };
  }

  /**
   * Constrói resposta de erro de validação
   */
  buildValidationErrorResponse(validation, state) {
    return {
      success: false,
      result: {
        hasAvailability: false,
        error: validation.error,
        missingData: validation.missingData
      },
      nextStage: 'intent_confirmation',
      updates: {},
      metadata: {
        debugInfo: {
          validationFailed: true,
          missingData: validation.missingData
        }
      }
    };
  }

  /**
   * Utilitários de data/hora
   */
  isSameDay(date1, date2) {
    return date1.toDateString() === date2.toDateString();
  }

  formatDate(date) {
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    });
  }

  formatTime(time) {
    const [hour, minute] = time.split(':');
    return `${hour}h${minute !== '00' ? minute : ''}`;
  }

  /**
   * Cache de disponibilidade
   */
  generateAvailabilityCacheKey(professionalId, serviceId, targetDates) {
    const dateStrs = targetDates.map(d => d.toISOString().split('T')[0]).join(',');
    return `availability_${professionalId}_${serviceId}_${dateStrs}`;
  }

  getCachedAvailability(cacheKey) {
    const cached = this.availabilityCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp < this.cacheExpiration)) {
      return cached.data;
    }
    return null;
  }

  setCachedAvailability(cacheKey, data) {
    this.availabilityCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    // Limpar cache antigo
    if (this.availabilityCache.size > 100) {
      const oldestKey = this.availabilityCache.keys().next().value;
      this.availabilityCache.delete(oldestKey);
    }
  }

  /**
   * Obtém estatísticas de verificação de disponibilidade
   */
  getAvailabilityStats() {
    return {
      ...this.getMetrics(),
      cacheSize: this.availabilityCache.size,
      cacheHitRate: this.calculateCacheHitRate(),
      availabilityFoundRate: this.calculateAvailabilityFoundRate(),
      avgSlotsPerRequest: this.calculateAvgSlotsPerRequest()
    };
  }

  calculateCacheHitRate() {
    const totalRequests = this.executionCount || 1;
    const cacheHits = this.cacheHitCount || 0;
    return (cacheHits / totalRequests) * 100;
  }

  calculateAvailabilityFoundRate() {
    const totalRequests = this.executionCount || 1;
    const successfulRequests = this.successCount || 0;
    return (successfulRequests / totalRequests) * 100;
  }

  calculateAvgSlotsPerRequest() {
    // Implementar baseado em dados históricos
    return 5.2; // Mock value
  }
}

module.exports = AvailabilityCheckNode;