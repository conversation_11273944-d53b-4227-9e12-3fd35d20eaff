/**
 * AppointmentRetrievalNode - Nó de Recuperação de Agendamentos
 * 
 * Responsabilidades:
 * - Buscar agendamentos existentes por telefone do cliente
 * - Filtrar agendamentos por status (futuros, passados, cancelados)
 * - Integração com API Trinks para dados reais
 * - Cache inteligente para performance
 * - Suporte a diferentes tipos de busca (confirmação, cancelamento, reagendamento)
 */

const BaseNode = require('../../core/BaseNode');

class AppointmentRetrievalNode extends BaseNode {
  constructor() {
    super('appointment_retrieval', {
      timeout: 10000, // 10s para buscar agendamentos
      retryAttempts: 2
    });
    
    // Configuração da API Trinks
    this.trinksConfig = {
      baseUrl: process.env.TRINKS_API_BASE_URL || 'https://api.trinks.com',
      apiKey: process.env.TRINKS_API_KEY,
      establishmentId: process.env.TRINKS_ESTABLISHMENT_ID
    };
    
    // Cache de agendamentos
    this.appointmentCache = new Map();
    this.cacheExpiration = 5 * 60 * 1000; // 5 minutos
    
    // Status de agendamentos suportados
    this.appointmentStatuses = {
      SCHEDULED: 'agendado',
      CONFIRMED: 'confirmado',
      COMPLETED: 'concluído',
      CANCELLED: 'cancelado',
      NO_SHOW: 'não compareceu'
    };
  }

  /**
   * Executa busca de agendamentos
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Determinar tipo de busca baseado na intenção
      const searchType = this.determineSearchType(state);
      
      // Verificar cache primeiro
      const cacheKey = this.generateCacheKey(customerPhone, searchType);
      const cachedResult = this.getCachedAppointments(cacheKey);
      
      if (cachedResult) {
        this.logExecution('DEBUG', customerPhone, {
          action: 'cache_hit',
          appointmentCount: cachedResult.appointments.length,
          searchType
        });
        
        return {
          success: true,
          result: cachedResult,
          nextStage: this.determineNextStage(cachedResult, state),
          updates: {
            context: {
              ...state.context,
              appointments: cachedResult
            }
          }
        };
      }

      // Buscar agendamentos via API
      let appointmentData;
      if (this.trinksConfig.apiKey) {
        appointmentData = await this.fetchFromTrinksAPI(customerPhone, searchType);
      } else {
        // Fallback para dados mock em desenvolvimento
        appointmentData = this.generateMockAppointments(customerPhone, searchType);
      }

      // Processar e normalizar dados
      const processedData = this.processAppointmentData(appointmentData, searchType);
      
      // Salvar no cache
      this.setCachedAppointments(cacheKey, processedData);
      
      return {
        success: true,
        result: processedData,
        nextStage: this.determineNextStage(processedData, state),
        updates: {
          context: {
            ...state.context,
            appointments: processedData
          }
        },
        metadata: {
          debugInfo: {
            searchType,
            appointmentCount: processedData.appointments.length,
            futureAppointments: processedData.futureAppointments,
            pastAppointments: processedData.pastAppointments,
            usedAPI: !!this.trinksConfig.apiKey,
            cacheUsed: false
          }
        }
      };
      
    } catch (error) {
      // Em caso de erro, retornar dados vazios para não bloquear o fluxo
      const emptyResult = {
        appointments: [],
        futureAppointments: 0,
        pastAppointments: 0,
        hasAppointments: false,
        searchType: this.determineSearchType(state),
        error: error.message
      };
      
      return {
        success: false,
        result: emptyResult,
        nextStage: 'intent_confirmation', // Permitir que usuário continue sem dados de agendamento
        updates: {
          context: {
            ...state.context,
            appointments: emptyResult
          }
        },
        error: {
          message: error.message,
          code: 'APPOINTMENT_RETRIEVAL_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Determina tipo de busca baseado na intenção
   */
  determineSearchType(state) {
    const intent = state.intent?.type || 'unknown';
    
    switch (intent) {
      case 'cancellation':
      case 'rescheduling':
        return 'future'; // Buscar apenas agendamentos futuros
      case 'confirmation':
        return 'pending_confirmation'; // Buscar agendamentos que precisam confirmação
      case 'inquiry':
        return 'all'; // Buscar todos os agendamentos
      case 'scheduling':
      default:
        return 'future'; // Para contexto geral
    }
  }

  /**
   * Busca agendamentos via API Trinks
   */
  async fetchFromTrinksAPI(customerPhone, searchType) {
    // Normalizar telefone (remover caracteres especiais)
    const normalizedPhone = this.normalizePhone(customerPhone);
    
    // Construir parâmetros da busca
    const searchParams = this.buildSearchParams(normalizedPhone, searchType);
    
    // Fazer chamada à API
    const response = await this.callExternalService('trinks_api', async () => {
      const url = `${this.trinksConfig.baseUrl}/v1/agendamentos/cliente`;
      
      const requestOptions = {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.trinksConfig.apiKey}`,
          'Content-Type': 'application/json',
          'X-Establishment-ID': this.trinksConfig.establishmentId
        }
      };
      
      // Adicionar parâmetros de query
      const queryParams = new URLSearchParams(searchParams);
      const fullUrl = `${url}?${queryParams.toString()}`;
      
      const apiResponse = await fetch(fullUrl, requestOptions);
      
      if (!apiResponse.ok) {
        throw new Error(`API Error: ${apiResponse.status} - ${apiResponse.statusText}`);
      }
      
      return await apiResponse.json();
    });
    
    return response.data || response.agendamentos || [];
  }

  /**
   * Constrói parâmetros de busca para API
   */
  buildSearchParams(phone, searchType) {
    const params = {
      telefone: phone,
      estabelecimento_id: this.trinksConfig.establishmentId
    };
    
    // Usar data ajustada para timezone Brasil para consistência
    const now = new Date();
    const brazilTime = new Date(now.getTime() - (now.getTimezoneOffset() * 60000) + (-3 * 3600000));
    const today = brazilTime.toISOString().split('T')[0];
    
    switch (searchType) {
      case 'future':
        params.data_inicio = today;
        params.status = 'agendado,confirmado';
        break;
      case 'pending_confirmation':
        params.data_inicio = today;
        params.status = 'agendado';
        break;
      case 'past':
        params.data_fim = today;
        break;
      case 'all':
      default:
        // Sem filtros de data - buscar todos
        break;
    }
    
    return params;
  }

  /**
   * Normaliza número de telefone
   */
  normalizePhone(phone) {
    // Remove todos os caracteres não numéricos
    const numbersOnly = phone.replace(/\D/g, '');
    
    // Se começar com +55, remover
    if (numbersOnly.startsWith('55') && numbersOnly.length === 13) {
      return numbersOnly.substring(2);
    }
    
    return numbersOnly;
  }

  /**
   * Processa dados de agendamentos da API
   */
  processAppointmentData(rawData, searchType) {
    const appointments = Array.isArray(rawData) ? rawData : [];
    
    // Classificar agendamentos - usar data/hora atual para comparação precisa
    const now = new Date();
    // Ajustar para timezone Brasil (UTC-3) para consistência com backend
    const nowBrazilTime = new Date(now.getTime() - (now.getTimezoneOffset() * 60000) + (-3 * 3600000));
    
    let futureAppointments = 0;
    let pastAppointments = 0;
    let pendingConfirmation = 0;
    
    // Filtrar agendamentos cancelados primeiro (mesma lógica do backend)
    const activeAppointments = appointments.filter(apt => {
      const status = (apt.status || '').toLowerCase();
      
      // Padrões de cancelamento (mesma lista do backend)
      const cancelledPatterns = [
        'cancelado', 'canceled', 'cancelled', 
        'desmarcado', 'anulado', 'cancelou',
        'não compareceu', 'faltou', 'ausente'
      ];
      
      const isCancelled = cancelledPatterns.some(pattern => status.includes(pattern));
      return !isCancelled;
    });
    
    const processedAppointments = activeAppointments.map(apt => {
      // Tentar diferentes formatos de data da API
      let appointmentDate;
      if (apt.dataHoraInicio) {
        // Formato ISO da API Trinks: "2025-08-29T16:00:00"
        appointmentDate = new Date(apt.dataHoraInicio);
      } else if (apt.dataHora) {
        appointmentDate = new Date(apt.dataHora);
      } else if (apt.data_hora) {
        appointmentDate = new Date(apt.data_hora);
      } else if (apt.date) {
        appointmentDate = new Date(apt.date);
      } else {
        // Fallback: usar data atual
        appointmentDate = new Date();
      }
      
      // Comparação precisa considerando data E hora
      const isFuture = appointmentDate > nowBrazilTime;
      const isPending = apt.status === 'agendado' || apt.status === 'SCHEDULED';
      
      if (isFuture) {
        futureAppointments++;
        if (isPending) pendingConfirmation++;
      } else {
        pastAppointments++;
      }
      
      return {
        id: apt.id,
        dataHora: appointmentDate.toISOString(),
        dataHoraFormatted: this.formatDateTime(appointmentDate),
        servico: apt.servico?.nome || apt.service?.name || apt.servico || 'Serviço não especificado',
        profissional: apt.profissional?.nome || apt.professional?.name || apt.profissional || 'Profissional não especificado',
        status: apt.status,
        statusFormatted: this.formatStatus(apt.status),
        valor: apt.valor || apt.price || 0,
        duracao: apt.duracao || apt.duration || apt.duracaoEmMinutos || 30,
        observacoes: apt.observacoes || apt.notes || '',
        isFuture,
        isPending,
        canBeCancelled: isFuture && !['cancelado', 'CANCELLED'].includes(apt.status),
        canBeRescheduled: isFuture && !['cancelado', 'CANCELLED'].includes(apt.status)
      };
    });
    
    // Ordenar por data (mais próximos primeiro)
    processedAppointments.sort((a, b) => new Date(a.dataHora) - new Date(b.dataHora));
    
    return {
      appointments: processedAppointments,
      futureAppointments,
      pastAppointments,
      pendingConfirmation,
      hasAppointments: activeAppointments.length > 0,
      hasFutureAppointments: futureAppointments > 0,
      hasPendingConfirmation: pendingConfirmation > 0,
      searchType,
      retrievedAt: new Date(),
      totalCount: activeAppointments.length, // Apenas agendamentos ativos
      originalCount: appointments.length,     // Total original incluindo cancelados
      filteredCount: appointments.length - activeAppointments.length // Quantos foram filtrados
    };
  }

  /**
   * Gera dados mock para desenvolvimento
   */
  generateMockAppointments(customerPhone, searchType) {
    // Se não tem API key, gerar dados mock baseados no telefone
    const phoneHash = this.hashPhone(customerPhone);
    const appointmentCount = (phoneHash % 3) + 1; // 1-3 agendamentos
    
    const mockAppointments = [];
    const now = new Date();
    
    for (let i = 0; i < appointmentCount; i++) {
      const futureDate = new Date(now);
      futureDate.setDate(now.getDate() + (i + 1) * 7); // Uma semana para frente cada
      
      mockAppointments.push({
        id: `mock_${phoneHash}_${i}`,
        dataHora: futureDate.toISOString(),
        servico: { nome: ['Corte', 'Barba', 'Unha'][i % 3] },
        profissional: { nome: ['Ana Silva', 'João Santos', 'Maria Oliveira'][i % 3] },
        status: i === 0 ? 'agendado' : 'confirmado',
        valor: [30, 25, 40][i % 3],
        duracao: [30, 15, 45][i % 3],
        observacoes: `Agendamento mock #${i + 1}`
      });
    }
    
    return mockAppointments;
  }

  /**
   * Hash simples do telefone para gerar dados consistentes
   */
  hashPhone(phone) {
    let hash = 0;
    for (let i = 0; i < phone.length; i++) {
      const char = phone.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Converter para 32bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Formata data e hora para exibição
   */
  formatDateTime(date) {
    return date.toLocaleString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  /**
   * Formata status para exibição
   */
  formatStatus(status) {
    const statusMap = {
      'agendado': 'Agendado',
      'confirmado': 'Confirmado',
      'concluido': 'Concluído',
      'cancelado': 'Cancelado',
      'nao_compareceu': 'Não Compareceu',
      'SCHEDULED': 'Agendado',
      'CONFIRMED': 'Confirmado',
      'COMPLETED': 'Concluído',
      'CANCELLED': 'Cancelado',
      'NO_SHOW': 'Não Compareceu'
    };
    
    return statusMap[status] || status;
  }

  /**
   * Determina próximo estágio baseado nos agendamentos encontrados
   */
  determineNextStage(appointmentData, state) {
    const intent = state.intent?.type || 'unknown';
    
    if (intent === 'cancellation' || intent === 'rescheduling') {
      if (appointmentData.hasFutureAppointments) {
        return 'appointment_selection'; // Permite escolher qual cancelar/reagendar
      } else {
        return 'intent_confirmation'; // Informar que não há agendamentos para cancelar
      }
    }
    
    if (intent === 'confirmation') {
      if (appointmentData.hasPendingConfirmation) {
        return 'appointment_confirmation'; // Confirmar agendamentos pendentes
      } else {
        return 'intent_confirmation'; // Informar que não há agendamentos para confirmar
      }
    }
    
    // Para outras intenções, continuar fluxo normal
    return 'intent_confirmation';
  }

  /**
   * Cache de agendamentos
   */
  generateCacheKey(phone, searchType) {
    return `appointments_${this.normalizePhone(phone)}_${searchType}`;
  }

  getCachedAppointments(cacheKey) {
    const cached = this.appointmentCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp < this.cacheExpiration)) {
      return cached.data;
    }
    return null;
  }

  setCachedAppointments(cacheKey, data) {
    this.appointmentCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    // Limpar cache antigo
    if (this.appointmentCache.size > 50) {
      const oldestKey = this.appointmentCache.keys().next().value;
      this.appointmentCache.delete(oldestKey);
    }
  }

  /**
   * Obtém estatísticas de busca de agendamentos
   */
  getRetrievalStats() {
    return {
      ...this.getMetrics(),
      cacheSize: this.appointmentCache.size,
      cacheHitRate: this.calculateCacheHitRate(),
      apiCallsCount: this.apiCallCount || 0,
      avgAppointmentsPerCustomer: this.calculateAvgAppointments()
    };
  }

  calculateCacheHitRate() {
    const totalRequests = this.executionCount || 1;
    const cacheHits = this.cacheHitCount || 0;
    return (cacheHits / totalRequests) * 100;
  }

  calculateAvgAppointments() {
    // Implementar cálculo baseado em dados históricos
    return 2.3; // Mock value
  }
}

module.exports = AppointmentRetrievalNode;