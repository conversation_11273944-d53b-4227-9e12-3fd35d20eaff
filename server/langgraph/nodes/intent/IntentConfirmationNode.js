/**
 * IntentConfirmationNode - Nó de Confirmação de Intenção
 * 
 * Responsabilidades:
 * - Detectar mudanças de intenção durante a conversa
 * - Confirmar intenções ambíguas com o cliente
 * - Gerar respostas apropriadas para cada situação
 * - Tratar dados faltantes de forma elegante
 */

const BaseNode = require('../../core/BaseNode');
const Anthropic = require('@anthropic-ai/sdk');
const messageLogger = require('../../../utils/messageLogger');
const ClaudeEvaluationLogger = require('../../../utils/ClaudeEvaluationLogger');

class IntentConfirmationNode extends BaseNode {
  constructor() {
    super('intent_confirmation', {
      timeout: 20000, // 20s para gerar resposta
      retryAttempts: 2
    });
    
    // Inicializar cliente Anthropic
    this.anthropic = process.env.ANTHROPIC_API_KEY ? 
      new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY }) : null;
    
    // Templates de resposta para diferentes situações
    this.responseTemplates = {
      // Mudanças de intenção
      intent_change: {
        'scheduling_to_cancellation': 'Ah, entendi! Na verdade você quer cancelar um agendamento, não fazer um novo. Vou te ajudar com isso!',
        'inquiry_to_scheduling': 'Perfeito! Vi que agora você quer agendar. Vamos fazer isso!',
        'greeting_to_scheduling': 'Ótimo! Vamos cuidar do seu agendamento então.'
      },
      
      // Dados faltantes
      missing_data: {
        'service': 'Que tipo de serviço você gostaria de agendar? Temos corte, barba, unha, sobrancelha...',
        'date': 'Para que dia você gostaria de agendar?',
        'professional_or_time': 'Tem preferência por algum horário ou profissional específico?',
        'customer_identification': 'Qual seu nome para eu localizar seus agendamentos?'
      },
      
      // Regras de negócio violadas
      business_rules: {
        'working_hours': 'Esse horário fica fora do nosso funcionamento. Funcionamos das 8h às 18h. Que tal escolher outro horário?',
        'working_days': 'Não funcionamos nesse dia. Que tal agendar para outro dia?',
        'minimum_advance': 'Precisamos de pelo menos 1 hora de antecedência. Pode agendar para mais tarde?'
      },
      
      // Confirmações
      confirmation: {
        'scheduling_ready': 'Perfeito! Tenho todos os dados para seu agendamento. Vou confirmar tudo agora.',
        'cancellation_ready': 'Entendi, vou processar o cancelamento do seu agendamento.',
        'inquiry_response': 'Vou consultar essas informações para você.'
      }
    };
  }

  /**
   * Executa confirmação de intenção e geração de resposta
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Analisar situação atual
      const situationAnalysis = this.analyzeSituation(input);
      
      // Detectar mudanças de intenção
      const intentChangeDetection = this.detectIntentChange(state);
      
      // Determinar tipo de resposta necessária
      const responseType = this.determineResponseType(situationAnalysis, intentChangeDetection);
      
      // Gerar resposta apropriada
      const response = await this.generateResponse(situationAnalysis, responseType, customerPhone);
      
      return {
        success: true,
        result: {
          response: response.content,
          responseType,
          situationAnalysis,
          intentChangeDetection,
          needsUserInput: response.needsUserInput,
          nextExpectedData: response.nextExpectedData
        },
        nextStage: this.determineNextStage(responseType, response.needsUserInput),
        updates: {
          context: {
            ...state.context,
            lastConfirmation: {
              type: responseType,
              timestamp: new Date(),
              needsUserInput: response.needsUserInput
            }
          }
        },
        metadata: {
          debugInfo: {
            responseType,
            usedAI: response.usedAI,
            intentChange: intentChangeDetection.detected,
            businessRuleViolations: situationAnalysis.businessValidation?.violations?.length || 0
          }
        }
      };
      
    } catch (error) {
      return {
        success: false,
        result: {
          response: 'Desculpe, tive um problema para processar sua mensagem. Pode repetir?',
          responseType: 'error_recovery'
        },
        nextStage: 'intent_classification',
        updates: {},
        error: {
          message: error.message,
          code: 'INTENT_CONFIRMATION_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Analisa situação atual baseada no input
   */
  analyzeSituation(input) {
    const { state } = input;
    
    return {
      currentIntent: state.intent?.type || state.context?.intent?.type || 'unknown',
      intentConfidence: state.intent?.confidence || state.context?.intent?.confidence || 0,
      validation: state.context?.validation || {},
      missingData: state.context?.validation?.missingData || [],
      businessValidation: state.context?.validation?.businessValidation || { isValid: true },
      conversationStage: state.currentStage,
      messageCount: state.messages?.length || 0,
      lastUserMessage: this.getLastUserMessage(state.messages)
    };
  }

  /**
   * Detecta mudanças de intenção comparando histórico
   */
  detectIntentChange(state) {
    const detection = {
      detected: false,
      previousIntent: null,
      currentIntent: state.intent?.type,
      changeType: null,
      confidence: 0
    };
    
    // Verificar histórico de intenções (se existir no contexto)
    const intentHistory = state.context?.intentHistory || [];
    
    if (intentHistory.length > 0) {
      const lastIntent = intentHistory[intentHistory.length - 1];
      const currentIntent = state.intent?.type || state.context?.intent?.type;
      
      if (lastIntent.type !== currentIntent && currentIntent !== 'unknown') {
        detection.detected = true;
        detection.previousIntent = lastIntent.type;
        detection.changeType = `${lastIntent.type}_to_${currentIntent}`;
        detection.confidence = state.intent?.confidence || state.context?.intent?.confidence || 0;
      }
    }
    
    return detection;
  }

  /**
   * Determina tipo de resposta baseado na análise
   */
  determineResponseType(situationAnalysis, intentChangeDetection) {
    // Prioridade 1: Mudança de intenção detectada
    if (intentChangeDetection.detected && intentChangeDetection.confidence > 0.7) {
      return {
        type: 'intent_change_confirmation',
        subtype: intentChangeDetection.changeType,
        priority: 'high'
      };
    }
    
    // Prioridade 2: Violações de regras de negócio
    if (!situationAnalysis.businessValidation.isValid) {
      return {
        type: 'business_rule_violation',
        subtype: situationAnalysis.businessValidation.violations[0]?.rule || 'general',
        priority: 'high'
      };
    }
    
    // Prioridade 3: Dados faltantes críticos
    const criticalMissingData = situationAnalysis.missingData.filter(data =>
      ['service', 'customer_identification', 'date'].includes(data.type)
    );
    
    if (criticalMissingData.length > 0) {
      return {
        type: 'missing_data_request',
        subtype: criticalMissingData[0].type,
        priority: 'medium',
        missingData: criticalMissingData
      };
    }
    
    // Prioridade 4: Intenção com baixa confiança
    if (situationAnalysis.intentConfidence < 0.6) {
      return {
        type: 'intent_clarification',
        subtype: 'low_confidence',
        priority: 'medium'
      };
    }
    
    // Prioridade 5: Contexto completo - prosseguir
    if (situationAnalysis.validation.isComplete) {
      return {
        type: 'proceed_confirmation',
        subtype: situationAnalysis.currentIntent,
        priority: 'normal'
      };
    }
    
    // Fallback: Dados não críticos faltando
    return {
      type: 'missing_data_request',
      subtype: situationAnalysis.missingData[0]?.type || 'general',
      priority: 'low',
      missingData: situationAnalysis.missingData
    };
  }

  /**
   * Gera resposta apropriada
   */
  async generateResponse(situationAnalysis, responseType, customerPhone) {
    // Tentar usar IA primeiro
    if (this.anthropic) {
      try {
        return await this.generateAIResponse(situationAnalysis, responseType, customerPhone);
      } catch (error) {
        console.warn(`Falhou ao gerar resposta com IA: ${error.message}. Usando template.`);
      }
    }
    
    // Fallback para templates
    return this.generateTemplateResponse(situationAnalysis, responseType);
  }

  /**
   * Gera resposta usando IA
   */
  async generateAIResponse(situationAnalysis, responseType, customerPhone) {
    const prompt = this.buildResponsePrompt(situationAnalysis, responseType);
    
    const startTime = Date.now();
    const response = await this.callExternalService('claude', async () => {
      return await this.anthropic.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 300,
        temperature: 0.3,
        messages: [{
          role: 'user',
          content: prompt
        }]
      });
    });
    const duration = Date.now() - startTime;
    
    const content = response.content[0]?.text;
    
    // Log da chamada da IA
    messageLogger.logAIInteraction(
      'IntentConfirmation',
      prompt,
      content,
      duration,
      'claude-sonnet-4-20250514'
    );
    if (!content) {
      throw new Error('Empty response from Claude API');
    }
    
    return {
      content: this.sanitizeResponse(content.trim()),
      needsUserInput: this.responseNeedsUserInput(responseType),
      nextExpectedData: this.getNextExpectedData(responseType),
      usedAI: true
    };
  }

  /**
   * Constrói prompt para geração de resposta via IA
   */
  buildResponsePrompt(situationAnalysis, responseType) {
    const context = {
      intent: situationAnalysis.currentIntent,
      confidence: situationAnalysis.intentConfidence,
      missingData: situationAnalysis.missingData,
      lastMessage: situationAnalysis.lastUserMessage
    };
    
    // 🌎 CONTEXTO TEMPORAL E GEOGRÁFICO COMPLETO
    const dateUtils = require('../../../utils/dateUtils');
    const currentDate = dateUtils.getCurrentDateBR();
    const today = dateUtils.formatDateBR(currentDate);
    const todayISO = dateUtils.getDateStringBR(currentDate);
    
    // Período do dia e contexto cultural
    const currentHour = currentDate.getHours();
    let periodOfDay = '';
    if (currentHour >= 6 && currentHour < 12) {
      periodOfDay = 'manhã';
    } else if (currentHour >= 12 && currentHour < 18) {
      periodOfDay = 'tarde';
    } else if (currentHour >= 18 && currentHour < 22) {
      periodOfDay = 'início da noite';
    } else {
      periodOfDay = 'noite';
    }
    
    const currentTime = dateUtils.formatDateTimeBR(currentDate).split(' ')[1];
    
    let basePrompt = `Você é uma recepcionista calorosa de um salão de beleza brasileiro.

## 🌎 CONTEXTO TEMPORAL E GEOGRÁFICO
**LOCALIZAÇÃO:** Rio de Janeiro, Brasil (America/Sao_Paulo timezone)
**DATA ATUAL:** ${today} (${todayISO})
**HORÁRIO ATUAL:** ${currentTime} (${periodOfDay})
**CONTEXTO CULTURAL:** Salão de beleza brasileiro, clientes falam português carioca

CONTEXTO ATUAL:
- Intenção do cliente: ${context.intent}
- Confiança: ${(context.confidence * 100).toFixed(0)}%
- Última mensagem: "${context.lastMessage}"
- Dados faltantes: ${context.missingData.map(d => d.type).join(', ') || 'nenhum'}

SITUAÇÃO: ${responseType.type} - ${responseType.subtype}

INSTRUÇÕES FUNDAMENTAIS:
- Fale de forma natural e calorosa
- Use linguagem brasileira autêntica
- Seja direta e útil
- NUNCA diga "vou verificar" ou "aguarde"
- Use emojis com moderação
- Máximo 2 frases
- INFERIR informações do contexto quando possível

**REFERÊNCIA TEMPORAL**: Quando cliente fala "hoje", se refere a ${today}. "Amanhã" seria ${new Date(currentDate.getTime() + 24*60*60*1000).toLocaleDateString('pt-BR')}.

`;

    switch (responseType.type) {
      case 'intent_change_confirmation':
        basePrompt += `O cliente mudou de intenção. Confirme a nova intenção de forma positiva e prestativa.`;
        break;
        
      case 'business_rule_violation':
        basePrompt += `Há uma restrição de horário/funcionamento. Explique gentilmente e sugira alternativas.`;
        break;
        
      case 'missing_data_request':
        // 🧠 ANÁLISE CONTEXTUAL DINÂMICA VIA IA - Melhorada com edge cases
        const conversationHistory = situationAnalysis.conversationHistory || [];
        const contextualAnalysisPrompt = this.buildContextualAnalysisPrompt(conversationHistory);
        
        basePrompt += `${contextualAnalysisPrompt}

HISTÓRICO DA CONVERSA PARA ANÁLISE:
${conversationHistory.map(msg => `${msg.role === 'user' ? 'Cliente' : 'Você'}: "${msg.content || msg.text}"`).join('\n')}

TAREFA: Peça educadamente APENAS as informações que ainda faltam para ${responseType.subtype}.

## INSTRUÇÕES CRÍTICAS PARA ANÁLISE CONTEXTUAL:

### 1. **CONTEXTO DE CONTINUIDADE** - Use TODAS as informações anteriores:
   - Se já foi mencionado "massagem" e cliente pergunta sobre "massagem" = CONFIRMA o serviço
   - Se já foi perguntado sobre profissional e cliente escolheu = USE essa informação
   - Se cliente menciona "amanhã de manhã" e você sugeriu "amanhã com Jailson" = data é AMANHÃ
   - Se cliente pergunta "horários do Jailson pela manhã" após sugestão "amanhã de manhã" = NÃO pergunte a data

### 2. **PRESERVAÇÃO DE DADOS** - Se já existem dados coletados, USE-OS como base:
   - Se serviço já foi identificado e cliente confirma = mantenha o serviço existente
   - Se profissional já foi escolhido = mantenha o profissional existente
   - APENAS sobrescreva se cliente mencionar algo DIFERENTE explicitamente

### 3. **INFERÊNCIA TEMPORAL INTELIGENTE**:
   - Se assistente sugeriu "amanhã" e cliente pergunta sobre horários = data é amanhã (${new Date(currentDate.getTime() + 24*60*60*1000).toLocaleDateString('pt-BR')})
   - Se cliente diz "hoje" = data é ${today}
   - Se contexto já estabeleceu período (manhã/tarde) = mantenha essa referência

### 4. **ANÁLISE DO HISTÓRICO COMPLETO**:
   - IDENTIFIQUE que informações o cliente já forneceu (serviços, profissionais, datas, horários)
   - IDENTIFIQUE que informações o ASSISTENTE já sugeriu ou mencionou
   - NÃO pergunte informações que já estão claras no contexto
   - Se há referência implícita a dados já discutidos, USE essa informação

### 5. **EXEMPLOS DE INFERÊNCIA**:
   - Assistente: "amanhã de manhã com Jailson" → Cliente: "horários do Jailson manhã" = DATA: amanhã, PROFISSIONAL: Jailson, PERÍODO: manhã
   - Assistente mencionou "massagem" → Cliente: "pode ser" = SERVIÇO: massagem
   - Contexto estabeleceu "terça-feira" → Cliente: "que horas?" = DATA: terça-feira

**REGRA FUNDAMENTAL**: Antes de perguntar qualquer informação, ANALISE se ela já foi fornecida direta ou indiretamente no histórico da conversa.`;
        break;
        
      case 'intent_clarification':
        basePrompt += `A intenção não ficou clara. Pergunte como pode ajudar de forma acolhedora.`;
        break;
        
      case 'proceed_confirmation':
        basePrompt += `Todos os dados estão completos. Confirme que vai prosseguir com o ${context.intent}.`;
        break;
    }
    
    basePrompt += `\n\nResponda apenas com a mensagem para o cliente:`;
    
    return basePrompt;
  }

  /**
   * Gera resposta usando templates
   */
  generateTemplateResponse(situationAnalysis, responseType) {
    let template = '';
    
    switch (responseType.type) {
      case 'intent_change_confirmation':
        template = this.responseTemplates.intent_change[responseType.subtype] ||
          'Entendi! Vamos focar no que você precisa agora.';
        break;
        
      case 'business_rule_violation':
        template = this.responseTemplates.business_rules[responseType.subtype] ||
          'Esse horário não está disponível. Podemos agendar para outro?';
        break;
        
      case 'missing_data_request':
        template = this.responseTemplates.missing_data[responseType.subtype] ||
          'Preciso de mais algumas informações para te ajudar melhor.';
        break;
        
      case 'intent_clarification':
        template = 'Como posso ajudar você hoje? 😊';
        break;
        
      case 'proceed_confirmation':
        template = this.responseTemplates.confirmation[`${situationAnalysis.currentIntent}_ready`] ||
          'Perfeito! Vou processar isso para você agora.';
        break;
        
      default:
        template = 'Estou aqui para ajudar! O que precisa?';
    }
    
    return {
      content: this.sanitizeResponse(template),
      needsUserInput: this.responseNeedsUserInput(responseType),
      nextExpectedData: this.getNextExpectedData(responseType),
      usedAI: false
    };
  }

  /**
   * Remove frases proibidas e normaliza a saída
   */
  sanitizeResponse(text) {
    if (!text) return text;
    const forbidden = [
      /vou\s+verificar/gi,
      /vou\s+consultar/gi,
      /vou\s+checar/gi,
      /aguarde/gi,
      /um\s+momento/gi,
      /só\s+um\s+momento/gi,
      /só\s+um\s+minutinho/gi
    ];
    let out = text;
    for (const rx of forbidden) out = out.replace(rx, '').trim();
    out = out.replace(/\s{2,}/g, ' ').replace(/^[,\.\s]+/, '').trim();
    return out || 'Como posso ajudar você hoje?';
  }

  /**
   * Determina se resposta precisa de input do usuário
   */
  responseNeedsUserInput(responseType) {
    return [
      'missing_data_request',
      'intent_clarification',
      'business_rule_violation'
    ].includes(responseType.type);
  }

  /**
   * Determina qual tipo de dados esperar do usuário
   */
  getNextExpectedData(responseType) {
    if (responseType.type === 'missing_data_request') {
      return responseType.subtype;
    }
    
    if (responseType.type === 'intent_clarification') {
      return 'intent_clarification';
    }
    
    if (responseType.type === 'business_rule_violation') {
      return 'alternative_option';
    }
    
    return null;
  }

  /**
   * Determina próximo estágio
   */
  determineNextStage(responseType, needsUserInput) {
    if (needsUserInput) {
      // Se precisa de mais input, voltar para classificar nova mensagem
      return 'intent_classification';
    }
    
    if (responseType.type === 'proceed_confirmation') {
      // Se pode prosseguir, ir para scheduling
      return 'scheduling';
    }
    
    // Para outros casos, esperar nova mensagem
    return 'intent_classification';
  }

  /**
   * Analisa padrões na conversa para detectar mudanças implícitas
   */
  analyzeConversationPatterns(messages) {
    if (messages.length < 2) return null;
    
    // Pegar últimas mensagens do usuário
    const userMessages = messages
      .filter(msg => msg.role === 'user')
      .slice(-3)
      .map(msg => msg.content.toLowerCase());
    
    // Detectar padrões de mudança
    const patterns = {
      scheduling_to_cancellation: [
        /agendar.*cancelar/,
        /fazer.*na verdade.*cancelar/
      ],
      inquiry_to_scheduling: [
        /disponível.*quero agendar/,
        /tem vaga.*marcar/
      ],
      greeting_to_scheduling: [
        /(oi|olá).*agendar/,
        /tudo bem.*marcar/
      ]
    };
    
    const recentText = userMessages.join(' ');
    
    for (const [patternType, regexes] of Object.entries(patterns)) {
      for (const regex of regexes) {
        if (regex.test(recentText)) {
          return patternType;
        }
      }
    }
    
    return null;
  }

  /**
   * Obtém métricas do nó de confirmação
   */
  getConfirmationMetrics() {
    return {
      ...this.getMetrics(),
      responseTypes: this.responseTypeDistribution || {},
      intentChangesDetected: this.intentChangeCount || 0,
      businessRuleViolations: this.businessRuleViolationCount || 0
    };
  }

  /**
   * Atualiza métricas específicas do nó
   */
  updateConfirmationMetrics(responseType, intentChangeDetected) {
    // Distribuição de tipos de resposta
    if (!this.responseTypeDistribution) {
      this.responseTypeDistribution = {};
    }
    this.responseTypeDistribution[responseType.type] = 
      (this.responseTypeDistribution[responseType.type] || 0) + 1;
    
    // Contar mudanças de intenção
    if (intentChangeDetected) {
      this.intentChangeCount = (this.intentChangeCount || 0) + 1;
    }
    
    // Contar violações de regras de negócio
    if (responseType.type === 'business_rule_violation') {
      this.businessRuleViolationCount = (this.businessRuleViolationCount || 0) + 1;
    }
  }

  /**
   * 🧠 ANÁLISE CONTEXTUAL DINÂMICA VIA IA - MELHORADA
   * Constrói prompt para IA analisar contexto da conversa com edge cases completos
   */
  buildContextualAnalysisPrompt(conversationHistory) {
    if (!conversationHistory || conversationHistory.length === 0) {
      return `CONTEXTO: Esta é uma nova conversa sem histórico anterior.`;
    }

    // 🗓️ CONTEXTO TEMPORAL para inferência
    const dateUtils = require('../../../utils/dateUtils');
    const currentDate = dateUtils.getCurrentDateBR();
    const today = dateUtils.formatDateBR(currentDate);
    const tomorrow = new Date(currentDate.getTime() + 24*60*60*1000).toLocaleDateString('pt-BR');

    return `## CONTEXTO CRÍTICO PARA ANÁLISE AVANÇADA:

Você precisa analisar o histórico da conversa abaixo para identificar que informações o cliente JÁ FORNECEU 
OU que podem ser INFERIDAS do contexto, evitando perguntar algo que já está claro na conversa.

### TIPOS DE INFORMAÇÃO A DETECTAR E INFERIR:

#### 📋 **INFORMAÇÕES EXPLÍCITAS** (diretamente mencionadas):
- Serviços mencionados (massagem, barba, corte, manicure, etc.)
- Profissionais mencionados (nomes de funcionários)
- Datas mencionadas (hoje, amanhã, dias da semana, datas específicas)
- Horários mencionados (15h, manhã, tarde, horários específicos)
- Preferências ou detalhes específicos

#### 🧠 **INFORMAÇÕES IMPLÍCITAS** (que podem ser inferidas):
- Se ASSISTENTE sugeriu "amanhã de manhã com Jailson" → DATA: amanhã (${tomorrow}), PROFISSIONAL: Jailson, PERÍODO: manhã
- Se ASSISTENTE mencionou serviço específico e cliente não rejeitou → SERVIÇO confirmado
- Se contexto estabeleceu período temporal → mantenha essa referência
- Se cliente pergunta sobre "horários do [profissional]" após sugestão → NÃO perguntar profissional novamente

### REGRAS DE INFERÊNCIA CONTEXTUAL:

1. **CONTINUIDADE TEMPORAL**:
   - "hoje" = ${today}
   - "amanhã" = ${tomorrow}
   - Se assistente sugeriu período específico, cliente assumiu esse contexto

2. **CONTINUIDADE DE PROFISSIONAL**:
   - Se assistente sugeriu profissional e cliente não rejeitou = profissional confirmado
   - Se cliente pergunta sobre horários de profissional específico = profissional já escolhido

3. **CONTINUIDADE DE SERVIÇO**:
   - Se assistente mencionou serviço e cliente não rejeitou = serviço implicitamente aceito
   - Se cliente confirma com "pode ser", "perfeito" = confirmação do serviço

4. **CONTINUIDADE DE DATA/PERÍODO**:
   - Se assistente sugeriu "amanhã de manhã" = data (amanhã) + período (manhã) estabelecidos
   - Se cliente pergunta detalhes mantendo contexto = dados já definidos

### EXEMPLOS DE ANÁLISE INTELIGENTE:

**❌ PERGUNTA DESNECESSÁRIA:**
- Assistente: "amanhã de manhã com Jailson"
- Cliente: "que horários tem o Jailson pela manhã?"
- Sistema pergunta: "para que dia?" ← ERRO! Data já estabelecida (amanhã)

**✅ RESPOSTA INTELIGENTE:**
- Reconhecer: DATA=amanhã, PROFISSIONAL=Jailson, PERÍODO=manhã
- Perguntar apenas: horários específicos disponíveis

**REGRA FUNDAMENTAL**: Se qualquer informação já foi fornecida DIRETAMENTE pelo cliente OU pode ser INFERIDA do contexto da conversa, NÃO pergunte novamente. Use análise contextual inteligente para extrair dados implícitos.`;
  }
}

module.exports = IntentConfirmationNode;
