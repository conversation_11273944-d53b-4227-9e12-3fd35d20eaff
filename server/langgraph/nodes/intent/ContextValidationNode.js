/**
 * ContextValidationNode - Nó de Validação de Contexto
 * 
 * Responsabilidades:
 * - Validar se temos dados suficientes para prosseguir com a intenção
 * - Identificar informações faltantes
 * - Aplicar regras de negócio (horário de funcionamento, etc.)
 * - Determinar se precisamos coletar mais dados do cliente
 */

const BaseNode = require('../../core/BaseNode');

class ContextValidationNode extends BaseNode {
  constructor() {
    super('context_validation', {
      timeout: 5000, // 5s para validação
      retryAttempts: 1
    });
    
    // Regras de negócio do salão
    this.businessRules = {
      workingHours: {
        start: '08:00',
        end: '18:00'
      },
      workingDays: [1, 2, 3, 4, 5, 6], // Segunda a Sábado (1-6, sendo 0 = Domingo)
      minimumAdvanceBooking: 1, // 1 hora de antecedência mínima
      maximumAdvanceBooking: 30 // máximo 30 dias
    };
    
    // Dados obrigatórios por tipo de intenção
    this.requiredDataByIntent = {
      'scheduling': ['service', 'professional_or_time', 'date_or_relative'],
      'cancellation': ['customer_identification', 'appointment_reference'],
      'rescheduling': ['customer_identification', 'appointment_reference', 'new_time_or_date'],
      'inquiry': ['inquiry_type'],
      'greeting': [], // Nenhum dado obrigatório
      'unknown': ['clarification']
    };
  }

  /**
   * Executa validação de contexto
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Obter intenção atual
      const intent = state.intent?.type || 'unknown';
      
      // Analisar contexto atual
      const contextAnalysis = this.analyzeCurrentContext(state, intent);
      
      // Validar regras de negócio
      const businessValidation = this.validateBusinessRules(contextAnalysis, intent);
      
      // Determinar dados faltantes
      const missingData = this.identifyMissingData(contextAnalysis, intent);
      
      // Decidir próxima ação
      const nextAction = this.determineNextAction(contextAnalysis, businessValidation, missingData, intent);
      
      return {
        success: true,
        result: {
          intent,
          contextAnalysis,
          businessValidation,
          missingData,
          isComplete: missingData.length === 0 && businessValidation.isValid,
          nextAction,
          validationTimestamp: new Date()
        },
        nextStage: nextAction.stage,
        updates: {
          context: {
            ...state.context,
            validation: {
              isComplete: missingData.length === 0 && businessValidation.isValid,
              missingData,
              businessValidation
            }
          }
        },
        metadata: {
          debugInfo: {
            intent,
            missingDataCount: missingData.length,
            businessRulesValid: businessValidation.isValid,
            contextCompleteness: this.calculateContextCompleteness(contextAnalysis, intent)
          }
        }
      };
      
    } catch (error) {
      return {
        success: false,
        result: {
          intent: 'unknown',
          isComplete: false,
          error: error.message
        },
        nextStage: 'error',
        updates: {},
        error: {
          message: error.message,
          code: 'CONTEXT_VALIDATION_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Analisa contexto atual extraindo informações disponíveis
   */
  analyzeCurrentContext(state, intent) {
    const analysis = {
      // Dados do agendamento
      service: null,
      professional: null,
      date: null,
      time: null,
      
      // Dados do cliente
      customerName: null,
      customerPhone: state.customerPhone || null,
      
      // Contexto da conversa
      lastUserMessage: this.getLastUserMessage(state.messages),
      conversationLength: state.messages.length,
      
      // Informações extraídas anteriormente
      extractedContext: state.intent?.extractedContext || {},
      schedulingData: state.schedulingData || {},
      
      // Análise temporal
      temporal: this.analyzeTemporalContext(state.messages),
      
      // Análise de entidades mencionadas
      entities: this.extractEntities(state.messages)
    };
    
    // Consolidar informações de diferentes fontes
    this.consolidateInformation(analysis);
    
    return analysis;
  }

  /**
   * Analisa contexto temporal nas mensagens
   */
  analyzeTemporalContext(messages) {
    const originalLast = this.getLastUserMessage(messages) || '';
    const lastUserMessage = originalLast.toLowerCase();
    
    const temporal = {
      relative: null,
      specific: null,
      timeOfDay: null
    };
    
    // Detectar referências relativas
    if (lastUserMessage.includes('hoje')) {
      temporal.relative = 'today';
    } else if (lastUserMessage.includes('amanhã')) {
      temporal.relative = 'tomorrow';
    } else if (lastUserMessage.includes('depois de amanhã')) {
      temporal.relative = 'day_after_tomorrow';
    }
    
    // Detectar horários específicos
    const timeRegex = /(\d{1,2})[:h](\d{2})?/;
    const timeMatch = lastUserMessage.match(timeRegex);
    if (timeMatch) {
      temporal.timeOfDay = timeMatch[0];
    }
    
    // Detectar períodos do dia
    if (lastUserMessage.includes('manhã')) {
      temporal.timeOfDay = temporal.timeOfDay || 'morning';
    } else if (lastUserMessage.includes('tarde')) {
      temporal.timeOfDay = temporal.timeOfDay || 'afternoon';
    } else if (lastUserMessage.includes('noite')) {
      temporal.timeOfDay = temporal.timeOfDay || 'evening';
    }
    
    return temporal;
  }

  /**
   * Extrai entidades mencionadas nas mensagens
   */
  extractEntities(messages) {
    const originalLast = this.getLastUserMessage(messages);
    const lastUserMessage = originalLast.toLowerCase();
    
    const entities = {
      services: [],
      professionals: [],
      locations: []
    };
    
    // Serviços comuns
    const serviceKeywords = {
      'corte': 'corte de cabelo',
      'barba': 'barba',
      'unha': 'manicure',
      'sobrancelha': 'design de sobrancelha',
      'cabelo': 'tratamento capilar',
      'escova': 'escova',
      'progressiva': 'progressiva',
      'tintura': 'coloração'
    };
    
    for (const [keyword, service] of Object.entries(serviceKeywords)) {
      if (lastUserMessage.includes(keyword)) {
        entities.services.push(service);
      }
    }
    
    // Nomes de profissionais (detectar padrões de nome)
    const namePattern = /\b([A-ZÁÉÍÓÚÂÊÔÃÕÇ][a-záéíóúâêôãõç]+)\b/g;
    const matches = originalLast.match(namePattern);
    if (matches) {
      entities.professionals = matches.filter(name => 
        name.length > 2 && !['Com', 'Para', 'Por', 'Que'].includes(name)
      );
    }
    
    return entities;
  }

  /**
   * Consolida informações de diferentes fontes
   */
  consolidateInformation(analysis) {
    // Priorizar informações do contexto extraído da IA
    if (analysis.extractedContext.mentioned_service) {
      analysis.service = analysis.extractedContext.mentioned_service;
    } else if (analysis.entities.services.length > 0) {
      analysis.service = analysis.entities.services[0];
    }
    
    if (analysis.extractedContext.mentioned_professional) {
      analysis.professional = analysis.extractedContext.mentioned_professional;
    } else if (analysis.entities.professionals.length > 0) {
      analysis.professional = analysis.entities.professionals[0];
    }
    
    if (analysis.extractedContext.mentioned_time) {
      analysis.time = analysis.extractedContext.mentioned_time;
    } else if (analysis.temporal.timeOfDay) {
      analysis.time = analysis.temporal.timeOfDay;
    }
    
    if (analysis.extractedContext.mentioned_date) {
      analysis.date = analysis.extractedContext.mentioned_date;
    } else if (analysis.temporal.relative) {
      analysis.date = analysis.temporal.relative;
    }
    
    // Usar dados do schedulingData se disponíveis
    if (analysis.schedulingData.professional?.name) {
      analysis.professional = analysis.schedulingData.professional.name;
    }
    if (analysis.schedulingData.service?.name) {
      analysis.service = analysis.schedulingData.service.name;
    }
    if (analysis.schedulingData.date) {
      analysis.date = analysis.schedulingData.date;
    }
    if (analysis.schedulingData.time) {
      analysis.time = analysis.schedulingData.time;
    }
  }

  /**
   * Valida regras de negócio
   */
  validateBusinessRules(contextAnalysis, intent) {
    const validation = {
      isValid: true,
      violations: [],
      warnings: []
    };
    
    // Validar horário de funcionamento se time foi especificado
    if (contextAnalysis.time) {
      const timeValidation = this.validateWorkingHours(contextAnalysis.time);
      if (!timeValidation.isValid) {
        validation.isValid = false;
        validation.violations.push({
          rule: 'working_hours',
          message: `Horário ${contextAnalysis.time} está fora do funcionamento (${this.businessRules.workingHours.start}-${this.businessRules.workingHours.end})`
        });
      }
    }
    
    // Validar dia de funcionamento se date foi especificado
    if (contextAnalysis.date) {
      const dateValidation = this.validateWorkingDays(contextAnalysis.date);
      if (!dateValidation.isValid) {
        validation.isValid = false;
        validation.violations.push({
          rule: 'working_days',
          message: dateValidation.message
        });
      }
    }
    
    // Validar antecedência mínima para agendamentos
    if (intent === 'scheduling' && contextAnalysis.date && contextAnalysis.time) {
      const advanceValidation = this.validateAdvanceBooking(contextAnalysis.date, contextAnalysis.time);
      if (!advanceValidation.isValid) {
        validation.isValid = false;
        validation.violations.push({
          rule: 'minimum_advance',
          message: advanceValidation.message
        });
      }
    }
    
    return validation;
  }

  /**
   * Identifica dados faltantes baseado na intenção
   */
  identifyMissingData(contextAnalysis, intent) {
    const required = this.requiredDataByIntent[intent] || [];
    const missing = [];
    
    for (const requirement of required) {
      switch (requirement) {
        case 'service':
          if (!contextAnalysis.service) {
            missing.push({
              type: 'service',
              description: 'Tipo de serviço desejado',
              question: 'Que serviço gostaria de agendar?'
            });
          }
          break;
          
        case 'professional_or_time':
          if (!contextAnalysis.professional && !contextAnalysis.time) {
            missing.push({
              type: 'professional_or_time',
              description: 'Profissional preferido ou horário desejado',
              question: 'Tem preferência por algum profissional ou horário?'
            });
          }
          break;
          
        case 'date_or_relative':
          if (!contextAnalysis.date) {
            missing.push({
              type: 'date',
              description: 'Data para o agendamento',
              question: 'Para que dia gostaria de agendar?'
            });
          }
          break;
          
        case 'customer_identification':
          if (!contextAnalysis.customerName && !contextAnalysis.customerPhone) {
            missing.push({
              type: 'customer_identification',
              description: 'Identificação do cliente',
              question: 'Qual seu nome para localizar o agendamento?'
            });
          }
          break;
          
        case 'appointment_reference':
          missing.push({
            type: 'appointment_reference',
            description: 'Referência do agendamento existente',
            question: 'Qual agendamento gostaria de cancelar/alterar?'
          });
          break;
          
        case 'inquiry_type':
          if (!this.identifyInquiryType(contextAnalysis)) {
            missing.push({
              type: 'inquiry_type',
              description: 'Tipo de consulta',
              question: 'Sobre o que gostaria de saber?'
            });
          }
          break;
          
        case 'clarification':
          missing.push({
            type: 'clarification',
            description: 'Esclarecimento da intenção',
            question: 'Como posso ajudar você hoje?'
          });
          break;
      }
    }
    
    return missing;
  }

  /**
   * Determina próxima ação baseada na validação
   */
  determineNextAction(contextAnalysis, businessValidation, missingData, intent) {
    // Se há violações de regras de negócio, informar cliente
    if (!businessValidation.isValid) {
      return {
        stage: 'intent_confirmation',
        action: 'inform_business_rule_violations',
        priority: 'high',
        message: 'Informar sobre restrições de horário/funcionamento'
      };
    }
    
    // Se há dados críticos faltando, coletar
    if (missingData.length > 0) {
      const criticalMissing = missingData.filter(data => 
        ['service', 'date', 'customer_identification'].includes(data.type)
      );
      
      if (criticalMissing.length > 0) {
        return {
          stage: 'intent_confirmation',
          action: 'collect_missing_data',
          priority: 'high',
          missingData: criticalMissing
        };
      }
    }
    
    // Se contexto está completo, prosseguir baseado na intenção
    switch (intent) {
      case 'scheduling':
        return {
          stage: 'scheduling',
          action: 'proceed_with_scheduling',
          priority: 'normal'
        };
        
      case 'cancellation':
      case 'rescheduling':
        return {
          stage: 'scheduling', // Mesmo estágio, ações diferentes
          action: intent === 'cancellation' ? 'proceed_with_cancellation' : 'proceed_with_rescheduling',
          priority: 'normal'
        };
        
      case 'inquiry':
        return {
          stage: 'scheduling', // Responder consulta
          action: 'respond_to_inquiry',
          priority: 'normal'
        };
        
      case 'greeting':
        return {
          stage: 'greeting',
          action: 'continue_greeting',
          priority: 'low'
        };
        
      default:
        return {
          stage: 'intent_confirmation',
          action: 'clarify_intent',
          priority: 'high'
        };
    }
  }

  /**
   * Calcula completude do contexto (0-1)
   */
  calculateContextCompleteness(contextAnalysis, intent) {
    const requiredFields = this.requiredDataByIntent[intent] || [];
    if (requiredFields.length === 0) return 1.0;
    
    let completedFields = 0;
    
    // Verificar cada campo obrigatório
    for (const field of requiredFields) {
      switch (field) {
        case 'service':
          if (contextAnalysis.service) completedFields++;
          break;
        case 'professional_or_time':
          if (contextAnalysis.professional || contextAnalysis.time) completedFields++;
          break;
        case 'date_or_relative':
          if (contextAnalysis.date) completedFields++;
          break;
        case 'customer_identification':
          if (contextAnalysis.customerName || contextAnalysis.customerPhone) completedFields++;
          break;
        default:
          completedFields += 0.5; // Campos mais difíceis de validar automaticamente
      }
    }
    
    return completedFields / requiredFields.length;
  }

  /**
   * Valida horário de funcionamento
   */
  validateWorkingHours(time) {
    // Converter time para formato comparável
    const timeStr = this.normalizeTime(time);
    if (!timeStr) {
      return { isValid: false, message: 'Horário inválido' };
    }
    
    const [hour, minute] = timeStr.split(':').map(Number);
    const timeMinutes = hour * 60 + minute;
    
    const [startHour, startMinute] = this.businessRules.workingHours.start.split(':').map(Number);
    const startMinutes = startHour * 60 + startMinute;
    
    const [endHour, endMinute] = this.businessRules.workingHours.end.split(':').map(Number);
    const endMinutes = endHour * 60 + endMinute;
    
    return {
      isValid: timeMinutes >= startMinutes && timeMinutes <= endMinutes,
      message: timeMinutes < startMinutes ? 'Muito cedo' : 'Muito tarde'
    };
  }

  /**
   * Valida dias de funcionamento
   */
  validateWorkingDays(date) {
    // Para datas relativas
    if (date === 'today' || date === 'tomorrow' || date === 'day_after_tomorrow') {
      const targetDate = new Date();
      if (date === 'tomorrow') targetDate.setDate(targetDate.getDate() + 1);
      if (date === 'day_after_tomorrow') targetDate.setDate(targetDate.getDate() + 2);
      
      const dayOfWeek = targetDate.getDay();
      const isWorkingDay = this.businessRules.workingDays.includes(dayOfWeek);
      
      return {
        isValid: isWorkingDay,
        message: isWorkingDay ? '' : 'Não funcionamos neste dia'
      };
    }
    
    // Para outras datas, assumir válido por ora
    return { isValid: true, message: '' };
  }

  /**
   * Valida antecedência mínima
   */
  validateAdvanceBooking(date, time) {
    try {
      const norm = this.normalizeTime(time);
      if (!norm) return { isValid: false, message: 'Horário inválido' };
      const [h, m] = norm.split(':').map(Number);
      const now = new Date();
      const target = new Date();
      // Ajustar data relativa
      if (date === 'tomorrow') target.setDate(now.getDate() + 1);
      if (date === 'day_after_tomorrow') target.setDate(now.getDate() + 2);
      // Datas absolutas simples YYYY-MM-DD
      if (/^\d{4}-\d{2}-\d{2}$/.test(date || '')) {
        const [Y, Mo, D] = (date || '').split('-').map(Number);
        target.setFullYear(Y, (Mo - 1), D);
      }
      target.setHours(h, m, 0, 0);
      const diffMin = (target.getTime() - now.getTime()) / 60000;
      if (diffMin < 60) {
        return { isValid: false, message: 'Antecedência mínima de 1 hora' };
      }
      return { isValid: true, message: '' };
    } catch (_) {
      return { isValid: true, message: '' };
    }
  }

  /**
   * Identifica tipo de consulta
   */
  identifyInquiryType(contextAnalysis) {
    const message = contextAnalysis.lastUserMessage.toLowerCase();
    
    if (message.includes('preço') || message.includes('valor') || message.includes('custa')) {
      return 'price_inquiry';
    }
    if (message.includes('horário') || message.includes('vaga') || message.includes('disponível')) {
      return 'availability_inquiry';
    }
    if (message.includes('serviço') || message.includes('fazem')) {
      return 'service_inquiry';
    }
    
    return null;
  }

  /**
   * Normaliza formato de horário
   */
  normalizeTime(time) {
    if (typeof time !== 'string') return null;
    
    // Tentar diferentes formatos
    const timePatterns = [
      /^(\d{1,2}):(\d{2})$/,           // 14:30
      /^(\d{1,2})h(\d{2})?$/,         // 14h30 ou 14h
      /^(\d{1,2})$/                    // 14
    ];
    
    for (const pattern of timePatterns) {
      const match = time.match(pattern);
      if (match) {
        const hour = parseInt(match[1]);
        const minute = parseInt(match[2] || '0');
        
        if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
          return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
        }
      }
    }
    
    return null;
  }
}

module.exports = ContextValidationNode;
