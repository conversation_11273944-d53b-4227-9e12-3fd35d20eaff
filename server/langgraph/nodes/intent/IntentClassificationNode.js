/**
 * IntentClassificationNode - Nó de Classificação de Intenções
 * 
 * Substitui: detectSchedulingIntentWithAI() do sistema atual
 * 
 * Responsabilidades:
 * - Classificar intenção da conversa com alta precisão
 * - Detectar mudanças de intenção durante a conversa
 * - Fornecer confidence score para cada classificação
 * - Extrair contexto relevante para a intenção detectada
 */

const BaseNode = require('../../core/BaseNode');
const Anthropic = require('@anthropic-ai/sdk');
const { errorLogger, ERROR_CATEGORIES } = require('../../../utils/ErrorLogger');
const messageLogger = require('../../../utils/messageLogger');
const ClaudeEvaluationLogger = require('../../../utils/ClaudeEvaluationLogger');

class IntentClassificationNode extends BaseNode {
  constructor() {
    super('intent_classification', {
      timeout: 15000, // 15s para classificação de intenção
      retryAttempts: 2
    });
    
    // Inicializar cliente Anthropic
    this.anthropic = process.env.ANTHROPIC_API_KEY ? 
      new Anthropic({ apiKey: process.env.ANTHROPIC_API_KEY }) : null;
    
    // Inicializar sistema de avaliação Claude
    this.evaluationLogger = new ClaudeEvaluationLogger();
    
    // Definir intenções suportadas
    this.supportedIntents = [
      'scheduling',    // Agendamento de serviços
      'cancellation',  // Cancelamento de agendamentos
      'rescheduling',  // Reagendamento
      'inquiry',       // Consultas sobre horários/serviços
      'greeting',      // Saudações e conversas casuais
      'abandonment',   // Desistir de nova solicitação
      'unknown'        // Intenção não identificada
    ];
    
    // Cache de classificações recentes
    this.classificationCache = new Map();
    this.cacheExpiration = 5 * 60 * 1000; // 5 minutos
  }

  /**
   * Executa classificação de intenção
   */
  async execute(input) {
    const { customerPhone, state, message } = input;
    
    try {
      // Preparar contexto da conversa
      const conversationContext = this.prepareConversationContext(state);
      
      // Verificar cache primeiro
      const cacheKey = this.generateCacheKey(conversationContext.recentMessages);
      const cachedResult = this.getCachedClassification(cacheKey);
      
      if (cachedResult) {
        this.logExecution('DEBUG', customerPhone, {
          action: 'cache_hit',
          intent: cachedResult.intent
        });
        
        const normalized = this.normalizeClassification(cachedResult);
        return {
          success: true,
          result: normalized,
          nextStage: this.determineNextStage(normalized.type, normalized.confidence),
          updates: {
            intent: normalized,
            context: {
              intent: normalized,
              intentHistory: this.appendIntentHistory(state?.context?.intentHistory, normalized)
            }
          }
        };
      }
      
      // Classificar intenção via IA
      let classification;
      if (this.anthropic) {
        classification = await this.classifyWithClaude(conversationContext);
      } else {
        const error = new Error('Claude API não disponível - sistema configurado para falhar sem IA');
        await errorLogger.logError(error, ERROR_CATEGORIES.IA_ERROR, {
          function: 'execute',
          nodeId: this.nodeId,
          hasAnthropic: !!this.anthropic,
          conversationLength: conversationContext?.length || 0
        });
        throw error;
      }
      
      // Validar classificação
      const validatedClassification = this.validateClassification(classification);
      
      // Salvar no cache
      this.setCachedClassification(cacheKey, validatedClassification);
      const normalized = this.normalizeClassification(validatedClassification);
      
      return {
        success: true,
        result: normalized,
        nextStage: this.determineNextStage(normalized.type, normalized.confidence),
        updates: {
          intent: normalized,
          context: {
            intent: normalized,
            intentHistory: this.appendIntentHistory(state?.context?.intentHistory, normalized)
          }
        },
        metadata: {
          debugInfo: {
            usedAI: !!this.anthropic,
            cacheUsed: false,
            conversationLength: conversationContext.recentMessages.length
          }
        }
      };
      
    } catch (error) {
      // Em caso de erro, usar classificação de emergência
      const emergencyClassification = this.getEmergencyClassification(input);
      
      const normalizedEmergency = this.normalizeClassification(emergencyClassification);
      return {
        success: false,
        result: normalizedEmergency,
        nextStage: 'context_validation',
        updates: {
          intent: normalizedEmergency,
          context: {
            intent: normalizedEmergency,
            intentHistory: this.appendIntentHistory(state?.context?.intentHistory, normalizedEmergency)
          }
        },
        error: {
          message: error.message,
          code: 'INTENT_CLASSIFICATION_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Prepara contexto da conversa para classificação
   */
  prepareConversationContext(state) {
    // Pegar últimas 10 mensagens para contexto
    const recentMessages = state.messages.slice(-10);
    
    // Extrair apenas mensagens do usuário para análise de intenção
    const userMessages = recentMessages.filter(msg => msg.role === 'user');
    const lastUserMessage = userMessages[userMessages.length - 1]?.content || '';
    
    // Preparar texto da conversa
    const conversationText = recentMessages
      .map(msg => `${msg.role}: ${msg.content}`)
      .join('\n');
    
    return {
      recentMessages,
      userMessages,
      lastUserMessage,
      conversationText,
      messageCount: recentMessages.length,
      currentStage: state.currentStage
    };
  }

  /**
   * Classificação usando Claude AI
   */
  async classifyWithClaude(context) {
    const prompt = this.buildClassificationPrompt(context);
    
    // ✨ Interceptar chamada Claude com sistema de avaliação automática
    const claudeParams = {
      model: 'claude-sonnet-4-20250514',
      max_tokens: 200,
      temperature: 0.1,
      messages: [{
        role: 'user',
        content: prompt
      }]
    }
    
    const startTime = Date.now();
    const response = await this.callExternalService('claude', async () => {
      return await this.evaluationLogger.interceptClaudeCall(
        claudeParams,
        (params) => this.anthropic.messages.create(params),
        {
          source: 'langgraph_intent_classification',
          sessionId: messageLogger.sessionId || 'unknown-session',
          interactionType: 'intent_classification'
        }
      );
    });
    const duration = Date.now() - startTime;
    
    const content = response.content[0]?.text;
    
    // Log da chamada da IA
    messageLogger.logAIInteraction(
      'IntentClassification',
      prompt,
      content,
      duration,
      'claude-sonnet-4-20250514'
    );
    if (!content) {
      const error = new Error('Empty response from Claude API');
      await errorLogger.logError(error, ERROR_CATEGORIES.IA_ERROR, {
        function: 'classifyWithClaude',
        nodeId: this.nodeId,
        response: response,
        conversationLength: conversationContext?.length || 0
      });
      throw error;
    }
    
    // Parsear resposta JSON (remover markdown se presente)
    try {
      let jsonContent = content;
      
      // Remover blocos de código markdown se presentes
      if (content.includes('```json')) {
        const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/);
        if (jsonMatch) {
          jsonContent = jsonMatch[1];
        }
      } else if (content.includes('```')) {
        const codeMatch = content.match(/```\n([\s\S]*?)\n```/);
        if (codeMatch) {
          jsonContent = codeMatch[1];
        }
      }
      
      const classification = JSON.parse(jsonContent);
      return {
        intent: classification.intent,
        confidence: classification.confidence,
        reasoning: classification.reasoning,
        extractedContext: classification.context || {},
        method: 'ai',
        timestamp: new Date()
      };
    } catch (parseError) {
      throw new Error(`Failed to parse Claude response: ${parseError.message}`);
    }
  }

  /**
   * Monta prompt para classificação via IA - SINCRONIZADO COM ai.js
   */
  buildClassificationPrompt(context) {
    // 🌎 CONTEXTO TEMPORAL E GEOGRÁFICO COMPLETO
    const dateUtils = require('../../../utils/dateUtils');
    const currentDate = dateUtils.getCurrentDateBR();
    const today = dateUtils.formatDateBR(currentDate);
    const todayISO = dateUtils.getDateStringBR(currentDate);
    const tomorrow = new Date(currentDate.getTime() + 24*60*60*1000).toLocaleDateString('pt-BR');
    const tomorrowISO = new Date(currentDate.getTime() + 24*60*60*1000).toISOString().split('T')[0];
    
    // Período do dia e contexto cultural
    const currentHour = currentDate.getHours();
    let periodOfDay = '';
    if (currentHour >= 6 && currentHour < 12) {
      periodOfDay = 'manhã';
    } else if (currentHour >= 12 && currentHour < 18) {
      periodOfDay = 'tarde';
    } else if (currentHour >= 18 && currentHour < 22) {
      periodOfDay = 'início da noite';
    } else {
      periodOfDay = 'noite';
    }
    
    const currentTime = dateUtils.formatDateTimeBR(currentDate).split(' ')[1];
    
    return `Você é um especialista em análise de conversas para salões de beleza brasileiros.

## 🌎 CONTEXTO TEMPORAL E GEOGRÁFICO
**LOCALIZAÇÃO:** Rio de Janeiro, Brasil (America/Sao_Paulo timezone)
**DATA ATUAL:** ${today} (${todayISO})
**HORÁRIO ATUAL:** ${currentTime} (${periodOfDay})
**CONTEXTO CULTURAL:** Salão de beleza brasileiro, clientes falam português carioca

Analise esta conversa e classifique a INTENÇÃO PRINCIPAL do cliente:

CONVERSA:
${context.conversationText}

ÚLTIMA MENSAGEM: "${context.lastUserMessage}"

INTENÇÕES POSSÍVEIS:
- "scheduling": Cliente quer AGENDAR um serviço
- "cancellation": Cliente quer CANCELAR um agendamento existente  
- "rescheduling": Cliente quer REAGENDAR/ALTERAR um agendamento
- "inquiry": Cliente quer CONSULTAR disponibilidade para NOVOS agendamentos
- "greeting": Cliente está apenas cumprimentando ou conversando casualmente
- "abandonment": Cliente está DESISTINDO de uma nova solicitação (não cancelando agendamento existente)
- "unknown": Intenção não está clara ou é sobre agendamento já mencionado

## CONTEXTO IMPORTANTE E REGRAS CRÍTICAS:

### 1. **CONTEXTO DE CONTINUIDADE** - Esta é uma conversa contínua:
   - Se já foi mencionado "massagem relaxante" e cliente diz "relaxante mesmo" = CONFIRMA o serviço
   - Se já foi perguntado sobre profissional e cliente escolheu = USE essa informação
   - Se cliente menciona "depois do meu outro serviço" = CONTEXTO TEMPORAL dos agendamentos futuros

### 2. **INTENÇÃO** - Analise cuidadosamente o contexto - SEJA MAIS INCLUSIVO para detectar agendamentos:
   - **"scheduling"**: QUALQUER menção de:
     * Agendamento direto: "quero agendar", "marcar", "agendar", "reservar"  
     * Interesse em horário: "gostaria de ver um horário", "quero ver", "pode ser"
     * Menciona serviço + tempo: "barba amanhã", "corte na segunda", "manicure às 15h"
     * Disponibilidade: "tem horário?", "está livre?", "pode fazer?"
     * Preferência: "prefere com a Ágata", "pode ser com", "quero com"
     * Confirmação/continuidade: "relaxante mesmo", "pode ser", "perfeito", "quero esse"
     * **REFERÊNCIAS TEMPORAIS RELATIVAS**: "depois dele", "após", "na sequência", "depois do meu outro serviço"
   - **"cancellation"**: "cancelar", "desmarcar", "não vou poder" REFERINDO-SE A AGENDAMENTO EXISTENTE
   - **"rescheduling"**: "trocar horário", "mudar dia", "reagendar"
   - **"inquiry"**: "tem vaga?", "quanto custa?", "horários?" para NOVOS agendamentos
   - **"greeting"**: "oi", "olá", "tudo bem?" SEM contexto de serviço/data
   - **"abandonment"**: "deixa para lá", "esquece", "não precisa mais" REFERINDO-SE A NOVA SOLICITAÇÃO
   - **"unknown"**: Conversa JÁ MENCIONA agendamento futuro e cliente pergunta sobre ele

### 3. **EXEMPLOS DE INTENÇÃO EXPANDIDOS**:
   - "Oi" / "Olá" / "Tudo bem?" → "greeting" (apenas saudação)
   - "Quero marcar barba amanhã" → "scheduling"
   - "Gostaria de ver um horário para barba" → "scheduling" 
   - "Pode ser com a Ágata amanhã?" → "scheduling"
   - "Tem horário livre sábado?" → "scheduling"
   - "Como funciona o salão?" → "unknown" (pergunta geral sem serviço)
   - "Cancelar agendamento" → "cancellation"
   - "Que horários tem o Jailson pela manhã?" (após sugestão de "amanhã com Jailson") → "inquiry"

### 4. **PRESERVAÇÃO DE DADOS** - Se já existem dados coletados, USE-OS:
   - Se serviço já foi identificado e cliente confirma ("relaxante mesmo") = mantenha
   - Se profissional já foi escolhido = mantenha
   - APENAS sobrescreva se cliente mencionar algo DIFERENTE

### 5. **REFERÊNCIA TEMPORAL** - Para análise contextual:
   - "hoje" = ${today} (${todayISO})
   - "amanhã" = ${tomorrow} (${tomorrowISO})
   - Use essas referências para classificação temporal

### 6. **ABANDONMENT vs CANCELLATION**:
   - "deixa para lá" após discussão de NOVO agendamento = abandonment
   - "cancelar" referindo-se a agendamento JÁ EXISTENTE = cancellation
   - Analise o contexto: se cliente estava tentando agendar algo novo e desistiu = abandonment

### 7. **CONFIDENCE LEVELS**:
   - 0.9+ = muito confiante
   - 0.7-0.8 = confiante
   - 0.5-0.6 = incerto
   - <0.5 = muito incerto
   - Se há mudança de intenção, use a mais recente

Responda APENAS com JSON válido:

{
  "intent": "scheduling|cancellation|rescheduling|inquiry|greeting|abandonment|unknown",
  "confidence": 0.95,
  "reasoning": "Explicação clara da decisão baseada nas regras críticas acima",
  "context": {
    "mentioned_service": "nome do serviço se mencionado",
    "mentioned_professional": "nome do profissional se mencionado", 
    "mentioned_time": "horário mencionado",
    "mentioned_date": "data mencionada"
  }
}`;
  }

  /**
   * Classificação baseada em regras (fallback)
   */
  classifyWithRules(context) {
    const lastMessage = context.lastUserMessage.toLowerCase();
    
    // Palavras-chave para cada intenção
    const keywords = {
      scheduling: ['agendar', 'marcar', 'quero fazer', 'preciso fazer', 'gostaria de', 'vou querer'],
      cancellation: ['cancelar', 'desmarcar', 'não vou poder', 'não posso mais', 'desfazer'],
      rescheduling: ['trocar', 'mudar', 'reagendar', 'alterar', 'outro horário', 'outro dia'],
      inquiry: ['tem vaga', 'está livre', 'horário', 'preço', 'quanto custa', 'disponível'],
      greeting: ['oi', 'olá', 'ola', 'hey', 'tudo bem', 'bom dia', 'boa tarde', 'boa noite']
    };
    
    let bestMatch = { intent: 'unknown', score: 0 };
    
    // Calcular scores para cada intenção
    for (const [intent, words] of Object.entries(keywords)) {
      let score = 0;
      for (const word of words) {
        if (lastMessage.includes(word)) {
          score += 1;
        }
      }
      
      if (score > bestMatch.score) {
        bestMatch = { intent, score };
      }
    }
    
    // Determinar confidence baseado no score
    const confidence = bestMatch.score > 0 ? 
      Math.min(0.8, 0.5 + (bestMatch.score * 0.1)) : 0.3;
    
    return {
      intent: bestMatch.intent,
      confidence,
      reasoning: `Classificação por regras: encontradas ${bestMatch.score} palavras-chave`,
      extractedContext: this.extractContextFromMessage(lastMessage),
      method: 'rules',
      timestamp: new Date()
    };
  }

  /**
   * Extrai contexto básico da mensagem
   */
  extractContextFromMessage(message) {
    const context = {};
    
    // Detectar menções a serviços comuns
    const services = ['corte', 'barba', 'unha', 'sobrancelha', 'cabelo', 'escova', 'progressiva'];
    for (const service of services) {
      if (message.includes(service)) {
        context.mentioned_service = service;
        break;
      }
    }
    
    // Detectar menções a horários
    const timeRegex = /(\d{1,2})[:h](\d{2})?/;
    const timeMatch = message.match(timeRegex);
    if (timeMatch) {
      context.mentioned_time = timeMatch[0];
    }
    
    // Detectar menções a dias
    const dayKeywords = ['hoje', 'amanhã', 'segunda', 'terça', 'quarta', 'quinta', 'sexta', 'sábado', 'domingo'];
    for (const day of dayKeywords) {
      if (message.includes(day)) {
        context.mentioned_date = day;
        break;
      }
    }
    
    return context;
  }

  /**
   * Valida e normaliza classificação
   */
  validateClassification(classification) {
    // Validar intent
    if (!this.supportedIntents.includes(classification.intent)) {
      classification.intent = 'unknown';
      classification.confidence = Math.min(classification.confidence, 0.3);
    }
    
    // Validar confidence
    classification.confidence = Math.max(0, Math.min(1, classification.confidence));
    
    // Garantir campos obrigatórios
    classification.extractedContext = classification.extractedContext || {};
    classification.reasoning = classification.reasoning || 'Classificação sem detalhes';
    classification.timestamp = classification.timestamp || new Date();
    
    return classification;
  }

  /**
   * Normaliza para o formato esperado pelo StateManager.intent
   */
  normalizeClassification(classification) {
    return {
      type: classification.intent,
      confidence: classification.confidence,
      extractedAt: classification.timestamp || new Date(),
      context: classification.extractedContext || classification.context || {},
      method: classification.method || 'rules',
      reasoning: classification.reasoning || undefined
    };
  }

  appendIntentHistory(history = [], normalized) {
    const next = Array.isArray(history) ? history.slice() : [];
    next.push({ type: normalized.type, confidence: normalized.confidence, at: new Date() });
    return next.slice(-10);
  }

  /**
   * Determina próximo estágio baseado na intenção
   */
  determineNextStage(intent, confidence) {
    // Se confidence muito baixa, validar contexto
    if (confidence < 0.6) {
      return 'context_validation';
    }
    
    // Roteamento baseado na intenção
    switch (intent) {
      case 'scheduling':
        return 'context_validation'; // Validar dados antes de prosseguir
      case 'cancellation':
      case 'rescheduling':
        return 'context_validation'; // Validar dados do agendamento existente
      case 'inquiry':
        return 'context_validation'; // Validar que informações quer consultar
      case 'greeting':
        return 'greeting'; // Manter no estágio de saudação
      case 'unknown':
      default:
        return 'context_validation'; // Tentar extrair mais informações
    }
  }

  /**
   * Classificação de emergência em caso de erro
   */
  getEmergencyClassification(input) {
    return {
      intent: 'unknown',
      confidence: 0.1,
      reasoning: 'Classificação de emergência devido a erro no sistema',
      extractedContext: {},
      method: 'emergency',
      timestamp: new Date()
    };
  }

  /**
   * Cache de classificações
   */
  generateCacheKey(messages) {
    const lastThreeMessages = messages.slice(-3)
      .map(m => `${m.role}:${m.content}`)
      .join('|');
    return Buffer.from(lastThreeMessages).toString('base64').slice(0, 50);
  }

  getCachedClassification(cacheKey) {
    const cached = this.classificationCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp < this.cacheExpiration)) {
      return cached.classification;
    }
    return null;
  }

  setCachedClassification(cacheKey, classification) {
    this.classificationCache.set(cacheKey, {
      classification,
      timestamp: Date.now()
    });
    
    // Limpar cache antigo
    if (this.classificationCache.size > 100) {
      const oldestKey = this.classificationCache.keys().next().value;
      this.classificationCache.delete(oldestKey);
    }
  }

  /**
   * Obtém estatísticas de classificação
   */
  getClassificationStats() {
    const cacheEntries = Array.from(this.classificationCache.values());
    const intentCounts = cacheEntries.reduce((acc, entry) => {
      acc[entry.classification.intent] = (acc[entry.classification.intent] || 0) + 1;
      return acc;
    }, {});
    
    return {
      cacheSize: this.classificationCache.size,
      intentDistribution: intentCounts,
      averageConfidence: cacheEntries.length > 0 ?
        cacheEntries.reduce((sum, entry) => sum + entry.classification.confidence, 0) / cacheEntries.length : 0
    };
  }
}

module.exports = IntentClassificationNode;
