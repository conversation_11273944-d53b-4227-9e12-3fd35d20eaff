/**
 * IntentRoutingNode - Nó de Roteamento de Intenções
 * 
 * Responsabilidades:
 * - Rotear para subgrafos especializados baseado na intenção
 * - Determinar o fluxo apropriado para cada intenção
 * - Configurar contexto específico para cada subgrafo
 * - Validar pré-requisitos antes do roteamento
 * - Gerenciar transições entre diferentes fluxos
 */

const BaseNode = require('../../core/BaseNode');

class IntentRoutingNode extends BaseNode {
  constructor() {
    super('intent_routing', {
      timeout: 3000, // 3s para roteamento (rápido)
      retryAttempts: 1
    });
    
    // Mapeamento de intenções para subgrafos
    this.intentRoutes = {
      'scheduling': {
        targetGraph: 'SchedulingGraph',
        targetStage: 'customer_identification',
        requiredData: ['intent'],
        description: 'Fluxo de agendamento de novos serviços'
      },
      
      'confirmation': {
        targetGraph: 'ConfirmationGraph', 
        targetStage: 'appointment_display',
        requiredData: ['intent', 'appointments'],
        description: 'Fluxo de confirmação de agendamentos existentes'
      },
      
      'cancellation': {
        targetGraph: 'CancellationGraph',
        targetStage: 'appointment_selection',
        requiredData: ['intent', 'appointments'],
        description: 'Fluxo de cancelamento de agendamentos'
      },
      
      'rescheduling': {
        targetGraph: 'ReschedulingGraph',
        targetStage: 'reschedule_detection',
        requiredData: ['intent', 'appointments'],
        description: 'Fluxo de reagendamento (cancelar + agendar novo)'
      },
      
      'inquiry': {
        targetGraph: 'InquiryGraph',
        targetStage: 'inquiry_processing',
        requiredData: ['intent'],
        description: 'Fluxo de consultas sobre disponibilidade/preços'
      },
      
      'greeting': {
        targetGraph: 'GreetingGraph',
        targetStage: 'greeting_response',
        requiredData: ['intent'],
        description: 'Fluxo de saudações e conversas casuais'
      },
      
      'unknown': {
        targetGraph: 'IntentGraph',
        targetStage: 'intent_confirmation',
        requiredData: [],
        description: 'Volta para classificação de intenção'
      }
    };
    
    // Estatísticas de roteamento
    this.routingStats = new Map();
  }

  /**
   * Executa roteamento baseado na intenção
   */
  async execute(input) {
    const { customerPhone, state } = input;
    
    try {
      // Analisar intenção atual
      const intentAnalysis = this.analyzeIntent(state);
      
      // Validar pré-requisitos para roteamento
      const prerequisiteValidation = this.validatePrerequisites(intentAnalysis, state);
      
      if (!prerequisiteValidation.isValid) {
        return this.handleMissingPrerequisites(prerequisiteValidation, state, customerPhone);
      }
      
      // Determinar rota apropriada
      const route = this.determineRoute(intentAnalysis);
      
      // Preparar contexto para o subgrafo de destino
      const routingContext = this.prepareRoutingContext(intentAnalysis, state, route);
      
      // Registrar roteamento para estatísticas
      this.recordRouting(intentAnalysis.intent, route.targetGraph);
      
      return {
        success: true,
        result: {
          intent: intentAnalysis.intent,
          targetGraph: route.targetGraph,
          targetStage: route.targetStage,
          route: route,
          routingContext,
          routingDecision: {
            reason: `Intenção '${intentAnalysis.intent}' roteada para ${route.targetGraph}`,
            confidence: intentAnalysis.confidence,
            prerequisites: prerequisiteValidation.satisfiedRequirements
          }
        },
        nextStage: route.targetStage,
        updates: {
          context: {
            ...state.context,
            routing: routingContext,
            currentGraph: route.targetGraph
          }
        },
        metadata: {
          debugInfo: {
            intent: intentAnalysis.intent,
            confidence: intentAnalysis.confidence,
            targetGraph: route.targetGraph,
            targetStage: route.targetStage,
            prerequisitesMet: prerequisiteValidation.isValid,
            routingTime: Date.now()
          }
        }
      };
      
    } catch (error) {
      // Em caso de erro, rotear para intent_confirmation como fallback
      return {
        success: false,
        result: {
          intent: 'unknown',
          targetGraph: 'IntentGraph',
          targetStage: 'intent_confirmation',
          error: error.message
        },
        nextStage: 'intent_confirmation',
        updates: {},
        error: {
          message: error.message,
          code: 'INTENT_ROUTING_ERROR',
          recoverable: true
        }
      };
    }
  }

  /**
   * Analisa intenção atual do estado
   */
  analyzeIntent(state) {
    const intent = state.intent?.type || state.context?.intent?.intent || 'unknown';
    const confidence = state.intent?.confidence || state.context?.intent?.confidence || 0;
    const reasoning = state.intent?.reasoning || state.context?.intent?.reasoning || '';
    const extractedContext = state.intent?.extractedContext || state.context?.intent?.extractedContext || {};
    
    return {
      intent,
      confidence,
      reasoning,
      extractedContext,
      isValid: confidence > 0.5,
      hasHighConfidence: confidence > 0.8,
      timestamp: new Date()
    };
  }

  /**
   * Valida pré-requisitos para roteamento
   */
  validatePrerequisites(intentAnalysis, state) {
    const route = this.intentRoutes[intentAnalysis.intent];
    
    if (!route) {
      return {
        isValid: false,
        missingRequirements: [`Rota não encontrada para intenção: ${intentAnalysis.intent}`],
        satisfiedRequirements: []
      };
    }
    
    const validation = {
      isValid: true,
      missingRequirements: [],
      satisfiedRequirements: []
    };
    
    // Verificar cada requisito
    for (const requirement of route.requiredData) {
      switch (requirement) {
        case 'intent':
          if (intentAnalysis.isValid) {
            validation.satisfiedRequirements.push('intent');
          } else {
            validation.isValid = false;
            validation.missingRequirements.push('Intenção não válida ou com baixa confiança');
          }
          break;
          
        case 'appointments':
          if (state.context?.appointments || state.appointments) {
            validation.satisfiedRequirements.push('appointments');
          } else {
            validation.isValid = false;
            validation.missingRequirements.push('Dados de agendamentos não disponíveis');
          }
          break;
          
        case 'customer_data':
          if (state.customerPhone) {
            validation.satisfiedRequirements.push('customer_data');
          } else {
            validation.isValid = false;
            validation.missingRequirements.push('Dados do cliente não disponíveis');
          }
          break;
          
        default:
          // Requisito desconhecido - assumir satisfeito
          validation.satisfiedRequirements.push(requirement);
      }
    }
    
    return validation;
  }

  /**
   * Determina rota apropriada para a intenção
   */
  determineRoute(intentAnalysis) {
    const baseRoute = this.intentRoutes[intentAnalysis.intent];
    
    if (!baseRoute) {
      // Fallback para unknown
      return this.intentRoutes['unknown'];
    }
    
    // Criar cópia da rota base
    const route = { ...baseRoute };
    
    // Ajustar rota baseado no contexto específico
    route.adjustments = this.calculateRouteAdjustments(intentAnalysis);
    
    return route;
  }

  /**
   * Calcula ajustes na rota baseado no contexto
   */
  calculateRouteAdjustments(intentAnalysis) {
    const adjustments = {
      priority: 'normal',
      specialHandling: [],
      contextFlags: []
    };
    
    // Ajustes baseados na confiança
    if (intentAnalysis.confidence < 0.6) {
      adjustments.specialHandling.push('low_confidence_intent');
      adjustments.priority = 'low';
    } else if (intentAnalysis.confidence > 0.9) {
      adjustments.priority = 'high';
    }
    
    // Ajustes baseados no contexto extraído
    if (intentAnalysis.extractedContext.mentioned_professional) {
      adjustments.contextFlags.push('professional_specified');
    }
    if (intentAnalysis.extractedContext.mentioned_service) {
      adjustments.contextFlags.push('service_specified');
    }
    if (intentAnalysis.extractedContext.mentioned_time) {
      adjustments.contextFlags.push('time_specified');
    }
    
    return adjustments;
  }

  /**
   * Prepara contexto específico para o subgrafo de destino
   */
  prepareRoutingContext(intentAnalysis, state, route) {
    const context = {
      // Informações da rota
      sourceGraph: 'IntentGraph',
      targetGraph: route.targetGraph,
      targetStage: route.targetStage,
      routedAt: new Date(),
      
      // Dados da intenção
      originalIntent: intentAnalysis.intent,
      intentConfidence: intentAnalysis.confidence,
      intentReasoning: intentAnalysis.reasoning,
      
      // Contexto extraído
      extractedData: intentAnalysis.extractedContext,
      
      // Ajustes da rota
      routeAdjustments: route.adjustments,
      
      // Histórico de roteamento
      routingHistory: this.updateRoutingHistory(state.context?.routing?.routingHistory || [], {
        from: 'IntentGraph',
        to: route.targetGraph,
        intent: intentAnalysis.intent,
        timestamp: new Date()
      })
    };
    
    // Adicionar dados específicos baseado na intenção
    switch (intentAnalysis.intent) {
      case 'scheduling':
        context.schedulingContext = {
          isNewAppointment: true,
          preferredProfessional: intentAnalysis.extractedContext.mentioned_professional,
          preferredService: intentAnalysis.extractedContext.mentioned_service,
          preferredTime: intentAnalysis.extractedContext.mentioned_time,
          preferredDate: intentAnalysis.extractedContext.mentioned_date
        };
        break;
        
      case 'cancellation':
      case 'rescheduling':
        context.appointmentContext = {
          action: intentAnalysis.intent,
          appointmentData: state.context?.appointments,
          requiresSelection: (state.context?.appointments?.futureAppointments || 0) > 1
        };
        break;
        
      case 'confirmation':
        context.confirmationContext = {
          pendingAppointments: state.context?.appointments?.appointments?.filter(apt => apt.isPending) || []
        };
        break;
        
      case 'inquiry':
        context.inquiryContext = {
          inquiryType: this.detectInquiryType(intentAnalysis.extractedContext),
          specificData: intentAnalysis.extractedContext
        };
        break;
    }
    
    return context;
  }

  /**
   * Detecta tipo de consulta
   */
  detectInquiryType(extractedContext) {
    if (extractedContext.mentioned_service && !extractedContext.mentioned_time) {
      return 'service_availability';
    }
    if (extractedContext.mentioned_professional && !extractedContext.mentioned_time) {
      return 'professional_schedule';
    }
    if (extractedContext.mentioned_time || extractedContext.mentioned_date) {
      return 'time_availability';
    }
    return 'general_inquiry';
  }

  /**
   * Atualiza histórico de roteamento
   */
  updateRoutingHistory(currentHistory, newEntry) {
    const history = [...currentHistory, newEntry];
    
    // Manter apenas últimas 10 entradas
    return history.slice(-10);
  }

  /**
   * Lida com pré-requisitos faltantes
   */
  handleMissingPrerequisites(validation, state, customerPhone) {
    const missingData = validation.missingRequirements;
    
    this.logExecution('WARN', customerPhone, {
      action: 'prerequisites_not_met',
      missingRequirements: missingData
    });
    
    // Determinar ação para resolver pré-requisitos
    if (missingData.includes('Dados de agendamentos não disponíveis')) {
      // Rotear para buscar agendamentos primeiro
      return {
        success: true,
        result: {
          intent: state.intent?.type || 'unknown',
          targetGraph: 'AppointmentGraph',
          targetStage: 'appointment_retrieval',
          reason: 'Buscando agendamentos antes de prosseguir'
        },
        nextStage: 'appointment_retrieval',
        updates: {
          context: {
            ...state.context,
            pendingRouting: {
              originalIntent: state.intent?.type,
              targetAfterPrerequisites: this.intentRoutes[state.intent?.type]?.targetGraph
            }
          }
        }
      };
    }
    
    // Para outros casos, voltar para confirmação de intenção
    return {
      success: true,
      result: {
        intent: 'unknown',
        targetGraph: 'IntentGraph',
        targetStage: 'intent_confirmation',
        reason: 'Pré-requisitos não atendidos, solicitando mais informações'
      },
      nextStage: 'intent_confirmation',
      updates: {
        context: {
          ...state.context,
          routingIssue: {
            missingRequirements: missingData,
            originalIntent: state.intent?.type
          }
        }
      }
    };
  }

  /**
   * Registra estatísticas de roteamento
   */
  recordRouting(intent, targetGraph) {
    const routeKey = `${intent}_to_${targetGraph}`;
    
    if (!this.routingStats.has(routeKey)) {
      this.routingStats.set(routeKey, {
        count: 0,
        lastUsed: null,
        avgConfidence: 0
      });
    }
    
    const stats = this.routingStats.get(routeKey);
    stats.count++;
    stats.lastUsed = new Date();
    
    this.routingStats.set(routeKey, stats);
  }

  /**
   * Obtém estatísticas de roteamento
   */
  getRoutingStats() {
    const stats = {
      ...this.getMetrics(),
      totalRoutes: this.routingStats.size,
      routeDistribution: {},
      mostUsedRoute: null,
      leastUsedRoute: null
    };
    
    let maxCount = 0;
    let minCount = Infinity;
    
    for (const [route, data] of this.routingStats.entries()) {
      stats.routeDistribution[route] = data.count;
      
      if (data.count > maxCount) {
        maxCount = data.count;
        stats.mostUsedRoute = route;
      }
      
      if (data.count < minCount) {
        minCount = data.count;
        stats.leastUsedRoute = route;
      }
    }
    
    return stats;
  }

  /**
   * Obtém rotas disponíveis para debugging
   */
  getAvailableRoutes() {
    const routes = {};
    
    for (const [intent, route] of Object.entries(this.intentRoutes)) {
      routes[intent] = {
        targetGraph: route.targetGraph,
        targetStage: route.targetStage,
        description: route.description,
        requiredData: route.requiredData,
        usage: this.routingStats.get(`${intent}_to_${route.targetGraph}`)?.count || 0
      };
    }
    
    return routes;
  }

  /**
   * Valida configuração de rotas
   */
  validateRoutingConfiguration() {
    const validation = {
      isValid: true,
      issues: []
    };
    
    // Verificar se todas as intenções têm rotas definidas
    const expectedIntents = ['scheduling', 'confirmation', 'cancellation', 'rescheduling', 'inquiry', 'greeting', 'unknown'];
    
    for (const intent of expectedIntents) {
      if (!this.intentRoutes[intent]) {
        validation.isValid = false;
        validation.issues.push(`Rota não definida para intenção: ${intent}`);
      } else {
        const route = this.intentRoutes[intent];
        
        // Verificar se campos obrigatórios estão presentes
        if (!route.targetGraph || !route.targetStage) {
          validation.isValid = false;
          validation.issues.push(`Rota incompleta para intenção: ${intent}`);
        }
      }
    }
    
    return validation;
  }
}

module.exports = IntentRoutingNode;