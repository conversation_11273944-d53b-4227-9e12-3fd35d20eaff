/**
 * GraphLogger - Sistema de logging especializado para LangGraph
 * 
 * Funcionalidades:
 * - Logging de execução de grafos
 * - Tracking de fluxos de execução
 * - Métricas de performance
 * - Debug visual de caminhos
 * - Integração com sistema de logs existente
 */

const winston = require('winston');
const path = require('path');

class GraphLogger {
  constructor() {
    this.initializeLogger();
    this.executionTraces = new Map(); // customerPhone -> execution trace
    this.graphMetrics = new Map(); // graphId -> metrics
  }

  /**
   * Inicializa o logger Winston para LangGraph
   */
  initializeLogger() {
    // Configurar formato personalizado para LangGraph
    const langGraphFormat = winston.format.combine(
      winston.format.timestamp(),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        return JSON.stringify({
          timestamp,
          level,
          message,
          component: 'LangGraph',
          ...meta
        });
      })
    );

    // Logger específico para LangGraph
    this.logger = winston.createLogger({
      level: 'debug',
      format: langGraphFormat,
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
              const metaStr = Object.keys(meta).length > 0 ? JSON.stringify(meta) : '';
              return `🔗 ${timestamp} [${level}] ${message} ${metaStr}`;
            })
          )
        }),
        new winston.transports.File({
          filename: path.join(__dirname, '../../logs/langgraph.log'),
          format: langGraphFormat
        })
      ]
    });
  }

  /**
   * Inicia o trace de execução de um grafo
   */
  startGraphExecution(graphId, customerPhone, initialState) {
    const traceId = `${graphId}_${customerPhone}_${Date.now()}`;
    const startTime = Date.now();
    
    const trace = {
      traceId,
      graphId,
      customerPhone,
      startTime,
      endTime: null,
      nodes: [],
      transitions: [],
      errors: [],
      initialState: { ...initialState },
      finalState: null,
      status: 'running'
    };
    
    this.executionTraces.set(traceId, trace);
    
    this.logger.info('Graph execution started', {
      traceId,
      graphId,
      customerPhone,
      initialStage: initialState.currentStage
    });
    
    return traceId;
  }

  /**
   * Registra execução de um nó
   */
  logNodeExecution(traceId, nodeId, input, output) {
    const trace = this.executionTraces.get(traceId);
    if (!trace) {
      this.logger.warn('Trace not found for node execution', { traceId, nodeId });
      return;
    }
    
    const nodeExecution = {
      nodeId,
      startTime: Date.now(),
      endTime: Date.now() + (output.metadata?.executionTime || 0),
      input: {
        stage: input.state?.currentStage,
        messageCount: input.state?.messages?.length || 0
      },
      output: {
        success: output.success,
        nextStage: output.nextStage,
        hasResult: !!output.result,
        hasError: !!output.error
      },
      executionTime: output.metadata?.executionTime || 0,
      error: output.error || null
    };
    
    trace.nodes.push(nodeExecution);
    
    // Log baseado no resultado
    if (output.success) {
      this.logger.info('Node execution completed', {
        traceId,
        nodeId,
        executionTime: nodeExecution.executionTime,
        nextStage: output.nextStage
      });
    } else {
      this.logger.error('Node execution failed', {
        traceId,
        nodeId,
        error: output.error?.message,
        recoverable: output.error?.recoverable
      });
      
      trace.errors.push({
        nodeId,
        error: output.error,
        timestamp: new Date()
      });
    }
  }

  /**
   * Registra transição entre estágios
   */
  logStageTransition(traceId, fromStage, toStage, nodeId, reason = '') {
    const trace = this.executionTraces.get(traceId);
    if (!trace) {
      this.logger.warn('Trace not found for stage transition', { traceId });
      return;
    }
    
    const transition = {
      from: fromStage,
      to: toStage,
      nodeId,
      reason,
      timestamp: new Date()
    };
    
    trace.transitions.push(transition);
    
    this.logger.info('Stage transition', {
      traceId,
      transition: `${fromStage} → ${toStage}`,
      nodeId,
      reason
    });
  }

  /**
   * Finaliza o trace de execução
   */
  endGraphExecution(traceId, finalState, status = 'completed') {
    const trace = this.executionTraces.get(traceId);
    if (!trace) {
      this.logger.warn('Trace not found for graph completion', { traceId });
      return;
    }
    
    trace.endTime = Date.now();
    trace.finalState = { ...finalState };
    trace.status = status;
    
    const totalTime = trace.endTime - trace.startTime;
    const nodeCount = trace.nodes.length;
    const errorCount = trace.errors.length;
    
    this.logger.info('Graph execution completed', {
      traceId,
      status,
      totalTime,
      nodeCount,
      errorCount,
      finalStage: finalState.currentStage
    });
    
    // Atualizar métricas do grafo
    this.updateGraphMetrics(trace.graphId, trace);
    
    // Limpar trace antigo para economizar memória (manter apenas os últimos)
    this.cleanupOldTraces();
  }

  /**
   * Atualiza métricas agregadas do grafo
   */
  updateGraphMetrics(graphId, trace) {
    if (!this.graphMetrics.has(graphId)) {
      this.graphMetrics.set(graphId, {
        graphId,
        totalExecutions: 0,
        successfulExecutions: 0,
        failedExecutions: 0,
        totalExecutionTime: 0,
        averageExecutionTime: 0,
        nodeExecutions: {},
        commonPaths: {},
        errorsByNode: {},
        lastUpdated: new Date()
      });
    }
    
    const metrics = this.graphMetrics.get(graphId);
    
    // Atualizar contadores
    metrics.totalExecutions++;
    metrics.totalExecutionTime += (trace.endTime - trace.startTime);
    metrics.averageExecutionTime = metrics.totalExecutionTime / metrics.totalExecutions;
    
    if (trace.status === 'completed') {
      metrics.successfulExecutions++;
    } else {
      metrics.failedExecutions++;
    }
    
    // Atualizar estatísticas de nós
    trace.nodes.forEach(node => {
      if (!metrics.nodeExecutions[node.nodeId]) {
        metrics.nodeExecutions[node.nodeId] = {
          count: 0,
          totalTime: 0,
          errorCount: 0
        };
      }
      
      metrics.nodeExecutions[node.nodeId].count++;
      metrics.nodeExecutions[node.nodeId].totalTime += node.executionTime;
      
      if (node.error) {
        metrics.nodeExecutions[node.nodeId].errorCount++;
      }
    });
    
    // Registrar caminho comum (sequência de nós)
    const path = trace.nodes.map(n => n.nodeId).join(' → ');
    metrics.commonPaths[path] = (metrics.commonPaths[path] || 0) + 1;
    
    // Registrar erros por nó
    trace.errors.forEach(error => {
      if (!metrics.errorsByNode[error.nodeId]) {
        metrics.errorsByNode[error.nodeId] = {};
      }
      const errorType = error.error?.code || 'UNKNOWN_ERROR';
      metrics.errorsByNode[error.nodeId][errorType] = 
        (metrics.errorsByNode[error.nodeId][errorType] || 0) + 1;
    });
    
    metrics.lastUpdated = new Date();
  }

  /**
   * Obtém trace de execução específico
   */
  getExecutionTrace(traceId) {
    return this.executionTraces.get(traceId);
  }

  /**
   * Obtém todos os traces de um cliente
   */
  getCustomerTraces(customerPhone) {
    return Array.from(this.executionTraces.values())
      .filter(trace => trace.customerPhone === customerPhone)
      .sort((a, b) => b.startTime - a.startTime);
  }

  /**
   * Obtém métricas de um grafo
   */
  getGraphMetrics(graphId) {
    return this.graphMetrics.get(graphId);
  }

  /**
   * Obtém métricas de todos os grafos
   */
  getAllGraphMetrics() {
    return Object.fromEntries(this.graphMetrics);
  }

  /**
   * Gera relatório de performance
   */
  generatePerformanceReport(graphId) {
    const metrics = this.getGraphMetrics(graphId);
    if (!metrics) {
      return null;
    }
    
    // Calcular estatísticas adicionais
    const nodeStats = Object.entries(metrics.nodeExecutions).map(([nodeId, stats]) => ({
      nodeId,
      executionCount: stats.count,
      averageExecutionTime: stats.totalTime / stats.count,
      errorRate: stats.errorCount / stats.count,
      totalTime: stats.totalTime
    }));
    
    const topPaths = Object.entries(metrics.commonPaths)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([path, count]) => ({ path, count }));
    
    return {
      graphId,
      overview: {
        totalExecutions: metrics.totalExecutions,
        successRate: metrics.successfulExecutions / metrics.totalExecutions,
        averageExecutionTime: metrics.averageExecutionTime,
        lastUpdated: metrics.lastUpdated
      },
      nodePerformance: nodeStats.sort((a, b) => b.totalTime - a.totalTime),
      topExecutionPaths: topPaths,
      errorSummary: metrics.errorsByNode
    };
  }

  /**
   * Debug: Visualiza caminho de execução
   */
  visualizeExecutionPath(traceId) {
    const trace = this.executionTraces.get(traceId);
    if (!trace) {
      console.log(`❌ Trace ${traceId} não encontrado`);
      return;
    }
    
    console.log(`🔗 Visualização de Execução - ${traceId}`);
    console.log(`📊 Grafo: ${trace.graphId} | Cliente: ${trace.customerPhone}`);
    console.log(`⏱️ Tempo Total: ${trace.endTime - trace.startTime}ms`);
    console.log(`📈 Status: ${trace.status}`);
    console.log('');
    
    console.log('🛤️ Caminho de Execução:');
    trace.nodes.forEach((node, index) => {
      const status = node.error ? '❌' : '✅';
      const time = `${node.executionTime}ms`;
      console.log(`  ${index + 1}. ${status} ${node.nodeId} (${time})`);
      
      if (node.error) {
        console.log(`     ⚠️ Erro: ${node.error.message}`);
      }
    });
    
    if (trace.transitions.length > 0) {
      console.log('');
      console.log('🔄 Transições de Estágio:');
      trace.transitions.forEach((transition, index) => {
        console.log(`  ${index + 1}. ${transition.from} → ${transition.to} (${transition.nodeId})`);
        if (transition.reason) {
          console.log(`     💡 Razão: ${transition.reason}`);
        }
      });
    }
  }

  /**
   * Limpa traces antigos para economizar memória
   */
  cleanupOldTraces() {
    const maxTraces = 100;
    const traces = Array.from(this.executionTraces.entries())
      .sort(([,a], [,b]) => b.startTime - a.startTime);
    
    if (traces.length > maxTraces) {
      const tracesToRemove = traces.slice(maxTraces);
      tracesToRemove.forEach(([traceId]) => {
        this.executionTraces.delete(traceId);
      });
      
      this.logger.debug('Cleaned up old traces', {
        removed: tracesToRemove.length,
        remaining: this.executionTraces.size
      });
    }
  }

  /**
   * Export de dados para análise externa
   */
  exportData(options = {}) {
    const data = {
      timestamp: new Date(),
      traces: options.includeTraces ? Array.from(this.executionTraces.values()) : null,
      metrics: Array.from(this.graphMetrics.values()),
      summary: {
        totalTraces: this.executionTraces.size,
        totalGraphs: this.graphMetrics.size,
        exportOptions: options
      }
    };
    
    return data;
  }
}

module.exports = GraphLogger;