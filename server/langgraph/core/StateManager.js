/**
 * StateManager - Gerenciador de Estado para LangGraph
 * 
 * Responsável por:
 * - Manter estado consistente da conversa
 * - Validar transições de estado
 * - Persistir contexto entre nós
 * - Fornecer histórico de estados
 */

const { z } = require('zod');

// Schema de validação do estado da conversa
const ConversationStateSchema = z.object({
  customerPhone: z.string().min(10),
  currentStage: z.enum(['greeting', 'intent_classification', 'context_validation', 'intent_confirmation', 'scheduling', 'confirmation', 'completed', 'error']),
  intent: z.object({
    type: z.enum(['scheduling', 'cancellation', 'rescheduling', 'inquiry', 'greeting', 'unknown']).optional(),
    confidence: z.number().min(0).max(1).optional(),
    extractedAt: z.date().optional(),
    context: z.record(z.any()).optional()
  }).optional(),
  schedulingData: z.object({
    professional: z.object({
      id: z.number().nullable().optional(),
      name: z.string().nullable().optional()
    }).optional(),
    service: z.object({
      id: z.number().nullable().optional(),
      name: z.string().nullable().optional(),
      price: z.number().nullable().optional()
    }).optional(),
    date: z.string().nullable().optional(), // formato YYYY-MM-DD
    time: z.string().nullable().optional(), // formato HH:MM
    extractedAt: z.date().nullable().optional(),
    source: z.enum(['ai', 'manual']).optional(),
    isComplete: z.boolean().optional()
  }).optional(),
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string(),
    timestamp: z.date(),
    type: z.enum(['text', 'audio', 'image']).default('text')
  })).default([]),
  context: z.record(z.any()).default({}),
  metadata: z.object({
    createdAt: z.date(),
    updatedAt: z.date(),
    nodeHistory: z.array(z.string()).default([]),
    transitionHistory: z.array(z.object({
      from: z.string(),
      to: z.string(),
      timestamp: z.date(),
      reason: z.string().optional()
    })).default([]),
    errorCount: z.number().default(0),
    lastError: z.object({
      message: z.string(),
      timestamp: z.date(),
      node: z.string()
    }).optional()
  })
});

class StateManager {
  constructor() {
    this.states = new Map(); // customerPhone -> conversationState
    this.stateHistory = new Map(); // customerPhone -> array of historical states
    this.maxHistoryLength = 50;
  }

  /**
   * Inicializa um novo estado de conversa
   */
  initializeState(customerPhone) {
    const now = new Date();
    const initialState = {
      customerPhone,
      currentStage: 'greeting',
      intent: {},
      schedulingData: {
        professional: { id: null, name: null },
        service: { id: null, name: null, price: null },
        date: null,
        time: null,
        extractedAt: null,
        source: 'ai',
        isComplete: false
      },
      messages: [],
      context: {},
      metadata: {
        createdAt: now,
        updatedAt: now,
        nodeHistory: [],
        transitionHistory: [],
        errorCount: 0
      }
    };

    // Validar estado inicial
    const validatedState = ConversationStateSchema.parse(initialState);
    
    this.states.set(customerPhone, validatedState);
    this.initializeHistory(customerPhone, validatedState);
    
    console.log(`🟢 Estado inicializado para ${customerPhone}`);
    return validatedState;
  }

  /**
   * Obtém o estado atual da conversa
   */
  getState(customerPhone) {
    return this.states.get(customerPhone);
  }

  /**
   * Atualiza o estado da conversa com validação
   */
  updateState(customerPhone, updates) {
    const currentState = this.getState(customerPhone);
    if (!currentState) {
      throw new Error(`Estado não encontrado para ${customerPhone}. Inicialize primeiro.`);
    }

    const updatedState = {
      ...currentState,
      ...updates,
      context: {
        ...currentState.context,
        ...updates.context
      },
      metadata: {
        ...currentState.metadata,
        updatedAt: new Date()
      }
    };

    // Validar estado atualizado
    const validatedState = ConversationStateSchema.parse(updatedState);
    
    // Salvar no histórico antes de atualizar
    this.addToHistory(customerPhone, currentState);
    
    // Atualizar estado atual
    this.states.set(customerPhone, validatedState);
    
    console.log(`🔄 Estado atualizado para ${customerPhone}: ${currentState.currentStage} → ${validatedState.currentStage}`);
    return validatedState;
  }

  /**
   * Adiciona uma mensagem ao histórico
   */
  addMessage(customerPhone, message) {
    const currentState = this.getState(customerPhone);
    if (!currentState) {
      throw new Error(`Estado não encontrado para ${customerPhone}`);
    }

    const messageWithTimestamp = {
      ...message,
      timestamp: new Date()
    };

    const updatedMessages = [...currentState.messages, messageWithTimestamp];
    
    return this.updateState(customerPhone, {
      messages: updatedMessages
    });
  }

  /**
   * Transição de estágio com validação
   */
  transitionStage(customerPhone, newStage, reason = '') {
    const currentState = this.getState(customerPhone);
    if (!currentState) {
      throw new Error(`Estado não encontrado para ${customerPhone}`);
    }

    // Validar transição
    if (!this.isValidTransition(currentState.currentStage, newStage)) {
      throw new Error(`Transição inválida: ${currentState.currentStage} → ${newStage}`);
    }

    const transition = {
      from: currentState.currentStage,
      to: newStage,
      timestamp: new Date(),
      reason
    };

    const updatedTransitionHistory = [
      ...currentState.metadata.transitionHistory,
      transition
    ];

    return this.updateState(customerPhone, {
      currentStage: newStage,
      metadata: {
        ...currentState.metadata,
        transitionHistory: updatedTransitionHistory
      }
    });
  }

  /**
   * Adiciona um nó ao histórico de execução
   */
  addNodeToHistory(customerPhone, nodeName) {
    const currentState = this.getState(customerPhone);
    if (!currentState) {
      throw new Error(`Estado não encontrado para ${customerPhone}`);
    }

    const updatedNodeHistory = [
      ...currentState.metadata.nodeHistory,
      `${nodeName}:${new Date().toISOString()}`
    ];

    return this.updateState(customerPhone, {
      metadata: {
        ...currentState.metadata,
        nodeHistory: updatedNodeHistory
      }
    });
  }

  /**
   * Registra um erro no estado
   */
  recordError(customerPhone, error, nodeName) {
    const currentState = this.getState(customerPhone);
    if (!currentState) {
      throw new Error(`Estado não encontrado para ${customerPhone}`);
    }

    const errorInfo = {
      message: error.message || error,
      timestamp: new Date(),
      node: nodeName
    };

    return this.updateState(customerPhone, {
      metadata: {
        ...currentState.metadata,
        errorCount: currentState.metadata.errorCount + 1,
        lastError: errorInfo
      }
    });
  }

  /**
   * Valida se uma transição de estágio é permitida
   */
  isValidTransition(fromStage, toStage) {
    const validTransitions = {
      'greeting': ['intent_classification', 'context_validation', 'scheduling', 'greeting', 'error'],
      'intent_classification': ['context_validation', 'scheduling', 'confirmation', 'intent_confirmation', 'greeting', 'error'],
      'context_validation': ['scheduling', 'confirmation', 'intent_confirmation', 'intent_classification', 'greeting', 'error'],
      'intent_confirmation': ['scheduling', 'confirmation', 'completed', 'intent_classification', 'context_validation', 'error'],
      'scheduling': ['confirmation', 'completed', 'context_validation', 'error'],
      'confirmation': ['completed', 'scheduling', 'error'],
      'completed': ['greeting', 'intent_classification'],
      'error': ['greeting', 'intent_classification', 'context_validation', 'scheduling', 'intent_confirmation']
    };

    return validTransitions[fromStage]?.includes(toStage) || false;
  }

  /**
   * Inicializa histórico para um cliente
   */
  initializeHistory(customerPhone, initialState) {
    this.stateHistory.set(customerPhone, [initialState]);
  }

  /**
   * Adiciona estado ao histórico
   */
  addToHistory(customerPhone, state) {
    const history = this.stateHistory.get(customerPhone) || [];
    
    // Adicionar ao histórico
    const updatedHistory = [...history, { ...state, timestamp: new Date() }];
    
    // Limitar tamanho do histórico
    if (updatedHistory.length > this.maxHistoryLength) {
      updatedHistory.shift();
    }
    
    this.stateHistory.set(customerPhone, updatedHistory);
  }

  /**
   * Obtém histórico de estados
   */
  getHistory(customerPhone) {
    return this.stateHistory.get(customerPhone) || [];
  }

  /**
   * Remove estado da memória (limpeza)
   */
  clearState(customerPhone) {
    this.states.delete(customerPhone);
    this.stateHistory.delete(customerPhone);
    console.log(`🧹 Estado limpo para ${customerPhone}`);
  }

  /**
   * Obtém estatísticas do gerenciador de estado
   */
  getStats() {
    return {
      activeStates: this.states.size,
      totalHistoryItems: Array.from(this.stateHistory.values())
        .reduce((sum, history) => sum + history.length, 0),
      stageDistribution: Array.from(this.states.values())
        .reduce((acc, state) => {
          acc[state.currentStage] = (acc[state.currentStage] || 0) + 1;
          return acc;
        }, {}),
      averageMessages: Array.from(this.states.values())
        .reduce((sum, state) => sum + state.messages.length, 0) / (this.states.size || 1)
    };
  }

  /**
   * Debug: Exibe estado completo
   */
  debugState(customerPhone) {
    const state = this.getState(customerPhone);
    const history = this.getHistory(customerPhone);
    
    console.log(`🔍 DEBUG Estado para ${customerPhone}:`);
    console.log('Estado atual:', JSON.stringify(state, null, 2));
    console.log('Histórico:', history.length, 'items');
    console.log('Transições:', state?.metadata?.transitionHistory?.length || 0);
    console.log('Nós executados:', state?.metadata?.nodeHistory?.length || 0);
  }
}

module.exports = StateManager;