/**
 * BaseNode - Classe base para todos os nós do LangGraph
 * 
 * Responsabilidades:
 * - Interface comum para todos os nós
 * - Logging e debug consistente
 * - Validação de entrada e saída
 * - Tratamento de erros padronizado
 * - Métricas de performance
 */

const { z } = require('zod');

// Schema base para input de nós
const NodeInputSchema = z.object({
  customerPhone: z.string().min(10),
  state: z.object({
    currentStage: z.string(),
    messages: z.array(z.any()).default([]),
    context: z.record(z.any()).default({})
  }),
  message: z.string().optional(),
  metadata: z.record(z.any()).default({})
});

// Schema base para output de nós
const NodeOutputSchema = z.object({
  customerPhone: z.string(),
  success: z.boolean(),
  result: z.any().optional(),
  nextStage: z.string().optional(),
  updates: z.record(z.any()).default({}),
  metadata: z.object({
    executionTime: z.number(),
    nodeId: z.string(),
    timestamp: z.date(),
    debugInfo: z.record(z.any()).default({})
  }),
  error: z.object({
    message: z.string(),
    code: z.string().optional(),
    recoverable: z.boolean().default(true)
  }).optional()
});

class BaseNode {
  constructor(nodeId, config = {}) {
    this.nodeId = nodeId;
    this.config = {
      timeout: 30000, // 30s default timeout
      retryAttempts: 3,
      retryDelay: 1000,
      logLevel: 'info',
      ...config
    };
    this.executionCount = 0;
    this.errorCount = 0;
    this.totalExecutionTime = 0;
  }

  /**
   * Método principal de execução do nó
   * Este método deve ser implementado pelos nós filhos
   */
  async execute(input) {
    throw new Error(`Execute method must be implemented by ${this.nodeId}`);
  }

  /**
   * Wrapper de execução com validação, logging e tratamento de erros
   */
  async run(input) {
    const startTime = Date.now();
    
    try {
      // Incrementar contador
      this.executionCount++;
      
      // Validar entrada
      const validatedInput = this.validateInput(input);
      
      // Log início da execução
      this.logExecution('START', validatedInput.customerPhone, {
        nodeId: this.nodeId,
        stage: validatedInput.state.currentStage
      });
      
      // Executar lógica do nó com timeout
      const result = await this.executeWithTimeout(validatedInput);
      
      // Validar saída
      const validatedOutput = this.validateOutput(result, validatedInput.customerPhone, startTime);
      
      // Log sucesso
      this.logExecution('SUCCESS', validatedInput.customerPhone, {
        executionTime: validatedOutput.metadata.executionTime,
        nextStage: validatedOutput.nextStage
      });
      
      // Atualizar métricas
      this.updateMetrics(validatedOutput.metadata.executionTime);
      
      return validatedOutput;
      
    } catch (error) {
      this.errorCount++;
      const executionTime = Date.now() - startTime;
      
      // Log erro
      this.logExecution('ERROR', input.customerPhone || 'unknown', {
        error: error.message,
        executionTime
      });
      
      // Retornar resultado de erro estruturado
      return this.createErrorOutput(input.customerPhone || 'unknown', error, executionTime);
    }
  }

  /**
   * Executa a lógica do nó com timeout
   */
  async executeWithTimeout(input) {
    return new Promise(async (resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Node ${this.nodeId} timed out after ${this.config.timeout}ms`));
      }, this.config.timeout);
      
      try {
        const result = await this.execute(input);
        clearTimeout(timeout);
        resolve(result);
      } catch (error) {
        clearTimeout(timeout);
        reject(error);
      }
    });
  }

  /**
   * Valida entrada do nó
   */
  validateInput(input) {
    try {
      return NodeInputSchema.parse(input);
    } catch (error) {
      throw new Error(`Invalid input for node ${this.nodeId}: ${error.message}`);
    }
  }

  /**
   * Valida e formata saída do nó
   */
  validateOutput(result, customerPhone, startTime) {
    const executionTime = Date.now() - startTime;
    
    // Se o resultado já está no formato correto, apenas validar
    if (result && typeof result === 'object' && 'success' in result) {
      const output = {
        ...result,
        customerPhone,
        metadata: {
          executionTime,
          nodeId: this.nodeId,
          timestamp: new Date(),
          debugInfo: result.metadata?.debugInfo || {},
          ...result.metadata
        }
      };
      
      return NodeOutputSchema.parse(output);
    }
    
    // Se não, criar formato padrão
    const output = {
      customerPhone,
      success: true,
      result,
      updates: {},
      metadata: {
        executionTime,
        nodeId: this.nodeId,
        timestamp: new Date(),
        debugInfo: {}
      }
    };
    
    return NodeOutputSchema.parse(output);
  }

  /**
   * Cria saída de erro padronizada
   */
  createErrorOutput(customerPhone, error, executionTime) {
    return {
      customerPhone,
      success: false,
      result: null,
      updates: {},
      metadata: {
        executionTime,
        nodeId: this.nodeId,
        timestamp: new Date(),
        debugInfo: {
          errorStack: error.stack
        }
      },
      error: {
        message: error.message,
        code: error.code || 'NODE_EXECUTION_ERROR',
        recoverable: !error.message.includes('timeout') && !error.message.includes('fatal')
      }
    };
  }

  /**
   * Logging consistente para todos os nós
   */
  logExecution(level, customerPhone, details) {
    const logData = {
      nodeId: this.nodeId,
      customerPhone,
      timestamp: new Date().toISOString(),
      ...details
    };
    
    switch (level) {
      case 'START':
        console.log(`🚀 [${this.nodeId}] Iniciando execução`, logData);
        break;
      case 'SUCCESS':
        console.log(`✅ [${this.nodeId}] Execução bem-sucedida`, logData);
        break;
      case 'ERROR':
        console.error(`❌ [${this.nodeId}] Erro na execução`, logData);
        break;
      default:
        console.log(`📝 [${this.nodeId}] ${level}`, logData);
    }
  }

  /**
   * Atualiza métricas do nó
   */
  updateMetrics(executionTime) {
    this.totalExecutionTime += executionTime;
  }

  /**
   * Obtém métricas do nó
   */
  getMetrics() {
    return {
      nodeId: this.nodeId,
      executionCount: this.executionCount,
      errorCount: this.errorCount,
      errorRate: this.executionCount > 0 ? this.errorCount / this.executionCount : 0,
      totalExecutionTime: this.totalExecutionTime,
      averageExecutionTime: this.executionCount > 0 ? this.totalExecutionTime / this.executionCount : 0,
      lastUpdated: new Date()
    };
  }

  /**
   * Reset das métricas
   */
  resetMetrics() {
    this.executionCount = 0;
    this.errorCount = 0;
    this.totalExecutionTime = 0;
  }

  /**
   * Método utilitário para chamar serviços externos (IA, APIs)
   */
  async callExternalService(serviceName, serviceCall, retries = null) {
    const maxRetries = retries || this.config.retryAttempts;
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        this.logExecution('DEBUG', 'system', {
          action: 'external_service_call',
          service: serviceName,
          attempt,
          maxRetries
        });
        
        const result = await serviceCall();
        
        if (attempt > 1) {
          this.logExecution('DEBUG', 'system', {
            action: 'external_service_retry_success',
            service: serviceName,
            attempt
          });
        }
        
        return result;
        
      } catch (error) {
        lastError = error;
        
        this.logExecution('DEBUG', 'system', {
          action: 'external_service_error',
          service: serviceName,
          attempt,
          error: error.message
        });
        
        if (attempt < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }
      }
    }
    
    throw new Error(`Failed to call ${serviceName} after ${maxRetries} attempts: ${lastError.message}`);
  }

  /**
   * Método helper para extrair informações das mensagens
   */
  getLastUserMessage(messages) {
    const userMessages = messages.filter(msg => msg.role === 'user');
    return userMessages[userMessages.length - 1]?.content || '';
  }

  /**
   * Método helper para contar mensagens por tipo
   */
  getMessageStats(messages) {
    return {
      total: messages.length,
      user: messages.filter(msg => msg.role === 'user').length,
      assistant: messages.filter(msg => msg.role === 'assistant').length,
      recent: messages.slice(-5).length // últimas 5 mensagens
    };
  }

  /**
   * Debug info do nó
   */
  getDebugInfo() {
    return {
      nodeId: this.nodeId,
      config: this.config,
      metrics: this.getMetrics(),
      timestamp: new Date()
    };
  }
}

module.exports = BaseNode;