/**
 * Cache inteligente de disponibilidade por profissional
 * Gerencia múltiplos profissionais e datas com TTL configurável
 */

class AvailabilityCache {
  constructor () {
    // Estrutura: cache[professionalName][date] = { data, timestamp }
    this.cache = {}
    this.defaultTTL = 5 * 60 * 1000 // 5 minutos
    console.log('🗄️ AvailabilityCache inicializado')
  }

  /**
   * Gera chave única para o cache incluindo filtro de serviço
   */
  generateKey (professionalName, date, serviceId = null) {
    const baseKey = `${professionalName}|${date}`
    return serviceId ? `${baseKey}|service:${serviceId}` : baseKey
  }

  /**
   * Armazena disponibilidade no cache com suporte a filtro de serviço
   */
  set (professionalName, date, availabilityData, serviceId = null, ttl = this.defaultTTL) {
    const key = this.generateKey(professionalName, date, serviceId)

    if (!this.cache[professionalName]) {
      this.cache[professionalName] = {}
    }

    // Criar estrutura aninhada para suportar serviços
    const dateKey = serviceId ? `${date}|service:${serviceId}` : date

    this.cache[professionalName][dateKey] = {
      data: availabilityData,
      timestamp: Date.now(),
      ttl,
      key,
      serviceId
    }

    console.log(`💾 Cache armazenado: ${key} (TTL: ${ttl / 1000}s)${serviceId ? ` [Filtro de serviço: ${serviceId}]` : ''}`)
  }

  /**
   * Recupera disponibilidade do cache se ainda válida, com suporte a filtro de serviço
   */
  get (professionalName, date, serviceId = null) {
    const dateKey = serviceId ? `${date}|service:${serviceId}` : date
    const key = this.generateKey(professionalName, date, serviceId)

    if (!this.cache[professionalName] || !this.cache[professionalName][dateKey]) {
      console.log(`❌ Cache miss: ${key}`)
      return null
    }

    const cached = this.cache[professionalName][dateKey]
    const age = Date.now() - cached.timestamp

    if (age > cached.ttl) {
      console.log(`⏰ Cache expirado: ${cached.key} (${Math.floor(age / 1000)}s > ${cached.ttl / 1000}s)`)
      this.delete(professionalName, date, serviceId)
      return null
    }

    console.log(`✅ Cache hit: ${cached.key} (idade: ${Math.floor(age / 1000)}s)`)
    return cached.data
  }

  /**
   * Remove entrada específica do cache com suporte a filtro de serviço
   */
  delete (professionalName, date, serviceId = null) {
    const dateKey = serviceId ? `${date}|service:${serviceId}` : date
    const key = this.generateKey(professionalName, date, serviceId)

    if (this.cache[professionalName] && this.cache[professionalName][dateKey]) {
      delete this.cache[professionalName][dateKey]
      console.log(`🗑️ Cache removido: ${key}`)

      // Remove profissional se não tem mais dados
      if (Object.keys(this.cache[professionalName]).length === 0) {
        delete this.cache[professionalName]
        console.log(`🗑️ Profissional removido do cache: ${professionalName}`)
      }
    }
  }

  /**
   * Remove todas as entradas de um profissional
   */
  clearProfessional (professionalName) {
    if (this.cache[professionalName]) {
      const count = Object.keys(this.cache[professionalName]).length
      delete this.cache[professionalName]
      console.log(`🧹 Cache limpo para ${professionalName}: ${count} entradas removidas`)
    }
  }

  /**
   * Remove entradas expiradas (limpeza geral)
   */
  cleanExpired () {
    let removedCount = 0
    const now = Date.now()

    for (const professionalName in this.cache) {
      for (const date in this.cache[professionalName]) {
        const cached = this.cache[professionalName][date]
        const age = now - cached.timestamp

        if (age > cached.ttl) {
          this.delete(professionalName, date)
          removedCount++
        }
      }
    }

    if (removedCount > 0) {
      console.log(`🧹 Limpeza automática: ${removedCount} entradas expiradas removidas`)
    }
  }

  /**
   * Obtém estatísticas do cache
   */
  getStats () {
    let totalEntries = 0
    let expiredEntries = 0
    const professionals = Object.keys(this.cache)
    const now = Date.now()

    for (const professionalName in this.cache) {
      for (const date in this.cache[professionalName]) {
        totalEntries++
        const cached = this.cache[professionalName][date]
        const age = now - cached.timestamp

        if (age > cached.ttl) {
          expiredEntries++
        }
      }
    }

    return {
      totalEntries,
      expiredEntries,
      validEntries: totalEntries - expiredEntries,
      professionals: professionals.length,
      professionalNames: professionals,
      memorySize: JSON.stringify(this.cache).length // Aproximação
    }
  }

  /**
   * Limpa todo o cache
   */
  clear () {
    const stats = this.getStats()
    this.cache = {}
    console.log(`🧹 Cache completamente limpo: ${stats.totalEntries} entradas removidas`)
  }

  /**
   * Verifica se tem dados em cache para um profissional/data
   */
  has (professionalName, date) {
    return this.get(professionalName, date) !== null
  }

  /**
   * Lista todas as chaves no cache (para debug)
   */
  listKeys () {
    const keys = []
    for (const professionalName in this.cache) {
      for (const date in this.cache[professionalName]) {
        keys.push(this.generateKey(professionalName, date))
      }
    }
    return keys
  }
}

// Instância singleton
const availabilityCache = new AvailabilityCache()

// Limpeza automática a cada 10 minutos
setInterval(() => {
  availabilityCache.cleanExpired()
}, 10 * 60 * 1000)

module.exports = availabilityCache
