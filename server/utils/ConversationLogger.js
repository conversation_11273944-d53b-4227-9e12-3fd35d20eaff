const fs = require('fs');
const path = require('path');

/**
 * Logger especializado para conversas cliente-usuário
 * 
 * Gera logs limpos e focados apenas nas trocas de mensagens
 * entre cliente e sistema, facilitando debug e análise.
 */
class ConversationLogger {
  constructor() {
    this.logsDir = path.join(__dirname, '../logs/conversations');
    this.ensureLogDirectory();
  }

  /**
   * Garante que o diretório de logs existe
   */
  ensureLogDirectory() {
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
      console.log('📁 Diretório de conversas criado:', this.logsDir);
    }
  }

  /**
   * Gera nome do arquivo baseado na data e telefone do cliente
   */
  getLogFilename(customerPhone) {
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const phoneClean = customerPhone.replace(/\D/g, ''); // Remove caracteres especiais
    return `conversation-${date}-${phoneClean}.log`;
  }

  /**
   * Formata timestamp para log legível
   */
  formatTimestamp(timestamp = new Date()) {
    return timestamp.toLocaleString('pt-BR', {
      year: 'numeric',
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }

  /**
   * Log de mensagem do cliente
   */
  async logClientMessage(customerPhone, message, messageType = 'text') {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    let logEntry;
    switch(messageType) {
      case 'audio':
        logEntry = `[${timestamp}] 📱 CLIENTE: 🎤 [Áudio recebido]\n`;
        break;
      case 'image':
        logEntry = `[${timestamp}] 📱 CLIENTE: 🖼️ [Imagem recebida]\n`;
        break;
      case 'document':
        logEntry = `[${timestamp}] 📱 CLIENTE: 📄 [Documento recebido]\n`;
        break;
      default:
        logEntry = `[${timestamp}] 📱 CLIENTE: ${message}\n`;
    }

    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Log de resposta do sistema
   */
  async logSystemResponse(customerPhone, response, responseType = 'text', metadata = {}) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    let logEntry = `[${timestamp}] 🤖 SISTEMA: ${response}`;

    // Adicionar metadados importantes
    if (metadata.usedTemplate) {
      logEntry += ` [Template: ${metadata.usedTemplate}]`;
    }
    if (metadata.duration) {
      logEntry += ` [Duração: ${metadata.duration}ms]`;
    }
    if (metadata.stage) {
      logEntry += ` [Estágio: ${metadata.stage}]`;
    }

    logEntry += '\n';

    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Log de evento especial (agendamento, cancelamento, etc)
   */
  async logSpecialEvent(customerPhone, eventType, details) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    let emoji = '';
    switch(eventType) {
      case 'appointment_created': emoji = '✅'; break;
      case 'appointment_cancelled': emoji = '❌'; break;
      case 'availability_checked': emoji = '🔍'; break;
      case 'customer_identified': emoji = '👤'; break;
      default: emoji = 'ℹ️';
    }

    const logEntry = `[${timestamp}] ${emoji} EVENTO: ${eventType.toUpperCase()} - ${details}\n`;
    
    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Log de início de sessão com separador visual
   */
  async logSessionStart(customerPhone, sessionId) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    const separator = '\n' + '='.repeat(80) + '\n';
    const sessionHeader = `${separator}[${timestamp}] 🚀 NOVA SESSÃO - ID: ${sessionId}${separator}`;

    await this.appendToFile(filepath, sessionHeader);
  }

  /**
   * Log de fim de sessão
   */
  async logSessionEnd(customerPhone, sessionId, duration) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    const sessionFooter = `[${timestamp}] 🏁 SESSÃO FINALIZADA - ID: ${sessionId} - Duração: ${duration}ms\n\n`;

    await this.appendToFile(filepath, sessionFooter);
  }

  /**
   * Escreve no arquivo de log
   */
  async appendToFile(filepath, content) {
    try {
      await fs.promises.appendFile(filepath, content, 'utf8');
    } catch (error) {
      console.error('❌ Erro ao escrever log de conversa:', error.message);
    }
  }

  /**
   * Limpa logs antigos (opcional - manter últimos N dias)
   */
  async cleanOldLogs(daysToKeep = 30) {
    try {
      const files = await fs.promises.readdir(this.logsDir);
      const now = Date.now();
      const maxAge = daysToKeep * 24 * 60 * 60 * 1000; // dias em ms

      for (const file of files) {
        const filepath = path.join(this.logsDir, file);
        const stats = await fs.promises.stat(filepath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.promises.unlink(filepath);
          console.log(`🗑️ Log antigo removido: ${file}`);
        }
      }
    } catch (error) {
      console.error('❌ Erro ao limpar logs antigos:', error.message);
    }
  }

  /**
   * Busca conversas por telefone em um período
   */
  async searchConversations(customerPhone, startDate, endDate) {
    const phoneClean = customerPhone.replace(/\D/g, '');
    const results = [];

    try {
      const files = await fs.promises.readdir(this.logsDir);
      
      for (const file of files) {
        if (file.includes(phoneClean)) {
          const [, fileDate] = file.match(/conversation-(\d{4}-\d{2}-\d{2})-/) || [];
          
          if (fileDate && fileDate >= startDate && fileDate <= endDate) {
            const filepath = path.join(this.logsDir, file);
            const content = await fs.promises.readFile(filepath, 'utf8');
            results.push({
              date: fileDate,
              filename: file,
              content: content
            });
          }
        }
      }
      
      return results.sort((a, b) => b.date.localeCompare(a.date));
    } catch (error) {
      console.error('❌ Erro ao buscar conversas:', error.message);
      return [];
    }
  }
}

module.exports = new ConversationLogger();