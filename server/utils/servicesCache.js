const trinksService = require('../services/trinks');
const { logger } = require('./logger');

let servicesCache = null;
let servicesLastFetch = null;
const SERVICES_CACHE_TTL = 15 * 60 * 1000; // 15 minutos

class ServicesCache {
  async getServices(trinksApiCalls = []) {
    const now = Date.now();
    
    // Check cache validity
    if (servicesCache && servicesLastFetch && (now - servicesLastFetch) < SERVICES_CACHE_TTL) {
      logger.info('Using cached services data');
      return servicesCache;
    }
    
    try {
      logger.info('Fetching fresh services data from API');
      servicesCache = await trinksService.getServicesReal();
      servicesLastFetch = now;
      
      // Track API call for debugging
      if (trinksApiCalls) {
        trinksApiCalls.push({
          endpoint: '/v1/servicos',
          method: 'GET',
          timestamp: new Date().toISOString(),
          cached: false
        });
      }
      
      logger.info(`Cached ${servicesCache?.length || 0} services`);
      return servicesCache;
    } catch (error) {
      logger.error('Failed to fetch services:', error.message);
      
      // Return cached data if available, even if expired
      if (servicesCache) {
        logger.warn('Returning expired cached services due to API error');
        return servicesCache;
      }
      
      return [];
    }
  }
  
  clearCache() {
    servicesCache = null;
    servicesLastFetch = null;
    logger.info('Services cache cleared');
  }
  
  getCachedData() {
    return {
      data: servicesCache,
      lastFetch: servicesLastFetch,
      isExpired: servicesLastFetch ? (Date.now() - servicesLastFetch) >= SERVICES_CACHE_TTL : true
    };
  }

  async getServiceIdByName(serviceName, trinksServiceInstance, trinksApiCalls = []) {
    try {
      const services = await this.getServices(trinksApiCalls);
      
      if (!services || services.length === 0) {
        logger.warn('No services available for name lookup');
        return null;
      }

      // Buscar serviço por nome (case-insensitive e parcial)
      const normalizedSearchName = serviceName.toLowerCase().trim();
      
      const exactMatch = services.find(service => 
        service.nome.toLowerCase() === normalizedSearchName
      );

      if (exactMatch) {
        logger.info(`Found exact service match: ${exactMatch.nome} (ID: ${exactMatch.id})`);
        return {
          id: exactMatch.id,
          name: exactMatch.nome,
          price: exactMatch.valor || 0
        };
      }

      // Busca parcial se não encontrou exato
      const partialMatch = services.find(service => 
        service.nome.toLowerCase().includes(normalizedSearchName) ||
        normalizedSearchName.includes(service.nome.toLowerCase())
      );

      if (partialMatch) {
        logger.info(`Found partial service match: ${partialMatch.nome} (ID: ${partialMatch.id})`);
        return {
          id: partialMatch.id,
          name: partialMatch.nome,
          price: partialMatch.valor || 0
        };
      }

      logger.warn(`Service not found: ${serviceName}`);
      return null;

    } catch (error) {
      logger.error('Error looking up service by name:', error.message);
      return null;
    }
  }

  getCacheStatus() {
    return {
      hasData: !!servicesCache,
      count: servicesCache?.length || 0,
      lastFetch: servicesLastFetch,
      isExpired: servicesLastFetch ? (Date.now() - servicesLastFetch) >= SERVICES_CACHE_TTL : true
    };
  }
}

module.exports = new ServicesCache();