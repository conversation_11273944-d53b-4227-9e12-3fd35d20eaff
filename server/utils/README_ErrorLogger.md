# Sistema de Logs de Erro - ErrorLogger

Sistema completo de logging de erros e fallbacks que gera arquivos individuais para cada erro, similar ao sistema de logs de mensagens.

## 📋 Funcionalidades

- ✅ **Log individual por erro**: Cada erro/fallback gera um arquivo separado
- ✅ **Categorização automática**: 8 categorias diferentes de erro
- ✅ **Sanitização de dados**: Remove automaticamente dados sensíveis 
- ✅ **Integração com sessões**: Referência cruzada com logs de mensagem
- ✅ **Wrappers automáticos**: Intercepta erros sem modificar código existente
- ✅ **Estatísticas via API**: Endpoints para monitoramento
- ✅ **Stack trace completo**: Rastreamento completo de erros
- ✅ **Contexto preservado**: Mantém dados relevantes de debugging

## 🏗️ Arquitetura

### Estrutura de Arquivos
```
server/
├── logs/errors/                           # Logs de erro (novo)
│   ├── error-2025-09-04-{timestamp}-{uuid}.log
│   └── error-2025-09-04-{timestamp}-{uuid}.log
├── logs/messages/                         # Logs de mensagem (existente)
└── utils/ErrorLogger.js                  # Sistema principal
```

### Categorias de Erro
```javascript
ERROR_CATEGORIES = {
  FALLBACK: 'FALLBACK',           // Uso de fallbacks/mocks
  API_ERROR: 'API_ERROR',         // Erros de APIs externas genéricas  
  IA_ERROR: 'IA_ERROR',          // Erros específicos de IA/Claude
  SYSTEM_ERROR: 'SYSTEM_ERROR',   // Erros internos do sistema
  VALIDATION_ERROR: 'VALIDATION_ERROR', // Validação de dados
  CACHE_ERROR: 'CACHE_ERROR',     // Erros de cache
  WHATSAPP_ERROR: 'WHATSAPP_ERROR', // WhatsApp/Providers
  TRINKS_ERROR: 'TRINKS_ERROR'    // API Trinks específica
}
```

## 🚀 Como Usar

### 1. Importação Básica
```javascript
const { errorLogger, ERROR_CATEGORIES } = require('../utils/ErrorLogger');
```

### 2. Log Manual de Erro
```javascript
try {
  // Operação que pode falhar
  const result = await riskyOperation();
} catch (error) {
  await errorLogger.logError(error, ERROR_CATEGORIES.API_ERROR, {
    function: 'riskyOperation',
    parameters: { id: 123 },
    additionalInfo: 'contexto relevante'
  });
  throw error; // Re-throw se necessário
}
```

### 3. Log de Fallback
```javascript
const result = await errorLogger.logFallback(
  'IA indisponível',
  'Usando resposta pré-configurada',
  { originalPrompt: prompt, fallbackResponse: mockResponse }
);
```

### 4. Wrapper Automático (Recomendado)
```javascript
// Para operações assíncronas
const result = await errorLogger.executeWithLogging(
  async () => await apiCall(),
  ERROR_CATEGORIES.API_ERROR,
  { endpoint: '/api/data', method: 'GET' }
);

// Para funções síncronas  
const result = errorLogger.executeWithLoggingSync(
  () => processData(),
  ERROR_CATEGORIES.SYSTEM_ERROR,
  { dataSize: data.length }
);
```

### 5. Wrapper de Função (Uma vez só)
```javascript
const wrappedFunction = errorLogger.wrapFunction(
  originalFunction,
  ERROR_CATEGORIES.SYSTEM_ERROR,
  (args) => ({ params: args, context: 'auto-wrap' })
);

// Usar normalmente - erros são logados automaticamente
const result = wrappedFunction(param1, param2);
```

## 🔗 Integração Automática

### AI Service (ai.js)
- ✅ **Integrado**: Erros de Claude, OpenAI, geração de resposta
- ✅ **Session linking**: ErrorLogger automaticamente usa sessionId do MessageLogger
- ✅ **Categorização**: IA_ERROR para erros específicos de LLM

### Trinks Service (trinks.js)
- ✅ **Integrado**: Erros de API, criação de agendamentos, clientes
- ✅ **Categorização**: TRINKS_ERROR para erros específicos da API Trinks

### Cache System (HumanizedResponseCache.js)
- ✅ **Integrado**: Erros de cache, geração de templates
- ✅ **Categorização**: CACHE_ERROR para erros de cache

### LangGraph Nodes
- ✅ **Integrado**: IntentClassificationNode e outros nós críticos
- ✅ **Categorização**: IA_ERROR para falhas de processamento

## 📊 Monitoramento via API

### Estatísticas de Erro
```bash
GET /api/debug/error-stats?days=7

{
  "success": true,
  "data": {
    "totalErrors": 15,
    "byCategory": {
      "IA_ERROR": 8,
      "TRINKS_ERROR": 4,
      "SYSTEM_ERROR": 3
    },
    "recentErrors": [...],
    "period": "7 days"
  }
}
```

### Teste de Log (Desenvolvimento)
```bash
POST /api/debug/test-error-log
{
  "category": "SYSTEM_ERROR",
  "message": "Teste personalizado"
}
```

## 📄 Formato do Log

```
================================================================================
TRINKS IA - LOG DE ERRO DETALHADO  
================================================================================
Error ID: 1757002167577-481e0ab8
Timestamp: 2025-09-04T16:09:27.577Z
Category: SYSTEM_ERROR
Session ID: message-session-12345 (se disponível)
Process ID: 20017
Environment: development
================================================================================

🚨 ERROR DETAILS:
Name: Error
Message: Falha na operação crítica

📋 STACK TRACE:
Error: Falha na operação crítica
    at criticalOperation (/path/to/file.js:123:45)
    at processData (/path/to/service.js:67:12)
    ...

🔍 CONTEXT:
{
  "function": "criticalOperation",
  "parameters": {
    "userId": 123,
    "apiKey": "***MASKED***",
    "data": "dados normais"
  },
  "timestamp": "2025-09-04T16:09:27.576Z"
}

================================================================================
FIM DO LOG DE ERRO - Error ID: 1757002167577-481e0ab8
================================================================================
```

## 🔒 Segurança

### Sanitização Automática
Dados sensíveis são automaticamente mascarados:
- `password`, `token`, `apikey`, `api_key`
- `secret`, `authorization`, `auth`
- `anthropic_api_key`, `trinks_api_key`

### Limitação de Tamanho
- Strings > 5000 chars são truncadas
- Objetos grandes são limitados para prevenir logs gigantes

## ⚡ Performance

- **Assíncrono**: Não bloqueia operações críticas
- **Fallback graceful**: Se salvar log falhar, operação continua
- **Memory efficient**: Logs são escritos diretamente em disco
- **Auto-rotation**: Arquivos organizados por data

## 🧪 Testes

Execute os testes completos:
```bash
cd server
node test-error-logging.js
```

Testes incluem:
- ✅ Log básico de erro
- ✅ Log de fallback  
- ✅ Sanitização de dados sensíveis
- ✅ Wrappers automáticos
- ✅ Integração com session
- ✅ Estatísticas
- ✅ Validação de arquivos

## 🎯 Boas Práticas

### 1. Sempre Use Contexto
```javascript
await errorLogger.logError(error, ERROR_CATEGORIES.API_ERROR, {
  function: 'nomeDaFuncao',
  parameters: { /* params relevantes */ },
  endpoint: '/api/endpoint',
  method: 'POST'
});
```

### 2. Categorize Corretamente
- `IA_ERROR`: Claude, OpenAI, análise de linguagem
- `TRINKS_ERROR`: API Trinks específica
- `API_ERROR`: APIs externas genéricas
- `FALLBACK`: Quando sistema usa fallback/mock
- `SYSTEM_ERROR`: Erros internos, validação

### 3. Use Wrappers Quando Possível
```javascript
// ✅ Bom: Wrapper automático
const result = await errorLogger.executeWithLogging(operation, category, context);

// ⚠️ Aceitável: Log manual
try {
  const result = await operation();
} catch (error) {
  await errorLogger.logError(error, category, context);
  throw error;
}
```

### 4. Evite Logs Duplicados
O sistema já detecta alguns casos, mas evite:
```javascript
// ❌ Ruim: Log duplicado
try {
  await operation();
} catch (error) {
  await errorLogger.logError(error, category, context);
  await errorLogger.logError(error, category, context); // Duplicado!
  throw error;
}
```

## 🔧 Configuração

### Variáveis de Ambiente
```bash
NODE_ENV=development  # Habilita endpoint de teste
# Logs automáticos não requerem configuração adicional
```

### Personalização
```javascript
// Modificar TTL padrão (em ErrorLogger.js)
// Adicionar novas categorias
// Customizar sanitização
```

Este sistema fornece rastreabilidade completa de todos os erros e fallbacks do sistema, facilitando debugging, monitoramento e melhoria contínua da aplicação.