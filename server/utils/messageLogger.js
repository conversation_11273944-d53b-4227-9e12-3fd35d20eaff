const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// Create message logs directory
const messageLogsDir = path.join(__dirname, '../logs/messages');
if (!fs.existsSync(messageLogsDir)) {
  fs.mkdirSync(messageLogsDir, { recursive: true });
}

class MessageLogger {
  constructor() {
    this.sessionId = null;
    this.startTime = null;
    this.logs = [];
    this.originalConsole = null;
    this._isCapturing = false;
    this.essentialLogs = [];
  }

  get conciseMode() {
    return process.env.CONCISE_LOGS !== 'false';
  }

  /**
   * Inicia a captura de logs para uma nova mensagem
   */
  startSession(customerPhone, message, messageType = 'text') {
    this.sessionId = `${Date.now()}-${uuidv4().substring(0, 8)}`;
    this.startTime = new Date();
    this.logs = [];
    this.essentialLogs = [];
    this._isCapturing = true;

    // Log essencial: início da sessão
    this.addEssentialLog('USER_MESSAGE', {
      phone: customerPhone,
      message: message,
      messageType: messageType,
      timestamp: this.startTime.toISOString()
    });

    // Inicializar com informações da sessão (modo verbose)
    this.addLog('SESSION_START', {
      sessionId: this.sessionId,
      customerPhone: customerPhone,
      message: message,
      messageType: messageType,
      timestamp: this.startTime.toISOString(),
      processStarted: true
    });

    // Interceptar console.log para capturar todos os logs
    if (!this.conciseMode) {
      this.interceptConsole();
    }

    return this.sessionId;
  }

  /**
   * Adiciona um log estruturado à sessão
   */
  addLog(level, data) {
    if (!this.isCapturing) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      level: level,
      data: data,
      sessionId: this.sessionId
    };

    this.logs.push(logEntry);
  }

  /**
   * Adiciona um log essencial (sempre capturado)
   */
  addEssentialLog(type, data) {
    if (!this.isCapturing) return;

    const logEntry = {
      timestamp: new Date().toISOString(),
      type: type,
      data: data,
      sessionId: this.sessionId
    };

    this.essentialLogs.push(logEntry);
  }

  /**
   * Intercepta console.log para capturar todas as saídas
   */
  interceptConsole() {
    if (this.originalConsole) return; // Já interceptando

    this.originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
      debug: console.debug
    };

    // Interceptar console.log
    console.log = (...args) => {
      this.captureConsoleOutput('LOG', args);
      this.originalConsole.log.apply(console, args);
    };

    // Interceptar console.error
    console.error = (...args) => {
      this.captureConsoleOutput('ERROR', args);
      this.originalConsole.error.apply(console, args);
    };

    // Interceptar console.warn
    console.warn = (...args) => {
      this.captureConsoleOutput('WARN', args);
      this.originalConsole.warn.apply(console, args);
    };

    // Interceptar console.info
    console.info = (...args) => {
      this.captureConsoleOutput('INFO', args);
      this.originalConsole.info.apply(console, args);
    };

    // Interceptar console.debug
    console.debug = (...args) => {
      this.captureConsoleOutput('DEBUG', args);
      this.originalConsole.debug.apply(console, args);
    };
  }

  /**
   * Captura saída do console e adiciona aos logs
   */
  captureConsoleOutput(level, args) {
    if (!this.isCapturing) return;

    const message = args.map(arg => {
      if (typeof arg === 'object') {
        try {
          return JSON.stringify(arg, null, 2);
        } catch (e) {
          return String(arg);
        }
      }
      return String(arg);
    }).join(' ');

    // Capturar erros essenciais com stack completa
    if (level === 'ERROR') {
      const errorData = {
        message: message,
        timestamp: new Date().toISOString()
      };

      // Tentar capturar stack trace se houver Error objects
      const errorObjects = args.filter(arg => arg instanceof Error);
      if (errorObjects.length > 0) {
        errorData.errors = errorObjects.map(err => ({
          name: err.name,
          message: err.message,
          stack: err.stack,
          cause: err.cause
        }));
      }

      // Capturar stack trace atual para contexto
      const stack = new Error().stack;
      if (stack) {
        errorData.captureStack = stack;
      }

      this.addEssentialLog('ERROR_COMPLETE', errorData);
    }

    this.addLog(`CONSOLE_${level}`, {
      message: message,
      rawArgs: args.map(arg => {
        // Para erros, manter informações completas
        if (arg instanceof Error) {
          return {
            name: arg.name,
            message: arg.message,
            stack: arg.stack,
            cause: arg.cause
          };
        }
        
        // Sanitizar objetos muito grandes (exceto erros)
        if (typeof arg === 'object' && arg !== null) {
          try {
            const str = JSON.stringify(arg);
            if (str.length > 5000) {
              return { _truncated: true, preview: str.substring(0, 500) + '...' };
            }
            return arg;
          } catch (e) {
            return { _error: 'Failed to serialize object' };
          }
        }
        return arg;
      })
    });
  }

  /**
   * Restaura console original
   */
  restoreConsole() {
    if (!this.originalConsole) return;

    console.log = this.originalConsole.log;
    console.error = this.originalConsole.error;
    console.warn = this.originalConsole.warn;
    console.info = this.originalConsole.info;
    console.debug = this.originalConsole.debug;

    this.originalConsole = null;
  }

  /**
   * Finaliza a sessão e salva o arquivo de log
   */
  async endSession(result = null, error = null) {
    if (!this.isCapturing) return null;

    const endTime = new Date();
    const duration = endTime.getTime() - this.startTime.getTime();

    // Capturar erro completo se houver
    if (error) {
      this.logError(error, { 
        sessionId: this.sessionId, 
        phase: 'session_end',
        duration: `${duration}ms`
      });
    }

    // Adicionar informações finais
    this.addLog('SESSION_END', {
      sessionId: this.sessionId,
      duration: `${duration}ms`,
      endTime: endTime.toISOString(),
      success: !error,
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
        cause: error.cause
      } : null,
      result: result ? {
        response: result.response, // Resposta completa
        stage: result.stage,
        hasDebugInfo: !!result.debugInfo
      } : null,
      totalLogs: this.logs.length,
      essentialLogs: this.essentialLogs.length
    });

    // Restaurar console
    this.restoreConsole();
    this._isCapturing = false;

    // Salvar arquivo
    const filename = await this.saveLogFile();

    // Limpar dados da sessão
    const sessionData = {
      sessionId: this.sessionId,
      filename: filename,
      duration: duration,
      totalLogs: this.logs.length,
      success: !error
    };

    this.sessionId = null;
    this.startTime = null;
    this.logs = [];
    this.essentialLogs = [];

    return sessionData;
  }

  /**
   * Salva o arquivo de log da sessão
   */
  async saveLogFile() {
    if (!this.sessionId || this.logs.length === 0) return null;

    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const filename = `message-${date}-${this.sessionId}.log`;
    const filepath = path.join(messageLogsDir, filename);

    // Preparar conteúdo do log
    const logContent = this.formatLogContent();

    try {
      await fs.promises.writeFile(filepath, logContent, 'utf8');
      console.log(`📄 Log da mensagem salvo: ${filename}`);
      return filename;
    } catch (error) {
      console.error('❌ Erro ao salvar log da mensagem:', error.message);
      return null;
    }
  }

  /**
   * Formata o conteúdo do log para arquivo
   */
  formatLogContent() {
    if (this.conciseMode) {
      return this.formatConciseLogContent();
    }
    return this.formatVerboseLogContent();
  }

  /**
   * Formata logs no modo conciso (apenas informações essenciais)
   */
  formatConciseLogContent() {
    const duration = new Date().getTime() - this.startTime.getTime();
    const header = `
================================================================================
TRINKS IA - LOG ESSENCIAL DA MENSAGEM
================================================================================
Session ID: ${this.sessionId}
Data/Hora: ${this.startTime.toISOString()}
Duração: ${duration}ms
Logs Essenciais: ${this.essentialLogs.length}
================================================================================

`;

    const logEntries = this.essentialLogs.map(entry => {
      const timestamp = new Date(entry.timestamp).toLocaleString('pt-BR');
      const type = entry.type.padEnd(15, ' ');
      
      let content = '';
      if (entry.type === 'USER_MESSAGE') {
        content = `📱 ${entry.data.phone}: "${entry.data.message}"`;
      } else if (entry.type === 'AI_RESPONSE') {
        content = `🤖 IA: "${entry.data.response}" (${entry.data.duration}, ${entry.data.model})`;
      } else if (entry.type === 'PROMPT_COMPLETE') {
        content = `📝 Prompt COMPLETO (${entry.data.type}, ${entry.data.length} chars):\n${entry.data.prompt}`;
      } else if (entry.type === 'ERROR_COMPLETE') {
        let errorContent = `❌ ERRO COMPLETO: ${entry.data.message}`;
        if (entry.data.errors && entry.data.errors.length > 0) {
          errorContent += `\n\n🔍 Error Objects:`;
          entry.data.errors.forEach((err, i) => {
            errorContent += `\n  ${i+1}. ${err.name}: ${err.message}`;
            if (err.stack) errorContent += `\n     Stack: ${err.stack}`;
            if (err.cause) errorContent += `\n     Cause: ${err.cause}`;
          });
        }
        if (entry.data.captureStack) {
          errorContent += `\n\n📍 Capture Stack:\n${entry.data.captureStack}`;
        }
        content = errorContent;
      } else if (entry.type === 'EXCEPTION_COMPLETE') {
        content = `🚨 EXCEÇÃO: ${entry.data.name} - ${entry.data.message}\n\n📍 Stack Trace:\n${entry.data.stack}`;
        if (entry.data.cause) content += `\n\n🔗 Cause: ${entry.data.cause}`;
        if (Object.keys(entry.data.context).length > 0) {
          content += `\n\n🔍 Context: ${JSON.stringify(entry.data.context, null, 2)}`;
        }
      } else if (entry.type === 'API_ERROR') {
        const statusText = entry.data.status ? ` [${entry.data.status}]` : '';
        
        // ENHANCED: Log format with detailed network diagnostics
        content = `🔥 API Error: ${entry.data.method} ${entry.data.endpoint}${statusText} - ${entry.data.error} (${entry.data.duration})`;
        
        // Adicionar detalhes diagnósticos se disponível
        if (entry.data.errorType) {
          content += `\n    🔍 Type: ${entry.data.errorType}`;
        }
        if (entry.data.networkPhase) {
          content += ` | Phase: ${entry.data.networkPhase}`;
        }
        if (entry.data.debugSuggestion) {
          content += `\n    💡 Suggestion: ${entry.data.debugSuggestion}`;
        }
      } else if (entry.type === 'FALLBACK_USED') {
        content = `⚠️ Fallback: ${entry.data.reason} - usando ${entry.data.fallbackType}`;
      } else {
        content = JSON.stringify(entry.data, null, 2);
      }

      return `[${timestamp}] [${type}] ${content}\n`;
    }).join('\n');

    const footer = `
================================================================================
FIM DO LOG - Session: ${this.sessionId}
================================================================================
`;

    return header + logEntries + footer;
  }

  /**
   * Formata logs no modo verbose (formato original)
   */
  formatVerboseLogContent() {
    const header = `
================================================================================
TRINKS IA - LOG DETALHADO DA MENSAGEM
================================================================================
Session ID: ${this.sessionId}
Data/Hora: ${this.startTime.toISOString()}
Duração: ${new Date().getTime() - this.startTime.getTime()}ms
Total de Logs: ${this.logs.length}
================================================================================

`;

    const logEntries = this.logs.map(entry => {
      const timestamp = new Date(entry.timestamp).toLocaleString('pt-BR');
      const level = entry.level.padEnd(15, ' ');
      
      let content = '';
      if (typeof entry.data === 'object') {
        content = JSON.stringify(entry.data, null, 2);
      } else {
        content = String(entry.data);
      }

      return `[${timestamp}] [${level}] ${content}\n`;
    }).join('\n');

    const footer = `
================================================================================
FIM DO LOG - Session: ${this.sessionId}
================================================================================
`;

    return header + logEntries + footer;
  }

  /**
   * Adiciona informações específicas de API calls com contexto completo de erro
   */
  logApiCall(method, endpoint, duration, success, details = {}) {
    // Log essencial apenas para falhas de API - MELHORADO
    if (!success) {
      // Extract comprehensive error information
      const errorInfo = {
        method: method,
        endpoint: endpoint,
        duration: `${duration}ms`,
        status: details.status || 0,
        timestamp: new Date().toISOString()
      };

      // ENHANCED: Usar diagnóstico detalhado de rede quando disponível
      if (details.networkDiagnostic) {
        // Usar diagnóstico detalhado para error message específico e útil
        const diagnostic = details.networkDiagnostic;
        errorInfo.error = diagnostic.rootCause;
        errorInfo.errorType = diagnostic.errorType;
        errorInfo.networkPhase = diagnostic.phase;
        
        // Adicionar contexto específico baseado no tipo de erro
        if (diagnostic.isDnsIssue) {
          errorInfo.debugSuggestion = 'Verificar conectividade de rede e DNS';
        } else if (diagnostic.isTimeoutIssue) {
          errorInfo.debugSuggestion = 'API pode estar sobrecarregada. Considerar aumentar timeout';
        } else if (diagnostic.isSslIssue) {
          errorInfo.debugSuggestion = 'Verificar certificados SSL da API Trinks';
        } else if (diagnostic.isConnectionIssue) {
          errorInfo.debugSuggestion = 'Verificar firewall e conectividade com api.trinks.com';
        } else if (diagnostic.errorType === 'HTTP_ERROR') {
          errorInfo.debugSuggestion = 'Verificar dados da requisição e status da API';
        }
      } else {
        // Fallback para lógica existente quando não há diagnóstico detalhado
        if (details.errorMessage) {
          errorInfo.error = details.errorMessage;
        } else if (details.errorData && typeof details.errorData === 'object') {
          // Try to extract meaningful error from API response
          if (details.errorData.message) {
            errorInfo.error = details.errorData.message;
          } else if (details.errorData.error) {
            errorInfo.error = details.errorData.error;
          } else if (details.errorData.detail) {
            errorInfo.error = details.errorData.detail;
          } else {
            errorInfo.error = JSON.stringify(details.errorData);
          }
        } else if (details.error) {
          errorInfo.error = details.error;
        } else if (details.message) {
          errorInfo.error = details.message;
        } else {
          // IMPROVED: Classificação inteligente baseada em status e flags
          if (details.isDnsError) {
            errorInfo.error = 'Falha na resolução DNS (não foi possível resolver api.trinks.com)';
          } else if (details.isTimeoutError) {
            errorInfo.error = 'Timeout na conexão com a API Trinks';
          } else if (details.isNetworkError && details.status === 0) {
            errorInfo.error = 'Erro de conectividade de rede - API inacessível';
          } else if (details.status >= 500) {
            errorInfo.error = 'Erro interno do servidor API Trinks';
          } else if (details.status >= 400) {
            errorInfo.error = `Erro na requisição HTTP ${details.status}`;
          } else {
            errorInfo.error = `HTTP ${details.status || 'Unknown'} - Network or API error occurred`;
          }
        }
      }

      // NOVO: Adicionar contexto de erro detalhado
      if (details.originalErrorType) {
        errorInfo.originalErrorType = details.originalErrorType;
      }
      
      if (details.originalErrorCode) {
        errorInfo.originalErrorCode = details.originalErrorCode;
      }

      // Classificar tipo de erro para melhor debugging
      if (details.isDnsError) {
        errorInfo.errorType = 'DNS_RESOLUTION_FAILURE';
      } else if (details.isTimeoutError) {
        errorInfo.errorType = 'REQUEST_TIMEOUT';
      } else if (details.isNetworkError) {
        errorInfo.errorType = 'NETWORK_ERROR';
      } else if (details.status >= 400) {
        errorInfo.errorType = 'HTTP_ERROR';
      } else {
        errorInfo.errorType = 'UNKNOWN_ERROR';
      }

      // Add error code if available
      if (details.errorCode) {
        errorInfo.errorCode = details.errorCode;
      }

      this.addEssentialLog('API_ERROR', errorInfo);
    }

    this.addLog('API_CALL', {
      method: method,
      endpoint: endpoint,
      duration: `${duration}ms`,
      success: success,
      timestamp: new Date().toISOString(),
      ...details
    });
  }

  /**
   * NOVO: Log específico para erros completos com stack trace e contexto
   */
  logCompleteError(error, context = {}) {
    if (!this.isCapturing) return;

    const timestamp = new Date().toISOString();
    
    // Extrair informações completas do erro
    const errorDetails = {
      name: error.name || 'Error',
      message: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace available',
      timestamp: timestamp
    };

    // Se é erro axios, capturar detalhes específicos
    if (error.isAxiosError || error.config || error.response) {
      errorDetails.axios = {
        isAxiosError: error.isAxiosError || false,
        code: error.code,
        syscall: error.syscall,
        hostname: error.hostname,
        errno: error.errno
      };

      if (error.config) {
        errorDetails.axios.request = {
          method: error.config.method,
          url: error.config.url,
          baseURL: error.config.baseURL,
          timeout: error.config.timeout
        };
      }

      if (error.response) {
        errorDetails.axios.response = {
          status: error.response.status,
          statusText: error.response.statusText,
          dataPreview: typeof error.response.data === 'string' 
            ? error.response.data.substring(0, 200)
            : JSON.stringify(error.response.data, null, 2).substring(0, 200)
        };
      }
    }

    // Adicionar contexto fornecido
    if (context && Object.keys(context).length > 0) {
      errorDetails.context = context;
    }

    // Log essencial com erro completo
    this.addEssentialLog('ERROR_COMPLETE', errorDetails);

    // Log verbose com ainda mais detalhes (se não estivermos em modo conciso)
    if (!this.conciseMode) {
      this.addLog('ERROR_FULL_DETAILS', {
        ...errorDetails,
        // Propriedades adicionais do erro
        errorProperties: Object.getOwnPropertyNames(error).reduce((props, prop) => {
          if (!['name', 'message', 'stack'].includes(prop)) {
            try {
              props[prop] = error[prop];
            } catch (e) {
              props[prop] = `[Error accessing: ${e.message}]`;
            }
          }
          return props;
        }, {}),
        // Stack trace atual para contexto
        captureLocation: new Error().stack
      });
    }
  }

  /**
   * NOVO: Referenciar logs de erro externos (como ErrorLogger)
   */
  referenceExternalError(errorId, errorFilename, category) {
    if (!this.isCapturing) return;

    this.addEssentialLog('ERROR_REFERENCE', {
      errorId: errorId,
      errorFile: errorFilename,
      category: category,
      timestamp: new Date().toISOString(),
      location: `logs/errors/${errorFilename}`
    });
  }

  /**
   * Adiciona informações de fallback
   */
  logFallback(reason, fallbackType, context = {}) {
    this.addEssentialLog('FALLBACK_USED', {
      reason: reason,
      fallbackType: fallbackType,
      context: context,
      timestamp: new Date().toISOString()
    });

    this.addLog('FALLBACK', {
      reason: reason,
      fallbackType: fallbackType,
      context: context,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Adiciona informações específicas da IA
   */
  logAIInteraction(type, prompt, response, duration, model = null) {
    // Log essencial: apenas resposta da IA
    if (response) {
      this.addEssentialLog('AI_RESPONSE', {
        response: response,
        duration: `${duration}ms`,
        model: model,
        timestamp: new Date().toISOString()
      });
    }

    // Log essencial: prompt COMPLETO sempre
    if (prompt) {
      this.addEssentialLog('PROMPT_COMPLETE', {
        prompt: prompt, // PROMPT COMPLETO sem truncar
        type: type,
        length: prompt.length,
        timestamp: new Date().toISOString()
      });
    }

    // Log verbose completo
    this.addLog('AI_INTERACTION', {
      type: type,
      prompt: prompt, // PROMPT COMPLETO também no verbose
      response: response,
      duration: `${duration}ms`,
      model: model,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Adiciona mudanças de estado da conversa
   */
  logStateChange(from, to, context = {}) {
    this.addLog('STATE_CHANGE', {
      from: from,
      to: to,
      context: context,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Adiciona informações de debug específicas
   */
  logDebugInfo(category, info) {
    this.addLog(`DEBUG_${category.toUpperCase()}`, {
      info: info,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log de erro completo com stack trace
   */
  logError(error, context = {}) {
    const errorData = {
      name: error.name || 'Unknown Error',
      message: error.message || 'No error message',
      stack: error.stack || 'No stack trace available',
      cause: error.cause,
      context: context,
      timestamp: new Date().toISOString()
    };

    // Log essencial
    this.addEssentialLog('EXCEPTION_COMPLETE', errorData);

    // Log verbose
    this.addLog('EXCEPTION', errorData);
  }

  /**
   * Obter estatísticas da sessão atual
   */
  getSessionStats() {
    if (!this.isCapturing) return null;

    const duration = new Date().getTime() - this.startTime.getTime();
    const logsByLevel = {};
    const essentialLogsByType = {};
    
    this.logs.forEach(log => {
      logsByLevel[log.level] = (logsByLevel[log.level] || 0) + 1;
    });

    this.essentialLogs.forEach(log => {
      essentialLogsByType[log.type] = (essentialLogsByType[log.type] || 0) + 1;
    });

    return {
      sessionId: this.sessionId,
      duration: `${duration}ms`,
      totalLogs: this.logs.length,
      essentialLogs: this.essentialLogs.length,
      logsByLevel: logsByLevel,
      essentialLogsByType: essentialLogsByType,
      conciseMode: this.conciseMode,
      isActive: this.isCapturing
    };
  }

  /**
   * Verifica se está capturando logs
   */
  get isCapturing() {
    return this._isCapturing || false;
  }

  set isCapturing(value) {
    this._isCapturing = value;
  }
}

// Criar instância singleton
const messageLogger = new MessageLogger();

module.exports = messageLogger;