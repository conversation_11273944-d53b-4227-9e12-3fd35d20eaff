const fs = require('fs');
const path = require('path');

/**
 * Logger especializado para interações com IA
 * 
 * Registra especificamente os prompts enviados para IA e as respostas
 * recebidas, facilitando debug de problemas relacionados à IA.
 */
class AIInteractionLogger {
  constructor() {
    this.logsDir = path.join(__dirname, '../logs/ai-interactions');
    this.ensureLogDirectory();
  }

  /**
   * Garante que o diretório de logs existe
   */
  ensureLogDirectory() {
    if (!fs.existsSync(this.logsDir)) {
      fs.mkdirSync(this.logsDir, { recursive: true });
      console.log('📁 Diretório de interações IA criado:', this.logsDir);
    }
  }

  /**
   * Gera nome do arquivo baseado na data e opcionalmente no cliente
   */
  getLogFilename(customerPhone = null) {
    const date = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    
    if (customerPhone) {
      // Por cliente específico: ai-interactions-2025-09-02-5521998217917.log
      const phoneClean = customerPhone.replace(/\D/g, ''); // Remove caracteres especiais
      return `ai-interactions-${date}-${phoneClean}.log`;
    }
    
    // Arquivo geral (mantido para compatibilidade): ai-interactions-2025-09-02.log
    return `ai-interactions-${date}.log`;
  }

  /**
   * Formata timestamp para log legível
   */
  formatTimestamp(timestamp = new Date()) {
    return timestamp.toLocaleString('pt-BR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    });
  }

  /**
   * Log de prompt enviado para IA
   */
  async logPromptSent(sessionId, customerPhone, promptData) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    const logEntry = `
[${timestamp}] 🔄 PROMPT ENVIADO
╔══════════════════════════════════════════════════════════════════════════════
║ Session ID: ${sessionId}
║ Cliente: ${customerPhone}
║ Modelo: ${promptData.model || 'claude-sonnet-4-20250514'}
║ Template: ${promptData.templateType || 'N/A'}
║ Tipo: ${promptData.interactionType || 'general'}
║ Max Tokens: ${promptData.maxTokens || 'N/A'}
║ Temperature: ${promptData.temperature || 'N/A'}
╠══════════════════════════════════════════════════════════════════════════════
║ SYSTEM PROMPT:
║ ${this.formatMultilineText(promptData.systemPrompt || 'N/A')}
╠══════════════════════════════════════════════════════════════════════════════
║ USER MESSAGE:
║ ${this.formatMultilineText(promptData.userMessage || 'N/A')}
╠══════════════════════════════════════════════════════════════════════════════
║ CONTEXTO ADICIONAL:
║ ${this.formatContextData(promptData.context)}
╚══════════════════════════════════════════════════════════════════════════════

`;

    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Log de resposta recebida da IA
   */
  async logResponseReceived(sessionId, customerPhone, responseData) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    const logEntry = `
[${timestamp}] ✅ RESPOSTA IA RECEBIDA
╔══════════════════════════════════════════════════════════════════════════════
║ Session ID: ${sessionId}
║ Cliente: ${customerPhone}  
║ Duração: ${responseData.duration}ms
║ Modelo: ${responseData.model || 'claude-sonnet-4-20250514'}
║ Status: ${responseData.success ? '✅ Sucesso' : '❌ Erro'}
║ Tokens Usados: ${responseData.tokensUsed || 'N/A'}
╠══════════════════════════════════════════════════════════════════════════════
║ RESPOSTA:
║ ${this.formatMultilineText(responseData.response || responseData.error || 'N/A')}
╠══════════════════════════════════════════════════════════════════════════════
║ METADATA:
║ Stage: ${responseData.newStage || 'N/A'}
║ Context Updates: ${JSON.stringify(responseData.contextUpdates || {}, null, 2)}
║ Used Template: ${responseData.usedTemplate ? '✅ Sim' : '❌ Não'}
║ Cache Hit: ${responseData.fromCache ? '✅ Sim' : '❌ Não'}
╚══════════════════════════════════════════════════════════════════════════════

`;

    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Log de erro na IA
   */
  async logAIError(sessionId, customerPhone, errorData) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    const logEntry = `
[${timestamp}] ❌ ERRO NA IA
╔══════════════════════════════════════════════════════════════════════════════
║ Session ID: ${sessionId}
║ Cliente: ${customerPhone}
║ Tipo do Erro: ${errorData.errorType || 'UNKNOWN'}
║ Duração até Erro: ${errorData.duration}ms
╠══════════════════════════════════════════════════════════════════════════════
║ MENSAGEM DE ERRO:
║ ${this.formatMultilineText(errorData.errorMessage)}
╠══════════════════════════════════════════════════════════════════════════════
║ STACK TRACE:
║ ${this.formatMultilineText(errorData.stackTrace || 'N/A')}
╠══════════════════════════════════════════════════════════════════════════════
║ CONTEXTO DO ERRO:
║ ${JSON.stringify(errorData.context || {}, null, 2)}
╚══════════════════════════════════════════════════════════════════════════════

`;

    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Log de cache hit/miss
   */
  async logCacheEvent(sessionId, customerPhone, cacheData) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    const cacheEmoji = cacheData.hit ? '🎯' : '💨';
    const cacheStatus = cacheData.hit ? 'HIT' : 'MISS';

    const logEntry = `
[${timestamp}] ${cacheEmoji} CACHE ${cacheStatus}
╔══════════════════════════════════════════════════════════════════════════════
║ Session ID: ${sessionId}
║ Cliente: ${customerPhone}
║ Cache Type: ${cacheData.cacheType || 'N/A'}
║ Key: ${cacheData.cacheKey || 'N/A'}
║ TTL: ${cacheData.ttl || 'N/A'}
║ Age: ${cacheData.age || 'N/A'}
╠══════════════════════════════════════════════════════════════════════════════
║ DADOS:
║ ${this.formatMultilineText(JSON.stringify(cacheData.data || {}, null, 2))}
╚══════════════════════════════════════════════════════════════════════════════

`;

    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Log de uso de template específico
   */
  async logTemplateUsage(sessionId, customerPhone, templateData) {
    const filename = this.getLogFilename(customerPhone);
    const filepath = path.join(this.logsDir, filename);
    const timestamp = this.formatTimestamp();

    const logEntry = `
[${timestamp}] 🎨 TEMPLATE USADO
╔══════════════════════════════════════════════════════════════════════════════
║ Session ID: ${sessionId}
║ Cliente: ${customerPhone}
║ Template Type: ${templateData.templateType}
║ Template Name: ${templateData.templateName || 'N/A'}
║ Source: ${templateData.source || 'default'}
╠══════════════════════════════════════════════════════════════════════════════
║ VARIÁVEIS UTILIZADAS:
║ ${JSON.stringify(templateData.variables || {}, null, 2)}
╠══════════════════════════════════════════════════════════════════════════════
║ TEMPLATE PROCESSADO:
║ ${this.formatMultilineText(templateData.processedTemplate || 'N/A')}
╚══════════════════════════════════════════════════════════════════════════════

`;

    await this.appendToFile(filepath, logEntry);
  }

  /**
   * Formata texto multilinha com indentação
   */
  formatMultilineText(text, maxLength = 2000) {
    if (!text) return 'N/A';
    
    // Truncar se muito longo
    const truncatedText = text.length > maxLength 
      ? text.substring(0, maxLength) + '\n║ ... [TRUNCADO] ...'
      : text;

    // Adicionar prefixo ║ em cada linha
    return truncatedText
      .split('\n')
      .map(line => `║ ${line}`)
      .join('\n');
  }

  /**
   * Formata dados de contexto
   */
  formatContextData(context) {
    if (!context) return '║ Nenhum contexto adicional';

    const contextItems = [];
    
    if (context.availabilityContext) {
      contextItems.push(`║ • Disponibilidade: ${context.availabilityContext.professionalName} (${context.availabilityContext.totalSlots} slots)`);
    }
    
    if (context.appointmentData) {
      contextItems.push(`║ • Agendamento: ${JSON.stringify(context.appointmentData)}`);
    }

    if (context.customerData) {
      contextItems.push(`║ • Cliente: ${context.customerData.name || 'N/A'} (Tipo: ${context.customerData.type || 'N/A'})`);
    }

    if (context.conversationStage) {
      contextItems.push(`║ • Estágio: ${context.conversationStage}`);
    }

    return contextItems.length > 0 ? contextItems.join('\n') : '║ Contexto vazio';
  }

  /**
   * Escreve no arquivo de log
   */
  async appendToFile(filepath, content) {
    try {
      await fs.promises.appendFile(filepath, content, 'utf8');
    } catch (error) {
      console.error('❌ Erro ao escrever log de IA:', error.message);
    }
  }

  /**
   * Gera estatísticas diárias de uso da IA
   */
  async generateDailyStats(date = new Date().toISOString().split('T')[0]) {
    const filename = `ai-interactions-${date}.log`;
    const filepath = path.join(this.logsDir, filename);

    try {
      if (!fs.existsSync(filepath)) {
        return { error: 'Log não encontrado para esta data' };
      }

      const content = await fs.promises.readFile(filepath, 'utf8');
      
      const stats = {
        date: date,
        totalInteractions: (content.match(/🔄 PROMPT ENVIADO/g) || []).length,
        totalResponses: (content.match(/✅ RESPOSTA IA RECEBIDA/g) || []).length,
        totalErrors: (content.match(/❌ ERRO NA IA/g) || []).length,
        cacheHits: (content.match(/🎯 CACHE HIT/g) || []).length,
        cacheMisses: (content.match(/💨 CACHE MISS/g) || []).length,
        templatesUsed: (content.match(/🎨 TEMPLATE USADO/g) || []).length,
        uniqueCustomers: new Set(
          (content.match(/Cliente: \+\d+/g) || [])
          .map(match => match.replace('Cliente: ', ''))
        ).size
      };

      stats.cacheHitRate = stats.cacheHits + stats.cacheMisses > 0 
        ? ((stats.cacheHits / (stats.cacheHits + stats.cacheMisses)) * 100).toFixed(1) + '%'
        : '0%';

      stats.errorRate = stats.totalInteractions > 0
        ? ((stats.totalErrors / stats.totalInteractions) * 100).toFixed(1) + '%'
        : '0%';

      return stats;
    } catch (error) {
      console.error('❌ Erro ao gerar estatísticas de IA:', error.message);
      return { error: error.message };
    }
  }

  /**
   * Limpa logs antigos (manter últimos N dias)
   */
  async cleanOldLogs(daysToKeep = 30) {
    try {
      const files = await fs.promises.readdir(this.logsDir);
      const now = Date.now();
      const maxAge = daysToKeep * 24 * 60 * 60 * 1000;

      for (const file of files) {
        const filepath = path.join(this.logsDir, file);
        const stats = await fs.promises.stat(filepath);
        
        if (now - stats.mtime.getTime() > maxAge) {
          await fs.promises.unlink(filepath);
          console.log(`🗑️ Log de IA antigo removido: ${file}`);
        }
      }
    } catch (error) {
      console.error('❌ Erro ao limpar logs de IA antigos:', error.message);
    }
  }
}

module.exports = new AIInteractionLogger();