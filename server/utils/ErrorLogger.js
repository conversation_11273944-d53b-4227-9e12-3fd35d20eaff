const path = require('path')
const fs = require('fs')
const { v4: uuidv4 } = require('uuid')

// Create error logs directory
const errorLogsDir = path.join(__dirname, '../logs/errors')
if (!fs.existsSync(errorLogsDir)) {
  fs.mkdirSync(errorLogsDir, { recursive: true })
}

// Error categories
const ERROR_CATEGORIES = {
  FALLBACK: 'FALLBACK',
  API_ERROR: 'API_ERROR',
  IA_ERROR: 'IA_ERROR',
  SYSTEM_ERROR: 'SYSTEM_ERROR',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  CACHE_ERROR: 'CACHE_ERROR',
  WHATSAPP_ERROR: 'WHATSAPP_ERROR',
  TRINKS_ERROR: 'TRINKS_ERROR'
}

class ErrorLogger {
  constructor () {
    this.currentSessionId = null
  }

  /**
   * Define o session ID atual (para referenciar logs de mensagem)
   */
  setSessionId (sessionId) {
    this.currentSessionId = sessionId
  }

  /**
   * Limpa o session ID atual
   */
  clearSessionId () {
    this.currentSessionId = null
  }

  /**
   * Loga um erro em arquivo individual com máximo detalhamento
   */
  async logError (error, category = ERROR_CATEGORIES.SYSTEM_ERROR, context = {}) {
    const errorId = `${Date.now()}-${uuidv4().substring(0, 8)}`
    const timestamp = new Date()

    // Capturar detalhes completos do erro
    const errorDetails = this.extractCompleteErrorDetails(error)

    // Preparar dados do erro
    const errorData = {
      errorId,
      timestamp: timestamp.toISOString(),
      category,
      sessionId: this.currentSessionId,
      error: errorDetails,
      context: this.sanitizeContext(context),
      systemInfo: {
        nodeEnv: process.env.NODE_ENV,
        timestamp: Date.now(),
        processId: process.pid,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        platform: process.platform,
        nodeVersion: process.version
      }
    }

    // Salvar arquivo
    const filename = await this.saveErrorFile(errorData)

    // Log no console também (para não quebrar logs existentes)
    console.error(`❌ [${category}] ${error.message}`)
    if (filename) {
      console.error(`📄 Log de erro salvo: ${filename}`)
    }

    return {
      errorId,
      filename,
      category,
      sessionId: this.currentSessionId
    }
  }

  /**
   * Extrai todos os detalhes possíveis de um erro, especialmente erros de axios
   */
  extractCompleteErrorDetails (error) {
    const details = {
      name: error.name || 'Error',
      message: error.message || 'Unknown error',
      stack: error.stack || 'No stack trace available',
      type: this.getErrorType(error)
    }

    // Se é erro de axios, capturar detalhes específicos
    if (error.isAxiosError || error.config || error.response || error.request) {
      details.axios = this.extractAxiosErrorDetails(error)
    }

    // Capturar propriedades adicionais do erro
    const errorProps = Object.getOwnPropertyNames(error)
    details.additionalProperties = {}
    
    errorProps.forEach(prop => {
      if (!['name', 'message', 'stack'].includes(prop)) {
        try {
          const value = error[prop]
          // Evitar referências circulares
          if (value && typeof value === 'object') {
            details.additionalProperties[prop] = JSON.parse(JSON.stringify(value, null, 2))
          } else {
            details.additionalProperties[prop] = value
          }
        } catch (err) {
          details.additionalProperties[prop] = `[Error extracting: ${err.message}]`
        }
      }
    })

    return details
  }

  /**
   * Determina o tipo específico do erro para melhor categorização
   */
  getErrorType (error) {
    if (error.isAxiosError) {
      if (error.code === 'ENOTFOUND') return 'DNS_RESOLUTION_ERROR'
      if (error.code === 'ECONNREFUSED') return 'CONNECTION_REFUSED'
      if (error.code === 'ETIMEDOUT') return 'TIMEOUT_ERROR'
      if (error.code === 'ECONNRESET') return 'CONNECTION_RESET'
      if (error.code === 'CERT_HAS_EXPIRED') return 'SSL_CERTIFICATE_EXPIRED'
      if (error.code === 'UNABLE_TO_VERIFY_LEAF_SIGNATURE') return 'SSL_VERIFICATION_ERROR'
      if (error.response) return 'HTTP_RESPONSE_ERROR'
      if (error.request) return 'HTTP_REQUEST_ERROR'
      return 'AXIOS_ERROR'
    }

    if (error.name === 'TypeError') return 'TYPE_ERROR'
    if (error.name === 'ReferenceError') return 'REFERENCE_ERROR'
    if (error.name === 'SyntaxError') return 'SYNTAX_ERROR'
    if (error.name === 'RangeError') return 'RANGE_ERROR'

    return 'UNKNOWN_ERROR'
  }

  /**
   * Extrai detalhes específicos de erros do axios
   */
  extractAxiosErrorDetails (error) {
    const axiosDetails = {
      isAxiosError: error.isAxiosError || false,
      code: error.code || null,
      errno: error.errno || null,
      syscall: error.syscall || null,
      hostname: error.hostname || null
    }

    // Detalhes da configuração da requisição
    if (error.config) {
      axiosDetails.config = {
        url: error.config.url,
        method: error.config.method?.toUpperCase(),
        baseURL: error.config.baseURL,
        timeout: error.config.timeout,
        headers: this.sanitizeHeaders(error.config.headers),
        params: error.config.params,
        data: this.truncateData(error.config.data),
        maxContentLength: error.config.maxContentLength,
        maxBodyLength: error.config.maxBodyLength,
        validateStatus: error.config.validateStatus?.toString()
      }
    }

    // Detalhes da requisição HTTP
    if (error.request) {
      axiosDetails.request = {
        method: error.request.method,
        path: error.request.path,
        host: error.request.host,
        protocol: error.request.protocol,
        port: error.request.port,
        timeout: error.request.timeout,
        headers: this.sanitizeHeaders(error.request._headers),
        aborted: error.request.aborted,
        complete: error.request.complete,
        readyState: error.request.readyState,
        status: error.request.status || null,
        statusText: error.request.statusText || null
      }
    }

    // Detalhes da resposta HTTP (se houver)
    if (error.response) {
      axiosDetails.response = {
        status: error.response.status,
        statusText: error.response.statusText,
        headers: this.sanitizeHeaders(error.response.headers),
        data: this.truncateData(error.response.data),
        contentLength: error.response.headers?.['content-length'],
        contentType: error.response.headers?.['content-type']
      }
    }

    return axiosDetails
  }

  /**
   * Sanitiza headers removendo informações sensíveis
   */
  sanitizeHeaders (headers) {
    if (!headers) return null

    const sanitized = { ...headers }
    const sensitiveHeaders = ['authorization', 'x-api-key', 'cookie', 'set-cookie']

    sensitiveHeaders.forEach(header => {
      const variations = [header, header.toUpperCase(), header.toLowerCase()]
      variations.forEach(variation => {
        if (sanitized[variation]) {
          sanitized[variation] = '***MASKED***'
        }
      })
    })

    return sanitized
  }

  /**
   * Trunca dados grandes para evitar logs excessivamente grandes
   */
  truncateData (data) {
    if (!data) return null

    try {
      const stringified = typeof data === 'string' ? data : JSON.stringify(data)
      if (stringified.length > 2000) {
        return stringified.substring(0, 2000) + '... [TRUNCATED]'
      }
      return data
    } catch (error) {
      return '[Error serializing data]'
    }
  }

  /**
   * Loga um fallback em arquivo individual
   */
  async logFallback (reason, fallbackAction, context = {}) {
    const fallbackError = new Error(`Fallback activated: ${reason}`)
    fallbackError.name = 'FallbackActivated'

    const extendedContext = {
      ...context,
      fallbackReason: reason,
      fallbackAction,
      isFallback: true
    }

    return await this.logError(fallbackError, ERROR_CATEGORIES.FALLBACK, extendedContext)
  }

  /**
   * Salva o arquivo de log do erro
   */
  async saveErrorFile (errorData) {
    const date = new Date().toISOString().split('T')[0] // YYYY-MM-DD
    const filename = `error-${date}-${errorData.errorId}.log`
    const filepath = path.join(errorLogsDir, filename)

    // Preparar conteúdo do log
    const logContent = this.formatErrorContent(errorData)

    try {
      await fs.promises.writeFile(filepath, logContent, 'utf8')
      return filename
    } catch (saveError) {
      console.error('❌ Erro ao salvar log de erro:', saveError.message)
      return null
    }
  }

  /**
   * Formata o conteúdo do log de erro para arquivo com máximo detalhamento
   */
  formatErrorContent (errorData) {
    const header = `
================================================================================
TRINKS IA - LOG DE ERRO DETALHADO
================================================================================
Error ID: ${errorData.errorId}
Timestamp: ${errorData.timestamp}
Category: ${errorData.category}
Session ID: ${errorData.sessionId || 'N/A'}
Process ID: ${errorData.systemInfo.processId}
Environment: ${errorData.systemInfo.nodeEnv || 'unknown'}
Platform: ${errorData.systemInfo.platform}
Node Version: ${errorData.systemInfo.nodeVersion}
Memory Usage: RSS=${Math.round(errorData.systemInfo.memory.rss / 1024 / 1024)}MB, Heap=${Math.round(errorData.systemInfo.memory.heapUsed / 1024 / 1024)}MB
Uptime: ${Math.round(errorData.systemInfo.uptime)}s
================================================================================

`

    const errorSection = `
🚨 ERROR DETAILS:
Name: ${errorData.error.name}
Message: ${errorData.error.message}
Type: ${errorData.error.type}

📋 FULL STACK TRACE:
${errorData.error.stack}

`

    let axiosSection = ''
    if (errorData.error.axios) {
      axiosSection = this.formatAxiosErrorSection(errorData.error.axios)
    }

    let additionalPropsSection = ''
    if (errorData.error.additionalProperties && Object.keys(errorData.error.additionalProperties).length > 0) {
      additionalPropsSection = `
🔧 ADDITIONAL ERROR PROPERTIES:
${JSON.stringify(errorData.error.additionalProperties, null, 2)}

`
    }

    const contextSection = `
🔍 CONTEXT:
${JSON.stringify(errorData.context, null, 2)}

`

    const footer = `
================================================================================
FIM DO LOG DE ERRO - Error ID: ${errorData.errorId}
================================================================================
`

    return header + errorSection + axiosSection + additionalPropsSection + contextSection + footer
  }

  /**
   * Formata seção específica de erros do axios
   */
  formatAxiosErrorSection (axiosData) {
    let section = `
🌐 AXIOS ERROR DETAILS:
Is Axios Error: ${axiosData.isAxiosError}
Error Code: ${axiosData.code || 'N/A'}
System Call: ${axiosData.syscall || 'N/A'}
Hostname: ${axiosData.hostname || 'N/A'}
Errno: ${axiosData.errno || 'N/A'}

`

    if (axiosData.config) {
      section += `
📤 REQUEST CONFIGURATION:
URL: ${axiosData.config.url || 'N/A'}
Method: ${axiosData.config.method || 'N/A'}
Base URL: ${axiosData.config.baseURL || 'N/A'}
Timeout: ${axiosData.config.timeout || 'N/A'}ms
Max Content Length: ${axiosData.config.maxContentLength || 'N/A'}
Max Body Length: ${axiosData.config.maxBodyLength || 'N/A'}

Headers:
${JSON.stringify(axiosData.config.headers, null, 2)}

Params:
${JSON.stringify(axiosData.config.params, null, 2)}

Data:
${typeof axiosData.config.data === 'string' ? axiosData.config.data : JSON.stringify(axiosData.config.data, null, 2)}

`
    }

    if (axiosData.request) {
      section += `
🔗 HTTP REQUEST DETAILS:
Method: ${axiosData.request.method || 'N/A'}
Path: ${axiosData.request.path || 'N/A'}
Host: ${axiosData.request.host || 'N/A'}
Protocol: ${axiosData.request.protocol || 'N/A'}
Port: ${axiosData.request.port || 'N/A'}
Aborted: ${axiosData.request.aborted}
Complete: ${axiosData.request.complete}
Ready State: ${axiosData.request.readyState || 'N/A'}
Status: ${axiosData.request.status || 'N/A'}
Status Text: ${axiosData.request.statusText || 'N/A'}

Request Headers:
${JSON.stringify(axiosData.request.headers, null, 2)}

`
    }

    if (axiosData.response) {
      section += `
📥 HTTP RESPONSE DETAILS:
Status: ${axiosData.response.status}
Status Text: ${axiosData.response.statusText}
Content Type: ${axiosData.response.contentType || 'N/A'}
Content Length: ${axiosData.response.contentLength || 'N/A'}

Response Headers:
${JSON.stringify(axiosData.response.headers, null, 2)}

Response Data:
${typeof axiosData.response.data === 'string' ? axiosData.response.data : JSON.stringify(axiosData.response.data, null, 2)}

`
    }

    return section
  }

  /**
   * Sanitiza contexto para remover informações sensíveis
   */
  sanitizeContext (context) {
    if (!context || typeof context !== 'object') {
      return context
    }

    const sanitized = { ...context }

    // Campos sensíveis para remover/mascarar
    const sensitiveFields = [
      'password', 'token', 'apikey', 'api_key',
      'secret', 'authorization', 'auth',
      'anthropic_api_key', 'trinks_api_key'
    ]

    const sanitizeRecursive = (obj) => {
      if (obj === null || typeof obj !== 'object') {
        return obj
      }

      if (Array.isArray(obj)) {
        return obj.map(sanitizeRecursive)
      }

      const result = {}
      for (const [key, value] of Object.entries(obj)) {
        const lowerKey = key.toLowerCase()

        if (sensitiveFields.some(field => lowerKey.includes(field))) {
          result[key] = '***MASKED***'
        } else if (typeof value === 'string' && value.length > 5000) {
          // Truncar strings muito grandes
          result[key] = value.substring(0, 500) + '... [TRUNCATED]'
        } else {
          result[key] = sanitizeRecursive(value)
        }
      }
      return result
    }

    return sanitizeRecursive(sanitized)
  }

  /**
   * Wrapper para interceptar e logar erros automaticamente
   * Uso: const result = await errorLogger.logAndThrow(asyncOperation, 'API_ERROR', context);
   */
  async executeWithLogging (operation, category = ERROR_CATEGORIES.SYSTEM_ERROR, context = {}) {
    try {
      return await operation()
    } catch (error) {
      await this.logError(error, category, context)
      throw error // Re-throw para manter comportamento original
    }
  }

  /**
   * Versão síncrona do executeWithLogging
   */
  executeWithLoggingSync (operation, category = ERROR_CATEGORIES.SYSTEM_ERROR, context = {}) {
    try {
      return operation()
    } catch (error) {
      // Para operações síncronas, fazemos log assíncrono sem aguardar
      this.logError(error, category, context).catch(logError => {
        console.error('Erro ao salvar log de erro:', logError.message)
      })
      throw error // Re-throw para manter comportamento original
    }
  }

  /**
   * Cria um wrapper de função que automaticamente loga erros
   */
  wrapFunction (func, category = ERROR_CATEGORIES.SYSTEM_ERROR, contextBuilder = () => ({})) {
    const logger = this

    return function wrappedFunction (...args) {
      try {
        const result = func.apply(this, args)

        // Se retornar Promise, interceptar erros assíncronos
        if (result && typeof result.then === 'function') {
          return result.catch(async (error) => {
            const context = typeof contextBuilder === 'function' ? contextBuilder(args) : contextBuilder
            await logger.logError(error, category, context)
            throw error
          })
        }

        return result
      } catch (error) {
        // Erro síncrono
        const context = typeof contextBuilder === 'function' ? contextBuilder(args) : contextBuilder
        logger.logError(error, category, context).catch(logError => {
          console.error('Erro ao salvar log de erro:', logError.message)
        })
        throw error
      }
    }
  }

  /**
   * Obtém estatísticas dos logs de erro
   */
  async getErrorStats (days = 7) {
    try {
      const files = await fs.promises.readdir(errorLogsDir)
      const errorFiles = files.filter(f => f.startsWith('error-') && f.endsWith('.log'))

      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - days)

      const recentErrors = []
      const categoryCount = {}

      for (const file of errorFiles) {
        const match = file.match(/error-(\d{4}-\d{2}-\d{2})-/)
        if (match) {
          const fileDate = new Date(match[1])
          if (fileDate >= cutoffDate) {
            try {
              const content = await fs.promises.readFile(path.join(errorLogsDir, file), 'utf8')
              const categoryMatch = content.match(/Category: (\w+)/)
              if (categoryMatch) {
                const category = categoryMatch[1]
                categoryCount[category] = (categoryCount[category] || 0) + 1
                recentErrors.push({
                  file,
                  date: fileDate,
                  category
                })
              }
            } catch (readError) {
              console.error(`Erro ao ler arquivo de log ${file}:`, readError.message)
            }
          }
        }
      }

      return {
        totalErrors: recentErrors.length,
        byCategory: categoryCount,
        recentErrors: recentErrors.slice(-10), // Últimos 10 erros
        period: `${days} days`
      }
    } catch (error) {
      console.error('Erro ao obter estatísticas de erro:', error.message)
      return null
    }
  }

  /**
   * Log dedicado para erros da API Trinks com máximo detalhamento
   */
  async logTrinksError (errorData) {
    const trinksErrorDir = path.join(__dirname, '../logs/trinks-errors')
    if (!fs.existsSync(trinksErrorDir)) {
      fs.mkdirSync(trinksErrorDir, { recursive: true })
    }

    const timestamp = new Date()
    const filename = `trinks-error-${timestamp.toISOString().split('T')[0]}-${Date.now()}-${uuidv4().substring(0, 8)}.log`
    const filepath = path.join(trinksErrorDir, filename)

    // Sanitizar dados sensíveis antes de salvar
    const sanitizedData = this.sanitizeContext(errorData)

    // Log com máximo detalhamento
    const logContent = {
      ...sanitizedData,
      // Adicionar informações do sistema
      system: {
        platform: process.platform,
        nodeVersion: process.version,
        memory: process.memoryUsage(),
        uptime: process.uptime(),
        pid: process.pid
      },
      // Metadados do log
      logInfo: {
        logId: `trinks-${Date.now()}-${uuidv4().substring(0, 8)}`,
        timestamp: timestamp.toISOString(),
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        sessionId: this.currentSessionId
      }
    }

    try {
      await fs.promises.writeFile(filepath, JSON.stringify(logContent, null, 2))
      console.log(`🔥 Erro da API Trinks salvo em: ${filename}`)
    } catch (writeError) {
      console.error('❌ Erro ao salvar log da API Trinks:', writeError.message)
    }
  }
}

// Criar instância singleton
const errorLogger = new ErrorLogger()

// Exportar também as categorias para uso em outros módulos
module.exports = {
  errorLogger,
  ERROR_CATEGORIES
}
