const trinksService = require('../services/trinks');
const { logger } = require('./logger');

let professionalsCache = null;
let professionalsLastFetch = null;
const PROFESSIONALS_CACHE_TTL = 15 * 60 * 1000; // 15 minutos

class ProfessionalsCache {
  async getProfessionals(trinksApiCalls = []) {
    const now = Date.now();
    
    // Check cache validity
    if (professionalsCache && professionalsLastFetch && (now - professionalsLastFetch) < PROFESSIONALS_CACHE_TTL) {
      logger.info('Using cached professionals data');
      return professionalsCache;
    }
    
    try {
      logger.info('Fetching fresh professionals data from API');
      professionalsCache = await trinksService.getProfessionals();
      professionalsLastFetch = now;
      
      // Track API call for debugging
      if (trinksApiCalls) {
        trinksApiCalls.push({
          endpoint: '/v1/profissionais',
          method: 'GET',
          timestamp: new Date().toISOString(),
          cached: false
        });
      }
      
      logger.info(`Cached ${professionalsCache?.length || 0} professionals`);
      return professionalsCache;
    } catch (error) {
      logger.error('Failed to fetch professionals:', error.message);
      
      // Return cached data if available, even if expired
      if (professionalsCache) {
        logger.warn('Returning expired cached professionals due to API error');
        return professionalsCache;
      }
      
      return [];
    }
  }
  
  clearCache() {
    professionalsCache = null;
    professionalsLastFetch = null;
    logger.info('Professionals cache cleared');
  }
  
  getCachedData() {
    return {
      data: professionalsCache,
      lastFetch: professionalsLastFetch,
      isExpired: professionalsLastFetch ? (Date.now() - professionalsLastFetch) >= PROFESSIONALS_CACHE_TTL : true
    };
  }
}

module.exports = new ProfessionalsCache();