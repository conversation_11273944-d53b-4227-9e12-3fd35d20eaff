/**
 * Sistema de Logging e Avaliação Automática para Cha<PERSON><PERSON>
 * Cria logs individuais para cada chamada com avaliação por IA
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

class ClaudeEvaluationLogger {
  constructor() {
    this.logsDir = path.join(__dirname, '..', 'logs', 'claude-evaluation');
    this.isEnabled = process.env.DISABLE_CLAUDE_EVALUATION !== 'true';
    this.anthropic = null;
    this.initializeLogger();
  }

  async initializeLogger() {
    if (!this.isEnabled) {
      return;
    }

    try {
      await fs.mkdir(this.logsDir, { recursive: true });
      
      // Inicializar Anthropic apenas se habilitado
      if (process.env.ANTHROPIC_API_KEY) {
        const Anthropic = require('@anthropic-ai/sdk');
        this.anthropic = new Anthropic({
          apiKey: process.env.ANTHROPIC_API_KEY,
        });
      }
    } catch (error) {
      console.error('❌ Erro ao inicializar <PERSON>:', error.message);
    }
  }

  /**
   * Intercepta chamadas para anthropic.messages.create
   * @param {Object} originalParams - Parâmetros originais da chamada
   * @param {Function} originalFunction - Função original a ser chamada
   * @param {Object} context - Contexto adicional (fonte, sessão, etc.)
   */
  async interceptClaudeCall(originalParams, originalFunction, context = {}) {
    const startTime = Date.now();
    const callId = this.generateCallId();
    
    let result = null;
    let error = null;

    try {
      // Executar chamada original
      result = await originalFunction(originalParams);
    } catch (err) {
      error = err;
      throw err; // Re-lançar erro para não quebrar fluxo original
    } finally {
      if (this.isEnabled) {
        // Log assíncrono para não impactar performance
        setImmediate(async () => {
          try {
            await this.createCallLog({
              callId,
              originalParams,
              result,
              error,
              context,
              duration: Date.now() - startTime,
              timestamp: new Date()
            });
          } catch (logError) {
            console.error('❌ Erro ao criar log de chamada Claude:', logError.message);
          }
        });
      }
    }

    return result;
  }

  /**
   * Cria log individual para uma chamada
   */
  async createCallLog({ callId, originalParams, result, error, context, duration, timestamp }) {
    try {
      // Dados da chamada
      const callData = {
        callId,
        timestamp: timestamp.toISOString(),
        source: context.source || 'unknown',
        sessionId: context.sessionId || null,
        duration,
        success: !error,
        error: error ? error.message : null
      };

      // Extrair dados relevantes
      const prompt = this.extractPrompt(originalParams);
      const response = this.extractResponse(result);
      const conversationContext = this.extractConversationContext(originalParams);

      // Realizar avaliação por IA se disponível
      let evaluation = null;
      if (this.anthropic && prompt && response) {
        evaluation = await this.evaluateWithAI(prompt, response, conversationContext);
      }

      // Determinar nome do arquivo
      const fileName = this.generateFileName(callId, evaluation);
      const filePath = path.join(this.logsDir, fileName);

      // Gerar conteúdo markdown
      const markdownContent = this.generateMarkdownLog({
        callData,
        prompt,
        response,
        conversationContext,
        evaluation,
        originalParams,
        context
      });

      // Salvar arquivo
      await fs.writeFile(filePath, markdownContent, 'utf8');

      console.log(`📝 Log Claude criado: ${fileName}`);

    } catch (error) {
      console.error('❌ Erro ao criar log individual:', error.message);
    }
  }

  /**
   * Avalia uma chamada usando IA
   */
  async evaluateWithAI(prompt, response, conversationContext) {
    if (!this.anthropic) {
      return null;
    }

    try {
      const evaluationPrompt = this.buildEvaluationPrompt(prompt, response, conversationContext);
      
      const evalResult = await this.anthropic.messages.create({
        model: 'claude-sonnet-4-20250514', // Modelo Sonnet 4 para avaliação detalhada
        max_tokens: 1000,
        messages: [{ role: 'user', content: evaluationPrompt }]
      });

      const evaluationText = evalResult.content[0]?.text || '';
      return this.parseEvaluationResult(evaluationText);

    } catch (error) {
      console.error('❌ Erro na avaliação por IA:', error.message);
      return null;
    }
  }

  /**
   * Constrói prompt de avaliação
   */
  buildEvaluationPrompt(prompt, response, conversationContext) {
    return `# Avaliação de Resposta de IA para Atendimento

## Contexto da Conversa:
${conversationContext || 'Não disponível'}

## Prompt do Sistema:
${prompt.substring(0, 2000)}${prompt.length > 2000 ? '...[truncado]' : ''}

## Resposta Gerada:
${response}

## Critérios de Avaliação:

Avalie a resposta gerada nos seguintes critérios (escala 1-10):

1. **Humanização (1-10)**: A resposta soa natural e humana? Usa linguagem coloquial apropriada?
2. **Coerência Contextual (1-10)**: A resposta está coerente com o contexto da conversa?
3. **Precisão das Informações (1-10)**: As informações são precisas e relevantes?
4. **Tom de Voz (1-10)**: O tom está adequado para um atendimento de salão de beleza?
5. **Efetividade (1-10)**: A resposta resolve ou avança adequadamente a conversa?

## Formato de Resposta:

HUMANIZACAO: [nota 1-10]
COERENCIA: [nota 1-10]  
PRECISAO: [nota 1-10]
TOM: [nota 1-10]
EFETIVIDADE: [nota 1-10]

PONTOS_FORTES: [máximo 2 pontos principais]
PONTOS_FRACOS: [máximo 2 pontos principais]
SUGESTOES: [máximo 2 sugestões específicas de melhoria]

Seja objetivo e direto na avaliação.`;
  }

  /**
   * Faz parsing do resultado da avaliação
   */
  parseEvaluationResult(evaluationText) {
    const scores = {};
    const details = {};

    try {
      // Extrair scores
      const scoreRegex = /(HUMANIZACAO|COERENCIA|PRECISAO|TOM|EFETIVIDADE):\s*(\d+)/g;
      let match;
      while ((match = scoreRegex.exec(evaluationText)) !== null) {
        const criterion = match[1].toLowerCase();
        scores[criterion] = parseInt(match[2]);
      }

      // Calcular média
      const values = Object.values(scores);
      const average = values.length > 0 ? Math.round(values.reduce((a, b) => a + b, 0) / values.length) : 0;

      // Extrair detalhes
      details.pontosFortes = this.extractSection(evaluationText, 'PONTOS_FORTES');
      details.pontosFracos = this.extractSection(evaluationText, 'PONTOS_FRACOS');
      details.sugestoes = this.extractSection(evaluationText, 'SUGESTOES');

      return {
        scores,
        average,
        details,
        rawEvaluation: evaluationText
      };

    } catch (error) {
      console.error('❌ Erro ao parsear avaliação:', error.message);
      return {
        scores: {},
        average: 0,
        details: {},
        rawEvaluation: evaluationText
      };
    }
  }

  /**
   * Extrai seção específica do texto de avaliação
   */
  extractSection(text, sectionName) {
    const regex = new RegExp(`${sectionName}:\\s*(.+?)(?=\\n[A-Z_]+:|$)`, 's');
    const match = text.match(regex);
    return match ? match[1].trim() : '';
  }

  /**
   * Gera nome do arquivo baseado no ID e pontuação
   */
  generateFileName(callId, evaluation) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Verificar se pontuação média é menor que 5
    let prefix = '';
    if (evaluation && evaluation.average < 5) {
      prefix = 'ATENCAO-';
    }

    return `${prefix}claude-call-${timestamp}-${callId}.md`;
  }

  /**
   * Gera conteúdo markdown do log
   */
  generateMarkdownLog({ callData, prompt, response, conversationContext, evaluation, originalParams, context }) {
    let content = `# 🤖 Log de Chamada Claude - ${callData.callId}

## ℹ️ Informações da Chamada

| Campo | Valor |
|-------|-------|
| **ID da Chamada** | \`${callData.callId}\` |
| **Timestamp** | ${callData.timestamp} |
| **Fonte** | ${callData.source} |
| **Sessão** | ${callData.sessionId || 'N/A'} |
| **Duração** | ${callData.duration}ms |
| **Status** | ${callData.success ? '✅ Sucesso' : '❌ Erro'} |

${callData.error ? `
## ❌ Erro Ocorrido
\`\`\`
${callData.error}
\`\`\`
` : ''}

## 📝 Contexto da Conversa
${conversationContext || 'Não disponível'}

## 🎯 Prompt do Sistema
\`\`\`
${prompt || 'Não disponível'}
\`\`\`

## 🤖 Resposta Gerada
${response || 'Não disponível'}

## ⚙️ Parâmetros da Chamada
\`\`\`json
${JSON.stringify({
  model: originalParams.model,
  max_tokens: originalParams.max_tokens,
  temperature: originalParams.temperature,
  messages_count: originalParams.messages ? originalParams.messages.length : 0
}, null, 2)}
\`\`\`
`;

    // Adicionar avaliação se disponível
    if (evaluation) {
      content += `
## 📊 Avaliação Automática

### 🎯 Pontuações (1-10)
| Critério | Nota |
|----------|------|
| **Humanização** | ${evaluation.scores.humanizacao || 'N/A'} |
| **Coerência** | ${evaluation.scores.coerencia || 'N/A'} |
| **Precisão** | ${evaluation.scores.precisao || 'N/A'} |
| **Tom de Voz** | ${evaluation.scores.tom || 'N/A'} |
| **Efetividade** | ${evaluation.scores.efetividade || 'N/A'} |
| **📈 Média** | **${evaluation.average}** |

### ✅ Pontos Fortes
${evaluation.details.pontosFortes || 'Não identificados'}

### ⚠️ Pontos Fracos  
${evaluation.details.pontosFracos || 'Não identificados'}

### 💡 Sugestões de Melhoria
${evaluation.details.sugestoes || 'Nenhuma sugestão específica'}

### 📋 Avaliação Completa
\`\`\`
${evaluation.rawEvaluation}
\`\`\`
`;
    } else {
      content += `
## 📊 Avaliação Automática
⚠️ Avaliação não realizada (IA não disponível ou erro)
`;
    }

    content += `
---
*Log gerado automaticamente em ${new Date().toISOString()}*
*Sistema de Avaliação: ${this.anthropic ? 'Habilitado' : 'Desabilitado'}*
`;

    return content;
  }

  /**
   * Métodos auxiliares de extração
   */
  extractPrompt(params) {
    if (!params.messages) return null;
    
    // Buscar mensagem do sistema
    const systemMessage = params.messages.find(m => m.role === 'system');
    if (systemMessage) return systemMessage.content;
    
    // Se não há sistema, pegar todas as mensagens
    return params.messages.map(m => `${m.role}: ${m.content}`).join('\n\n');
  }

  extractResponse(result) {
    if (!result || !result.content) return null;
    return result.content[0]?.text || 'Resposta não disponível';
  }

  extractConversationContext(params) {
    if (!params.messages) return null;
    
    // Pegar apenas mensagens do usuário para contexto
    const userMessages = params.messages
      .filter(m => m.role === 'user')
      .map(m => `> ${m.content}`)
      .join('\n');
    
    return userMessages || 'Sem contexto de conversa';
  }

  generateCallId() {
    return crypto.randomBytes(8).toString('hex');
  }

  /**
   * Método para interceptar manualmente (se necessário)
   */
  static async interceptCall(originalParams, originalFunction, context = {}) {
    const logger = new ClaudeEvaluationLogger();
    return logger.interceptClaudeCall(originalParams, originalFunction, context);
  }
}

module.exports = ClaudeEvaluationLogger;