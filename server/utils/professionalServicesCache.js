/**
 * Cache de Serviços por Profissional
 * 
 * Armazena quais serviços cada profissional pode realizar,
 * evitando consultas desnecessárias à API Trinks.
 */

const { performance } = require('perf_hooks');

class ProfessionalServicesCache {
  constructor() {
    this.cache = new Map();
    this.lastFetch = new Map();
    this.cacheTTL = 24 * 60 * 60 * 1000; // 24 horas (serviços mudam raramente)
    this.stats = {
      hits: 0,
      misses: 0,
      fetches: 0,
      errors: 0
    };
    
    console.log('🗄️ ProfessionalServicesCache inicializado');
  }

  /**
   * Gera chave de cache para profissional
   */
  generateCacheKey(professionalId) {
    return `professional_services_${professionalId}`;
  }

  /**
   * Verifica se cache é válido para um profissional
   */
  isValid(professionalId) {
    const cacheKey = this.generateCacheKey(professionalId);
    const lastFetchTime = this.lastFetch.get(cacheKey);
    
    if (!lastFetchTime) {
      return false;
    }
    
    const age = Date.now() - lastFetchTime;
    return age < this.cacheTTL;
  }

  /**
   * Obtém serviços do cache
   */
  get(professionalId) {
    const cacheKey = this.generateCacheKey(professionalId);
    
    if (!this.isValid(professionalId)) {
      this.stats.misses++;
      return null;
    }
    
    const cachedData = this.cache.get(cacheKey);
    if (cachedData) {
      this.stats.hits++;
      const age = Date.now() - this.lastFetch.get(cacheKey);
      console.log(`💾 Cache HIT: Serviços do profissional ${professionalId} (idade: ${Math.round(age/1000/60)}min)`);
      return cachedData;
    }
    
    this.stats.misses++;
    return null;
  }

  /**
   * Armazena serviços no cache
   */
  set(professionalId, services) {
    const cacheKey = this.generateCacheKey(professionalId);
    
    // Validar dados antes de cachear
    if (!Array.isArray(services)) {
      console.error(`❌ Dados inválidos para cache do profissional ${professionalId}`);
      return false;
    }
    
    // Normalizar estrutura de serviços
    const normalizedServices = services.map(service => ({
      id: service.id,
      nome: service.nome,
      categoria: service.categoria,
      duracaoEmMinutos: service.duracaoEmMinutos,
      preco: service.preco,
      descricao: service.descricao
    }));
    
    this.cache.set(cacheKey, normalizedServices);
    this.lastFetch.set(cacheKey, Date.now());
    this.stats.fetches++;
    
    console.log(`📦 Cache SET: ${normalizedServices.length} serviços para profissional ${professionalId}`);
    return true;
  }

  /**
   * Remove serviços do cache (quando profissional é atualizado)
   */
  invalidate(professionalId) {
    const cacheKey = this.generateCacheKey(professionalId);
    
    const had = this.cache.has(cacheKey);
    this.cache.delete(cacheKey);
    this.lastFetch.delete(cacheKey);
    
    if (had) {
      console.log(`🗑️ Cache invalidado para profissional ${professionalId}`);
    }
    
    return had;
  }

  /**
   * Limpa todo o cache
   */
  clearAll() {
    const size = this.cache.size;
    this.cache.clear();
    this.lastFetch.clear();
    this.stats = { hits: 0, misses: 0, fetches: 0, errors: 0 };
    
    console.log(`🧹 Cache de serviços por profissional limpo (${size} entradas removidas)`);
  }

  /**
   * Busca vários profissionais de uma vez (batch)
   */
  getBatch(professionalIds) {
    const results = {};
    const missing = [];
    
    for (const professionalId of professionalIds) {
      const cached = this.get(professionalId);
      if (cached) {
        results[professionalId] = cached;
      } else {
        missing.push(professionalId);
      }
    }
    
    console.log(`📦 Batch cache: ${Object.keys(results).length} hits, ${missing.length} misses`);
    
    return {
      cached: results,
      missing: missing
    };
  }

  /**
   * Obtém estatísticas do cache
   */
  getStats() {
    const totalRequests = this.stats.hits + this.stats.misses;
    const hitRate = totalRequests > 0 ? ((this.stats.hits / totalRequests) * 100).toFixed(1) : '0';
    
    return {
      totalEntries: this.cache.size,
      hitRate: `${hitRate}%`,
      hits: this.stats.hits,
      misses: this.stats.misses,
      fetches: this.stats.fetches,
      errors: this.stats.errors,
      cacheKeys: Array.from(this.cache.keys())
    };
  }

  /**
   * Gera mapa consolidado profissional → serviços para prompts
   */
  generateProfessionalServicesMap(professionalIds) {
    const map = {};
    
    for (const professionalId of professionalIds) {
      const services = this.get(professionalId);
      if (services && services.length > 0) {
        // Agrupar por categoria para melhor visualização
        const servicesByCategory = services.reduce((acc, service) => {
          const category = service.categoria || 'Outros';
          if (!acc[category]) acc[category] = [];
          acc[category].push(`${service.nome} (R$${service.preco})`);
          return acc;
        }, {});
        
        map[professionalId] = servicesByCategory;
      }
    }
    
    return map;
  }

  /**
   * Formata mapa para prompt da IA
   */
  formatForPrompt(professionalNames, professionalServicesMap) {
    if (!professionalServicesMap || Object.keys(professionalServicesMap).length === 0) {
      return '(Aguardando dados dos serviços por profissional)';
    }
    
    const formatted = [];
    
    for (const [professionalId, servicesByCategory] of Object.entries(professionalServicesMap)) {
      const professionalName = professionalNames[professionalId] || `Profissional ${professionalId}`;
      const servicesList = [];
      
      for (const [category, services] of Object.entries(servicesByCategory)) {
        servicesList.push(`${category}: ${services.join(', ')}`);
      }
      
      formatted.push(`👨‍💼 ${professionalName}: ${servicesList.join(' | ')}`);
    }
    
    return formatted.join('\n');
  }
}

module.exports = new ProfessionalServicesCache();