# Sistema de Logs Otimizado - Trinks IA

## 🎯 Objetivo

Sistema de logging com dois modos: **conciso** (apenas informações essenciais) e **verbose** (log completo detalhado), resolvendo o problema de logs extremamente grandes (341KB para uma mensagem simples).

## 🔧 Como Usar

### <PERSON><PERSON> (Conciso) ⭐
```bash
# Não definir CONCISE_LOGS ou qualquer valor diferente de 'false'
# PADRÃO - logs otimizados
```

### Ativar Modo Verbose (Desenvolvimento)
```bash
# No .env do servidor para logs completos
CONCISE_LOGS=false
```

## 📊 Comparação de Tamanhos

| Modo | Tamanho Aproximado | Logs Capturados | Uso Recomendado |
|------|-------------------|-----------------|------------------|
| **Conciso** | ~2-5KB | 4-8 logs essenciais | Produção |
| **Verbose** | ~100-500KB | 200+ logs detalhados | Desenvolvimento/Debug |

## 📋 Informações Capturadas no Modo Conciso

### ✅ Sempre Capturado
- **📱 Mensagem do Usuário**: Texto enviado pelo cliente
- **🤖 Resposta da IA**: Resposta gerada pela IA
- **📝 Prompts COMPLETOS**: Templates integrais enviados para IA (sem truncar)
- **❌ Erros COMPLETOS**: Console.error com stack traces completas
- **🚨 Exceções COMPLETAS**: Erros capturados com stack, cause e contexto
- **🔥 Falhas de API**: Rate limiting e erros de API externa
- **⚠️ Fallbacks**: Quando sistema usa respostas alternativas

### ❌ Filtrado no Modo Conciso
- Console.log detalhados
- Debug de requisições HTTP
- Detalhes de interceptação de console
- Logs de sistema internos
- Cache e otimizações
- Detalhes de sessão WebSocket

## 🚀 Exemplo de Log Conciso

```
================================================================================
TRINKS IA - LOG ESSENCIAL DA MENSAGEM
================================================================================
Session ID: 1757003786928-c9568ecf
Data/Hora: 2025-09-04T16:36:26.928Z
Duração: 4470ms
Logs Essenciais: 5
================================================================================

[04/09/2025, 13:36:26] [USER_MESSAGE   ] 📱 5521998217917: "Olá"

[04/09/2025, 13:36:29] [API_ERROR      ] 🔥 API Error: GET /v1/agendamentos - Too Many Requests (1200ms)

[04/09/2025, 13:36:30] [PROMPT_COMPLETE] 📝 Prompt COMPLETO (conversation, 1692 chars):
# Identidade e Personalidade - Recepcionista do Salão

## 🎯 IDENTIDADE CRÍTICA
Você é uma recepcionista experiente e calorosa de um salão de beleza que adora o que faz! O cliente NÃO DEVE suspeitar que está falando com uma IA...
[PROMPT COMPLETO SEM TRUNCAR]

[04/09/2025, 13:36:31] [AI_RESPONSE    ] 🤖 IA: "Oi Fernando! Que bom te ver de novo! 😊\n\nVi que você já tem BARBA 15 marcado para hoje às 16h com o João! Está tudo certo ou precisa de alguma coisa?" (3263ms, claude-sonnet-4-20250514)

[04/09/2025, 13:36:31] [EXCEPTION_COMPLETE] 🚨 EXCEÇÃO: ValidationError - Falha na validação do prompt

📍 Stack Trace:
ValidationError: Falha na validação do prompt
    at aiService.generateResponse (/server/services/ai.js:123:15)
    at messageHandler.process (/server/services/messageHandler.js:87:22)

🔍 Context: {
  "operation": "prompt_validation",
  "promptLength": 1692,
  "timestamp": "2025-09-04T17:01:34.848Z"
}

================================================================================
FIM DO LOG - Session: 1757003786928-c9568ecf
================================================================================
```

## 🔧 Métodos de Logging Disponíveis

### Logs Essenciais (sempre capturados)
```javascript
// Resposta da IA com PROMPT COMPLETO
messageLogger.logAIInteraction(type, promptCompleto, response, duration, model);

// Erros de API
messageLogger.logApiCall(method, endpoint, duration, false, { error: 'Too Many Requests' });

// Uso de fallbacks
messageLogger.logFallback('IA indisponível', 'Mock Response', { reason: 'API timeout' });

// Erros gerais com stack completa
console.error('Erro crítico'); // Capturado automaticamente com stack

// Exceções completas com contexto
messageLogger.logError(error, { operation: 'validation', context: {...} });
```

### Logs Verbosos (apenas no modo verbose)
```javascript
// Logs detalhados do sistema
console.log('Debug info'); // Ignorado no modo conciso

// Logs de API bem-sucedidas
messageLogger.logApiCall('GET', '/v1/profissionais', 500, true);

// Mudanças de estado
messageLogger.logStateChange('reception', 'scheduling');
```

## 📈 Benefícios

### ✅ Modo Conciso
- **🎯 Foco**: Apenas informações relevantes para troubleshooting
- **📉 Performance**: Logs 50-100x menores
- **🔍 Rastreabilidade**: Mantém toda informação crítica
- **💰 Economia**: Menos espaço em disco e transferência

### ✅ Modo Verbose
- **🔧 Debug Completo**: Todos os detalhes para desenvolvimento
- **🐛 Troubleshooting Avançado**: Rastreamento completo do fluxo
- **📊 Análise Detalhada**: Métricas completas de performance

## 🚨 Configuração Recomendada

```bash
# Produção (PADRÃO)
# Não definir CONCISE_LOGS - usa modo conciso automaticamente

# Desenvolvimento (apenas se precisar de debug completo)
CONCISE_LOGS=false
```

## 🔄 Migração

O sistema é **100% compatível** com o código existente e **automaticamente usa logs otimizados por padrão**. Para debug completo, defina `CONCISE_LOGS=false`.