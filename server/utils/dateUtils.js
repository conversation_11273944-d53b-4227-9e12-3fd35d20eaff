/**
 * Date utilities for Brazil timezone handling
 * Ensures all date operations use America/Sao_Paulo timezone
 */

// Set default timezone for the process
process.env.TZ = 'America/Sao_Paulo';

/**
 * Get current date in Brazil timezone
 * @returns {Date} Current date/time in Brazil
 */
function getCurrentDateBR() {
  return new Date();
}

/**
 * Format date to Brazilian string (DD/MM/YYYY)
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted date string
 */
function formatDateBR(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

/**
 * Format datetime to Brazilian string (DD/MM/YYYY HH:MM)
 * @param {Date|string} date - Date to format
 * @returns {string} Formatted datetime string
 */
function formatDateTimeBR(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

/**
 * Get date string in YYYY-MM-DD format (Brazil timezone)
 * @param {Date} date - Date to format
 * @returns {string} Date string YYYY-MM-DD
 */
function getDateStringBR(date = new Date()) {
  return date.toLocaleDateString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  }).split('/').reverse().join('-');
}

/**
 * Check if date is today (Brazil timezone)
 * @param {string|Date} date - Date to check
 * @returns {boolean} True if date is today
 */
function isToday(date) {
  const dateStr = typeof date === 'string' ? date.split('T')[0] : getDateStringBR(date);
  const todayStr = getDateStringBR(new Date());
  return dateStr === todayStr;
}

/**
 * Check if date is tomorrow (Brazil timezone)
 * @param {string|Date} date - Date to check
 * @returns {boolean} True if date is tomorrow
 */
function isTomorrow(date) {
  const dateStr = typeof date === 'string' ? date.split('T')[0] : getDateStringBR(date);
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowStr = getDateStringBR(tomorrow);
  return dateStr === tomorrowStr;
}

/**
 * Check if date is in the future (Brazil timezone)
 * @param {string|Date} date - Date to check
 * @returns {boolean} True if date is in the future
 */
function isFuture(date) {
  const dateStr = typeof date === 'string' ? date.split('T')[0] : getDateStringBR(date);
  const todayStr = getDateStringBR(new Date());
  return dateStr >= todayStr;
}

/**
 * Parse Brazilian date string (DD/MM/YYYY) to Date object
 * @param {string} dateStr - Date string in DD/MM/YYYY format
 * @returns {Date} Date object
 */
function parseBRDate(dateStr) {
  const [day, month, year] = dateStr.split('/');
  return new Date(`${year}-${month}-${day}T12:00:00`);
}

/**
 * Get day name in Portuguese
 * @param {Date|string} date - Date to get day name
 * @returns {string} Day name in Portuguese
 */
function getDayNameBR(date) {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('pt-BR', {
    timeZone: 'America/Sao_Paulo',
    weekday: 'long'
  });
}

/**
 * Add days to a date
 * @param {Date} date - Base date
 * @param {number} days - Number of days to add
 * @returns {Date} New date
 */
function addDays(date, days) {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

/**
 * Get next occurrence of a weekday
 * @param {number} dayOfWeek - Day of week (0=Sunday, 6=Saturday)
 * @returns {Date} Next occurrence of that weekday
 */
function getNextWeekday(dayOfWeek) {
  const today = new Date();
  const todayDay = today.getDay();
  const daysUntil = (dayOfWeek - todayDay + 7) % 7 || 7;
  return addDays(today, daysUntil);
}

/**
 * Extract hour and minute from Brazilian formatted datetime string
 * @param {Date} date - Date to extract from
 * @returns {Object} Object with hour and minute in Brazil timezone
 */
function getHourMinuteBR(date = new Date()) {
  const formatted = formatDateTimeBR(date); // "04/09/2025, 16:32"
  const timePart = formatted.split(', ')[1]; // "16:32"
  const [hour, minute] = timePart.split(':').map(num => parseInt(num, 10));
  
  return {
    hour,
    minute,
    timeString: timePart
  };
}

module.exports = {
  getCurrentDateBR,
  formatDateBR,
  formatDateTimeBR,
  getDateStringBR,
  isToday,
  isTomorrow,
  isFuture,
  parseBRDate,
  getDayNameBR,
  addDays,
  getNextWeekday,
  getHourMinuteBR
};