/**
 * Cache Especializado: Mapeamento Profissional → Serviços
 *
 * Responsável por:
 * 1. Buscar todos profissionais do estabelecimento
 * 2. Para cada profissional, buscar seus serviços específicos
 * 3. Manter cache persistente com TTL configurável
 * 4. Fornecer dados estruturados para o sistema de prompts
 * 5. 🆕 CACHE EM DISCO: Salvar dados em arquivo JSON para inicialização rápida
 *
 * Estrutura do Cache:
 * {
 *   "782094": {
 *     "id": "782094",
 *     "nome": "<PERSON>",
 *     "apelido": "João",
 *     "servicos": [
 *       { "id": 11866299, "nome": "BARBA 15", "preco": 30, "duracaoEmMinutos": 15 },
 *       { "id": 11866300, "nome": "BARBA 50", "preco": 50, "duracaoEmMinutos": 45 }
 *     ]
 *   }
 * }
 */

const fs = require('fs').promises
const path = require('path')

class ProfessionalServicesMapping {
  constructor () {
    this.cache = new Map()
    this.lastUpdate = null
    this.isUpdating = false
    this.ttlHours = 24 // Cache válido por 24 horas (reduz reinicializações)
    this.trinksService = null // Será inicializado quando necessário

    // 🆕 CACHE EM DISCO: Configurações
    this.cacheDir = path.join(__dirname, '../cache')
    this.cacheFile = path.join(this.cacheDir, 'professional-services-mapping.json')
    this.diskCacheVersion = '1.0'

    console.log('🗺️ ProfessionalServicesMapping inicializado')
    console.log(`📁 Cache em disco: ${this.cacheFile}`)
  }

  /**
   * Função auxiliar para delay entre requisições
   */
  delay (ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * Inicializa referência ao TrinksService (lazy loading)
   */
  initTrinksService () {
    if (!this.trinksService) {
      this.trinksService = require('../services/trinks')
      console.log('🔗 TrinksService conectado ao ProfessionalServicesMapping')
    }
  }

  /**
   * 🆕 Carrega cache do disco se válido (< 24h)
   * Retorna true se conseguiu carregar cache válido
   */
  async loadFromDisk () {
    try {
      console.log('💾 Tentando carregar cache do disco...')

      // Verificar se arquivo existe
      try {
        await fs.access(this.cacheFile)
      } catch (error) {
        console.log('📁 Arquivo de cache não existe ainda')
        return false
      }

      // Ler arquivo JSON
      const fileContent = await fs.readFile(this.cacheFile, 'utf8')
      const diskCache = JSON.parse(fileContent)

      // Validar estrutura do arquivo
      if (!diskCache.version || !diskCache.lastUpdate || !diskCache.data) {
        console.log('⚠️ Estrutura inválida do arquivo de cache')
        return false
      }

      // Verificar se é versão compatível
      if (diskCache.version !== this.diskCacheVersion) {
        console.log(`⚠️ Versão incompatível: arquivo ${diskCache.version}, esperado ${this.diskCacheVersion}`)
        return false
      }

      // Verificar TTL (24 horas)
      const cacheAge = Date.now() - new Date(diskCache.lastUpdate).getTime()
      const cacheAgeHours = cacheAge / (1000 * 60 * 60)

      if (cacheAgeHours >= this.ttlHours) {
        console.log(`⏰ Cache expirado: ${cacheAgeHours.toFixed(1)}h > ${this.ttlHours}h`)
        return false
      }

      // Converter dados para Map
      this.cache.clear()
      let loadedCount = 0
      let totalServices = 0

      for (const [professionalId, professionalData] of Object.entries(diskCache.data)) {
        this.cache.set(professionalId, professionalData)
        loadedCount++
        totalServices += (professionalData.servicos || []).length
      }

      this.lastUpdate = new Date(diskCache.lastUpdate).getTime()

      console.log('✅ Cache carregado do disco com sucesso!')
      console.log(`   📊 ${loadedCount} profissionais, ${totalServices} serviços`)
      console.log(`   ⏰ Idade: ${cacheAgeHours.toFixed(1)}h/${this.ttlHours}h`)

      return true
    } catch (error) {
      console.error('❌ Erro ao carregar cache do disco:', error.message)

      // Se erro de parsing JSON, arquivo pode estar corrompido - deletar
      if (error instanceof SyntaxError) {
        try {
          await fs.unlink(this.cacheFile)
          console.log('🗑️ Arquivo de cache corrompido removido')
        } catch (unlinkError) {
          console.error('❌ Erro ao remover arquivo corrompido:', unlinkError.message)
        }
      }

      return false
    }
  }

  /**
   * 🆕 Salva cache atual em disco
   */
  async saveToDisk () {
    try {
      console.log('💾 Salvando cache em disco...')

      // Criar diretório se não existir
      try {
        await fs.mkdir(this.cacheDir, { recursive: true })
      } catch (error) {
        // Ignorar erro se diretório já existe
        if (error.code !== 'EEXIST') {
          throw error
        }
      }

      // Converter Map para Object para serialização JSON
      const cacheData = Object.fromEntries(this.cache)

      // Contar dados para log
      const professionalsCount = this.cache.size
      let totalServices = 0
      this.cache.forEach(professional => {
        totalServices += (professional.servicos || []).length
      })

      // Criar estrutura do arquivo
      const diskCache = {
        version: this.diskCacheVersion,
        lastUpdate: new Date(this.lastUpdate).toISOString(),
        ttlHours: this.ttlHours,
        metadata: {
          professionalsCount,
          totalServices,
          generatedAt: new Date().toISOString()
        },
        data: cacheData
      }

      // Salvar arquivo JSON
      const jsonContent = JSON.stringify(diskCache, null, 2)
      await fs.writeFile(this.cacheFile, jsonContent, 'utf8')

      console.log('✅ Cache salvo em disco com sucesso!')
      console.log(`   📊 ${professionalsCount} profissionais, ${totalServices} serviços`)
      console.log(`   📁 Arquivo: ${this.cacheFile}`)
      console.log(`   📦 Tamanho: ${Math.round(jsonContent.length / 1024)}KB`)
    } catch (error) {
      console.error('❌ Erro ao salvar cache em disco:', error.message)

      // Log adicional para debug
      console.error('   📁 Diretório:', this.cacheDir)
      console.error('   📄 Arquivo:', this.cacheFile)
      console.error('   💾 Cache size:', this.cache.size)
    }
  }

  /**
   * Verifica se o cache está válido
   */
  isCacheValid () {
    if (!this.lastUpdate || this.cache.size === 0) {
      return false
    }

    const ageHours = (Date.now() - this.lastUpdate) / (1000 * 60 * 60)
    return ageHours < this.ttlHours
  }

  /**
   * Busca todos os profissionais do estabelecimento
   */
  async fetchAllProfessionals () {
    this.initTrinksService()
    console.log('👥 Buscando todos os profissionais...')

    try {
      const professionals = await this.trinksService.getProfessionals()

      if (!professionals || !Array.isArray(professionals)) {
        throw new Error('Resposta inválida da API de profissionais')
      }

      console.log(`✅ ${professionals.length} profissionais encontrados`)
      return professionals
    } catch (error) {
      console.error('❌ Erro ao buscar profissionais:', error.message)
      throw error
    }
  }

  /**
   * Busca serviços de um profissional específico
   */
  async fetchProfessionalServices (professionalId, professionalName) {
    this.initTrinksService()

    try {
      console.log(`🔍 Buscando serviços do profissional ${professionalName} (${professionalId})...`)

      const result = await this.trinksService.getProfessionalServices(professionalId)

      if (!result || !result.success) {
        console.warn(`⚠️ Falha ao buscar serviços do profissional ${professionalName}`)
        return []
      }

      const services = result.services || []
      console.log(`✅ ${services.length} serviços encontrados para ${professionalName}`)

      return services
    } catch (error) {
      console.error(`❌ Erro ao buscar serviços do profissional ${professionalName}:`, error.message)
      return []
    }
  }

  /**
   * Atualiza o cache completo
   */
  async updateCache () {
    if (this.isUpdating) {
      console.log('⏳ Cache já está sendo atualizado...')
      return
    }

    this.isUpdating = true
    const startTime = Date.now()

    try {
      console.log('🔄 Iniciando atualização completa do cache profissional → serviços...')

      // 1. Buscar todos os profissionais
      const professionals = await this.fetchAllProfessionals()

      const newCache = new Map()
      let totalServices = 0
      let successCount = 0
      let errorCount = 0

      console.log(`👥 Processando ${professionals.length} profissionais sequencialmente...`)

      // 2. Processar profissionais SEQUENCIALMENTE (não paralelo)
      for (let i = 0; i < professionals.length; i++) {
        const professional = professionals[i]

        try {
          console.log(`🔍 [${i + 1}/${professionals.length}] Buscando serviços de ${professional.nome}...`)

          const services = await this.fetchProfessionalServices(professional.id, professional.nome)

          const professionalData = {
            id: professional.id,
            nome: professional.nome,
            apelido: professional.apelido || professional.nome.split(' ')[0],
            servicos: services
          }

          newCache.set(professionalData.id, professionalData)
          totalServices += professionalData.servicos.length
          successCount++

          console.log(`✅ ${professionalData.apelido}: ${professionalData.servicos.length} serviços encontrados`)

          // Delay de 1 segundo entre requisições (exceto na última)
          if (i < professionals.length - 1) {
            console.log('⏳ Aguardando 1 segundo antes da próxima requisição...')
            await this.delay(1000)
          }
        } catch (error) {
          errorCount++
          console.error(`❌ Erro ao processar ${professional.nome}:`, error.message)

          // Continuar processamento mesmo com erro
          const professionalData = {
            id: professional.id,
            nome: professional.nome,
            apelido: professional.apelido || professional.nome.split(' ')[0],
            servicos: []
          }

          newCache.set(professionalData.id, professionalData)
        }
      }

      // 3. Atualizar cache e estatísticas
      this.cache = newCache
      this.lastUpdate = Date.now()

      const duration = Date.now() - startTime
      const durationMinutes = (duration / (1000 * 60)).toFixed(1)

      console.log('✅ Cache profissional → serviços atualizado com sucesso!')
      console.log('📊 Estatísticas finais:')
      console.log(`   • ${successCount} profissionais processados com sucesso`)
      console.log(`   • ${errorCount} erros durante processamento`)
      console.log(`   • ${totalServices} serviços mapeados no total`)
      console.log(`   • ${duration}ms (${durationMinutes}min) de duração total`)
      console.log(`   • Próxima atualização automática em ${this.ttlHours}h`)

      // 🆕 SALVAR CACHE EM DISCO após atualização bem-sucedida
      if (this.cache.size > 0) {
        await this.saveToDisk()
      }
    } catch (error) {
      console.error('❌ Erro crítico na atualização do cache:', error.message)

      // Manter cache anterior em caso de erro
      if (this.cache.size === 0) {
        console.error('🚨 Cache vazio após erro - sistema pode não funcionar corretamente')
      }
    } finally {
      this.isUpdating = false
    }
  }

  /**
   * Obtém dados do cache (com atualização automática se necessário)
   * 🆕 PRIORIZA CACHE EM DISCO: Tenta carregar do arquivo primeiro
   */
  async getMapping () {
    // 🆕 PRIORIDADE 1: Tentar carregar cache válido do disco
    if (!this.isCacheValid()) {
      console.log('💾 Cache em memória inválido, tentando carregar do disco...')

      const diskLoadSuccess = await this.loadFromDisk()

      if (diskLoadSuccess && this.isCacheValid()) {
        console.log('✅ Cache carregado do disco com sucesso!')
        return Object.fromEntries(this.cache)
      }
    }

    // PRIORIDADE 2: Se cache em memória válido, usar
    if (this.isCacheValid()) {
      console.log('♻️ Usando cache em memória válido')
      return Object.fromEntries(this.cache)
    }

    // PRIORIDADE 3: Buscar da API (última opção)
    console.log('🌐 Nenhum cache válido - buscando da API...')
    console.log('🔄 Atualizando cache via API...')
    await this.updateCache()

    return Object.fromEntries(this.cache)
  }

  /**
   * Busca serviços de um profissional específico no cache
   */
  async getProfessionalServices (professionalId) {
    const mapping = await this.getMapping()
    return mapping[professionalId] || null
  }

  /**
   * Busca profissional por nome ou apelido
   */
  async findProfessionalByName (name) {
    const mapping = await this.getMapping()
    const normalizedName = name.toLowerCase().trim()

    for (const professional of Object.values(mapping)) {
      const nomeMatch = professional.nome.toLowerCase().includes(normalizedName)
      const apelidoMatch = professional.apelido.toLowerCase().includes(normalizedName)

      if (nomeMatch || apelidoMatch) {
        return professional
      }
    }

    return null
  }

  /**
   * Gera texto formatado para inclusão no prompt da IA
   */
  async generatePromptText () {
    // 🆕 PRIORIZAR CACHE EM DISCO: Tentar carregar primeiro se cache vazio
    if (this.cache.size === 0 && !this.lastUpdate) {
      console.log('🚀 generatePromptText: Cache vazio, tentando carregar do disco primeiro...')
      const diskLoadSuccess = await this.loadFromDisk()
      if (diskLoadSuccess) {
        console.log('✅ generatePromptText: Cache carregado do disco com sucesso!')
      }
    }
    
    const mapping = await this.getMapping()

    if (Object.keys(mapping).length === 0) {
      return '⚠️ Nenhum mapeamento profissional → serviços disponível no momento.'
    }

    let promptText = '## 👨‍💼 MAPEAMENTO COMPLETO: PROFISSIONAL → SERVIÇOS\n\n'

    for (const professional of Object.values(mapping)) {
      promptText += `**${professional.nome}** (${professional.apelido}):\n`

      if (professional.servicos.length === 0) {
        promptText += '  • Nenhum serviço específico encontrado\n'
      } else {
        professional.servicos.forEach(service => {
          const preco = service.preco || service.valor || '?'
          const duracao = service.duracaoEmMinutos || '?'
          promptText += `  • ${service.nome} (${duracao}min) - R$${preco}\n`
        })
      }
      promptText += '\n'
    }

    promptText += '**🚨 INSTRUÇÃO CRÍTICA - VERIFICAÇÃO OBRIGATÓRIA**: Use EXATAMENTE as informações acima para responder sobre:\n'
    promptText += '- Quais serviços cada profissional oferece\n'
    promptText += '- PREÇOS EXATOS (R$) de cada serviço\n'
    promptText += '- DURAÇÕES EXATAS (minutos) de cada serviço\n'
    promptText += '**🔍 REGRA FUNDAMENTAL**: ANTES de sugerir qualquer profissional para um serviço, VERIFIQUE se esse serviço está na lista dele acima!\n'
    promptText += '**❌ PROIBIDO ABSOLUTO**: Sugerir profissional que NÃO faz o serviço solicitado!\n'
    promptText += '**✅ EXEMPLO CORRETO**: Se cliente quer MASSAGEM QUICK, só sugira profissionais que têm "MASSAGEM QUICK" listada!\n'
    promptText += '**❌ EXEMPLO INCORRETO**: NUNCA sugira Ágata para MASSAGEM QUICK pois ela só faz BARBA 15!\n'
    promptText += 'NUNCA invente, assuma ou alucine preços/durações/serviços não listados acima!\n\n'

    promptText += '## 🏃‍♀️ INSTRUÇÕES PROATIVAS CRÍTICAS\n\n'
    promptText += '**SEMPRE que cliente mencionar um SERVIÇO ou ter INTENÇÃO de agendamento**:\n\n'
    promptText += '1. **VERIFIQUE** se cliente tem profissional PREFERIDO para esse serviço (veja seção de preferências históricas)\n'
    promptText += '2. **SE TEM preferência**: Destaque o preferido primeiro com contexto histórico\n'
    promptText += '3. **SE NÃO tem**: Sugira todos profissionais disponíveis de forma amigável\n'
    promptText += '4. **INCLUA sempre**: preços (R$) e durações (min) dos serviços\n'
    promptText += '5. **USE linguagem brasileira natural** - JAMAIS seja robótico\n\n'
    promptText += '**EXEMPLOS OBRIGATÓRIOS**:\n'
    promptText += '• **COM preferência**: "Opa! Barba de novo? O João que você sempre vai tá livre amanhã! R$30 em 15min como sempre. Bora marcar?" \n'
    promptText += '• **SEM preferência**: "Opa! Barba? Temos o João (R$30, 15min) e Jailson (R$50, 45min) que são demais! Qual prefere? 🔥" \n\n'
    promptText += '**❌ JAMAIS**: "Qual profissional você prefere?" SEM mostrar opções primeiro\n'
    promptText += '**❌ JAMAIS**: Listas secas ou respostas genéricas como "temos os seguintes profissionais"\n\n'

    return promptText
  }

  /**
   * Força atualização do cache (para uso manual/debug)
   */
  async forceUpdate () {
    console.log('🔄 Forçando atualização do cache...')
    this.lastUpdate = null // Invalida cache atual
    await this.updateCache()
  }

  /**
   * Obtém estatísticas do cache
   */
  getCacheStats () {
    const ageMs = this.lastUpdate ? Date.now() - this.lastUpdate : null
    const ageHours = ageMs ? (ageMs / (1000 * 60 * 60)).toFixed(1) : null

    let totalServices = 0
    this.cache.forEach(professional => {
      totalServices += professional.servicos.length
    })

    return {
      professionalsCount: this.cache.size,
      totalServices,
      lastUpdate: this.lastUpdate,
      ageHours,
      isValid: this.isCacheValid(),
      isUpdating: this.isUpdating,
      ttlHours: this.ttlHours
    }
  }
}

// Instância singleton
const professionalServicesMapping = new ProfessionalServicesMapping()

module.exports = professionalServicesMapping
