const express = require('express');
const cors = require('cors');
const path = require('path');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// Initialize logging
const { logger, apiLogger } = require('./utils/logger');

const app = express();
const server = createServer(app);
const io = new Server(server, {
  cors: {
    origin: ["http://localhost:3000", "http://localhost:3002"],
    methods: ["GET", "POST"]
  }
});

const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: ["http://localhost:3000", "http://localhost:3002"],
  methods: ["GET", "POST", "PUT", "DELETE"],
  allowedHeaders: ["Content-Type", "Authorization"]
}));
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logMessage = `${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms - ${req.ip}`;
    
    if (res.statusCode >= 400) {
      apiLogger.error(logMessage);
    } else {
      apiLogger.info(logMessage);
    }
  });
  
  next();
});

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Routes
app.use('/api', require('./routes/api'));
app.use('/api/prompt-templates', require('./routes/prompt-templates'));

// Webhook endpoints are provider-specific below

// WAHA API webhook endpoint
app.post('/webhook/waha', async (req, res) => {
  try {
    console.log('🎯 ===== WAHA WEBHOOK RECEIVED =====');
    console.log('📡 WAHA API webhook body:', JSON.stringify(req.body, null, 2));
    console.log('🔍 Request headers:', JSON.stringify(req.headers, null, 2));
    console.log('⏰ Timestamp:', new Date().toISOString());

    // Get the WhatsApp service
    const whatsappService = require('./services/whatsapp');
    console.log('🔧 WhatsApp service loaded successfully');
    
    // Check current provider
    const currentProvider = whatsappService.getCurrentProvider();
    console.log('🔍 Current provider:', currentProvider ? currentProvider.connectionType : 'none');

    // Handle the webhook
    console.log('📤 Calling whatsappService.handleWebhook...');
    await whatsappService.handleWebhook(req.body);
    console.log('✅ Webhook handled successfully');

    console.log('=====================================');
    res.status(200).json({ success: true });
  } catch (error) {
    console.error('❌ ===== WAHA WEBHOOK ERROR =====');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    console.error('=================================');
    res.status(500).json({ error: error.message });
  }
});

// Socket.io connection handling
const messageHandler = require('./services/messageHandler');
messageHandler.initializeSocket(io);

// QR Test page
app.get('/test-qr', (req, res) => {
  try {
    const fs = require('fs');
    const htmlContent = fs.readFileSync(path.join(__dirname, 'public', 'test-qr.html'), 'utf8');
    res.setHeader('Content-Type', 'text/html');
    res.send(htmlContent);
  } catch (error) {
    res.status(404).send('Test page not found');
  }
});

// Audio file serving endpoint
app.get('/api/audio/:audioId', async (req, res) => {
  try {
    const { audioId } = req.params;

    // Get audio data from memory store (you might want to use Redis or database in production)
    const audioData = messageHandler.getAudioData(audioId);

    if (!audioData) {
      return res.status(404).json({ error: 'Audio not found' });
    }

    // Handle base64 data
    if (audioData.startsWith('data:')) {
      const [header, base64Data] = audioData.split(',');
      const mimeType = header.match(/data:([^;]+)/)?.[1] || 'audio/ogg';
      const buffer = Buffer.from(base64Data, 'base64');

      res.set({
        'Content-Type': mimeType,
        'Content-Length': buffer.length,
        'Cache-Control': 'public, max-age=3600'
      });

      return res.send(buffer);
    }

    // Handle URL data (proxy from WAHA)
    if (audioData.startsWith('http')) {
      const axios = require('axios');
      try {
        const response = await axios.get(audioData, { responseType: 'stream' });
        res.set({
          'Content-Type': response.headers['content-type'] || 'audio/ogg',
          'Cache-Control': 'public, max-age=3600'
        });
        response.data.pipe(res);
      } catch (error) {
        console.error('Error proxying audio:', error);
        res.status(500).json({ error: 'Failed to fetch audio' });
      }
      return;
    }

    res.status(400).json({ error: 'Invalid audio data format' });
  } catch (error) {
    console.error('Error serving audio:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Test mood analysis endpoint
app.post('/api/test-mood', async (req, res) => {
  try {
    const { phone, message } = req.body;
    
    if (!phone || !message) {
      return res.status(400).json({ error: 'Phone and message are required' });
    }
    
    console.log(`🧪 [TEST] Simulating mood analysis for ${phone}: "${message}"`);
    
    // Get the message handler and trigger mood analysis
    await messageHandler.handleWhatsAppMessage({
      customerPhone: phone,
      customerName: 'Test User',
      content: message,
      type: 'text',
      originalMessage: {
        fromMe: false,
        timestamp: Date.now(),
        isHistoricalMessage: false,
        isNewMessage: true
      }
    });
    
    res.json({ success: true, message: 'Mood analysis triggered' });
  } catch (error) {
    console.error('Error in test mood endpoint:', error);
    res.status(500).json({ error: error.message });
  }
});

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

server.listen(PORT, async () => {
  const message = `🚀 Trinks IA Server running on port ${PORT}`;
  const socketMessage = `📱 Socket.io ready for connections`;
  
  console.log(message);
  console.log(socketMessage);
  
  logger.info(`Server started on port ${PORT}`);
  logger.info('Socket.io initialized and ready for connections');
  
  // Try to auto-connect WhatsApp if there's an existing session
  const whatsappService = require('./services/whatsapp');
  try {
    await whatsappService.tryAutoConnect();
  } catch (error) {
    console.log('⚠️ WhatsApp auto-connect failed:', error.message);
  }
});