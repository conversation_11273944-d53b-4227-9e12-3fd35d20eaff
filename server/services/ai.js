const Anthropic = require('@anthropic-ai/sdk')
const OpenAI = require('openai')
const trinksService = require('./trinks')
const conversationLogger = require('./conversationLogger')
const PromptComposer = require('./PromptComposer')
const messageLogger = require('../utils/messageLogger')
const aiInteractionLogger = require('../utils/AIInteractionLogger')
const dateUtils = require('../utils/dateUtils')
const fs = require('fs')
const path = require('path')
const professionalsCache = require('../utils/professionalsCache')
const servicesCache = require('../utils/servicesCache')
const IntentGraphAdapter = require('../langgraph/adapters/IntentGraphAdapter')
const HumanizedResponseCache = require('../cache/HumanizedResponseCache')
const { errorLogger, ERROR_CATEGORIES } = require('../utils/ErrorLogger')
const ClaudeEvaluationLogger = require('../utils/ClaudeEvaluationLogger')

class AIService {
  constructor () {
    this.anthropic = null
    this.openai = null
    this.conversationMemory = new Map() // Store conversation context
    this.customerCache = new Map() // Cache customer data to avoid repeated API calls
    this.availabilityCache = new Map() // Cache availability data
    this.promptComposer = new PromptComposer() // NEW: Sistema modular de prompts
    this.intentGraphAdapter = new IntentGraphAdapter() // NEW: LangGraph integration
    this.responseCache = new HumanizedResponseCache() // NEW: Cache de respostas humanizadas
    this.professionalServicesMapping = require('../utils/professionalServicesMapping') // NEW: Cache profissional → serviços
    this.evaluationLogger = new ClaudeEvaluationLogger() // NEW: Sistema de avaliação automática
    this.settings = this.loadSettings()
    this.initializeAPI()
    this.initializeProfessionalMapping()
  }

  // Cache management methods
  getCachedCustomer (phoneNumber) {
    const cacheKey = phoneNumber
    const cached = this.customerCache.get(cacheKey)

    if (cached && (Date.now() - cached.timestamp) < 300000) { // 5 minutes TTL (reduced from 15min)
      console.log(`💾 Using cached customer data for ${phoneNumber}`)
      return cached.data
    }

    return null
  }

  setCachedCustomer (phoneNumber, customerData) {
    const cacheKey = phoneNumber
    this.customerCache.set(cacheKey, {
      data: customerData,
      timestamp: Date.now()
    })
    console.log(`💾 Cached customer data for ${phoneNumber}`)
  }

  loadSettings () {
    try {
      const fs = require('fs')
      const path = require('path')
      const settingsPath = path.join(__dirname, '../config/settings.json')

      if (fs.existsSync(settingsPath)) {
        return JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
      }
    } catch (error) {
      console.log('No settings file found, using defaults')
    }

    return {
      establishmentName: 'Salão Trinks',
      services: ['Corte Feminino', 'Corte + Escova', 'Pintura + Corte', 'Hidratação', 'Corte Masculino', 'Corte + Barba', 'Manicure', 'Pedicure'],
      professionals: ['Ana', 'Carla', 'Roberto', 'Paula'],
      workingHours: '9:00 às 18:00, Segunda a Sábado',
      customPrompt: '',
      anthropicApiKey: ''
    }
  }

  initializeAPI () {
    // Initialize Claude API
    const claudeApiKey = this.settings.anthropicApiKey || process.env.ANTHROPIC_API_KEY
    if (claudeApiKey) {
      this.anthropic = new Anthropic({ apiKey: claudeApiKey })
      console.log('🤖 Claude API initialized')
    } else {
      console.log('🤖 No Claude API key found, using mock responses')
    }

    // Initialize OpenAI API for Whisper transcription
    const openaiApiKey = this.settings.openaiApiKey || process.env.OPENAI_API_KEY
    if (openaiApiKey) {
      this.openai = new OpenAI({ apiKey: openaiApiKey })
      console.log('🎤 OpenAI Whisper API initialized')
    } else {
      console.log('🎤 No OpenAI API key found, using mock transcription')
    }
  }

  /**
   * Inicializa o cache de mapeamento profissional → serviços
   */
  async initializeProfessionalMapping () {
    try {
      console.log('🗺️ Inicializando cache de mapeamento profissional → serviços...')

      // Inicialização assíncrona em background para não bloquear startup
      setTimeout(async () => {
        try {
          const stats = this.professionalServicesMapping.getCacheStats()

          if (!stats.isValid) {
            console.log('🔄 Cache de mapeamento expirado, forçando atualização inicial...')
            await this.professionalServicesMapping.forceUpdate()
          } else {
            console.log(`✅ Cache de mapeamento válido: ${stats.professionalsCount} profissionais, ${stats.totalServices} serviços`)
          }

          // Configurar atualização automática periódica
          this.setupPeriodicCacheUpdate()
        } catch (error) {
          console.error('❌ Erro na inicialização do cache de mapeamento:', error.message)
        }
      }, 2000) // 2 segundos de delay para não atrasar startup
    } catch (error) {
      console.error('❌ Erro crítico na inicialização do mapeamento profissional:', error.message)
    }
  }

  /**
   * Configura atualização automática periódica do cache
   */
  setupPeriodicCacheUpdate () {
    const updateIntervalHours = 6 // Atualizar a cada 6 horas
    const updateIntervalMs = updateIntervalHours * 60 * 60 * 1000

    console.log(`⏰ Configurando atualização automática do cache a cada ${updateIntervalHours}h`)

    setInterval(async () => {
      try {
        console.log('🔄 Iniciando atualização periódica do cache de mapeamento...')
        await this.professionalServicesMapping.forceUpdate()
        console.log('✅ Atualização periódica concluída')
      } catch (error) {
        console.error('❌ Erro na atualização periódica do cache:', error.message)
      }
    }, updateIntervalMs)
  }

  // Função utilitária para extrair apenas o primeiro nome do profissional
  extractFirstName (fullName) {
    if (!fullName) return 'Profissional'

    // Remove títulos comuns no início
    const cleanName = fullName.replace(/^(Sr\.|Sra\.|Dr\.|Dra\.|Prof\.|Profª\.)\s*/i, '')

    // Pega apenas o primeiro nome (primeira palavra)
    const firstName = cleanName.split(' ')[0]

    return firstName || 'Profissional'
  }

  /**
   * Transcreve áudio usando OpenAI Whisper
   * @param {string} audioData - Dados do áudio em base64
   * @returns {Promise<string>} - Texto transcrito
   */
  async transcribeAudio (audioData) {
    if (!this.openai) {
      const error = new Error('OpenAI API não está configurada - configure OPENAI_API_KEY')
      await errorLogger.logError(error, ERROR_CATEGORIES.IA_ERROR, {
        function: 'transcribeAudio',
        hasOpenAI: !!this.openai,
        audioDataLength: audioData?.length
      })
      throw error
    }

    console.log('🎤 Iniciando transcrição de áudio com OpenAI Whisper...')

    try {
      // Convert base64 audio data to buffer
      let audioBuffer
      if (audioData.startsWith('data:audio/')) {
        // Handle data URL format: data:audio/webm;base64,<base64data>
        const base64Data = audioData.split(',')[1]
        audioBuffer = Buffer.from(base64Data, 'base64')
      } else {
        // Handle plain base64 data
        audioBuffer = Buffer.from(audioData, 'base64')
      }

      console.log(`🎤 Audio buffer size: ${audioBuffer.length} bytes`)

      // Create a file stream for OpenAI API using Node.js approach
      const fs = require('fs')
      const path = require('path')
      const { Readable } = require('stream')

      // Create a readable stream from buffer
      const audioStream = Readable.from(audioBuffer)
      audioStream.path = 'audio.webm' // Add path property for OpenAI

      const response = await this.openai.audio.transcriptions.create({
        file: audioStream,
        model: 'whisper-1',
        language: 'pt', // Portuguese language
        response_format: 'text'
      })

      const transcriptionText = response.trim()
      console.log(`🎤 Transcrição bem-sucedida: "${transcriptionText}"`)

      return transcriptionText
    } catch (error) {
      console.error('❌ Erro na transcrição de áudio:', error.message)
      console.error('❌ Stack trace:', error.stack)

      const context = {
        function: 'transcribeAudio',
        audioDataLength: audioData?.length,
        errorCode: error.code,
        originalMessage: error.message
      }

      // Check for specific OpenAI errors
      if (error.code === 'invalid_api_key') {
        const specificError = new Error('Chave da API OpenAI inválida ou não configurada')
        await errorLogger.logError(specificError, ERROR_CATEGORIES.IA_ERROR, context)
        throw specificError
      } else if (error.code === 'audio_too_large') {
        const specificError = new Error('Arquivo de áudio muito grande para transcrição')
        await errorLogger.logError(specificError, ERROR_CATEGORIES.IA_ERROR, context)
        throw specificError
      } else if (error.code === 'unsupported_file') {
        const specificError = new Error('Formato de áudio não suportado')
        await errorLogger.logError(specificError, ERROR_CATEGORIES.IA_ERROR, context)
        throw specificError
      }

      const generalError = new Error(`Falha na transcrição: ${error.message}`)
      await errorLogger.logError(generalError, ERROR_CATEGORIES.IA_ERROR, context)
      throw generalError
    }
  }

  async processMessage (customerPhone, message, customerData = null, messageType = 'text', moodAdjustments = null, isBatchedMessage = false) {
    // Skip empty messages for non-audio types (third line of defense)
    if (messageType !== 'audio' && (!message || (typeof message === 'string' && message.trim().length === 0))) {
      console.log(`📝 Empty message ignored from ${customerPhone} - third line of defense`)
      return {
        response: '', // Return empty response to avoid sending anything back
        debugInfo: {
          emptyMessage: true,
          reason: 'Empty message ignored at AI service level'
        }
      }
    }

    // Iniciar logging detalhado da mensagem
    const sessionId = messageLogger.startSession(customerPhone, message, messageType)

    // Configurar ErrorLogger para usar o mesmo sessionId
    errorLogger.setSessionId(sessionId)
    messageLogger.addLog('PROCESS_START', {
      customerPhone,
      messageLength: message?.length || 0,
      messageType,
      hasCustomerData: !!customerData,
      hasMoodAdjustments: !!moodAdjustments,
      isBatchedMessage
    })

    const performanceMetrics = {
      startTime: Date.now(),
      steps: {}
    }

    const trinksApiCalls = []

    try {
      let transcription = null

      // Process audio transcription if message type is audio
      if (messageType === 'audio') {
        console.log('🎤 Processing audio message...')
        console.log('🔍 Audio data preview:', message.substring(0, 100) + '...')

        try {
          transcription = await this.transcribeAudio(message)

          if (transcription && transcription.trim().length > 0) {
            console.log(`🎤 ✅ Audio transcribed successfully: "${transcription}"`)
            // Replace message content with transcription for further processing
            message = transcription.trim()
          } else {
            console.log('🎤 ⚠️ Empty transcription received')
            transcription = null
            message = 'Desculpe, não consegui entender seu áudio. Pode repetir ou digitar sua mensagem?'
          }
        } catch (transcriptionError) {
          console.error('❌ Error transcribing audio:', transcriptionError.message)
          console.error('❌ Error details:', {
            errorCode: transcriptionError.code,
            apiAvailable: !!this.openai,
            audioDataType: typeof message,
            audioDataLength: message?.length || 0
          })

          transcription = null

          // Different error messages based on error type
          if (transcriptionError.message.includes('OpenAI API não está configurada')) {
            message = 'Recebi sua mensagem de voz! Nossa equipe irá ouvi-la em breve. 🎧'
            console.log('🎤 📢 Using fallback message - OpenAI API not configured')
          } else if (transcriptionError.message.includes('muito grande')) {
            message = 'Seu áudio é muito longo. Tente enviar um áudio mais curto ou digite sua mensagem. 📝'
          } else if (transcriptionError.message.includes('não suportado')) {
            message = 'Formato de áudio não suportado. Tente enviar em outro formato ou digite sua mensagem. 📝'
          } else {
            message = 'Desculpe, tive dificuldade para processar seu áudio. Pode repetir ou digitar sua mensagem? 🔄'
          }
        }
      }

      // Get or initialize conversation memory
      const conversation = this.conversationMemory.get(customerPhone) || {
        messages: [],
        context: {},
        stage: 'greeting',
        customerPhone, // CORREÇÃO: adicionar customerPhone à conversa
        schedulingData: {
          professional: { id: null, name: null },
          service: { id: null, name: null, price: null },
          date: null, // formato YYYY-MM-DD
          time: null, // formato HH:MM
          extractedAt: null, // timestamp da última extração
          source: 'ai', // 'ai' ou 'manual'
          isComplete: false // true quando todos os dados necessários estão presentes
        }
      }

      // Inicializar schedulingData se não existir (conversas já existentes)
      if (!conversation.schedulingData) {
        conversation.schedulingData = {
          professional: { id: null, name: null },
          service: { id: null, name: null, price: null },
          date: null,
          time: null,
          extractedAt: null,
          source: 'ai',
          isComplete: false
        }
      }

      // CORREÇÃO: Garantir que customerPhone sempre existe na conversa
      if (!conversation.customerPhone) {
        conversation.customerPhone = customerPhone
      }

      // LOGGING: Estado inicial da conversa
      this.logConversationState(conversation, 'BEFORE_MESSAGE', {
        newMessage: message,
        messageType,
        customerPhone
      })

      // Add user message to conversation
      conversation.messages.push({
        role: 'user',
        content: message,
        timestamp: new Date(),
        type: messageType
      })

      // Get customer data if not provided
      if (!customerData && customerPhone) {
        messageLogger.addLog('CUSTOMER_SEARCH_START', { customerPhone })
        customerData = this.getCachedCustomer(customerPhone)

        if (!customerData) {
          try {
            messageLogger.addLog('CUSTOMER_API_CALL_START', { customerPhone })
            const customerStartTime = Date.now()
            customerData = await trinksService.getCustomerByPhone(customerPhone)
            const customerEndTime = Date.now()

            // Track customer API call
            trinksApiCalls.push({
              endpoint: '/v1/cliente/telefone',
              method: 'GET',
              purpose: `Buscar dados do cliente pelo telefone ${customerPhone}`,
              responseTime: customerEndTime - customerStartTime,
              success: !!customerData,
              resultCount: customerData ? 1 : 0,
              startTime: new Date(customerStartTime).toISOString(),
              endTime: new Date(customerEndTime).toISOString(),
              request: { phone: customerPhone },
              response: {
                status: customerData ? 200 : 404,
                data: customerData ? `Cliente encontrado: ${customerData.nome}` : 'Cliente não encontrado'
              },
              cacheInfo: {
                usedCache: false,
                cacheAge: null,
                cacheValid: false,
                savedToCache: !!customerData
              }
            })

            if (customerData) {
              this.setCachedCustomer(customerPhone, customerData)
              messageLogger.addLog('CUSTOMER_FOUND', {
                customerId: customerData.id,
                customerName: customerData.nome,
                hasHistory: !!(customerData.ultimosServicos?.length)
              })
            }
          } catch (error) {
            console.log(`❌ Customer fetch error: ${error.message}`)
            messageLogger.addLog('CUSTOMER_ERROR', {
              errorMessage: error.message,
              customerPhone
            })

            // Track failed customer API call
            trinksApiCalls.push({
              endpoint: '/v1/cliente/telefone',
              method: 'GET',
              purpose: `Buscar dados do cliente pelo telefone ${customerPhone}`,
              responseTime: 0,
              success: false,
              resultCount: 0,
              startTime: new Date().toISOString(),
              endTime: new Date().toISOString(),
              request: { phone: customerPhone },
              response: { status: 500, error: error.message },
              error: error.message
            })
          }
        } else {
          // Track cache usage for customer
          trinksApiCalls.push({
            endpoint: '/v1/cliente/telefone',
            method: 'GET',
            purpose: `Buscar dados do cliente pelo telefone ${customerPhone}`,
            responseTime: 1,
            success: true,
            resultCount: 1,
            startTime: new Date().toISOString(),
            endTime: new Date().toISOString(),
            request: { phone: customerPhone },
            response: {
              status: 200,
              data: `Cliente encontrado no cache: ${customerData.nome}`
            },
            cacheInfo: {
              usedCache: true,
              cacheAge: Math.round((Date.now() - this.customerCache.get(customerPhone).timestamp) / 1000 / 60),
              cacheValid: true,
              savedToCache: false
            }
          })
        }
      }

      // Progressive availability detection: Check if client made any scheduling-related choices
      messageLogger.addLog('AVAILABILITY_CHECK_START', {
        conversationStage: conversation.stage,
        messageLength: message?.length
      })
      await this.handleProgressiveAvailability(conversation, trinksApiCalls)

      // NOVA FUNCIONALIDADE: Buscar agendamentos futuros do cliente para contexto
      let futureAppointmentsContext = null
      if (customerData && customerData.id) {
        const startTime = Date.now()
        try {
          const futureAppointments = await trinksService.getFutureAppointments(customerPhone)
          trinksApiCalls.push({
            endpoint: '/v1/agendamentos/cliente/futuros',
            method: 'GET',
            purpose: 'Buscar agendamentos futuros para contexto',
            responseTime: Date.now() - startTime,
            success: futureAppointments.success,
            resultCount: futureAppointments.count || 0,
            startTime: new Date().toISOString(),
            endTime: new Date().toISOString(),
            request: { customerPhone },
            response: {
              status: futureAppointments.success ? 200 : 404,
              data: `${futureAppointments.count || 0} agendamentos futuros`
            }
          })

          if (futureAppointments.success && futureAppointments.count > 0) {
            // Separar agendamentos em dois contextos: hoje finalizados vs realmente futuros
            const dateUtils = require('../utils/dateUtils')
            const now = dateUtils.getCurrentDateBR()
            const { hour: currentHour } = dateUtils.getHourMinuteBR(now)
            const todayStr = dateUtils.getDateStringBR(now)

            const todayCompleted = []
            const reallyFuture = []

            futureAppointments.appointments.forEach(apt => {
              const aptDateTime = new Date(apt.dataHoraInicio)
              const aptDateStr = dateUtils.getDateStringBR(aptDateTime)

              // Se é hoje, comparar data e hora completa
              if (aptDateStr === todayStr) {
                // Comparar timestamps completos para precisão
                const currentTimestamp = now.getTime()
                const aptTimestamp = aptDateTime.getTime()

                // Se já passou da hora (incluindo minutos), vai para "hoje finalizado"
                if (aptTimestamp < currentTimestamp) {
                  todayCompleted.push({
                    id: apt.id,
                    dataHora: apt.dataHoraInicio,
                    profissional: this.extractFirstName(apt.profissional?.nome),
                    servico: apt.servico?.nome || 'Serviço',
                    valor: apt.valor || 0
                  })
                }
                // Caso contrário, ainda é futuro (mesmo sendo hoje)
                else {
                  reallyFuture.push({
                    id: apt.id,
                    dataHora: apt.dataHoraInicio,
                    profissional: this.extractFirstName(apt.profissional?.nome),
                    servico: apt.servico?.nome || 'Serviço',
                    valor: apt.valor || 0
                  })
                }
              }
              // Se não é hoje, é definitivamente futuro
              else {
                reallyFuture.push({
                  id: apt.id,
                  dataHora: apt.dataHoraInicio,
                  profissional: this.extractFirstName(apt.profissional?.nome),
                  servico: apt.servico?.nome || 'Serviço',
                  valor: apt.valor || 0
                })
              }
            })

            futureAppointmentsContext = {
              hasAppointments: reallyFuture.length > 0,
              count: reallyFuture.length,
              appointments: reallyFuture,
              // Novo contexto para agendamentos finalizados hoje
              todayCompleted: {
                hasAppointments: todayCompleted.length > 0,
                count: todayCompleted.length,
                appointments: todayCompleted
              }
            }

            console.log(`📅 Cliente tem ${reallyFuture.length} agendamento(s) futuro(s) e ${todayCompleted.length} finalizado(s) hoje`)
          } else {
            futureAppointmentsContext = {
              hasAppointments: false,
              count: 0,
              appointments: [],
              todayCompleted: {
                hasAppointments: false,
                count: 0,
                appointments: []
              }
            }

            console.log('📅 Cliente não tem agendamentos futuros')
          }
        } catch (error) {
          console.error('❌ Erro ao buscar agendamentos futuros:', error.message)
          // Não bloqueia o fluxo em caso de erro
        }
      }

      // Adicionar contexto de agendamentos futuros à conversa
      if (futureAppointmentsContext) {
        conversation.futureAppointmentsContext = futureAppointmentsContext
      }

      // NOVA FUNCIONALIDADE: Handle cancellation confirmation state
      let cancellationResult = conversation?.cancellationResult || null

      if (conversation.stage === 'awaiting_cancellation_confirmation') {
        console.log('🤖 Cliente em estado de confirmação de cancelamento - processando resposta...')

        // Evitar processar confirmação no MESMO turno em que foi solicitada
        const reqAt = conversation.cancellationData?.confirmationRequestedAt || conversation.cancellationData?.createdAt
        const justRequested = reqAt ? (Date.now() - new Date(reqAt).getTime() < 1500) : false
        if (justRequested) {
          console.log('⏭️ Skipping immediate confirmation processing in same turn')
        } else {
          cancellationResult = await this.processCancellationConfirmation(
            conversation,
            customerData,
            trinksApiCalls,
            customerPhone,
            message
          )
        }

        if (cancellationResult.success) {
          if (cancellationResult.cancelled) {
            console.log(`✅ Agendamento cancelado após confirmação: ${cancellationResult.cancelledAppointmentId}`)
          } else if (cancellationResult.intentChange) {
            console.log(`🔄 Cliente mudou intenção para: ${cancellationResult.newIntent}`)
          } else {
            console.log('✅ Agendamento mantido após resposta do cliente')
          }
        } else {
          console.log(`❌ Erro na confirmação de cancelamento: ${cancellationResult.error}`)
        }
      } else if (conversation.cancellationData &&
                 conversation.cancellationData.intent === 'cancel' &&
                 !conversation.cancellationData.awaitingConfirmation) {
        // Original cancellation flow - now asks for confirmation (only if not already processed)
        console.log('🚫 Processando solicitação inicial de cancelamento...')

        // ETAPA 3: DEBUG FLUXO DE ESTADOS - Verificar estado da conversa
        console.log('🔍 DEBUG: Estado da conversa antes de handleCancellation:')
        console.log('  - customerPhone:', customerPhone)
        console.log('  - conversation.cancellationData:', JSON.stringify(conversation.cancellationData, null, 2))
        console.log('  - conversation.stage:', conversation.stage)
        console.log('  - customerData exists:', !!customerData)
        console.log('  - trinksApiCalls length:', trinksApiCalls.length)

        cancellationResult = await this.handleCancellation(conversation, customerData, trinksApiCalls, customerPhone)

        // ETAPA 3: DEBUG FLUXO DE ESTADOS - Verificar resultado retornado
        console.log('🔍 DEBUG: Resultado de handleCancellation:')
        console.log('  - cancellationResult type:', typeof cancellationResult)
        console.log('  - cancellationResult:', JSON.stringify(cancellationResult, null, 2))
        console.log('  - cancellationResult.success:', cancellationResult?.success)
        console.log('  - cancellationResult.error:', cancellationResult?.error)

        // ETAPA 3: PROTEÇÃO DEFENSIVA - Verificar se cancellationResult existe
        if (!cancellationResult) {
          console.log('❌ ERRO CRÍTICO: handleCancellation retornou undefined')
          cancellationResult = {
            success: false,
            error: 'handleCancellation returned undefined - internal error'
          }
        }

        if (cancellationResult.success && cancellationResult.requiresConfirmation) {
          console.log(`❓ Confirmação de cancelamento solicitada para agendamento: ${cancellationResult.appointmentToCancel.id}`)
        } else if (cancellationResult.success) {
          console.log(`✅ Agendamento cancelado: ${cancellationResult.cancelledAppointmentId}`)
        } else {
          console.log(`❌ Falha no cancelamento: ${cancellationResult.error || 'Erro desconhecido'}`)
        }
      }

      // Handle appointment creation confirmation flow
      let appointmentResult = null

      // Check for appointment confirmation response
      if (conversation.stage === 'awaiting_appointment_confirmation') {
        console.log('🤖 Processando confirmação de agendamento...', message)
        appointmentResult = await this.processAppointmentConfirmation(
          conversation,
          customerData,
          trinksApiCalls,
          customerPhone,
          message
        )

        if (appointmentResult.success) {
          if (appointmentResult.created) {
            console.log(`✅ Agendamento criado após confirmação: ${appointmentResult.appointmentId}`)
          } else {
            console.log('❌ Cliente recusou agendamento ou mudou de intenção')
          }
        } else {
          console.log(`❌ Erro na confirmação de agendamento: ${appointmentResult.error}`)
        }

        // 🔍 DEBUG: Verificar se appointmentResult tem mensagem
        if (!appointmentResult.message || appointmentResult.message.trim().length === 0) {
          console.error('🚨 [CRITICAL] appointmentResult.message está vazio!')
          console.error('🔍 [DEBUG] appointmentResult completo:', JSON.stringify(appointmentResult, null, 2))
        }
      } else if (conversation.schedulingData &&
          conversation.schedulingData.isComplete &&
          !conversation.schedulingData.appointmentCreated &&
          !conversation.schedulingData.awaitingConfirmation) {
        // 🔥 CORREÇÃO CRÍTICA: Verificar intenção ANTES de forçar confirmação
        // Problema: Sistema forçava confirmação apenas por schedulingData.isComplete
        // Solução: Usar LangGraph para detectar se usuário realmente quer agendar
        console.log('🤔 Dados completos encontrados - verificando intenção antes de confirmar...')

        const currentIntent = await this.detectCurrentIntentWithLangGraph(conversation, message)
        console.log(`🎯 Intenção detectada: ${currentIntent} para mensagem: "${message}"`)

        // Se usuário está apenas fazendo pergunta informativa, NÃO forçar confirmação
        if (currentIntent === 'inquiry') {
          console.log('📝 Intenção = CONSULTA - respondendo informação sem forçar agendamento')
          // Continuar com fluxo normal de resposta sem entrar em confirmação
        } else if (currentIntent === 'ai_contextual_response') {
          console.log('🤖 EDGE CASE - Processamento contextual via IA com dados completos')
          console.log('📊 Contexto disponível: agendamentos futuros + histórico + serviços mapeados')
          // Deixar IA processar com contexto completo (futureAppointmentsContext + conversationHistory + serviceData)
          // NÃO forçar confirmação - deixar IA decidir baseada no contexto
        } else if (currentIntent === 'scheduling') {
          console.log('📅 Intenção = AGENDAMENTO - solicitando confirmação...')
          messageLogger.addLog('APPOINTMENT_CONFIRMATION_REQUEST', {
            professionalId: conversation.schedulingData.professional.id,
            professionalName: conversation.schedulingData.professional.name,
            serviceId: conversation.schedulingData.service.id,
            serviceName: conversation.schedulingData.service.name,
            date: conversation.schedulingData.date,
            time: conversation.schedulingData.time
          })

          // NOVO FLUXO: Solicitar confirmação antes de criar
          const appointmentDate = new Date(`${conversation.schedulingData.date}T${conversation.schedulingData.time}`)
          const formattedDate = appointmentDate.toLocaleDateString('pt-BR', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
          })
          const formattedTime = appointmentDate.toLocaleTimeString('pt-BR', {
            hour: '2-digit',
            minute: '2-digit'
          })

          // Configurar dados para confirmação
          conversation.schedulingData.awaitingConfirmation = true
          conversation.schedulingData.confirmationRequestedAt = new Date().toISOString()

          conversation.stage = 'awaiting_appointment_confirmation'

          // Usar cache semântico para mensagem de solicitação de confirmação
          const requestContext = {
            customerName: customerData?.nome || '',
            serviceName: conversation.schedulingData.service.name,
            professionalName: conversation.schedulingData.professional.name,
            formattedDate,
            formattedTime,
            servicePrice: conversation.schedulingData.service.price,
            customerType: customerData?.isNewCustomer ? 'new' : 'returning'
          }

          const self = this // Fix scope issue
          const conversationContext = this.buildConversationContext(conversation)
          const requestMessage = await this.responseCache.getOrGenerate(
            'appointment_request_confirm',
            requestContext,
            {
              basePrompt: this.basePrompt,
              useTemplate: true,
              assistantName: this.settings?.assistantName || 'Assistente Virtual',
              generator: async (processedTemplate) => {
              // Use processed template to generate final humanized text
                if (processedTemplate) {
                  return await self.generateSimpleResponse(
                    processedTemplate.prompt,
                    conversationContext
                  )
                }

                return await self.generateSimpleResponse(
                `${self.basePrompt}\n\nCliente solicitou agendamento. Gere uma mensagem de confirmação calorosa e profissional confirmando os detalhes antes de criar o agendamento.`,
                conversationContext
                )
              }
            }
          )

          appointmentResult = {
            success: true,
            needsConfirmation: true,
            message: requestMessage
          }
        } else {
          // 📝 OUTRAS INTENÇÕES (inquiry, greeting, etc.) - NÃO forçar confirmação
          console.log(`🔄 Intenção "${currentIntent}" - seguindo com fluxo normal (sem forçar confirmação)`)
          // Não definir appointmentResult, deixar fluxo normal de IA processar
        }
      }

      // Check if cancellation or appointment result has a direct message (bypass AI generation)
      let aiResponse
      if (cancellationResult && (cancellationResult.confirmationMessage || cancellationResult.message)) {
        console.log('📝 Usando mensagem direta do resultado de cancelamento...')

        const directMessage = cancellationResult.confirmationMessage || cancellationResult.message
        aiResponse = {
          response: directMessage,
          newStage: cancellationResult.nextStage || conversation.stage,
          context: {},
          debugInfo: {
            apiUsed: !!this.anthropic,
            mockResponse: !this.anthropic,
            usedDirectMessage: true,
            cancellationResult,
            messageSource: 'cancellation_handler',
            systemPrompt: this.basePrompt || 'Prompt de cancelamento via IA',
            messagesContext: this.buildConversationContext(conversation),
            model: 'claude-sonnet-4-20250514',
            lastMessage: message
          }
        }

        // Update conversation stage if provided by cancellation result
        if (cancellationResult.nextStage) {
          conversation.stage = cancellationResult.nextStage
        }
      } else if (appointmentResult && appointmentResult.message) {
        console.log('📝 Usando mensagem direta do resultado de agendamento...')
        console.log('🔍 [DEBUG] appointmentResult.message:', appointmentResult.message)
        console.log('🔍 [DEBUG] appointmentResult.success:', appointmentResult.success)
        console.log('🔍 [DEBUG] appointmentResult.created:', appointmentResult.created)

        aiResponse = {
          response: appointmentResult.message,
          newStage: appointmentResult.nextStage || conversation.stage,
          context: {},
          debugInfo: {
            apiUsed: !!this.anthropic,
            mockResponse: !this.anthropic,
            usedDirectMessage: true,
            appointmentResult,
            messageSource: 'appointment_confirmation_handler',
            systemPrompt: this.basePrompt || 'Prompt de confirmação de agendamento via IA',
            messagesContext: this.buildConversationContext(conversation),
            model: 'claude-sonnet-4-20250514',
            lastMessage: message
          }
        }

        // Update conversation stage if provided by appointment result
        if (appointmentResult.nextStage) {
          conversation.stage = appointmentResult.nextStage
        }
      } else {
        // NOVA FUNCIONALIDADE: Verificar se deve usar template de disponibilidade
        const shouldUseAvailabilityTemplate = this.shouldUseAvailabilityTemplate(conversation)

        if (shouldUseAvailabilityTemplate) {
          console.log('📅 Usando template de disponibilidade humanizada...')

          const availabilityResponse = await this.generateAvailabilityResponse(conversation, customerData, performanceMetrics)

          aiResponse = {
            response: availabilityResponse.response,
            newStage: availabilityResponse.newStage || conversation.stage,
            context: availabilityResponse.context || {},
            debugInfo: {
              apiUsed: !!this.anthropic,
              mockResponse: !this.anthropic,
              usedAvailabilityTemplate: true,
              templateUsed: 'availability_suggestion',
              systemPrompt: 'Template de disponibilidade com IA',
              messagesContext: this.buildConversationContext(conversation),
              model: 'claude-sonnet-4-20250514',
              lastMessage: message,
              availabilityData: {
                professional: conversation.availabilityContext?.professionalName,
                date: conversation.availabilityContext?.date,
                totalSlots: conversation.availabilityContext?.totalSlots
              }
            }
          }
        } else {
          // Generate AI response using the new modular system
          messageLogger.addLog('AI_GENERATION_START', {
            conversationMessagesCount: conversation.messages?.length || 0,
            hasAvailabilityContext: !!conversation.availabilityContext,
            hasAppointmentResult: !!appointmentResult,
            trinksApiCallsCount: trinksApiCalls.length
          })

          aiResponse = await this.generateResponse(conversation, customerData, moodAdjustments, performanceMetrics, customerPhone, trinksApiCalls, appointmentResult, cancellationResult)
        }
      }

      // Add AI response to conversation
      conversation.messages.push({
        role: 'assistant',
        content: aiResponse.response,
        timestamp: new Date()
      })

      // Update conversation stage and context
      if (aiResponse.newStage) {
        conversation.stage = aiResponse.newStage
      }
      if (aiResponse.context) {
        conversation.context = { ...conversation.context, ...aiResponse.context }
      }

      // LOGGING: Estado final da conversa
      this.logConversationState(conversation, 'AFTER_AI_RESPONSE', {
        aiResponse: aiResponse.response,
        newStage: conversation.stage
      })

      // Store updated conversation
      this.conversationMemory.set(customerPhone, conversation)

      // 🛡️ VALIDAÇÃO CRÍTICA: Garantir que resposta nunca seja vazia
      let finalResponse = typeof aiResponse.response === 'string' ? aiResponse.response : String(aiResponse.response || '')

      // Se resposta está vazia, usar fallback
      if (!finalResponse || finalResponse.trim().length === 0) {
        console.error('🚨 [CRITICAL] Resposta da IA está vazia! Usando fallback...')
        console.error('🔍 [DEBUG] aiResponse completo:', JSON.stringify(aiResponse, null, 2))
        console.error('🔍 [DEBUG] appointmentResult:', JSON.stringify(appointmentResult, null, 2))
        console.error('🔍 [DEBUG] cancellationResult:', JSON.stringify(cancellationResult, null, 2))

        finalResponse = this.getEmergencyFallbackResponse(conversation, appointmentResult, cancellationResult)

        // Log do fallback no AIInteractionLogger
        const sessionId = messageLogger.sessionId || 'unknown-session'
        await aiInteractionLogger.logAIError(sessionId, customerPhone, {
          errorType: 'EMPTY_RESPONSE',
          errorMessage: 'IA retornou resposta vazia',
          duration: 0,
          context: {
            conversationStage: conversation?.stage,
            appointmentResult,
            cancellationResult,
            aiResponse
          }
        })
      }

      const result = {
        response: finalResponse,
        transcription,
        stage: conversation.stage,
        context: conversation.context,
        debugInfo: {
          ...aiResponse.debugInfo,
          sessionId,
          trinksApiCalls,
          hadEmptyResponse: finalResponse !== (aiResponse.response || ''),
          originalResponse: aiResponse.response,
          // Audio-specific information
          audioProcessed: messageType === 'audio',
          transcriptionEngine: messageType === 'audio' ? (this.openai ? 'openai-whisper' : 'none') : null,
          transcriptionSuccess: messageType === 'audio' ? !!transcription : null,
          transcriptionText: messageType === 'audio' ? transcription : null
        }
      }

      // Log final result
      messageLogger.addLog('PROCESS_SUCCESS', {
        responseLength: result.response?.length || 0,
        stage: result.stage,
        hasDebugInfo: !!result.debugInfo,
        trinksApiCallsCount: trinksApiCalls.length,
        totalProcessingTime: `${Date.now() - performanceMetrics.startTime}ms`
      })

      // Finalizar sessão de logging
      await messageLogger.endSession(result)

      // Limpar sessionId do ErrorLogger
      errorLogger.clearSessionId()

      return result
    } catch (error) {
      console.error('❌ Error processing message:', error)

      // Log do erro
      messageLogger.addLog('PROCESS_ERROR', {
        errorMessage: error.message,
        errorStack: error.stack,
        processingTime: `${Date.now() - performanceMetrics.startTime}ms`
      })

      const errorResponse = 'Desculpe, tive um problema técnico. Pode repetir sua mensagem?'
      const errorResult = {
        response: errorResponse,
        stage: 'error',
        context: {},
        debugInfo: {
          sessionId,
          error: error.message
        }
      }

      // Finalizar sessão com erro
      await messageLogger.endSession(errorResult, error)

      // Limpar sessionId do ErrorLogger
      errorLogger.clearSessionId()

      return errorResult
    }
  }

  async generateResponse (conversation, customerData, moodAdjustments = null, performanceMetrics = null, customerPhone = null, existingTrinksApiCalls = [], appointmentResult = null, cancellationResult = null) {
    const trinksApiCalls = [...existingTrinksApiCalls]
    const startTime = Date.now()

    try {
      // Build system prompt using the new modular system
      performanceMetrics.steps.promptBuildStart = Date.now()
      const { prompt } = await this.buildSystemPrompt(customerData, trinksApiCalls, conversation, moodAdjustments, appointmentResult, cancellationResult)
      performanceMetrics.steps.promptBuildEnd = Date.now()

      console.log(`⏱️ [AI] Prompt build: ${performanceMetrics.steps.promptBuildEnd - performanceMetrics.steps.promptBuildStart}ms`)

      // Build conversation context
      const conversationContext = this.buildConversationContext(conversation)

      let content = ''
      let response = null

      // Validate conversation context before API call
      const validContext = conversationContext.filter(msg =>
        msg.content && msg.content.trim().length > 0
      )

      if (this.anthropic) {
        if (validContext.length === 0) {
          console.log('⚠️ Contexto de conversa vazio, adicionando mensagem padrão')
          validContext.push({
            role: 'user',
            content: conversation.messages[conversation.messages.length - 1]?.content || 'Olá'
          })
        }

        // Call Claude API
        performanceMetrics.steps.claudeCallStart = Date.now()

        // Log AI interaction (MessageLogger - detalhado)
        messageLogger.logAIInteraction(
          'CLAUDE_REQUEST',
          prompt,
          null, // Response será logado após receber
          0, // Duration será calculado após
          'claude-sonnet-4-20250514'
        )

        // 🧠 Log prompt enviado no AIInteractionLogger
        const sessionId = messageLogger.sessionId || 'unknown-session'
        await aiInteractionLogger.logPromptSent(sessionId, customerPhone, {
          model: 'claude-sonnet-4-20250514',
          systemPrompt: prompt,
          userMessage: validContext.map(msg => `${msg.role}: ${msg.content}`).join('\n'),
          maxTokens: 500,
          temperature: 0.7,
          interactionType: 'general_response',
          context: {
            availabilityContext: conversation?.availabilityContext,
            conversationStage: conversation?.stage,
            customerData: customerData ? { name: customerData.name, type: customerData.type } : null
          }
        })

        // ✨ Interceptar chamada Claude com sistema de avaliação automática
        const claudeParams = {
          model: 'claude-sonnet-4-20250514',
          max_tokens: 500,
          temperature: 0.7,
          system: prompt,
          messages: validContext
        }
        
        response = await this.evaluationLogger.interceptClaudeCall(
          claudeParams,
          (params) => this.anthropic.messages.create(params),
          {
            source: 'ai_service_general_response',
            sessionId: sessionId,
            customerPhone: customerPhone,
            interactionType: 'general_response'
          }
        )
        performanceMetrics.steps.claudeCallEnd = Date.now()

        const claudeDuration = performanceMetrics.steps.claudeCallEnd - performanceMetrics.steps.claudeCallStart
        console.log(`⏱️ [AI] Claude API: ${claudeDuration}ms`)

        // Extract content with proper error handling
        if (!response.content || !response.content[0] || typeof response.content[0].text !== 'string') {
          console.error('❌ Invalid Claude API response structure:', JSON.stringify(response, null, 2))
          throw new Error('Invalid Claude API response structure: missing content[0].text')
        }

        content = response.content[0].text

        // Log AI response (MessageLogger - detalhado)
        messageLogger.logAIInteraction(
          'CLAUDE_RESPONSE',
          null, // Prompt já foi logado
          content,
          claudeDuration,
          'claude-sonnet-4-20250514'
        )

        // 🧠 Log resposta recebida no AIInteractionLogger
        await aiInteractionLogger.logResponseReceived(sessionId, customerPhone, {
          response: content,
          duration: claudeDuration,
          model: 'claude-sonnet-4-20250514',
          success: true,
          tokensUsed: response.usage?.total_tokens || null,
          newStage: null, // será definido depois se houver mudança
          contextUpdates: {},
          usedTemplate: false,
          fromCache: false
        })

        // Validate response with guardrails
        const validation = this.promptComposer.validateResponse(content)
        if (!validation.valid) {
          console.log('⚠️ Response validation warnings:', validation.warnings)
        }
      } else {
        // Mock response when no API
        content = this.getMockResponse(customerData)
      }

      return {
        response: content,
        newStage: 'active',
        context: {},
        debugInfo: {
          apiUsed: !!this.anthropic,
          mockResponse: !this.anthropic,
          systemPrompt: prompt,
          messagesContext: validContext || conversationContext,
          rawResponse: this.anthropic ? response : null,
          model: 'claude-sonnet-4-20250514',
          lastMessage: conversation.messages[conversation.messages.length - 1]?.content || '',
          stage: conversation.stage || 'active',
          customerData: customerData
            ? {
                nome: customerData.nome,
                hasHistory: !!(customerData.appointmentsData?.past?.length || customerData.ultimosServicos?.length)
              }
            : null,
          trinksApiCalls,
          totalTime: `${Date.now() - startTime}ms`,
          promptTokens: Math.ceil(prompt.length / 4),
          performanceMetrics: performanceMetrics
            ? {
                totalTime: `${Date.now() - performanceMetrics.startTime}ms`,
                steps: performanceMetrics.steps
              }
            : null,
          validation: this.promptComposer.validateResponse(content)
        }
      }
    } catch (error) {
      console.error('❌ Error calling Claude API:', {
        name: error.name,
        message: error.message,
        stack: error.stack,
        fullError: error
      })

      // 🧠 Log erro da IA no AIInteractionLogger
      const sessionId = messageLogger.sessionId || 'unknown-session'
      const errorDuration = Date.now() - performanceMetrics.steps.claudeCallStart
      await aiInteractionLogger.logAIError(sessionId, customerPhone, {
        errorType: error.name || 'UNKNOWN_ERROR',
        errorMessage: error.message,
        duration: errorDuration,
        stackTrace: error.stack,
        context: {
          conversationStage: conversation?.stage,
          customerData: customerData ? { name: customerData.name, type: customerData.type } : null
        }
      })

      const mockContent = this.getMockResponse(customerData)
      return {
        response: mockContent,
        newStage: 'error',
        context: {},
        debugInfo: {
          apiUsed: false,
          mockResponse: true,
          systemPrompt: null,
          messagesContext: null,
          rawResponse: null,
          model: 'claude-sonnet-4-20250514',
          lastMessage: conversation?.messages[conversation.messages.length - 1]?.content || '',
          stage: 'error',
          customerData: customerData
            ? {
                nome: customerData.nome,
                hasHistory: !!(customerData.appointmentsData?.past?.length || customerData.ultimosServicos?.length)
              }
            : null,
          trinksApiCalls,
          error: error.message,
          totalTime: `${Date.now() - startTime}ms`
        }
      }
    }
  }

  getMockResponse (customerData) {
    const responses = [
      'Oi! Que bom falar com você! 😊\nComo posso ajudar hoje?',
      'E aí! Tudo bem? 🌟\nQuer marcar um horário?',
      'Opa! Seja bem-vindo(a)! ✨\nO que vai ser hoje?'
    ]
    return responses[Math.floor(Math.random() * responses.length)]
  }

  async buildSystemPrompt (customerData, trinksApiCalls = [], conversation = null, moodAdjustments = null, appointmentResult = null, cancellationResult = null) {
    console.log('🔄 Using NEW modular prompt system...')

    try {
      // Prepare establishment information for prompt composition
      const establishmentInfo = await this.getEstablishmentInfo(trinksApiCalls)

      // Controlar envio de disponibilidade para Claude AI
      let availabilityToSend = this.determineAvailabilityForAI(conversation)

      // 🔥 CORREÇÃO CRÍTICA: SEMPRE buscar disponibilidade ANTES de gerar resposta se há dados de agendamento
      // Isso evita respostas como "deixa eu confirmar" pois sistema já sabe a disponibilidade
      if (conversation?.schedulingData) {
        const { date, professional, service } = conversation.schedulingData

        // 🎯 NOVA LÓGICA: Usar dados já identificados para buscar disponibilidade automaticamente
        console.log('🎯 Dados de agendamento detectados na conversa:')
        console.log(`   👨‍💼 Profissional: ${professional?.name || 'não definido'} (ID: ${professional?.id || 'não definido'})`)
        console.log(`   💼 Serviço: ${service?.name || 'não definido'} (ID: ${service?.id || 'não definido'})`)
        console.log(`   📅 Data: ${date || 'não definida'}`)
        console.log(`   ⏰ Horário: ${conversation.schedulingData.time || 'não especificado'}`)

        // SEMPRE buscar disponibilidade quando há pelo menos data + (profissional OU serviço)
        if (date && (professional?.name || professional?.id || service?.name || service?.id)) {
          console.log('🚀 FORÇANDO busca de disponibilidade com dados do estado da conversa')

          // Passar dados do schedulingData diretamente para busca de disponibilidade
          await this.fetchAvailabilityWithSchedulingData(conversation, trinksApiCalls)
          availabilityToSend = this.determineAvailabilityForAI(conversation)

          if (availabilityToSend) {
            console.log('✅ SUCESSO: Disponibilidade obtida ANTES da resposta usando schedulingData')
            console.log(`   📊 ${availabilityToSend.totalSlots || 0} horários encontrados`)
          } else {
            console.log('❌ ATENÇÃO: Não conseguiu obter disponibilidade com schedulingData - tentando busca padrão')
            // Fallback para busca tradicional
            await this.handleProgressiveAvailability(conversation, trinksApiCalls)
            availabilityToSend = this.determineAvailabilityForAI(conversation)
          }
        } else {
          console.log('⚠️ Dados parciais insuficientes - usando busca tradicional de disponibilidade')
          await this.handleProgressiveAvailability(conversation, trinksApiCalls)
          availabilityToSend = this.determineAvailabilityForAI(conversation)
        }
      }

      // Use the new PromptComposer to build the prompt
      const composedPrompt = await this.promptComposer.composePrompt(
        customerData,
        conversation,
        availabilityToSend,
        establishmentInfo,
        appointmentResult,
        cancellationResult
      )

      // Add mood adjustments if available (maintain backward compatibility)
      let finalPrompt = composedPrompt
      if (moodAdjustments) {
        finalPrompt += `

=== ANÁLISE DE HUMOR DO CLIENTE ===
HUMOR ATUAL: ${moodAdjustments.moodCategory}
ESTILO DE RESPOSTA RECOMENDADO: ${moodAdjustments.responseStyle || 'professional_neutral'}

IMPORTANTE: Ajuste seu tom baseado no humor detectado do cliente.`
      }

      console.log('✅ Prompt modular composto com sucesso')
      return {
        prompt: finalPrompt,
        trinksApiCalls
      }
    } catch (error) {
      console.error('❌ Erro no sistema modular de prompts:', error.message)
      throw error // Remover fallback - sistema deve falhar se construção de prompt falhar
    }
  }

  /**
   * Determina se e qual disponibilidade enviar para Claude AI
   * Controla reenvio baseado em mudanças de contexto
   * CORRIGIDO: Também verifica schedulingData para forçar busca
   */
  determineAvailabilityForAI (conversation) {
    const availabilityContext = conversation?.availabilityContext
    const schedulingData = conversation?.schedulingData

    // CORREÇÃO: Se tem dados de agendamento mas não tem disponibilidade, forçar busca
    if (!availabilityContext && schedulingData) {
      const hasEnoughData = schedulingData.date &&
                           (schedulingData.professional?.name || schedulingData.service?.name)

      if (hasEnoughData) {
        console.log('⚠️ Tem schedulingData mas sem availabilityContext - precisa buscar disponibilidade')
        console.log('   📅 Data:', schedulingData.date)
        console.log('   👨‍💼 Profissional:', schedulingData.professional?.name)
        console.log('   💼 Serviço:', schedulingData.service?.name)
        // Retorna null mas sinaliza que precisa buscar
        return null
      }
    }

    if (!availabilityContext) {
      console.log('📤 Sem contexto de disponibilidade para enviar à IA')
      return null
    }

    // 🔥 CORREÇÃO CRÍTICA: Calcular idade dos dados
    const dataAge = availabilityContext.contextChangedAt
      ? Date.now() - new Date(availabilityContext.contextChangedAt).getTime()
      : Number.MAX_VALUE

    const isFreshData = dataAge < 30000 // Dados com menos de 30 segundos
    const hasAvailability = availabilityContext.hasAvailability && availabilityContext.availableSlots?.length > 0

    // 🎯 REGRA FUNDAMENTAL: SEMPRE enviar dados frescos ou quando há disponibilidade real
    if (isFreshData || hasAvailability) {
      console.log('✅ Enviando disponibilidade COMPLETA para IA:')
      console.log(`   📅 Data: ${availabilityContext.date}`)
      console.log(`   👨‍💼 Profissional: ${availabilityContext.professionalName}`)
      console.log(`   ⏰ ${availabilityContext.totalSlots || 0} horários disponíveis`)
      console.log(`   🕐 Idade dos dados: ${Math.round(dataAge / 1000)}s`)
      console.log(`   📦 Do cache: ${availabilityContext.fromCache ? 'Sim' : 'Não'}`)

      // Reset do flag para garantir envio
      availabilityContext.sentToAI = false
      availabilityContext.sentToAIAt = new Date().toISOString()

      return {
        ...availabilityContext,
        contextType: 'full',
        mustUseData: true // Flag indicando que IA DEVE usar estes dados
      }
    }

    // Dados antigos sem disponibilidade - enviar contexto minimal
    const currentProfessional = conversation?.context?.selectedProfessional
    const contextProfessional = availabilityContext.professionalName

    if (availabilityContext.sentToAI && currentProfessional === contextProfessional && !hasAvailability) {
      console.log(`⚠️ Dados antigos (${Math.round(dataAge / 1000)}s) sem disponibilidade - enviando referência`)

      return {
        ...availabilityContext,
        availableSlots: [],
        formattedSlots: 'Sem horários disponíveis no momento',
        alreadySentToAI: true,
        contextType: 'reference'
      }
    }

    // Sempre enviar se há disponibilidade
    console.log('📤 Enviando contexto de disponibilidade para IA')
    return {
      ...availabilityContext,
      contextType: 'full'
    }
  }

  async getEstablishmentInfo (trinksApiCalls) {
    const establishmentInfo = {
      establishmentName: this.settings.establishmentName || 'Salão Trinks',
      workingHours: this.settings.workingHours || '9h às 18h, Segunda a Sábado',
      professionals: (this.settings.professionals || []).join(', ') || 'Equipe qualificada',
      servicesInfo: ''
    }

    // Try to get real services data from Trinks API
    try {
      const startTime = Date.now()
      const servicesCache = require('../utils/servicesCache')
      const cacheStatus = servicesCache.getCacheStatus()
      const services = await trinksService.getServices()
      const endTime = Date.now()

      // Track API call for debug info (including cache information)
      trinksApiCalls.push({
        endpoint: '/v1/servicos',
        method: 'GET',
        purpose: 'Buscar lista de serviços disponíveis',
        responseTime: endTime - startTime,
        success: !!(services && services.length > 0),
        resultCount: services ? services.length : 0,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
        cacheInfo: {
          usedCache: cacheStatus.isValid,
          cacheAge: cacheStatus.ageMinutes,
          cacheValid: cacheStatus.isValid,
          cacheServices: cacheStatus.servicesCount
        },
        request: null,
        response: {
          status: services && services.length > 0 ? 200 : 404,
          data: services ? `${services.length} serviços encontrados` : 'Nenhum serviço encontrado'
        }
      })

      if (services && services.length > 0) {
        establishmentInfo.servicesInfo = services.map(service =>
          `${service.nome} - ${service.duracao || service.duracaoEmMinutos || '?'}min - R$${service.preco || service.valor || '?'}`
        ).join('\n')
      } else {
        establishmentInfo.servicesInfo = (this.settings.services || []).join(', ') || 'Serviços gerais'
      }
    } catch (error) {
      console.log('Could not fetch services for modular prompt:', error.message)
      establishmentInfo.servicesInfo = (this.settings.services || []).join(', ') || 'Serviços gerais'
    }

    return establishmentInfo
  }

  buildSystemPromptFallback (customerData, trinksApiCalls, conversation, moodAdjustments) {
    console.log('⚠️ Using fallback prompt system...')

    const basicPrompt = `Você é uma recepcionista calorosa de um salão de beleza brasileiro.
    
- Fale de forma natural e brasileira
- Use expressões como "E aí!", "Que massa!", "Show!"
- Mantenha tom caloroso e profissional
- NUNCA diga "vou verificar" ou "aguarde"
- Use dados reais quando disponível

Cliente: ${customerData?.nome || 'Cliente'}`

    return {
      prompt: basicPrompt,
      trinksApiCalls
    }
  }

  buildConversationContext (conversation) {
    // Convert conversation to Claude format, keeping last 10 messages
    const recentMessages = conversation.messages.slice(-10)

    return recentMessages
      .filter(msg => msg.content && msg.content.trim().length > 0) // Filter out empty messages
      .map(msg => ({
        role: msg.role === 'user' ? 'user' : 'assistant',
        content: msg.content.trim()
      }))
  }

  /**
   * Progressive availability detection and fetching
   * This function analyzes the conversation and fetches availability when appropriate
   */
  async handleProgressiveAvailability (conversation, trinksApiCalls) {
    if (!conversation || !conversation.messages || conversation.messages.length === 0) {
      return
    }

    const lastMessage = conversation.messages[conversation.messages.length - 1]?.content || ''
    const currentStage = conversation.stage || 'greeting'

    console.log('🔍 Checking if availability fetch is needed...')

    // EVITAR LOOP: não rodar análise de disponibilidade/cancelamento durante confirmação de cancelamento
    if (currentStage === 'awaiting_cancellation_confirmation') {
      console.log('⏭️ Skipping progressive analysis: awaiting cancellation confirmation')
      return
    }

    // 🎯 NOVA LÓGICA OTIMIZADA: Primeiro verificar se já temos dados de agendamento coletados
    const hasSchedulingData = conversation?.schedulingData && (
      conversation.schedulingData.date ||
      conversation.schedulingData.professional?.name ||
      conversation.schedulingData.service?.name
    )

    if (hasSchedulingData) {
      console.log('✅ Dados de agendamento já coletados no schedulingData - prosseguindo diretamente')
      console.log(`   📅 Data: ${conversation.schedulingData.date || 'não definida'}`)
      console.log(`   👨‍💼 Profissional: ${conversation.schedulingData.professional?.name || 'não definido'}`)
      console.log(`   💼 Serviço: ${conversation.schedulingData.service?.name || 'não definido'}`)

      // Prosseguir direto para busca de disponibilidade sem análise de intenção
    } else {
      // LAZY LOADING: Skip availability fetch for early conversation stages
      if (currentStage === 'greeting' || currentStage === 'initial') {
        const isSimpleGreeting = /^(oi|olá|ola|hey|hello|hi)$|^(oi|olá|ola)\s*(tudo\s*bem|blz)?[.!?]*$/i.test(lastMessage.trim())
        if (isSimpleGreeting) {
          console.log('🚫 Skipping availability fetch for simple greeting')
          return
        }
      }

      // DETECÇÃO DE INTENÇÃO: Usar LangGraph apenas se não há dados de agendamento
      const hasSchedulingIntent = await this.detectSchedulingIntentWithLangGraph(conversation)

      if (!hasSchedulingIntent) {
        console.log('🚫 Skipping availability fetch - no scheduling intent detected by AI')
        return
      }

      console.log('✅ Scheduling intent detected by AI, proceeding with availability analysis...')
    }

    try {
      // 🎯 NOVA LÓGICA: Se temos schedulingData, usar busca otimizada
      if (hasSchedulingData) {
        console.log('🚀 Usando fetchAvailabilityWithSchedulingData (otimizada)')
        await this.fetchAvailabilityWithSchedulingData(conversation, trinksApiCalls)

        // Verificar se conseguimos obter disponibilidade
        if (conversation.availabilityContext?.hasAvailability) {
          console.log('✅ Disponibilidade obtida usando schedulingData')
          return
        } else {
          console.log('⚠️ Fallback: schedulingData não resultou em disponibilidade - tentando método tradicional')
        }
      }

      // FALLBACK OU MÉTODO TRADICIONAL: Extrair dados e buscar disponibilidade
      console.log('🔄 Usando método tradicional de busca de disponibilidade...')
      await this.extractSchedulingInfoAndFetchAvailability(conversation, trinksApiCalls)

      // NOVO: Busca progressiva de disponibilidade baseada nos dados extraídos
      const availabilityData = await this.fetchProgressiveAvailability(conversation)

      if (availabilityData) {
        console.log('✅ Disponibilidade obtida via busca progressiva tradicional')
        conversation.availabilityContext = availabilityData
      } else {
        console.log('⚠️ Nenhuma disponibilidade encontrada na busca progressiva')
      }
    } catch (error) {
      console.error('❌ Error in progressive availability handling:', error.message)

      // Log do erro
      const context = {
        customerPhone: conversation.customerPhone,
        stage: conversation.stage,
        hasExtractedData: !!conversation.extractedData,
        scenario: 'progressive_availability_handling'
      }

      await errorLogger.logError(error, ERROR_CATEGORIES.IA_ERROR, context)
    }
  }

  /**
   * Detecta intenção de agendamento usando LangGraph (NOVA IMPLEMENTAÇÃO)
   * Substitui sistema anterior por análise estruturada e validada
   */
  async detectSchedulingIntentWithLangGraph (conversation) {
    console.log('🔗 Usando LangGraph para detecção de intenção...')

    // Usar IntentGraphAdapter para detectar intenção - SEM FALLBACK
    const hasIntent = await this.intentGraphAdapter.detectSchedulingIntentWithAI(conversation)

    console.log(`📊 LangGraph Result: ${hasIntent ? 'SIM' : 'NÃO'} - Intenção de agendamento detectada`)

    return hasIntent
  }

  /**
   * 🔥 NOVO: Detecta intenção específica da mensagem atual usando IA
   * Usado para evitar loops de confirmação quando usuário faz pergunta informativa
   * SEMPRE usa IA - sem fallbacks de palavras-chave
   *
   * ⚡ EDGE CASE HANDLING: Se confidence baixa ou intenção desconhecida,
   * retorna 'ai_contextual_response' para processamento contextual completo
   *
   * @param {Object} conversation - Conversa atual
   * @param {string} currentMessage - Mensagem atual do usuário
   * @returns {string} - 'scheduling', 'inquiry', 'greeting', 'cancellation', 'ai_contextual_response'
   */
  async detectCurrentIntentWithLangGraph (conversation, currentMessage) {
    console.log('🔍 Detectando intenção específica da mensagem atual via IA...')

    try {
      // Criar conversa temporária com contexto completo para análise da IA
      const tempConversation = {
        ...conversation,
        messages: [
          ...conversation.messages, // TODAS as mensagens para contexto completo
          { role: 'user', content: currentMessage, timestamp: new Date() }
        ]
      }

      // Usar IntentGraphAdapter para classificação completa de intenção VIA IA
      const intentResult = await this.intentGraphAdapter.detectFullIntentWithAI(tempConversation)

      console.log(`🎯 IA detectou intenção: ${intentResult?.intent || 'unknown'} (confidence: ${intentResult?.confidence || 0})`)
      console.log(`💭 Contexto extraído: ${JSON.stringify(intentResult?.context || {})}`)

      const detectedIntent = intentResult?.intent || 'unknown'
      const confidence = intentResult?.confidence || 0

      // 🚨 EDGE CASE DETECTION: Low confidence or unknown intent
      // Usar AI contextual response instead of rigid intent mapping
      if (confidence < 0.7 || detectedIntent === 'unknown') {
        console.log(`🔄 EDGE CASE: Confidence baixa (${confidence}) ou intenção desconhecida (${detectedIntent})`)
        console.log('🤖 Retornando ai_contextual_response para processamento contextual completo')
        return 'ai_contextual_response'
      }

      return detectedIntent
    } catch (error) {
      console.error('❌ Erro ao detectar intenção atual via IA:', error.message)
      // Se IA falhar, sistema deve falhar explicitamente (sem fallbacks)
      throw new Error(`Detecção de intenção via IA falhou: ${error.message}`)
    }
  }

  /**
   * Processa solicitação de cancelamento identificada pela IA
   * Busca agendamento futuro do cliente e executa o cancelamento via API Trinks
   */
  async processCancellationRequest (conversation, extractedInfo, trinksApiCalls) {
    console.log('🚫 Processando solicitação de cancelamento...')
    console.log('📊 Dados extraídos:', extractedInfo)

    try {
      // Buscar dados do cliente
      const customerData = await trinksService.getCustomerByPhone(conversation.customerPhone)
      if (!customerData || !customerData.id) {
        console.log('❌ Cliente não encontrado para cancelamento')
        conversation.cancellationResult = {
          success: false,
          error: 'Cliente não encontrado',
          message: 'Não consegui encontrar seus dados no sistema.'
        }
        return
      }

      // Buscar agendamentos futuros do cliente
      const futureAppointmentsResult = await trinksService.getFutureAppointments(conversation.customerPhone)

      if (!futureAppointmentsResult || !futureAppointmentsResult.success ||
          !futureAppointmentsResult.appointments || futureAppointmentsResult.appointments.length === 0) {
        console.log('📅 Nenhum agendamento futuro encontrado')
        conversation.cancellationResult = {
          success: false,
          noAppointments: true,
          error: 'Nenhum agendamento futuro'
        }
        return
      }

      const futureAppointments = futureAppointmentsResult.appointments

      // Se extraiu data/profissional específicos, filtrar
      let targetAppointment = null

      if (extractedInfo.date || extractedInfo.professional) {
        console.log('🎯 Buscando agendamento específico...')

        targetAppointment = futureAppointments.find(apt => {
          const aptDate = apt.dataHoraInicio.split('T')[0] // YYYY-MM-DD
          const aptProfessional = apt.profissional?.nome

          const dateMatch = !extractedInfo.date || aptDate === extractedInfo.date
          const professionalMatch = !extractedInfo.professional ||
            (aptProfessional && aptProfessional.toLowerCase().includes(extractedInfo.professional.toLowerCase()))

          return dateMatch && professionalMatch
        })
      } else {
        // Pegar o próximo agendamento (mais comum)
        targetAppointment = futureAppointments[0]
      }

      if (!targetAppointment) {
        console.log('❌ Agendamento específico não encontrado')
        conversation.cancellationResult = {
          success: false,
          error: 'Agendamento não encontrado',
          message: `Não encontrei o agendamento específico que você quer cancelar. Você tem: ${futureAppointments.map(a => {
            const date = new Date(a.dataHoraInicio).toLocaleDateString('pt-BR')
            const time = new Date(a.dataHoraInicio).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
            return `${a.servico.nome} em ${date} às ${time}`
          }).join(', ')}.`
        }
        return
      }

      console.log(`🎯 Agendamento alvo: ${targetAppointment.id} - ${targetAppointment.servico.nome}`)

      // NOVO FLUXO: Solicitar confirmação antes de cancelar
      const appointmentDate = new Date(targetAppointment.dataHoraInicio)
      const formattedDate = appointmentDate.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
      const formattedTime = appointmentDate.toLocaleTimeString('pt-BR', {
        hour: '2-digit',
        minute: '2-digit'
      })

      // CORREÇÃO: Configurar dados para confirmação preservando dados anteriores
      if (!conversation.cancellationData) {
        conversation.cancellationData = {}
      }
      Object.assign(conversation.cancellationData, {
        awaitingConfirmation: true,
        appointmentToCancel: targetAppointment,
        reason: extractedInfo.reason || 'não informado',
        clarificationAttempts: 0,
        createdAt: new Date().toISOString(),
        confirmationRequestedAt: new Date().toISOString()
      })

      conversation.stage = 'awaiting_cancellation_confirmation'

      // Gerar mensagem de confirmação via cache humanizado
      const cancellationContext = {
        serviceName: targetAppointment.servico.nome,
        professionalName: targetAppointment.profissional?.nome || 'o profissional',
        appointmentDate: formattedDate,
        appointmentTime: formattedTime,
        customerName: conversation.customerData?.name || 'Cliente',
        conversationHistory: this.getConversationHistory(conversation)
      }

      // Usar cache humanizado para gerar mensagem de confirmação
      const conversationContext = this.buildConversationContext(conversation)
      const confirmationMessage = await this.responseCache.getOrGenerate(
        'cancellation_confirm',
        cancellationContext,
        {
          basePrompt: this.basePrompt,
          useTemplate: true,
          assistantName: this.settings?.assistantName || 'Assistente Virtual',
          generator: async (processedTemplate) => {
            if (processedTemplate) {
              return await this.generateSimpleResponse(
                processedTemplate.prompt,
                conversationContext
              )
            }

            if (!this.anthropic) {
              throw new Error('Claude API não disponível - sistema configurado para falhar sem IA')
            }

            return await this.generateSimpleResponse(
              `${this.basePrompt}\n\nCliente quer cancelar agendamento. Gere uma mensagem de confirmação natural perguntando o motivo.`,
              conversationContext
            )
          }
        }
      )

      conversation.cancellationResult = {
        success: true,
        needsConfirmation: true,
        message: confirmationMessage
      }
    } catch (error) {
      console.error('❌ Erro no processamento de cancelamento:', error.message)
      conversation.cancellationResult = {
        success: false,
        error: error.message,
        message: 'Ocorreu um erro ao processar seu cancelamento. Tente novamente.'
      }
    }
  }

  /**
   * Extract scheduling information from conversation and fetch availability
   * NOVA VERSÃO: Sempre busca disponibilidade quando tem data + profissional
   */
  async extractSchedulingInfoAndFetchAvailability (conversation, trinksApiCalls) {
    const messages = conversation.messages.slice(-10) // Last 10 messages for context (igual aos edge cases)
    const conversationText = messages.map(m => `${m.role}: ${m.content}`).join('\n')

    console.log('🔍 Analyzing conversation for scheduling information...')

    // EVITAR LOOP: se estamos aguardando confirmação de cancelamento, não extrair/agregar nada
    if (conversation.stage === 'awaiting_cancellation_confirmation') {
      console.log('⏭️ Skipping extraction during cancellation confirmation stage')
      return
    }

    // NOVA ABORDAGEM: Usar APENAS IA para extrair dados de agendamento
    const extractedInfo = await this.extractSchedulingInfoWithAI(conversationText, trinksApiCalls, conversation)

    // VERIFICAR SE É CANCELAMENTO
    if (extractedInfo && extractedInfo.intent === 'cancel') {
      console.log('🚫 Intent de CANCELAMENTO detectado - processando...')
      await this.processCancellationRequest(conversation, extractedInfo, trinksApiCalls)
      return // Exit early for cancellation
    }

    let targetDate = null
    let serviceId = null
    let professionalName = null

    if (extractedInfo) {
      targetDate = extractedInfo.date
      serviceId = extractedInfo.service
      professionalName = extractedInfo.professional

      console.log('🤖 IA extraiu:', extractedInfo)

      // NOVA FUNCIONALIDADE: Detectar intenção de cancelamento
      if (extractedInfo.intent === 'cancel') {
        console.log('🚫 Intenção de cancelamento detectada!')

        // CORREÇÃO: Armazenar dados de cancelamento preservando dados anteriores
        if (!conversation.cancellationData) {
          conversation.cancellationData = {}
        }
        Object.assign(conversation.cancellationData, {
          intent: 'cancel',
          professional: extractedInfo.professional,
          date: extractedInfo.date,
          service: extractedInfo.service,
          time: extractedInfo.time,
          extractedAt: new Date().toISOString()
        })

        // Não atualizar schedulingData para cancelamento
        return
      }

      // Atualizar dados estruturados de agendamento (apenas para agendamentos)
      await this.updateSchedulingData(conversation, extractedInfo, trinksApiCalls)
    }

    console.log(`📋 Extracted info - Date: ${targetDate}, Service: ${serviceId}, Professional: ${professionalName}`)

    // NOVA LÓGICA: Detectar intenção e controlar busca de disponibilidade
    const intentionType = this.detectProfessionalIntention(conversationText, professionalName)
    console.log(`🎯 Intenção detectada: ${intentionType} para profissional: ${professionalName}`)

    // Log detalhado da extração
    this.logConversationState(conversation, 'AFTER_EXTRACTION', {
      extractedData: { targetDate, serviceId, professionalName },
      intentionType
    })

    // ⚡ REGRA INTELIGENTE: Buscar disponibilidade quando há intenção OU dados que sugerem agendamento
    const shouldSearch = (extractedInfo && extractedInfo.intent === 'schedule') ||
                        (targetDate && (professionalName || serviceId)) // Data + (profissional OU serviço)

    if (shouldSearch && (targetDate && (professionalName || serviceId))) {
      const intentReason = extractedInfo?.intent === 'schedule' ? 'Intent Schedule' : 'Data+Serviço detectado'
      console.log(`🎯 AGENDAMENTO detectado (${intentReason}) - buscando disponibilidade`)
      console.log(`   📅 Data: ${targetDate}`)
      console.log(`   👨‍💼 Profissional: ${professionalName}`)
      console.log(`   💼 Serviço ID: ${serviceId || 'N/A'}`)

      if (professionalName) {
        // Com profissional específico
        const shouldFetch = this.shouldFetchAvailability(conversation, targetDate, professionalName)

        if (shouldFetch || intentionType === 'selection') {
          console.log('🔄 Buscando slots específicos para agendamento')
          await this.fetchSpecificProfessionalSlots(conversation, targetDate, professionalName, serviceId, trinksApiCalls)
        } else {
          console.log('♻️ Usando dados existentes de disponibilidade')
        }
      } else if (targetDate && serviceId) {
        // Com data + serviço, sem profissional específico
        console.log('🔄 Buscando disponibilidade geral (sem profissional específico)')
        await this.fetchAndSetAvailabilityContext(conversation, targetDate, serviceId, null, trinksApiCalls)
      }

      // Log do resultado
      const afterContext = conversation.availabilityContext
      if (afterContext && afterContext.availableSlots && afterContext.availableSlots.length > 0) {
        console.log(`✅ Disponibilidade encontrada: ${afterContext.totalSlots || 0} slots`)
        console.log(`   ⏰ Horários: ${afterContext.availableSlots.join(', ')}`)
      } else if (afterContext) {
        console.log('⚠️ Nenhuma disponibilidade encontrada para os critérios')
      } else {
        console.log('❌ Erro: Contexto de disponibilidade não foi criado')
      }
    } else if (targetDate || professionalName) {
      console.log('ℹ️ Dados parciais detectados sem intenção clara de agendamento')
      console.log(`   Intent: ${extractedInfo?.intent || 'N/A'}`)
      console.log(`   Data: ${targetDate || 'N/A'}`)
      console.log(`   Profissional: ${professionalName || 'N/A'}`)
    } else {
      console.log('ℹ️ Nenhuma intenção de agendamento detectada')
    }
  }

  /**
   * Log detalhado do estado da conversa para debugging
   * Especialmente útil para rastrear mudanças de profissional
   */
  logConversationState (conversation, phase, additionalData = {}) {
    const context = conversation?.context || {}
    const availabilityContext = conversation?.availabilityContext || {}

    console.log(`\n🔍 ==[CONVERSATION DEBUG - ${phase}]==`)
    console.log(`📞 Customer Phone: ${additionalData.customerPhone || 'N/A'}`)
    console.log(`💬 Messages Count: ${conversation?.messages?.length || 0}`)
    console.log(`🎭 Current Stage: ${conversation?.stage || 'N/A'}`)

    // Log do contexto persistente de profissional
    console.log('\n👨‍💼 PROFESSIONAL CONTEXT:')
    console.log(`   Selected Professional: ${context.selectedProfessional || 'None'}`)
    console.log(`   Professional ID: ${context.selectedProfessionalId || 'None'}`)
    console.log(`   Selected At: ${context.professionalSelectedAt || 'Never'}`)
    console.log(`   Last Update: ${context.lastProfessionalUpdate || 'Never'}`)

    if (context.previousProfessionals?.length > 0) {
      console.log('   Previous Professionals:')
      context.previousProfessionals.forEach((prev, i) => {
        console.log(`     ${i + 1}. ${prev.name} (changed at: ${prev.changedAt})`)
      })
    }

    // Log do contexto de disponibilidade
    console.log('\n📅 AVAILABILITY CONTEXT:')
    console.log(`   Date: ${availabilityContext.date || 'None'}`)
    console.log(`   Professional: ${availabilityContext.professionalName || 'None'}`)
    console.log(`   Has Availability: ${availabilityContext.hasAvailability || false}`)
    console.log(`   Sent to AI: ${availabilityContext.sentToAI || false}`)
    console.log(`   Context Type: ${availabilityContext.contextType || 'N/A'}`)
    console.log(`   From Cache: ${availabilityContext.fromCache || false}`)
    console.log(`   Slots Count: ${availabilityContext.totalSlots || 0}`)

    // Log de dados adicionais baseado na fase
    if (additionalData.newMessage) {
      console.log(`\n📝 NEW MESSAGE: "${additionalData.newMessage.substring(0, 100)}${additionalData.newMessage.length > 100 ? '...' : ''}"`)
    }

    if (additionalData.aiResponse) {
      const responseStr = typeof additionalData.aiResponse === 'string' ? additionalData.aiResponse : String(additionalData.aiResponse)
      console.log(`\n🤖 AI RESPONSE: "${responseStr.substring(0, 100)}${responseStr.length > 100 ? '...' : ''}"`)
    }

    if (additionalData.intentionType) {
      console.log(`\n🎯 INTENTION DETECTED: ${additionalData.intentionType}`)
    }

    if (additionalData.extractedData) {
      const extracted = additionalData.extractedData
      console.log('\n📊 EXTRACTED DATA:')
      console.log(`   Target Date: ${extracted.targetDate || 'None'}`)
      console.log(`   Service ID: ${extracted.serviceId || 'None'}`)
      console.log(`   Professional Name: ${extracted.professionalName || 'None'}`)
    }

    console.log(`🔍 ==[END DEBUG - ${phase}]==\n`)
  }

  /**
   * Detecta a intenção do cliente em relação ao profissional mencionado
   * Retorna: 'selection', 'inquiry', 'mention'
   */
  detectProfessionalIntention (conversationText, professionalName) {
    if (!professionalName || !conversationText) {
      return 'mention'
    }

    const lowerText = conversationText.toLowerCase()
    const lowerProfessional = professionalName.toLowerCase()

    // Padrões de SELEÇÃO definitiva (mudar contexto)
    const selectionPatterns = [
      `quero com ${lowerProfessional}`,
      `prefiro ${lowerProfessional}`,
      `pode ser com ${lowerProfessional}`,
      `marca com ${lowerProfessional}`,
      `agendar com ${lowerProfessional}`,
      `${lowerProfessional} mesmo`,
      `escolho ${lowerProfessional}`,
      `vou com ${lowerProfessional}`
    ]

    // Padrões de PERGUNTA/CONSULTA (não muda contexto)
    const inquiryPatterns = [
      `${lowerProfessional} atende`,
      `${lowerProfessional} trabalha`,
      `${lowerProfessional} faz`,
      `${lowerProfessional} tem horário`,
      `${lowerProfessional} está`,
      `${lowerProfessional} também`,
      `e ${lowerProfessional}`,
      `qual ${lowerProfessional}`
    ]

    // Verifica seleção primeiro (mais específico)
    for (const pattern of selectionPatterns) {
      if (lowerText.includes(pattern)) {
        console.log(`✅ Seleção detectada: "${pattern}"`)
        return 'selection'
      }
    }

    // Verifica consulta
    for (const pattern of inquiryPatterns) {
      if (lowerText.includes(pattern)) {
        console.log(`❓ Consulta detectada: "${pattern}"`)
        return 'inquiry'
      }
    }

    // Se apenas menciona o nome
    if (lowerText.includes(lowerProfessional)) {
      console.log('📝 Menção simples detectada')
      return 'mention'
    }

    return 'mention'
  }

  /**
   * Nova função: Buscar slots específicos de um profissional
   * Usa o endpoint correto da API Trinks com profissionalId
   */
  async fetchSpecificProfessionalSlots (conversation, targetDate, professionalName, serviceId, trinksApiCalls) {
    console.log(`🎯 Buscando slots específicos - Data: ${targetDate}, Profissional: ${professionalName}, Serviço: ${serviceId}`)

    try {
      const trinksService = require('./trinks')
      const availabilityCache = require('../utils/availabilityCache')

      // PASSO 0: Verificar cache primeiro (incluindo filtro de serviço)
      const cachedData = availabilityCache.get(professionalName, targetDate, serviceId)
      if (cachedData) {
        console.log(`♻️ Usando dados do cache para ${professionalName} em ${targetDate}${serviceId ? ` [Serviço: ${serviceId}]` : ''}`)

        // Atualizar contexto persistente mesmo usando cache
        this.updateProfessionalContext(conversation, professionalName, cachedData.professionalId)

        // Definir contexto de disponibilidade do cache
        conversation.availabilityContext = {
          ...cachedData,
          sentToAI: false, // Resetar para permitir reenvio se necessário
          contextChangedAt: new Date().toISOString(),
          fromCache: true
        }

        return
      }

      if (!targetDate) {
        // Use tomorrow as default if no date specified
        const tomorrow = new Date(dateUtils.getCurrentDateBR())
        tomorrow.setDate(tomorrow.getDate() + 1)
        targetDate = dateUtils.getDateStringBR(tomorrow)
        console.log(`📅 Using default date: ${targetDate}`)
      }

      // PASSO 1: Buscar ID do profissional pelo nome via API
      const startTimeGetId = Date.now()
      const professionalId = await trinksService.getProfessionalIdByName(professionalName)
      const endTimeGetId = Date.now()

      // Registrar busca do ID do profissional
      trinksApiCalls.push({
        endpoint: '/v1/profissionais',
        method: 'GET',
        purpose: `Buscar ID do profissional ${professionalName}`,
        responseTime: endTimeGetId - startTimeGetId,
        success: !!professionalId,
        resultCount: professionalId ? 1 : 0,
        startTime: new Date(startTimeGetId).toISOString(),
        endTime: new Date(endTimeGetId).toISOString(),
        request: { professionalName },
        response: {
          status: professionalId ? 200 : 404,
          data: professionalId ? `ID encontrado: ${professionalId}` : 'Profissional não encontrado'
        }
      })

      if (!professionalId) {
        console.log(`❌ Profissional ${professionalName} não encontrado na API`)
        conversation.availabilityContext = {
          date: targetDate,
          error: true,
          errorMessage: `Profissional ${professionalName} não encontrado`
        }
        return
      }

      // PASSO 2: Buscar slots específicos do profissional
      const startTimeSlots = Date.now()
      const slotsResult = await trinksService.getProfessionalSlots(targetDate, professionalId, serviceId)
      const endTimeSlots = Date.now()

      // Registrar busca dos slots
      trinksApiCalls.push({
        endpoint: `/v1/agendamentos/profissionais/${targetDate}`,
        method: 'GET',
        purpose: `Buscar slots do profissional ${professionalName} (ID: ${professionalId}) para ${targetDate}`,
        responseTime: endTimeSlots - startTimeSlots,
        success: slotsResult.hasAvailability,
        resultCount: slotsResult.totalSlots,
        startTime: new Date(startTimeSlots).toISOString(),
        endTime: new Date(endTimeSlots).toISOString(),
        request: {
          date: targetDate,
          professionalId,
          serviceId: serviceId || null
        },
        response: {
          status: slotsResult.hasAvailability ? 200 : 404,
          data: slotsResult
        }
      })

      // PASSO 3: Atualizar contexto persistente de profissional
      this.updateProfessionalContext(conversation, professionalName, professionalId)

      // PASSO 4: Aplicar filtro temporal contextual se necessário
      let filteredSlots = slotsResult.slots || []
      let temporalFilterApplied = false

      // Verificar se existe contexto temporal ("depois" de agendamento existente)
      if (conversation.futureAppointments && conversation.futureAppointments.length > 0) {
        // Procurar agendamento na mesma data para usar como referência
        const referenceAppointment = conversation.futureAppointments.find(apt => apt.date === targetDate)

        if (referenceAppointment) {
          console.log(`🕐 FILTRO TEMPORAL: Filtrando slots após ${referenceAppointment.time} (agendamento existente)`)

          const referenceTime = referenceAppointment.time // formato "HH:MM"
          const [refHour, refMinute] = referenceTime.split(':').map(Number)
          const referenceMinutes = refHour * 60 + refMinute

          // Filtrar slots para mostrar apenas os posteriores ao agendamento de referência
          filteredSlots = (slotsResult.slots || []).filter(slot => {
            const [slotHour, slotMinute] = slot.split(':').map(Number)
            const slotMinutes = slotHour * 60 + slotMinute
            return slotMinutes > referenceMinutes
          })

          temporalFilterApplied = true
          console.log(`🎯 Filtrado: ${slotsResult.slots?.length || 0} → ${filteredSlots.length} slots (após ${referenceTime})`)
        }
      }

      // PASSO 5: Definir contexto de disponibilidade específico
      const availabilityContext = {
        date: targetDate,
        professionalName,
        professionalId,
        specificProfessional: true, // Flag importante para o prompt
        availableSlots: filteredSlots,
        totalSlots: filteredSlots.length,
        hasAvailability: filteredSlots.length > 0,
        error: false,
        serviceId,
        // Metadados do filtro temporal
        temporalFilterApplied,
        originalSlotsCount: slotsResult.totalSlots,
        // Formato amigável para o prompt
        formattedSlots: filteredSlots.length > 0 ? filteredSlots.join(', ') : 'Nenhum horário disponível após o agendamento existente',
        // Controle de envio para IA
        sentToAI: false,
        contextChangedAt: new Date().toISOString(),
        fromCache: false
      }

      // PASSO 6: Salvar no cache para reutilização (incluindo filtro de serviço)
      availabilityCache.set(professionalName, targetDate, availabilityContext, serviceId)

      // Definir no contexto da conversa
      conversation.availabilityContext = availabilityContext

      console.log(`✅ Slots específicos definidos para ${professionalName}: ${filteredSlots.length}/${slotsResult.totalSlots} horários${temporalFilterApplied ? ' (filtrado temporalmente)' : ''}`)
    } catch (error) {
      console.error('❌ Error fetching specific professional slots:', error.message)
      conversation.availabilityContext = {
        date: targetDate,
        professionalName,
        specificProfessional: true,
        error: true,
        errorMessage: error.message
      }
    }
  }

  /**
   * Extrai histórico das mensagens para contexto conversacional
   * Retorna últimas 10 mensagens formatadas de forma legível
   */
  getConversationHistory (conversation, maxMessages = 10) {
    if (!conversation?.messages || conversation.messages.length === 0) {
      return 'Primeira mensagem da conversa'
    }

    // Pegar últimas mensagens (máximo 10)
    const recentMessages = conversation.messages.slice(-maxMessages)

    // Formatar mensagens para contexto legível
    const formattedMessages = recentMessages.map((msg, index) => {
      const role = msg.role === 'user' ? 'Cliente' : 'Assistente'
      const content = msg.content.substring(0, 150) // Limitar tamanho
      return `${role}: ${content}`
    }).join('\n')

    return formattedMessages || 'Conversa iniciando'
  }

  /**
   * Atualiza o contexto persistente de profissional
   * Controla mudanças de contexto e histórico
   */
  updateProfessionalContext (conversation, newProfessionalName, newProfessionalId) {
    // Inicializa contexto se não existir
    if (!conversation.context) {
      conversation.context = {}
    }

    const currentProfessional = conversation.context.selectedProfessional
    const currentProfessionalId = conversation.context.selectedProfessionalId

    // Se é a mesma pessoa, apenas atualiza timestamp
    if (currentProfessional === newProfessionalName && currentProfessionalId === newProfessionalId) {
      console.log(`📌 Mantendo contexto do profissional: ${newProfessionalName}`)
      conversation.context.lastProfessionalUpdate = new Date().toISOString()
      return false // Sem mudança
    }

    // Mudança detectada - salva o anterior no histórico
    if (currentProfessional && currentProfessional !== newProfessionalName) {
      console.log(`🔄 Mudança de profissional detectada: ${currentProfessional} → ${newProfessionalName}`)

      if (!conversation.context.previousProfessionals) {
        conversation.context.previousProfessionals = []
      }

      conversation.context.previousProfessionals.push({
        name: currentProfessional,
        id: currentProfessionalId,
        changedAt: new Date().toISOString()
      })
    }

    // Atualiza para o novo profissional
    conversation.context.selectedProfessional = newProfessionalName
    conversation.context.selectedProfessionalId = newProfessionalId
    conversation.context.professionalSelectedAt = new Date().toISOString()
    conversation.context.lastProfessionalUpdate = new Date().toISOString()

    console.log(`✅ Contexto de profissional atualizado para: ${newProfessionalName} (ID: ${newProfessionalId})`)
    return true // Houve mudança
  }

  /**
   * Atualiza os dados estruturados de agendamento
   * CORRIGIDO: Sempre agrega dados, preservando informações anteriores
   */
  async updateSchedulingData (conversation, extractedInfo, trinksApiCalls = []) {
    if (!extractedInfo) return

    // CORREÇÃO: Garantir que schedulingData existe e preservar dados anteriores
    if (!conversation.schedulingData) {
      conversation.schedulingData = {
        professional: { id: null, name: null },
        service: { id: null, name: null, price: null },
        date: null,
        time: null,
        extractedAt: null,
        source: 'ai',
        isComplete: false
      }
    }

    const schedulingData = conversation.schedulingData
    const trinksService = require('./trinks')
    let changed = false

    console.log('📋 Atualizando dados estruturados de agendamento...')
    console.log('   📌 Estado anterior:', {
      professional: schedulingData.professional?.name,
      service: schedulingData.service?.name,
      date: schedulingData.date,
      time: schedulingData.time
    })

    // Atualizar profissional (só se novo valor foi fornecido)
    if (extractedInfo.professional && extractedInfo.professional !== schedulingData.professional.name) {
      const professionalId = await trinksService.getProfessionalIdByName(extractedInfo.professional)
      if (professionalId) {
        schedulingData.professional.id = professionalId
        schedulingData.professional.name = extractedInfo.professional
        changed = true
        console.log(`✅ Profissional atualizado: ${extractedInfo.professional} (ID: ${professionalId})`)
      }
    }

    // Atualizar serviço (só se novo valor foi fornecido)
    if (extractedInfo.service && extractedInfo.service !== schedulingData.service.name) {
      const serviceData = await trinksService.getServiceIdByName(extractedInfo.service, trinksApiCalls)
      if (serviceData) {
        schedulingData.service.id = serviceData.id
        schedulingData.service.name = serviceData.name
        schedulingData.service.price = serviceData.price
        changed = true
        console.log(`✅ Serviço atualizado: ${serviceData.name} (ID: ${serviceData.id})`)
      }
    }

    // Atualizar data (só se novo valor foi fornecido)
    if (extractedInfo.date && extractedInfo.date !== schedulingData.date) {
      schedulingData.date = extractedInfo.date
      changed = true
      console.log(`✅ Data atualizada: ${extractedInfo.date}`)
    }

    // Atualizar horário (só se novo valor foi fornecido)
    if (extractedInfo.time && extractedInfo.time !== schedulingData.time) {
      schedulingData.time = extractedInfo.time
      changed = true
      console.log(`✅ Horário atualizado: ${extractedInfo.time}`)
    }

    if (changed) {
      schedulingData.extractedAt = new Date().toISOString()
      schedulingData.source = 'ai'

      // Verificar se dados estão completos
      schedulingData.isComplete = !!(
        schedulingData.professional.id &&
        schedulingData.service.id &&
        schedulingData.date &&
        schedulingData.time
      )

      console.log(`📊 Dados de agendamento atualizados - Completo: ${schedulingData.isComplete}`)
      console.log(`   Profissional: ${schedulingData.professional.name} (${schedulingData.professional.id})`)
      console.log(`   Serviço: ${schedulingData.service.name} (${schedulingData.service.id})`)
      console.log(`   Data: ${schedulingData.date}`)
      console.log(`   Horário: ${schedulingData.time}`)
    }

    return changed
  }

  /**
   * Verifica se precisa buscar nova disponibilidade baseado no contexto
   */
  shouldFetchAvailability (conversation, targetDate, professionalName) {
    // Se não há contexto de disponibilidade, sempre buscar
    if (!conversation.availabilityContext) {
      console.log('📋 Sem contexto de disponibilidade - buscando...')
      return true
    }

    const current = conversation.availabilityContext

    // Se mudou a data, sempre buscar
    if (current.date !== targetDate) {
      console.log(`📅 Data mudou (${current.date} → ${targetDate}) - buscando nova disponibilidade`)
      return true
    }

    // Se mudou o profissional, sempre buscar
    if (current.professionalName !== professionalName) {
      console.log(`👨‍💼 Profissional mudou (${current.professionalName} → ${professionalName}) - buscando nova disponibilidade`)
      return true
    }

    // Se o cache expirou (5 minutos), buscar novamente
    if (current.contextChangedAt) {
      const cacheAge = Date.now() - new Date(current.contextChangedAt).getTime()
      const maxAge = 5 * 60 * 1000 // 5 minutos

      if (cacheAge > maxAge) {
        console.log(`⏰ Cache de disponibilidade expirado (${Math.floor(cacheAge / 1000)}s) - buscando novamente`)
        return true
      }
    }

    console.log(`♻️ Usando disponibilidade em cache para ${professionalName} em ${targetDate}`)
    return false
  }

  /**
   * Extrai informações de agendamento usando Claude AI
   * Compara com lista real de profissionais da API Trinks
   */
  async extractSchedulingInfoWithAI (conversationText, trinksApiCalls = [], conversation = null) {
    console.log('🤖 Iniciando extração via IA com contexto completo...')

    try {
      // Buscar lista de profissionais da API
      const professionals = await professionalsCache.getProfessionals(trinksApiCalls)

      if (!professionals || professionals.length === 0) {
        console.log('❌ Nenhum profissional disponível para comparação')
        return null
      }

      // Buscar lista de serviços da API (limitada aos mais comuns para performance)
      const services = await servicesCache.getServices(trinksApiCalls)

      if (!services || services.length === 0) {
        console.log('⚠️ Nenhum serviço disponível da API')
      }

      // Preparar listas para o prompt
      const professionalsList = professionals.map(p => ({
        name: p.nome || p.apelido,
        nickname: p.apelido || p.nome,
        id: p.id
      }))

      // Filtrar serviços relevantes com critério mais abrangente (otimização melhorada)
      const relevantServices = services
        ? services.filter(s => {
          const name = (s.nome || s.descricao || '').toLowerCase()
          return name.includes('barb') || name.includes('corte') || name.includes('escova') ||
               name.includes('hidratação') || name.includes('hidrata') || name.includes('manicure') ||
               name.includes('pedicure') || name.includes('massagem') || name.includes('coloração') ||
               name.includes('pintura') || name.includes('unha') || name.includes('mão') ||
               name.includes('pé') || name.includes('limpeza') || name.includes('depilação')
        }).slice(0, 30)
        : [] // Expandir para 30 serviços mais relevantes

      const servicesList = relevantServices.map(s => ({
        name: s.nome || s.descricao,
        id: s.id
      }))

      // 🗓️ CONTEXTO TEMPORAL E GEOGRÁFICO COMPLETO
      const dateUtils = require('../utils/dateUtils')
      const currentDate = dateUtils.getCurrentDateBR()
      const today = dateUtils.formatDateBR(currentDate)
      const todayISO = dateUtils.getDateStringBR(currentDate)

      // Período do dia e contexto cultural
      const currentHour = currentDate.getHours()
      let periodOfDay = ''
      if (currentHour >= 6 && currentHour < 12) {
        periodOfDay = 'manhã'
      } else if (currentHour >= 12 && currentHour < 18) {
        periodOfDay = 'tarde'
      } else if (currentHour >= 18 && currentHour < 22) {
        periodOfDay = 'início da noite'
      } else {
        periodOfDay = 'noite'
      }

      const currentTime = dateUtils.formatDateTimeBR(currentDate).split(' ')[1]

      // 🗓️ LOG DE DEBUG para verificar data sendo passada à IA
      console.log('🗓️ AI Service: Contexto temporal/geográfico para prompt:')
      console.log(`   Data formatada (pt-BR): ${today}`)
      console.log(`   Data ISO: ${todayISO}`)
      console.log(`   Horário: ${currentTime} (${periodOfDay})`)
      console.log(`   Timestamp atual: ${currentDate.toISOString()}`)

      // Preparar contexto de agendamentos futuros e schedulingData existente
      let futureAppointmentsContext = ''
      let existingSchedulingData = ''

      if (conversation) {
        // Incluir agendamentos futuros se disponíveis (ENHANCED com detalhes temporais)
        if (conversation.futureAppointments && conversation.futureAppointments.length > 0) {
          futureAppointmentsContext = '\n**AGENDAMENTOS FUTUROS DO CLIENTE:**\n'
          conversation.futureAppointments.forEach(apt => {
            const aptDate = apt.date
            const aptTime = apt.time
            const serviceName = apt.serviceName || apt.service
            const professionalName = apt.professionalName || apt.professional

            // Calcular contexto temporal
            const aptDateTime = new Date(`${aptDate}T${aptTime}`)
            const now = new Date()
            const diffHours = (aptDateTime - now) / (1000 * 60 * 60)

            let temporalContext = ''
            if (diffHours < 24 && diffHours > 0) {
              temporalContext = ' (hoje)'
            } else if (diffHours < 48 && diffHours > 24) {
              temporalContext = ' (amanhã)'
            }

            futureAppointmentsContext += `- ${serviceName} com ${professionalName} em ${aptDate} às ${aptTime}${temporalContext}\n`
            futureAppointmentsContext += `  → REFERÊNCIA TEMPORAL: Se cliente menciona "depois dele/dela", significa APÓS ${aptTime} de ${aptDate}\n`
          })
          futureAppointmentsContext += '\n**IMPORTANTE**: Use estes agendamentos como referência para "depois", "após", "na sequência".\n'
        }

        // Incluir schedulingData existente se disponível
        if (conversation.schedulingData && !conversation.schedulingData.isComplete) {
          existingSchedulingData = '\n**DADOS JÁ COLETADOS NESTA CONVERSA:**\n'
          if (conversation.schedulingData.service && conversation.schedulingData.service.name) {
            existingSchedulingData += `- Serviço: ${conversation.schedulingData.service.name}\n`
          }
          if (conversation.schedulingData.professional && conversation.schedulingData.professional.name) {
            existingSchedulingData += `- Profissional: ${conversation.schedulingData.professional.name}\n`
          }
          if (conversation.schedulingData.date) {
            existingSchedulingData += `- Data: ${conversation.schedulingData.date}\n`
          }
          if (conversation.schedulingData.time) {
            existingSchedulingData += `- Horário: ${conversation.schedulingData.time}\n`
          }
          existingSchedulingData += '\n'
        }
      }

      const extractionPrompt = `
Você é um assistente especializado em extrair informações de agendamento e cancelamento.

## 🌎 CONTEXTO TEMPORAL E GEOGRÁFICO
**LOCALIZAÇÃO:** Rio de Janeiro, Brasil (America/Sao_Paulo timezone)
**DATA ATUAL:** ${today} (${todayISO})
**HORÁRIO ATUAL:** ${currentTime} (${periodOfDay})
**CONTEXTO CULTURAL:** Salão de beleza brasileiro, clientes falam português carioca

${futureAppointmentsContext}${existingSchedulingData}

TAREFA: Analise a CONVERSA COMPLETA do cliente e extraia as informações de agendamento OU cancelamento.

**IMPORTANTE**: Esta é uma conversa contínua. Use TODAS as informações anteriores como contexto.
**REFERÊNCIA TEMPORAL**: Quando cliente fala "hoje", se refere a ${today}. "Amanhã" seria ${new Date(currentDate.getTime() + 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR')}.

CONVERSA COMPLETA: 
${conversationText}

PROFISSIONAIS DISPONÍVEIS:
${professionalsList.map(p => `- ${p.name}${p.nickname !== p.name ? ` (${p.nickname})` : ''}`).join('\n')}

SERVIÇOS DISPONÍVEIS:
${servicesList.length > 0 ? servicesList.map(s => `- ${s.name}`).join('\n') : '- Corte\n- Escova\n- Manicure\n- Pedicure\n- Barba\n- Hidratação'}

EXTRAIA E RETORNE EM JSON:
{
  "intent": "schedule" | "cancel" | "other",
  "professional": "nome_exato_do_profissional_da_lista_ou_null",
  "date": "YYYY-MM-DD_ou_null", 
  "service": "nome_exato_do_servico_da_lista_ou_null",
  "time": "HH:MM_ou_null"
}

REGRAS CRÍTICAS:
1. **CONTEXTO DE CONTINUIDADE**: Esta é uma conversa contínua. Use TODAS as informações anteriores:
   - Se já foi mencionado "massagem relaxante" e cliente diz "relaxante mesmo" = CONFIRMA o serviço
   - Se já foi perguntado sobre profissional e cliente escolheu = USE essa informação
   - Se cliente menciona "depois do meu outro serviço" = CONTEXTO TEMPORAL dos agendamentos futuros

2. **INTENÇÃO**: Analise cuidadosamente o contexto - SEJA MAIS INCLUSIVO para detectar agendamentos:
   - "schedule": QUALQUER menção de:
     * Agendamento direto: "quero agendar", "marcar", "agendar", "reservar"  
     * Interesse em horário: "gostaria de ver um horário", "quero ver", "pode ser"
     * Menciona serviço + tempo: "barba amanhã", "corte na segunda", "manicure às 15h"
     * Disponibilidade: "tem horário?", "está livre?", "pode fazer?"
     * Preferência: "prefere com a Ágata", "pode ser com", "quero com"
     * Confirmação/continuidade: "relaxante mesmo", "pode ser", "perfeito", "quero esse"
     * **REFERÊNCIAS TEMPORAIS RELATIVAS**: "depois dele", "após", "na sequência", "depois do meu outro serviço"
   - "cancel": Cancelamentos: "cancelar", "desmarcar", "não quero mais", "imprevisto"  
   - "other": APENAS saudações puras sem contexto de serviço/data OU desistências: "deixa para lá", "esquece", "não precisa mais"
   
2. **EXEMPLOS DE INTENÇÃO EXPANDIDOS**:
   - "Oi" / "Olá" / "Tudo bem?" → "other" (apenas saudação)
   - "Quero marcar barba amanhã" → "schedule"
   - "Gostaria de ver um horário para barba" → "schedule" 
   - "Pode ser com a Ágata amanhã?" → "schedule"
   - "Tem horário livre sábado?" → "schedule"
   - "Como funciona o salão?" → "other" (pergunta geral sem serviço)
   - "Cancelar agendamento" → "cancel"

3. **PRESERVAÇÃO DE DADOS**: Se já existem dados coletados, USE-OS como base:
   - Se serviço já foi identificado e cliente confirma ("relaxante mesmo") = mantenha o serviço existente
   - Se profissional já foi escolhido = mantenha o profissional existente
   - APENAS sobrescreva se cliente mencionar algo DIFERENTE explicitamente

4. **PROFISSIONAL**: Se mencionado, encontre na lista disponível a correspondência mais próxima (case-insensitive)
5. **SERVIÇO**: SEMPRE tente identificar o serviço mencionado:
   - "barba" → procure por serviços que contenham "BARBA" (ex: BARBA 15, BARBA 50)
   - "corte" → procure por serviços que contenham "CORTE" 
   - "manicure" → procure por serviços que contenham "MANICURE"
   - "relaxante" → procure por "massagem relaxante" ou similar
   - Use o nome EXATO da lista de serviços disponíveis
6. **DATA**: SEMPRE converta para formato ISO (YYYY-MM-DD):
   - "hoje" → ${todayISO}
   - "amanhã" → ${new Date(currentDate.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
   - "depois de amanhã" → ${new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
   - Use a data atual como referência para calcular
6. **HORÁRIO**: Sempre extraia menções de tempo (15h, 15:00, três da tarde, etc.) → formato HH:MM
7. **REFERÊNCIAS TEMPORAIS CRÍTICAS** - ANALISE O CONTEXTO DOS AGENDAMENTOS FUTUROS:
   - Se cliente diz "depois dele", "após", "depois do meu outro serviço" = SIGNIFICA após um agendamento existente
   - VERIFIQUE os agendamentos futuros acima para entender QUANDO é "depois"
   - Se agendamento futuro é amanhã às 16h, "depois dele" = amanhã após 16h (ou próximos dias)
   - Se agendamento futuro é hoje às 10h, "depois dele" = hoje após 10h
   - NUNCA ofereça horários ANTES do agendamento de referência
   - Se não há agendamentos futuros, "depois" pode ser qualquer horário
8. **DESISTÊNCIAS vs CANCELAMENTOS**:
   - "deixa para lá", "esquece", "não precisa mais" = "other" (desistindo de NOVA solicitação)
   - "cancelar", "desmarcar" + referência específica = "cancel" (cancelar agendamento EXISTENTE)
9. **CORRESPONDÊNCIA**: Use busca por similaridade - "agata" = "Ágata Cristina Ribeiro"
10. Retorne APENAS o JSON, nada mais

EXEMPLOS:
Mensagem: "Oi" ou "Olá" ou "Tudo bem?"
Resposta: {"intent":"other","professional":null,"date":null,"service":null,"time":null}

Mensagem: "quero marcar com a agata amanhã às 15h barba"
Resposta: {"intent":"schedule","professional":"Ágata Cristina Ribeiro","date":"${new Date(currentDate.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]}","service":"BARBA 15","time":"15:00"}

Mensagem: "Gostaria de ver um horário para depois de amanhã, barba 15"
Resposta: {"intent":"schedule","professional":null,"date":"${new Date(currentDate.getTime() + 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}","service":"BARBA 15","time":null}

Mensagem: "Pode ser com a Ágata mesmo, umas 16h, tem?"
Resposta: {"intent":"schedule","professional":"Ágata Cristina Ribeiro","date":null,"service":null,"time":"16:00"}

Mensagem: "Como funciona o agendamento?"
Resposta: {"intent":"other","professional":null,"date":null,"service":null,"time":null}

Mensagem: "quero cancelar meu agendamento de amanhã"
Resposta: {"intent":"cancel","professional":null,"date":"${new Date(currentDate.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]}","service":null,"time":null}

Mensagem: "queria então aproveitar que é rápido e marcar um novo agendamento de massagem para depois dele"
(contexto: cliente tem BARBA 15 amanhã às 16h)
Resposta: {"intent":"schedule","professional":null,"date":"${new Date(currentDate.getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0]}","service":"massagem","time":"17:00"}

Mensagem: "na verdade deixa para lá"
Resposta: {"intent":"other","professional":null,"date":null,"service":null,"time":null}`

      // Chamar Claude API
      const startTime = Date.now()
      const response = await this.callClaudeAPI(extractionPrompt)
      const endTime = Date.now()

      console.log(`⏱️ Extração IA levou ${endTime - startTime}ms`)

      if (!response || !response.content) {
        console.log('❌ Claude não retornou resposta para extração')
        return null
      }

      // Tentar parsear JSON da resposta
      let extractedInfo
      try {
        const cleanResponse = response.content.trim()
        const jsonMatch = cleanResponse.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          extractedInfo = JSON.parse(jsonMatch[0])
        } else {
          throw new Error('Resposta não contém JSON válido')
        }
      } catch (parseError) {
        console.log('❌ Erro ao parsear resposta da IA:', parseError.message)
        console.log('   Resposta recebida:', response.content)
        return null
      }

      // Validar e processar resposta
      if (extractedInfo) {
        console.log('✅ IA extraiu com sucesso:', extractedInfo)

        // Processar data relativa para absoluta se necessário
        if (extractedInfo.date && extractedInfo.date !== null) {
          if (['amanhã', 'hoje', 'segunda', 'terça', 'quarta', 'quinta', 'sexta', 'sábado', 'domingo'].includes(extractedInfo.date.toLowerCase())) {
            extractedInfo.date = this.convertRelativeDateToAbsolute(extractedInfo.date)
          }
        }

        return extractedInfo
      }

      return null
    } catch (error) {
      console.error('❌ Erro na extração via IA:', error.message)
      return null
    }
  }

  /**
   * Chama a API do Claude de forma simples para extração
   */
  async callClaudeAPI (prompt) {
    // Use the already initialized anthropic client
    if (!this.anthropic) {
      console.log('⚠️ Claude API não inicializado - usando modo mock')
      return null
    }

    try {
      const startTime = Date.now()
      // ✨ Interceptar chamada Claude com sistema de avaliação automática
      const claudeParams = {
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 500,
        temperature: 0.1,
        messages: [{
          role: 'user',
          content: prompt
        }]
      }
      
      const response = await this.evaluationLogger.interceptClaudeCall(
        claudeParams,
        (params) => this.anthropic.messages.create(params),
        {
          source: 'ai_service_simple_extraction',
          sessionId: messageLogger.sessionId || 'unknown-session',
          interactionType: 'simple_extraction'
        }
      )

      const duration = Date.now() - startTime
      const responseText = response.content[0]?.text || null

      // Log da chamada da IA
      messageLogger.logAIInteraction(
        'callClaudeAPI',
        prompt,
        responseText,
        duration,
        'claude-3-5-sonnet-20241022'
      )

      return {
        content: responseText
      }
    } catch (error) {
      console.error('❌ Erro na chamada Claude API:', error.message)

      // Log do erro
      messageLogger.logError(error, {
        operation: 'callClaudeAPI',
        promptLength: prompt?.length || 0
      })

      return null
    }
  }

  /**
   * Convert relative date expressions to absolute dates
   */
  convertRelativeDateToAbsolute (dateExpression) {
    // Usar dateUtils para garantir timezone brasileiro correto
    const today = dateUtils.getCurrentDateBR()

    switch (dateExpression.toLowerCase()) {
      case 'hoje':
        return dateUtils.getDateStringBR(today)
      case 'amanhã':
        const tomorrow = new Date(today)
        tomorrow.setDate(tomorrow.getDate() + 1)
        return dateUtils.getDateStringBR(tomorrow)
      case 'segunda':
        return this.getNextWeekday(today, 1)
      case 'terça':
        return this.getNextWeekday(today, 2)
      case 'quarta':
        return this.getNextWeekday(today, 3)
      case 'quinta':
        return this.getNextWeekday(today, 4)
      case 'sexta':
        return this.getNextWeekday(today, 5)
      case 'sábado':
        return this.getNextWeekday(today, 6)
      case 'domingo':
        return this.getNextWeekday(today, 0)
      default:
        return null
    }
  }

  /**
   * Get next occurrence of a specific weekday
   */
  getNextWeekday (date, targetDay) {
    const currentDay = date.getDay()
    let daysUntilTarget = targetDay - currentDay

    if (daysUntilTarget <= 0) {
      daysUntilTarget += 7
    }

    const targetDate = new Date(date)
    targetDate.setDate(date.getDate() + daysUntilTarget)
    return dateUtils.getDateStringBR(targetDate)
  }

  /**
   * Simple response helper used by the state machine (compat layer)
   * Returns a short greeting/answer using Claude with conversation history when available.
   * OBRIGATÓRIO: Sempre inclui histórico da conversa para contexto completo.
   */
  async generateSimpleResponse (prompt, conversationContext = null) {
    try {
      if (!this.anthropic) {
        const error = new Error('Claude API não disponível - sistema configurado para falhar sem IA')
        await errorLogger.logError(error, ERROR_CATEGORIES.IA_ERROR, {
          function: 'generateSimpleResponse',
          hasAnthropic: !!this.anthropic,
          prompt: prompt?.substring(0, 500),
          hasContext: !!conversationContext,
          contextLength: conversationContext?.length || 0
        })
        throw error
      }

      const requestData = {
        model: 'claude-sonnet-4-20250514',
        max_tokens: 300,
        temperature: 0.7
      }

      if (conversationContext && conversationContext.length > 0) {
        // Usar formato de conversa com histórico (OBRIGATÓRIO)
        console.log(`💬 generateSimpleResponse: Incluindo ${conversationContext.length} mensagens de histórico`)

        // Verificar se há mensagens válidas no contexto
        const validContext = conversationContext.filter(msg =>
          msg.content && msg.content.trim().length > 0
        )

        // Adicionar instrução de brevidade ao prompt
        const briefPrompt = `${prompt}\n\nIMPORTANTE: Responda com máximo 1-2 frases curtas, simulando conversa humana natural brasileira.`

        if (validContext.length > 0) {
          requestData.messages = validContext.concat([{
            role: 'user',
            content: briefPrompt
          }])
        } else {
          // Se não há contexto válido, usar modo sem histórico
          console.log('⚠️ generateSimpleResponse: Contexto vazio detectado, usando modo sem histórico')
          requestData.messages = [{
            role: 'user',
            content: briefPrompt
          }]
        }
      } else {
        // Sem histórico (situações específicas)
        console.log('⚠️ generateSimpleResponse: ATENÇÃO - Chamada sem histórico da conversa')
        requestData.messages = [{
          role: 'user',
          content: `${prompt}\n\nIMPORTANTE: Responda com máximo 1-2 frases curtas, simulando conversa humana natural brasileira.`
        }]
      }

      const startTime = Date.now()
      
      // ✨ Interceptar chamada Claude com sistema de avaliação automática
      const response = await this.evaluationLogger.interceptClaudeCall(
        requestData,
        (params) => this.anthropic.messages.create(params),
        {
          source: 'ai_service_generate_simple_response',
          sessionId: null, // generateSimpleResponse doesn't have sessionId parameter
          customerPhone: customerPhone,
          interactionType: 'simple_response',
          withHistory: withHistory
        }
      )
      
      const duration = Date.now() - startTime
      const content = response?.content?.[0]?.text?.trim()

      // Log da chamada da IA completa
      messageLogger.logAIInteraction(
        'generateSimpleResponse',
        prompt,
        content,
        duration,
        requestData.model || 'claude-sonnet-4-20250514'
      )

      if (!content) {
        const error = new Error('Claude retornou resposta vazia')
        await errorLogger.logError(error, ERROR_CATEGORIES.IA_ERROR, {
          function: 'generateSimpleResponse',
          prompt: prompt?.substring(0, 500),
          response,
          requestData: {
            model: requestData.model,
            messagesCount: requestData.messages?.length || 0
          }
        })
        throw error
      }

      return content
    } catch (error) {
      console.error('❌ AI simple response error:', error)

      // Log apenas se não foi já logado
      if (!error.message.includes('Claude API não disponível') && !error.message.includes('resposta vazia')) {
        await errorLogger.logError(error, ERROR_CATEGORIES.IA_ERROR, {
          function: 'generateSimpleResponse',
          prompt: prompt?.substring(0, 500),
          hasContext: !!conversationContext,
          contextLength: conversationContext?.length || 0,
          originalError: error.message
        })
      }

      throw error // Remover fallback - sistema deve falhar se IA não funcionar
    }
  }

  /**
   * Update runtime settings from /api/settings and reinitialize integrations
   * Maintains backward compatibility with existing routes.
   */
  updateSettings (newSettings = {}) {
    try {
      this.settings = { ...this.settings, ...newSettings }
      this.initializeAPI()

      // Update Trinks service configuration if provided
      if (
        Object.prototype.hasOwnProperty.call(newSettings, 'trinksEnabled') ||
        newSettings.trinksApiUrl ||
        newSettings.trinksApiKey ||
        newSettings.trinksEstabelecimentoId
      ) {
        const trinksService = require('./trinks')
        trinksService.updateConfig({
          enabled: newSettings.trinksEnabled,
          apiUrl: newSettings.trinksApiUrl,
          apiKey: newSettings.trinksApiKey,
          estabelecimentoId: newSettings.trinksEstabelecimentoId
        })
      }
    } catch (error) {
      console.error('❌ Error updating AI settings:', error)
    }
  }

  /**
   * Fetch availability and set in conversation context
   */
  async fetchAndSetAvailabilityContext (conversation, targetDate, serviceId, professionalName, trinksApiCalls) {
    console.log(`📅 Fetching availability - Date: ${targetDate}, Service: ${serviceId}, Professional: ${professionalName}`)

    try {
      const trinksService = require('./trinks')

      if (!targetDate) {
        // Use tomorrow as default if no date specified
        const tomorrow = new Date(dateUtils.getCurrentDateBR())
        tomorrow.setDate(tomorrow.getDate() + 1)
        targetDate = dateUtils.getDateStringBR(tomorrow)
        console.log(`📅 Using default date: ${targetDate}`)
      }

      // Get availability from Trinks API
      const startTime = Date.now()
      const availability = await trinksService.getProfessionalsAvailability(targetDate, serviceId)
      const endTime = Date.now()

      // Track API call with detailed info
      trinksApiCalls.push({
        endpoint: '/v1/agenda/disponibilidade',
        method: 'GET',
        purpose: `Buscar disponibilidade para ${targetDate}${serviceId ? ` (serviço: ${serviceId})` : ''}${professionalName ? ` (profissional: ${professionalName})` : ''}`,
        responseTime: endTime - startTime,
        success: !!(availability && availability.length > 0),
        resultCount: availability ? availability.length : 0,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
        request: {
          date: targetDate,
          serviceId: serviceId || null,
          professionalName: professionalName || null
        },
        response: {
          status: availability && availability.length > 0 ? 200 : 404,
          data: availability || []
        }
      })

      if (availability && availability.professionals && availability.professionals.length > 0) {
        // Transform professionals data to availability slots format
        const availabilitySlots = []

        availability.professionals.forEach(professional => {
          if (professional.horariosVagos && professional.horariosVagos.length > 0) {
            professional.horariosVagos.forEach(slot => {
              availabilitySlots.push({
                professional: {
                  id: professional.id,
                  nome: professional.nome
                },
                time: slot,
                date: targetDate,
                available: true
              })
            })
          }
        })

        // Filter by professional if specified
        let filteredAvailability = availabilitySlots
        if (professionalName) {
          filteredAvailability = availabilitySlots.filter(slot =>
            slot.professional &&
            slot.professional.nome.toLowerCase().includes(professionalName.toLowerCase())
          )
        }

        // Set availability context
        conversation.availabilityContext = {
          date: targetDate,
          availableSlots: filteredAvailability,
          professionalsCount: [...new Set(filteredAvailability.map(s => s.professional.nome))].length,
          totalSlots: filteredAvailability.length,
          error: false,
          noAvailability: filteredAvailability.length === 0
        }

        console.log(`✅ Availability fetched: ${filteredAvailability.length} slots for ${targetDate}`)
      } else {
        conversation.availabilityContext = {
          date: targetDate,
          availableSlots: [],
          professionalsCount: 0,
          error: false,
          noAvailability: true
        }
        console.log(`❌ No availability found for ${targetDate}`)
      }
    } catch (error) {
      console.error('❌ Error fetching availability:', error.message)
      conversation.availabilityContext = {
        date: targetDate,
        error: true,
        errorMessage: error.message
      }
    }
  }

  /**
   * Cria agendamento na API Trinks quando dados estão completos
   */
  async handleAppointmentCreation (conversation, customerData, trinksApiCalls, customerPhone) {
    console.log('🎯 Iniciando criação de agendamento...')

    const schedulingData = conversation.schedulingData

    try {
      // Validar se dados estão realmente completos
      if (!schedulingData || !schedulingData.isComplete) {
        console.log('❌ Dados de agendamento incompletos')
        return {
          success: false,
          error: 'Dados incompletos',
          shouldRetry: false
        }
      }

      // Validar dados obrigatórios
      if (!schedulingData.professional.id || !schedulingData.service.id ||
          !schedulingData.date || !schedulingData.time) {
        console.log('❌ Faltam dados obrigatórios para agendamento')
        return {
          success: false,
          error: 'Dados obrigatórios faltando',
          shouldRetry: false
        }
      }

      console.log('📋 Dados do agendamento validados:')
      console.log(`   👨‍💼 Profissional: ${schedulingData.professional.name} (ID: ${schedulingData.professional.id})`)
      console.log(`   💼 Serviço: ${schedulingData.service.name} (ID: ${schedulingData.service.id})`)
      console.log(`   📅 Data: ${schedulingData.date}`)
      console.log(`   ⏰ Horário: ${schedulingData.time}`)

      // Preparar dados para criação
      const appointmentData = {
        profissionalId: schedulingData.professional.id,
        servicoId: schedulingData.service.id,
        data: schedulingData.date,
        hora: schedulingData.time,
        observacoes: `Agendamento criado automaticamente via IA - ${customerData?.nome || 'Cliente'} (${customerPhone})`
      }

      console.log('🔄 Chamando Trinks API para criar agendamento...')
      const startTime = Date.now()

      // Chamar serviço Trinks para criar agendamento
      const result = await trinksService.createAppointmentWithCustomer(
        appointmentData,
        customerData
      )

      // CRITICAL: Invalidate customer cache after creating appointment
      if (result.success) {
        this.customerCache.delete(customerPhone)
        console.log(`🧹 Cache invalidated for ${customerPhone} after appointment creation`)
      }

      const endTime = Date.now()

      // Track API call
      trinksApiCalls.push({
        endpoint: '/v1/agendamentos',
        method: 'POST',
        purpose: 'Criar agendamento automático',
        responseTime: endTime - startTime,
        success: !!result.appointment,
        startTime: new Date(startTime).toISOString(),
        endTime: new Date(endTime).toISOString(),
        request: appointmentData,
        response: {
          status: result.appointment ? 200 : 500,
          data: result.appointment ? `Agendamento criado: #${result.appointment.id}` : 'Falha na criação'
        }
      })

      if (result.appointment && result.appointment.id) {
        console.log('✅ Agendamento criado com sucesso!')
        console.log(`🆔 ID do agendamento: ${result.appointment.id}`)

        // Marcar como processado para evitar recriação
        conversation.schedulingData.appointmentId = result.appointment.id
        conversation.schedulingData.appointmentCreated = true
        conversation.schedulingData.createdAt = new Date().toISOString()

        return {
          success: true,
          appointmentId: result.appointment.id,
          appointment: result.appointment,
          customerCreated: result.wasCustomerCreated || false
        }
      } else {
        console.error('❌ Falha na criação do agendamento - sem ID retornado')
        return {
          success: false,
          error: 'Agendamento não foi criado corretamente',
          shouldRetry: true
        }
      }
    } catch (error) {
      console.error('❌ Erro ao criar agendamento:', error.message)

      // Track failed API call
      trinksApiCalls.push({
        endpoint: '/v1/agendamentos',
        method: 'POST',
        purpose: 'Criar agendamento automático (ERRO)',
        responseTime: 0,
        success: false,
        error: error.message,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString()
      })

      // Determinar se deve tentar novamente baseado no tipo do erro
      const shouldRetry = error.message.includes('cliente não encontrado') ||
                         error.message.includes('horário indisponível') ||
                         error.message.includes('profissional não disponível')

      return {
        success: false,
        error: error.message,
        shouldRetry
      }
    }
  }

  /**
   * Handle appointment cancellation
   * Finds the customer's appointments and cancels the matching one
   */
  async handleCancellation (conversation, customerData, trinksApiCalls, customerPhone) {
    const trinksService = require('./trinks')
    const startTime = Date.now()

    try {
      console.log('🔍 Iniciando processo de cancelamento com confirmação...')

      // Buscar agendamentos futuros do cliente
      const futureAppointments = await trinksService.getFutureAppointments(customerPhone)

      // ETAPA 3: PROTEÇÃO CRÍTICA - Verificar se getFutureAppointments retornou algo
      if (!futureAppointments) {
        console.log('❌ ERRO CRÍTICO: getFutureAppointments retornou undefined/null')
        return {
          success: false,
          error: 'Erro interno: não foi possível buscar agendamentos',
          shouldRetry: true
        }
      }

      // ETAPA 2: DEBUG DETALHADO - Capturar estrutura completa do retorno
      console.log('🔍 DEBUG: Estrutura completa de futureAppointments:')
      console.log('  - success:', futureAppointments?.success)
      console.log('  - count:', futureAppointments?.count)
      console.log('  - appointments length:', futureAppointments?.appointments?.length)
      console.log('  - appointments:', JSON.stringify(futureAppointments?.appointments, null, 2))
      console.log('  - _debugInfo:', futureAppointments?._debugInfo)
      console.log('  - Objeto completo:', JSON.stringify(futureAppointments, null, 2))

      trinksApiCalls.push({
        endpoint: '/v1/agendamentos/cliente/futuros',
        method: 'GET',
        purpose: 'Buscar agendamentos futuros para cancelamento',
        responseTime: Date.now() - startTime,
        success: futureAppointments.success,
        resultCount: futureAppointments.count || 0,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        request: { customerPhone },
        response: {
          status: futureAppointments.success ? 200 : 404,
          data: `${futureAppointments.count || 0} agendamentos encontrados`
        }
      })

      if (!futureAppointments.success || futureAppointments.count === 0) {
        return {
          success: false,
          error: 'Nenhum agendamento futuro encontrado para cancelar',
          noAppointments: true
        }
      }

      // Identificar qual agendamento cancelar usando IA com contexto da conversa
      const cancellationData = conversation.cancellationData
      const identificationResult = await this.identifyAppointmentToCancel(
        futureAppointments.appointments,
        cancellationData,
        conversation
      )

      if (!identificationResult) {
        return {
          success: false,
          error: 'Não foi possível identificar qual agendamento cancelar',
          availableAppointments: futureAppointments.appointments.map(apt => ({
            id: apt.id,
            data: apt.dataHoraInicio,
            profissional: apt.profissionalNome,
            servico: apt.servicoNome
          }))
        }
      }

      const appointmentToCancel = identificationResult.appointment
      console.log(`🎯 Agendamento identificado para cancelamento: ${appointmentToCancel.id} (confiança: ${identificationResult.confidence})`)

      // NOVA LÓGICA: Solicitar confirmação ao invés de cancelar automaticamente
      const date = new Date(appointmentToCancel.dataHoraInicio)
      const dayOfWeek = date.toLocaleDateString('pt-BR', { weekday: 'long' })
      const formattedDate = date.toLocaleDateString('pt-BR')
      const formattedTime = date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })

      const appointmentInfo = `${appointmentToCancel.servicoNome} com ${appointmentToCancel.profissionalNome || 'profissional'} em ${dayOfWeek}, ${formattedDate} às ${formattedTime}`

      // Salvar dados do agendamento para confirmação posterior
      if (!conversation.cancellationData) {
        conversation.cancellationData = {}
      }
      conversation.cancellationData.pendingAppointment = appointmentToCancel
      conversation.cancellationData.appointmentInfo = appointmentInfo
      conversation.cancellationData.identificationResult = identificationResult
      conversation.cancellationData.awaitingConfirmation = true
      conversation.cancellationData.confirmationRequestedAt = new Date().toISOString()

      // Definir estado para aguardar confirmação (somente se não estiver já em confirmação)
      if (conversation.stage !== 'awaiting_cancellation_confirmation') {
        conversation.stage = 'awaiting_cancellation_confirmation'
      }

      // Gerar confirmação humanizada via cache
      const cancellationContext = {
        customerName: conversation.customerData?.name || 'Cliente',
        service: appointmentToCancel.servico?.nome || 'agendamento',
        professional: appointmentToCancel.profissional?.nome || 'profissional',
        timeContext: appointmentInfo || '',
        customerType: conversation.customerData?.isNewCustomer ? 'new' : 'returning'
      }

      const confirmationMessage = await this.responseCache.getOrGenerate(
        'cancellation_confirm',
        cancellationContext,
        {
          basePrompt: this.basePrompt,
          useTemplate: true,
          variables: {
            appointmentInfo
          },
          generator: async (processedTemplate) => {
            // Use processed template if available, otherwise fallback to legacy
            const prompt = processedTemplate
              ? processedTemplate.prompt
              : `${this.basePrompt}

Contexto: O cliente está tentando cancelar um agendamento.
Agendamento identificado: ${appointmentInfo}

Tarefa: Gere uma mensagem de confirmação natural e empática que:
1. Confirme que encontrou o agendamento específico
2. Pergunte se realmente deseja cancelar
3. Peça o motivo do cancelamento de forma educada
4. Use o tom da marca (brasileiro, caloroso, profissional mas descontraído)
5. Máximo 2 frases

IMPORTANTE: Seja direto mas empático. Use emojis moderadamente.`

            const maxTokens = processedTemplate ? processedTemplate.maxTokens : 150
            const temperature = processedTemplate ? processedTemplate.temperature : 0.3

            // ✨ Interceptar chamada Claude com sistema de avaliação automática
            const claudeParams = {
              model: this.modelName,
              max_tokens: maxTokens,
              messages: [{ role: 'user', content: prompt }],
              temperature
            }
            
            const response = await this.evaluationLogger.interceptClaudeCall(
              claudeParams,
              (params) => this.anthropic.messages.create(params),
              {
                source: 'ai_service_extract_cancellation_response',
                sessionId: messageLogger?.sessionId || 'unknown-session',
                interactionType: 'cancellation_extraction',
                maxTokens,
                temperature
              }
            )

            // Extract content with proper error handling
            if (!response.content || !response.content[0] || typeof response.content[0].text !== 'string') {
              console.error('❌ Invalid Claude API response structure in extractCancellationResponse:', JSON.stringify(response, null, 2))
              throw new Error('Invalid Claude API response structure: missing content[0].text')
            }

            return response.content[0].text.trim()
          }
        }
      )

      return {
        success: true,
        requiresConfirmation: true,
        appointmentToCancel,
        appointmentInfo,
        confirmationMessage,
        nextStage: 'awaiting_cancellation_confirmation'
      }
    } catch (error) {
      console.error('❌ Erro ao processar cancelamento:', error.message)

      // Track failed API call
      trinksApiCalls.push({
        endpoint: '/cancelamento',
        method: 'PROCESS',
        purpose: 'Processar cancelamento (ERRO)',
        responseTime: 0,
        success: false,
        error: error.message,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString()
      })

      return {
        success: false,
        error: error.message,
        shouldRetry: false
      }
    }
  }

  /**
   * Process cancellation confirmation after user responds
   * Executes actual cancellation or redirects based on user response
   */
  async processCancellationConfirmation (conversation, customerData, trinksApiCalls, customerPhone, customerResponse) {
    const trinksService = require('./trinks')
    const startTime = Date.now()

    try {
      console.log('🤖 Processando confirmação de cancelamento...', customerResponse)
      console.log('🔍 Estado atual da conversa:', {
        stage: conversation.stage,
        hasCancellationData: !!conversation.cancellationData,
        hasPendingAppointment: !!(conversation.cancellationData?.pendingAppointment),
        customerPhone
      })

      // Verificar se existe agendamento pendente para cancelamento
      if (!conversation.cancellationData || !conversation.cancellationData.pendingAppointment) {
        console.log('⚠️ Estado inconsistente: cliente estava aguardando confirmação mas dados foram perdidos')
        console.log('🔄 Tentando identificar agendamento automaticamente...')

        // Tentar identificar o agendamento para cancelar automaticamente
        const futureAppointments = await trinksService.getFutureAppointments(customerPhone)
        trinksApiCalls.push({
          method: 'GET',
          endpoint: '/v1/agendamentos/cliente',
          timestamp: new Date().toISOString(),
          purpose: 'Recovery fallback for cancellation'
        })
        // Estrutura correta: { success, appointments, count }
        if (futureAppointments?.success && futureAppointments.appointments?.length > 0) {
          const appointment = futureAppointments.appointments[0]
          console.log('🎯 Agendamento identificado automaticamente para cancelamento:', {
            id: appointment.id,
            servico: appointment.servico?.nome || appointment.servicoNome,
            profissional: appointment.profissional?.nome || appointment.profissionalNome,
            dataHora: appointment.dataHoraInicio || appointment.dataHora
          })

          // Configurar dados de cancelamento
          if (!conversation.cancellationData) {
            conversation.cancellationData = {}
          }
          conversation.cancellationData.pendingAppointment = appointment
          conversation.cancellationData.appointmentInfo = `${(appointment.servico?.nome || appointment.servicoNome || 'agendamento')} com ${(appointment.profissional?.nome || appointment.profissionalNome || 'profissional')}`
          conversation.cancellationData.awaitingConfirmation = true

          // Persistir no cache da conversa para evitar perda de estado
          this.conversationMemory.set(customerPhone, conversation)
        } else {
          console.log('❌ Nenhum agendamento futuro encontrado para cancelar')
          conversation.stage = 'completed'
          return {
            success: false,
            message: 'Desculpe, não encontrei nenhum agendamento futuro para cancelar.',
            nextStage: 'completed'
          }
        }
      }

      const appointmentToCancel = conversation.cancellationData.pendingAppointment
      const appointmentInfo = conversation.cancellationData.appointmentInfo

      console.log('📋 Dados do agendamento para cancelar:', {
        id: appointmentToCancel.id,
        info: appointmentInfo,
        dataHora: appointmentToCancel.dataHoraInicio || appointmentToCancel.dataHora
      })

      // Extrair confirmação e motivo da resposta do cliente usando IA
      const analysis = await this.extractCancellationResponse(customerResponse, appointmentInfo)

      console.log('📊 Análise da resposta de cancelamento:', analysis)

      // Processar diferentes tipos de resposta
      switch (analysis.confirmation) {
        case 'true':
          // Cliente confirmou o cancelamento - executar
          return await this.executeCancellation(appointmentToCancel, analysis.reason, trinksService, trinksApiCalls, conversation, customerPhone)

        case 'false':
          // Cliente não quer cancelar - manter agendamento
          conversation.stage = 'completed'
          conversation.cancellationData.awaitingConfirmation = false

          return {
            success: true,
            cancelled: false,
            message: `Perfeito! Seu agendamento de ${appointmentInfo} está mantido. Nos vemos lá! 😊`,
            nextStage: 'completed'
          }

        case 'rescheduling':
          // Cliente quer reagendar - redirecionar para fluxo de agendamento
          conversation.stage = 'scheduling'
          conversation.cancellationData.awaitingConfirmation = false

          return {
            success: true,
            intentChange: true,
            newIntent: 'scheduling',
            message: `Perfeito! Vamos reagendar então 😊\n\nSeu agendamento atual: ${appointmentInfo}\n\nPara qual dia e horário prefere remarcar?`,
            nextStage: 'scheduling',
            originalAppointment: appointmentToCancel
          }

        case 'keep_appointment':
          // Cliente quer manter agendamento
          conversation.stage = 'completed'
          conversation.cancellationData.awaitingConfirmation = false

          return {
            success: true,
            cancelled: false,
            message: `Entendido! Seu agendamento de ${appointmentInfo} continua confirmado. Te espero lá! 😊`,
            nextStage: 'completed'
          }

        case 'unclear':
        default:
          // Resposta ambígua - pedir esclarecimento
          if (!conversation.cancellationData.clarificationAttempts) {
            conversation.cancellationData.clarificationAttempts = 0
          }
          conversation.cancellationData.clarificationAttempts++

          if (conversation.cancellationData.clarificationAttempts >= 3) {
            // Máximo de tentativas atingido - escalação
            conversation.stage = 'completed'
            conversation.cancellationData.awaitingConfirmation = false

            return {
              success: false,
              escalation: true,
              message: 'Como não consegui entender sua intenção, vou transferir para um atendente humano te ajudar melhor. Aguarde um momento! 😊'
            }
          }

          // Tentar esclarecimento
          const clarificationMessages = [
            `Para confirmar: você QUER cancelar seu agendamento de ${appointmentInfo}? Por favor, responda sim ou não. 😊`,
            'Vejo que está em dúvida sobre o cancelamento. Suas opções são:\n• Cancelar o agendamento\n• Reagendar para outro horário\n• Manter como está\n\nQual prefere?',
            `Última tentativa: preciso de uma resposta clara sobre seu agendamento de ${appointmentInfo}. Cancelar, reagendar ou manter?`
          ]

          return {
            success: true,
            needsClarification: true,
            message: clarificationMessages[conversation.cancellationData.clarificationAttempts - 1],
            nextStage: 'awaiting_cancellation_confirmation',
            attemptsRemaining: 3 - conversation.cancellationData.clarificationAttempts
          }
      }
    } catch (error) {
      console.error('❌ Erro ao processar confirmação de cancelamento:', error.message)

      return {
        success: false,
        error: error.message,
        shouldRetry: true
      }
    }
  }

  /**
   * Execute the actual appointment cancellation
   */
  async executeCancellation (appointmentToCancel, reason, trinksService, trinksApiCalls, conversation, customerPhone) {
    const startTime = Date.now()

    try {
      console.log(`🚫 Executando cancelamento do agendamento ${appointmentToCancel.id}...`)

      // Preparar dados de cancelamento com motivo personalizado
      const cancelData = {
        quemCancelou: 1, // 1 = cliente
        motivo: reason && reason !== 'não informado' ? reason : 'Cancelado pelo cliente'
      }

      const cancelResult = await trinksService.cancelAppointment(appointmentToCancel.id, cancelData)

      // Track API call
      trinksApiCalls.push({
        endpoint: `/v1/agendamentos/${appointmentToCancel.id}/status/cancelado`,
        method: 'PATCH',
        purpose: 'Cancelar agendamento via IA com confirmação',
        responseTime: Date.now() - startTime,
        success: cancelResult.success,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        request: cancelData,
        response: {
          status: cancelResult.success ? 200 : 500,
          data: cancelResult.success ? `Agendamento cancelado: #${appointmentToCancel.id}` : 'Falha no cancelamento'
        }
      })

      if (cancelResult.success) {
        // CRITICAL: Invalidate customer cache after cancelling appointment
        this.customerCache.delete(customerPhone)
        console.log(`🧹 Cache invalidated for ${customerPhone} after appointment cancellation (confirmation flow)`)

        // Marcar como processado na conversa
        conversation.cancellationData.processed = true
        conversation.cancellationData.cancelledAt = new Date().toISOString()
        conversation.cancellationData.cancelledAppointmentId = appointmentToCancel.id
        conversation.cancellationData.awaitingConfirmation = false
        conversation.stage = 'completed'

        // Gerar mensagem de confirmação personalizada via cache humanizado
        const reasonMessage = reason && reason !== 'não informado' ? reason : ''
        const serviceName = appointmentToCancel.servico?.nome || appointmentToCancel.servicoNome || 'serviço'
        const profName = appointmentToCancel.profissional?.nome || appointmentToCancel.profissionalNome || 'profissional'

        // Contexto para mensagem de cancelamento executado
        const cancellationSuccessContext = {
          serviceName,
          professionalName: profName,
          appointmentId: appointmentToCancel.id,
          cancellationReason: reasonMessage,
          customerName: conversation.customerData?.name || 'Cliente',
          conversationHistory: this.getConversationHistory(conversation)
        }

        // Usar cache humanizado para gerar mensagem de cancelamento executado
        const conversationContext = this.buildConversationContext(conversation)
        const cancellationSuccessMessage = await this.responseCache.getOrGenerate(
          'cancellation_success',
          cancellationSuccessContext,
          {
            basePrompt: this.basePrompt,
            useTemplate: true,
            assistantName: this.settings?.assistantName || 'Assistente Virtual',
            generator: async (processedTemplate) => {
              if (processedTemplate) {
                return await this.generateSimpleResponse(
                  processedTemplate.prompt,
                  conversationContext
                )
              }

              if (!this.anthropic) {
                throw new Error('Claude API não disponível - sistema configurado para falhar sem IA')
              }

              return await this.generateSimpleResponse(
                `${this.basePrompt}\n\nAgendamento cancelado com sucesso! Gere uma mensagem de confirmação natural e calorosa.`,
                conversationContext
              )
            }
          }
        )

        // CORREÇÃO: Limpar dados de cancelamento após sucesso (manter histórico nas mensagens)
        conversation.cancellationData = null

        return {
          success: true,
          cancelled: true,
          cancelledAppointmentId: appointmentToCancel.id,
          cancelledAppointment: appointmentToCancel,
          cancellationReason: reason,
          message: cancellationSuccessMessage,
          nextStage: 'completed'
        }
      } else {
        return {
          success: false,
          error: cancelResult.error || 'Falha ao cancelar agendamento na API',
          shouldRetry: true
        }
      }
    } catch (error) {
      console.error('❌ Erro ao executar cancelamento:', error.message)

      return {
        success: false,
        error: error.message,
        shouldRetry: false
      }
    }
  }

  /**
   * Extract cancellation confirmation and reason from user response using AI
   * Handles multiple scenarios: clear confirmation, ambiguous responses, intent changes
   */
  async extractCancellationResponse (customerResponse, appointmentInfo) {
    console.log('🤖 Analisando resposta do cliente para cancelamento...', customerResponse)

    try {
      if (!this.anthropic) {
        throw new Error('Claude API não disponível - sistema configurado para falhar sem IA')
      }

      const prompt = `Analise esta resposta natural de um cliente brasileiro sobre cancelamento de agendamento.

CONTEXTO:
Cliente foi perguntado sobre cancelar agendamento de ${appointmentInfo} e o motivo.

RESPOSTA DO CLIENTE: "${customerResponse}"

ANÁLISE - Detecte respostas naturais como:

CONFIRMAÇÃO (retorne "confirmed"):
- "pode cancelar mesmo", "cancela aí", "não vou conseguir ir"
- "tive um imprevisto", "surgiu algo", "mudou minha agenda" 
- "não precisa mais", "não vai rolar", "não consigo mais"

RECUSA (retorne "denied"):
- "não, deixa", "quero manter", "vou sim", "mudei de ideia"
- "não precisa cancelar", "vai dar certo", "consigo ir"

REAGENDAMENTO (retourne "reschedule"):
- "quero remarcar", "outro dia", "pode ser amanhã?"
- "vamos reagendar", "prefiro outro horário"

MOTIVO: Extraia o motivo da resposta natural (imprevisto, trabalho, viagem, etc.)

JSON apenas:

{
  "confirmation": "true|false|unclear|rescheduling|keep_appointment",
  "reason": "motivo específico ou 'não informado' ou null",
  "newIntent": "scheduling|keep|inquiry|null",
  "needsClarification": "true se resposta ambígua",
  "confidence": 0.95
}`

      const startTime = Date.now()
      
      // ✨ Interceptar chamada Claude com sistema de avaliação automática
      const claudeParams = {
        model: 'claude-sonnet-4-20250514',
        max_tokens: 200,
        temperature: 0.1,
        messages: [{
          role: 'user',
          content: prompt
        }]
      }
      
      const response = await this.evaluationLogger.interceptClaudeCall(
        claudeParams,
        (params) => this.anthropic.messages.create(params),
        {
          source: 'ai_service_appointment_response_extraction',
          sessionId: messageLogger?.sessionId || 'unknown-session',
          interactionType: 'appointment_response_extraction',
          extractionType: 'cancellation_response'
        }
      )
      
      const duration = Date.now() - startTime

      const content = response.content[0]?.text

      // Log da chamada da IA
      messageLogger.logAIInteraction(
        'extractCancellationResponse',
        prompt,
        content,
        duration,
        'claude-sonnet-4-20250514'
      )

      if (!content) {
        throw new Error('Empty response from Claude API')
      }

      // Parse JSON response
      let jsonContent = content
      if (content.includes('```json')) {
        const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)
        if (jsonMatch) {
          jsonContent = jsonMatch[1]
        }
      }

      const analysis = JSON.parse(jsonContent)

      console.log('✅ Análise da resposta de cancelamento:', analysis)

      return {
        confirmation: analysis.confirmation,
        reason: analysis.reason,
        newIntent: analysis.newIntent,
        needsClarification: analysis.needsClarification || false,
        confidence: analysis.confidence || 0.8,
        method: 'ai',
        originalResponse: customerResponse
      }
    } catch (error) {
      console.error('❌ Erro ao analisar resposta de cancelamento:', error.message)
      throw error // Remover fallback - sistema deve falhar se IA não funcionar
    }
  }

  /**
   * Basic rule-based extraction when AI is not available
   */
  extractCancellationResponseBasic (response) {
    const text = (response || '').toLowerCase().trim()
    const has = (p) => text.includes(p)

    // 1) Recusas e manutenção SEMPRE primeiro
    const refusals = [
      'não cancelar', 'nao cancelar', 'não cancele', 'nao cancele',
      'manter', 'não quero', 'nao quero', 'deixa pra lá', 'deixa pra la',
      'prefiro manter', 'vou sim', 'consigo ir', 'mudei de ideia',
      'vai dar certo', 'não precisa cancelar', 'deixa', 'mantém'
    ]
    if (refusals.some(has) || /(\bnao|\bnão)\b.*cancelar/.test(text)) {
      return {
        confirmation: 'false',
        reason: null,
        newIntent: 'keep',
        needsClarification: false,
        confidence: 0.8,
        method: 'rules'
      }
    }

    // 2) Reagendamento explícito
    const reschedule = [
      'reagendar', 'remarcar', 'outro horário', 'outro horario',
      'mudar horário', 'mudar horario'
    ]
    if (reschedule.some(has)) {
      return {
        confirmation: 'rescheduling',
        reason: null,
        newIntent: 'scheduling',
        needsClarification: false,
        confidence: 0.8,
        method: 'rules'
      }
    }

    // 3) Confirmações naturais
    const confirmations = [
      'sim', 'confirmo', 'pode cancelar', 'quero cancelar', 'cancela aí',
      'não vou conseguir', 'não consigo', 'tive um imprevisto', 'surgiu algo',
      'mudou minha agenda', 'não precisa mais', 'não vai rolar',
      'cancelar mesmo', 'pode ser', 'com certeza', 'perfeito'
    ]

    // Detectar frases que implicam cancelamento
    const impliesCancel = has('imprevisto') || has('surgiu') || has('mudou') ||
                         has('não consegui') || has('não vai dar') || has('não vou') ||
                         text.includes('trabalho') && (has('chamou') || has('surgiu')) ||
                         text.includes('viagem') || text.includes('compromisso')

    if (confirmations.some(has) || impliesCancel) {
      // Tentar extrair motivo das frases naturais
      let reason = 'não informado'
      if (has('imprevisto')) reason = 'imprevisto'
      if (has('trabalho')) reason = 'trabalho'
      if (has('viagem')) reason = 'viagem'
      if (has('compromisso')) reason = 'compromisso'
      if (has('família') || has('familia')) reason = 'questão familiar'

      return {
        confirmation: 'true',
        reason,
        newIntent: null,
        needsClarification: false,
        confidence: 0.8,
        method: 'rules'
      }
    }

    // 4) Ambíguo
    return {
      confirmation: 'unclear',
      reason: null,
      newIntent: null,
      needsClarification: true,
      confidence: 0.3,
      method: 'rules'
    }
  }

  /**
   * Identify which appointment to cancel using AI analysis of conversation context
   */
  async identifyAppointmentToCancel (appointments, cancellationData, conversation) {
    if (!appointments || appointments.length === 0) {
      return null
    }

    // Se só tem um agendamento, cancela ele
    if (appointments.length === 1) {
      console.log('🎯 Apenas 1 agendamento futuro - cancelando automaticamente:', appointments[0].id)
      return {
        appointment: appointments[0],
        confidence: 1.0,
        reasoning: 'Único agendamento futuro disponível'
      }
    }

    console.log(`🤖 Identificando agendamento via IA entre ${appointments.length} opções...`)

    try {
      // Preparar contexto das conversas recentes (últimas 15 mensagens)
      const recentMessages = conversation.messages?.slice(-15) || []
      const conversationContext = recentMessages.map(msg =>
        `${msg.role === 'user' ? 'Cliente' : 'Atendente'}: ${msg.content}`
      ).join('\n')

      // Preparar lista de agendamentos futuros para a IA
      const appointmentsList = appointments.map((apt, index) => {
        const date = new Date(apt.dataHoraInicio)
        const dayOfWeek = date.toLocaleDateString('pt-BR', { weekday: 'long' })
        const formattedDate = date.toLocaleDateString('pt-BR')
        const formattedTime = date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })

        return {
          id: apt.id,
          index: index + 1,
          profissional: apt.profissionalNome || 'Não especificado',
          servico: apt.servicoNome,
          dataHora: `${dayOfWeek}, ${formattedDate} às ${formattedTime}`,
          valor: apt.valor
        }
      }).map(apt =>
        `${apt.index}. ID: ${apt.id} | ${apt.servico} com ${apt.profissional} | ${apt.dataHora} | R$ ${apt.valor}`
      ).join('\n')

      const prompt = `Você é um assistente especializado em identificar qual agendamento o cliente deseja cancelar.

CONTEXTO DA CONVERSA:
${conversationContext}

AGENDAMENTOS FUTUROS DISPONÍVEIS:
${appointmentsList}

OBJETIVO CRÍTICO:
Sua tarefa é identificar QUAL AGENDAMENTO o cliente deseja cancelar baseado em atributos específicos mencionados.
Você deve retornar o ID EXATO do agendamento que será usado para executar o cancelamento.

CRITÉRIOS DE IDENTIFICAÇÃO (em ordem de prioridade):
1. SERVIÇO mencionado pelo cliente (ex: "barba", "massagem", "corte", "unha")
2. PROFISSIONAL citado (ex: "com João", "Ágata", "Fernando")  
3. HORÁRIO específico (ex: "às 16h", "16:30", "de manhã")
4. DATA mencionada (ex: "amanhã", "sábado", "04/09")

IMPORTANTE: 
- NÃO use ordem cronológica ou temporal dos agendamentos ("primeiro", "último", "próximo")
- Identifique baseado APENAS nos atributos específicos mencionados pelo cliente
- Se cliente disse "barba", procure agendamento de BARBA
- Se disse "massagem", procure agendamento de MASSAGEM

SE NÃO CONSEGUIR IDENTIFICAR COM CERTEZA (confidence < 0.7):
- Liste todos os agendamentos de forma humanizada no campo "reasoning"
- Exemplo: "Você tem 2 agendamentos: BARBA 15 com João às 16:00 e Massagem Quick às 16:30. Qual deseja cancelar?"
- Formate de maneira natural e amigável para o cliente escolher

RESPOSTA OBRIGATÓRIA EM JSON:
{
  "appointmentId": "ID_DO_AGENDAMENTO",
  "confidence": 0.95,
  "reasoning": "Explicação detalhada da escolha OU lista humanizada para o cliente escolher"
}

IMPORTANTE: Responda APENAS o JSON, sem texto adicional.`

      const aiResponse = await this.callAnthropic([
        { role: 'user', content: prompt }
      ])

      console.log('🧠 Resposta da IA para identificação:', aiResponse)

      try {
        const result = JSON.parse(aiResponse)
        console.log('🎯 DEBUG: IA retornou appointmentId:', result.appointmentId)
        console.log('🎯 DEBUG: Confidence:', result.confidence)
        console.log('🎯 DEBUG: Reasoning:', result.reasoning)

        // Log de todos os IDs disponíveis para comparação
        console.log('📋 DEBUG: IDs disponíveis:', appointments.map(apt => `${apt.id}:${apt.servicoNome}`))

        // Validar se o ID existe nos agendamentos
        const selectedAppointment = appointments.find(apt => apt.id === result.appointmentId)
        console.log('✅ DEBUG: Agendamento selecionado:', selectedAppointment ? `${selectedAppointment.id}:${selectedAppointment.servicoNome}` : 'NULL')

        if (!selectedAppointment) {
          console.log('❌ IA retornou ID inválido:', result.appointmentId)
          return null
        }

        if (result.confidence < 0.7) {
          console.log(`⚠️ Confiança baixa (${result.confidence}) - requer confirmação manual`)
          return {
            appointment: selectedAppointment,
            confidence: result.confidence,
            reasoning: result.reasoning,
            requiresConfirmation: true
          }
        }

        console.log(`✅ Agendamento identificado via IA: ${result.appointmentId} (${result.confidence} confiança)`)
        return {
          appointment: selectedAppointment,
          confidence: result.confidence,
          reasoning: result.reasoning
        }
      } catch (parseError) {
        console.error('❌ Erro ao parsear resposta da IA:', parseError.message)
        console.log('📄 Resposta bruta:', aiResponse)
        return null
      }
    } catch (error) {
      console.error('❌ Erro na identificação via IA:', error.message)

      // Fallback: usar lógica anterior em caso de erro
      console.log('🔄 Usando fallback para identificação...')
      return {
        appointment: appointments[0],
        confidence: 0.5,
        reasoning: 'Fallback devido a erro na IA - selecionado primeiro agendamento'
      }
    }
  }

  /**
   * Process appointment confirmation after user responds
   * Executes actual appointment creation or cancels based on user response
   */
  async processAppointmentConfirmation (conversation, customerData, trinksApiCalls, customerPhone, customerResponse) {
    const trinksService = require('./trinks')
    const startTime = Date.now()
    const self = this // Fix scope issue for generator functions

    try {
      console.log('🤖 Processando confirmação de agendamento...', customerResponse)

      // Verificar se existe dados de agendamento pendentes
      if (!conversation.schedulingData || !conversation.schedulingData.awaitingConfirmation) {
        return {
          success: false,
          error: 'Nenhum agendamento pendente para confirmação'
        }
      }

      const schedulingData = conversation.schedulingData

      // Extrair confirmação da resposta do cliente usando IA
      const analysis = await this.extractAppointmentResponse(customerResponse, schedulingData)

      console.log('📊 Análise da resposta de agendamento:', analysis)

      // Processar diferentes tipos de resposta
      switch (analysis.confirmation) {
        case 'true':
          // Cliente confirmou o agendamento - criar
          console.log('✅ Cliente confirmou agendamento - executando criação...')

          const appointmentResult = await this.handleAppointmentCreation(conversation, customerData, trinksApiCalls, customerPhone)

          if (appointmentResult.success) {
            // Salvar dados para mensagem de confirmação ANTES de limpar
            const appointmentDate = new Date(`${schedulingData.date}T${schedulingData.time}`)
            const savedSchedulingData = { ...schedulingData } // Cópia para usar na mensagem

            // CORREÇÃO: Limpeza completa de estado após agendamento criado
            conversation.schedulingData = null // Limpar dados de agendamento
            conversation.availabilityContext = null // Limpar contexto de disponibilidade
            conversation.stage = 'completed'
            const formattedDate = appointmentDate.toLocaleDateString('pt-BR', {
              weekday: 'long',
              day: 'numeric',
              month: 'long'
            })
            const formattedTime = appointmentDate.toLocaleTimeString('pt-BR', {
              hour: '2-digit',
              minute: '2-digit'
            })

            // Usar cache semântico para mensagem de sucesso
            const confirmationContext = {
              customerName: customerData?.nome || '',
              serviceName: savedSchedulingData.service.name,
              professionalName: savedSchedulingData.professional.name,
              formattedDate,
              formattedTime,
              appointmentId: appointmentResult.appointmentId,
              servicePrice: savedSchedulingData.service.price,
              customerType: customerData?.isNewCustomer ? 'new' : 'returning'
            }

            const conversationContext = this.buildConversationContext(conversation)
            const confirmationMessage = await this.responseCache.getOrGenerate(
              'appointment_confirm',
              confirmationContext,
              {
                basePrompt: this.basePrompt,
                useTemplate: true,
                assistantName: this.settings?.assistantName || 'Assistente Virtual',
                generator: async (processedTemplate) => {
                  // Use processed template to generate final humanized text
                  if (processedTemplate) {
                    return await self.generateSimpleResponse(
                      processedTemplate.prompt,
                      conversationContext
                    )
                  }

                  return await self.generateSimpleResponse(
                    `${self.basePrompt}\n\nAgendamento criado com sucesso! Gere uma mensagem de confirmação calorosa e profissional com todos os detalhes.`,
                    conversationContext
                  )
                }
              }
            )

            return {
              success: true,
              created: true,
              appointmentId: appointmentResult.appointmentId,
              message: confirmationMessage,
              nextStage: 'completed'
            }
          } else {
            return {
              success: false,
              error: `Erro ao criar agendamento: ${appointmentResult.error}`,
              message: `Ops! Houve um problema ao confirmar seu agendamento: ${appointmentResult.error}\n\nPode tentar novamente? 😔`
            }
          }

        case 'false':
          // Cliente não quer agendar - cancelar
          conversation.stage = 'scheduling'
          conversation.schedulingData.awaitingConfirmation = false
          conversation.schedulingData.isComplete = false

          return {
            success: true,
            created: false,
            message: 'Sem problemas! Cancelei esse agendamento. Se quiser agendar outro horário ou serviço, é só me falar! 😊',
            nextStage: 'scheduling'
          }

        case 'change_time':
        case 'change_professional':
        case 'change_service':
          // Cliente quer alterar algo
          conversation.stage = 'scheduling'
          conversation.schedulingData.awaitingConfirmation = false
          conversation.schedulingData.isComplete = false

          let changeMessage = ''
          if (analysis.confirmation === 'change_time') {
            changeMessage = 'Vou buscar outros horários para você!'
            conversation.schedulingData.time = null
          } else if (analysis.confirmation === 'change_professional') {
            changeMessage = 'Vou ver outros profissionais disponíveis!'
            conversation.schedulingData.professional = { id: null, name: null }
          } else if (analysis.confirmation === 'change_service') {
            changeMessage = 'Que serviço você gostaria?'
            conversation.schedulingData.service = { id: null, name: null, price: null }
          }

          return {
            success: true,
            created: false,
            message: `Perfeito! ${changeMessage} Me fala o que você prefere. 😊`,
            nextStage: 'scheduling'
          }

        case 'unclear':
        default:
          // Resposta ambígua - pedir clarificação
          return {
            success: true,
            created: false,
            needsClarification: true,
            message: `Não entendi bem sua resposta. Você quer confirmar o agendamento de **${schedulingData.service.name}**? \n\nDigite "sim" para confirmar ou "não" se não quiser agendar. 😊`,
            nextStage: 'awaiting_appointment_confirmation'
          }
      }
    } catch (error) {
      console.error('❌ Erro ao processar confirmação de agendamento:', error.message)

      return {
        success: false,
        error: error.message,
        message: 'Desculpe, tive um problema para processar sua resposta. Você confirma o agendamento? Digite "sim" ou "não". 😊'
      }
    }
  }

  /**
   * Extract appointment confirmation from user response using AI
   * Similar to extractCancellationResponse but for appointment creation
   */
  async extractAppointmentResponse (customerResponse, schedulingData) {
    console.log('🤖 Analisando resposta do cliente para agendamento...', customerResponse)

    try {
      if (!this.anthropic) {
        throw new Error('Claude API não disponível - sistema configurado para falhar sem IA')
      }

      const appointmentInfo = `${schedulingData.service.name} com ${schedulingData.professional.name}`

      const prompt = `Você é um especialista em analisar respostas de clientes sobre confirmação de agendamentos.

CONTEXTO:
O cliente foi perguntado: "Confirma o agendamento de ${appointmentInfo}? Digite 'sim' para confirmar ou 'não' se quiser alterar algo."

RESPOSTA DO CLIENTE: "${customerResponse}"

ANÁLISE NECESSÁRIA:
1. CONFIRMAÇÃO: O cliente confirmou claramente o agendamento?
2. MUDANÇA: Ele quer alterar horário, profissional ou serviço?
3. CANCELAMENTO: Ele não quer mais agendar?

REGRAS:
- "Sim", "Confirmo", "Pode agendar", "Tá bom" = confirmação clara
- "Não", "Não quero", "Cancela" = recusa clara
- "Outro horário", "Mudar horário" = mudança de horário
- "Outro profissional", "Trocar" = mudança de profissional  
- "Outro serviço", "Diferente" = mudança de serviço
- "Talvez", "Não sei", "Pensando" = resposta ambígua

Responda APENAS com JSON válido:

{
  "confirmation": "true|false|change_time|change_professional|change_service|unclear",
  "confidence": 0.95,
  "reasoning": "Explicação clara da decisão",
  "method": "ai"
}`

      // ✨ Interceptar chamada Claude com sistema de avaliação automática
      const claudeParams = {
        model: 'claude-sonnet-4-20250514',
        max_tokens: 200,
        temperature: 0.1,
        messages: [{
          role: 'user',
          content: prompt
        }]
      }
      
      const startTime = Date.now()
      const response = await this.evaluationLogger.interceptClaudeCall(
        claudeParams,
        (params) => this.anthropic.messages.create(params),
        {
          source: 'ai_service_appointment_response',
          sessionId: messageLogger.sessionId || 'unknown-session',
          interactionType: 'appointment_response_extraction'
        }
      )
      const duration = Date.now() - startTime

      const content = response.content[0]?.text

      // Log da chamada da IA
      messageLogger.logAIInteraction(
        'extractAppointmentResponse',
        prompt,
        content,
        duration,
        'claude-sonnet-4-20250514'
      )

      if (!content) {
        throw new Error('Empty response from Claude API')
      }

      // Parse JSON response
      let jsonContent = content
      if (content.includes('```json')) {
        const jsonMatch = content.match(/```json\n([\s\S]*?)\n```/)
        if (jsonMatch) {
          jsonContent = jsonMatch[1]
        }
      } else if (content.includes('```')) {
        const codeMatch = content.match(/```\n([\s\S]*?)\n```/)
        if (codeMatch) {
          jsonContent = codeMatch[1]
        }
      }

      const analysis = JSON.parse(jsonContent)

      return {
        confirmation: analysis.confirmation,
        confidence: analysis.confidence || 0.5,
        reasoning: analysis.reasoning,
        method: 'ai'
      }
    } catch (error) {
      console.error('❌ Erro ao analisar resposta de agendamento:', error.message)
      throw error // Remover fallback - sistema deve falhar se IA não funcionar
    }
  }

  /**
   * Basic rule-based extraction for appointment confirmation when AI is not available
   */
  extractAppointmentResponseBasic (response) {
    const lowerResponse = response.toLowerCase().trim()

    // Confirmações claras
    const confirmationWords = ['sim', 'confirmo', 'pode agendar', 'tá bom', 'ok', 'beleza']
    if (confirmationWords.some(word => lowerResponse.includes(word))) {
      return {
        confirmation: 'true',
        confidence: 0.8,
        reasoning: 'Confirmação detectada por palavras-chave',
        method: 'rules'
      }
    }

    // Negações claras
    const negationWords = ['não', 'nao', 'não quero', 'cancela', 'deixa pra lá']
    if (negationWords.some(word => lowerResponse.includes(word))) {
      return {
        confirmation: 'false',
        confidence: 0.8,
        reasoning: 'Recusa detectada por palavras-chave',
        method: 'rules'
      }
    }

    // Mudanças de horário
    const timeChangeWords = ['outro horário', 'mudar horário', 'horário diferente']
    if (timeChangeWords.some(word => lowerResponse.includes(word))) {
      return {
        confirmation: 'change_time',
        confidence: 0.9,
        reasoning: 'Solicitação de mudança de horário detectada',
        method: 'rules'
      }
    }

    // Mudanças de profissional
    const professionalChangeWords = ['outro profissional', 'trocar', 'diferente']
    if (professionalChangeWords.some(word => lowerResponse.includes(word))) {
      return {
        confirmation: 'change_professional',
        confidence: 0.9,
        reasoning: 'Solicitação de mudança de profissional detectada',
        method: 'rules'
      }
    }

    // Resposta ambígua
    return {
      confirmation: 'unclear',
      confidence: 0.3,
      reasoning: 'Resposta não identificada - necessita clarificação',
      method: 'rules'
    }
  }

  /**
   * Normalize date from various formats to YYYY-MM-DD
   */
  normalizeDate (dateInput) {
    if (!dateInput) return null

    // Se já está no formato YYYY-MM-DD
    if (/^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
      return dateInput
    }

    // Converter expressões como "amanhã", "hoje"
    return this.convertRelativeDateToAbsolute(dateInput)
  }

  /**
   * Verifica se deve usar template de disponibilidade humanizada
   * baseado no contexto de disponibilidade recém-criado
   */
  shouldUseAvailabilityTemplate (conversation) {
    const availabilityContext = conversation?.availabilityContext

    if (!availabilityContext) {
      return false
    }

    // Verifica se o contexto foi criado recentemente (últimos 10 segundos)
    const contextAge = availabilityContext.contextChangedAt
      ? Date.now() - new Date(availabilityContext.contextChangedAt).getTime()
      : 0

    const isRecent = contextAge < 10000 // 10 segundos
    const hasSlots = availabilityContext.hasAvailability && availabilityContext.availableSlots?.length > 0
    const isSpecificProfessional = availabilityContext.specificProfessional

    // Usar template se:
    // 1. Contexto foi criado recentemente
    // 2. Tem disponibilidade real
    // 3. É busca por profissional específico
    const shouldUse = isRecent && hasSlots && isSpecificProfessional

    if (shouldUse) {
      console.log(`✅ Template de disponibilidade será usado - Contexto criado ${Math.round(contextAge / 1000)}s atrás, ${availabilityContext.totalSlots} slots`)
    }

    return shouldUse
  }

  /**
   * Gera resposta usando template de disponibilidade humanizada
   */
  async generateAvailabilityResponse (conversation, customerData, performanceMetrics) {
    const startTime = Date.now()

    try {
      const availabilityContext = conversation.availabilityContext
      const conversationHistory = this.getConversationHistory(conversation)

      // Preparar dados para o template
      const templateData = {
        conversationHistory,
        targetDate: this.formatDateForHumans(availabilityContext.date),
        professionalName: this.extractFirstName(availabilityContext.professionalName),
        availableSlots: availabilityContext.availableSlots.join(', '),
        totalSlots: availabilityContext.totalSlots
      }

      // Usar o sistema de templates configuráveis
      const promptTemplates = require('../config/prompt-templates')
      const templateSystem = promptTemplates

      const templateResponse = templateSystem.processTemplate('availability_suggestion', templateData)

      if (!templateResponse) {
        console.error('❌ Template availability_suggestion não encontrado')
        return this.generateFallbackAvailabilityResponse(availabilityContext)
      }

      // Gerar resposta usando Claude AI
      performanceMetrics.steps.availabilityTemplateStart = Date.now()

      if (this.anthropic) {
        // 🧠 Log uso do template no AIInteractionLogger
        const sessionId = messageLogger.sessionId || 'unknown-session'
        await aiInteractionLogger.logTemplateUsage(sessionId, conversation.customerPhone, {
          templateType: 'availability_suggestion',
          templateName: 'Sugestão de Disponibilidade Humanizada',
          source: 'configured',
          variables: templateData,
          processedTemplate: templateResponse.prompt
        })

        // 🧠 Log prompt enviado no AIInteractionLogger
        await aiInteractionLogger.logPromptSent(sessionId, conversation.customerPhone, {
          model: 'claude-sonnet-4-20250514',
          templateType: 'availability_suggestion',
          systemPrompt: templateResponse.prompt,
          userMessage: 'Responda de forma natural e humanizada sobre a disponibilidade encontrada.',
          maxTokens: templateResponse.maxTokens || 150,
          temperature: templateResponse.temperature || 0.6,
          interactionType: 'availability_template',
          context: {
            availabilityContext,
            templateData
          }
        })

        // ✨ Interceptar chamada Claude com sistema de avaliação automática
        const claudeParams = {
          model: 'claude-sonnet-4-20250514',
          max_tokens: templateResponse.maxTokens || 150,
          temperature: templateResponse.temperature || 0.6,
          system: templateResponse.prompt,
          messages: [
            {
              role: 'user',
              content: 'Responda de forma natural e humanizada sobre a disponibilidade encontrada.'
            }
          ]
        }
        
        const apiStartTime = Date.now()
        const response = await this.evaluationLogger.interceptClaudeCall(
          claudeParams,
          (params) => this.anthropic.messages.create(params),
          {
            source: 'ai_service_availability_template',
            sessionId: messageLogger.sessionId || 'unknown-session',
            interactionType: 'availability_template_response'
          }
        )
        const apiDuration = Date.now() - apiStartTime

        // Extract content with proper error handling
        if (!response.content || !response.content[0] || typeof response.content[0].text !== 'string') {
          console.error('❌ Invalid Claude API response structure in handleProgressiveAvailability:', JSON.stringify(response, null, 2))
          throw new Error('Invalid Claude API response structure: missing content[0].text')
        }

        const responseText = response.content[0].text.trim()

        // Log da chamada da IA
        messageLogger.logAIInteraction(
          'generateAvailabilityResponse',
          templateResponse.prompt,
          responseText,
          apiDuration,
          'claude-sonnet-4-20250514'
        )

        performanceMetrics.steps.availabilityTemplateEnd = Date.now()
        const duration = performanceMetrics.steps.availabilityTemplateEnd - performanceMetrics.steps.availabilityTemplateStart
        console.log(`⏱️ [AI] Template de disponibilidade: ${duration}ms`)

        // 🧠 Log resposta recebida no AIInteractionLogger
        await aiInteractionLogger.logResponseReceived(sessionId, conversation.customerPhone, {
          response: response.content[0].text,
          duration,
          model: 'claude-sonnet-4-20250514',
          success: true,
          tokensUsed: response.usage?.total_tokens || null,
          newStage: 'scheduling',
          contextUpdates: {
            lastAvailabilityShown: availabilityContext.date,
            lastProfessionalShown: availabilityContext.professionalName
          },
          usedTemplate: true,
          fromCache: false
        })

        return {
          response: response.content[0].text,
          newStage: 'scheduling',
          context: {
            lastAvailabilityShown: availabilityContext.date,
            lastProfessionalShown: availabilityContext.professionalName
          }
        }
      } else {
        throw new Error('Claude AI não configurado - template de disponibilidade requer IA real')
      }
    } catch (error) {
      console.error('❌ Erro ao gerar resposta de disponibilidade:', error.message)
      // Propagar erro original ao invés de tentar fallback
      throw error
    }
  }

  /**
   * Gera resposta via IA - SEM fallbacks hardcoded conforme CLAUDE.md
   * REGRA CRÍTICA: Sistema deve falhar explicitamente se IA não disponível
   */
  generateFallbackAvailabilityResponse (availabilityContext) {
    // ❌ REMOVIDO: Fallback hardcoded que viola regras do CLAUDE.md
    // 🚨 REGRA: "NUNCA diga 'vou verificar' ou use respostas fixas"

    console.error('❌ ERRO CRÍTICO: Sistema tentou usar fallback hardcoded')
    console.error('📋 CLAUDE.md: Sistema deve sempre usar IA para respostas')
    console.error('🔧 SOLUÇÃO: Configure ANTHROPIC_API_KEY corretamente')

    // Forçar erro para identificar problema de configuração
    throw new Error('Fallback hardcoded desabilitado conforme CLAUDE.md - Configure IA adequadamente')
  }

  /**
   * Formata data para linguagem mais humana
   */
  formatDateForHumans (dateString) {
    if (!dateString) return 'hoje'

    const date = new Date(dateString)
    const today = dateUtils.getCurrentDateBR()
    const tomorrow = new Date(today)
    tomorrow.setDate(today.getDate() + 1)

    const dateStr = date.toDateString()
    const todayStr = today.toDateString()
    const tomorrowStr = tomorrow.toDateString()

    if (dateStr === todayStr) return 'hoje'
    if (dateStr === tomorrowStr) return 'amanhã'

    // Formato brasileiro
    return date.toLocaleDateString('pt-BR', {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    })
  }

  /**
   * Extrai primeiro nome de um nome completo
   */
  extractFirstName (fullName) {
    if (!fullName) return 'Profissional'
    return fullName.split(' ')[0]
  }

  /**
   * Fallback de emergência quando resposta da IA está vazia
   */
  getEmergencyFallbackResponse (conversation, appointmentResult, cancellationResult) {
    console.log('🚨 Gerando fallback de emergência...')

    // Se é resultado de agendamento
    if (appointmentResult) {
      if (appointmentResult.created) {
        return 'Agendamento confirmado com sucesso! 😊'
      } else if (appointmentResult.success === false) {
        return 'Ops, tive um problema com o agendamento. Pode tentar novamente?'
      } else {
        // Caso change_time, change_professional, etc.
        return 'Entendi! Vamos ajustar isso. Me conta o que você prefere? 😊'
      }
    }

    // Se é resultado de cancelamento
    if (cancellationResult) {
      if (cancellationResult.success) {
        return 'Cancelamento realizado com sucesso!'
      } else {
        return 'Tive um problema para cancelar. Pode tentar novamente?'
      }
    }

    // Fallback geral baseado no estágio da conversa
    switch (conversation?.stage) {
      case 'greeting':
        return 'Oi! Como posso te ajudar hoje? 😊'
      case 'scheduling':
        return 'Vamos agendar! Que serviço você gostaria?'
      case 'awaiting_appointment_confirmation':
        return 'Posso confirmar esse agendamento para você?'
      case 'awaiting_cancellation_confirmation':
        return 'Posso confirmar o cancelamento?'
      case 'completed':
        return 'Agendamento concluído! Até logo! 😊'
      default:
        return 'Desculpe, tive um probleminha técnico. Pode repetir sua mensagem? 😊'
    }
  }

  /**
   * 📅 Sistema de Busca Progressiva de Disponibilidade
   * Busca horários disponíveis baseado nos 3 cenários:
   * 1. Profissional + Serviço + Data
   * 2. Profissional + Data
   * 3. Serviço + Data
   */
  async fetchProgressiveAvailability (conversation) {
    try {
      if (!conversation || !conversation.extractedData) {
        console.log('📋 Nenhum dado extraído disponível para busca de disponibilidade')
        return null
      }

      const { professional, date, service } = conversation.extractedData

      if (!date) {
        console.log('📋 Nenhuma data identificada - não buscar disponibilidade')
        return null
      }

      console.log('🔍 Buscando disponibilidade progressiva:', {
        professional: professional || 'N/A',
        service: service || 'N/A',
        date
      })

      let availabilityData = null

      // Cenário 1: Profissional + Serviço + Data (mais específico)
      if (professional && service) {
        console.log('🎯 Cenário 1: Profissional + Serviço + Data')
        availabilityData = await this.trinksService.getProfessionalServiceSlots(professional, service, date)
        if (availabilityData) {
          availabilityData.scenario = 'professional_service_date'
          availabilityData.specificProfessional = true
        }
      }
      // Cenário 2: Profissional + Data
      else if (professional) {
        console.log('🎯 Cenário 2: Profissional + Data')
        availabilityData = await this.trinksService.getProfessionalSlots(professional, date)
        if (availabilityData) {
          availabilityData.scenario = 'professional_date'
          availabilityData.specificProfessional = true
        }
      }
      // Cenário 3: Serviço + Data
      else if (service) {
        console.log('🎯 Cenário 3: Serviço + Data')
        availabilityData = await this.trinksService.getServiceSlots(service, date)
        if (availabilityData) {
          availabilityData.scenario = 'service_date'
          availabilityData.specificProfessional = false
        }
      }

      if (availabilityData) {
        // Padronizar dados de retorno
        const standardizedData = {
          ...availabilityData,
          date,
          hasAvailability: (availabilityData.slots && availabilityData.slots.length > 0),
          mustUseData: true,
          contextType: 'full'
        }

        console.log(`✅ Disponibilidade encontrada (${standardizedData.scenario}):`, {
          hasSlots: standardizedData.hasAvailability,
          slotsCount: standardizedData.slots?.length || 0,
          professional: standardizedData.professionalName,
          service: standardizedData.serviceName
        })

        return standardizedData
      } else {
        console.log('❌ Nenhuma disponibilidade encontrada para os critérios fornecidos')
        return null
      }
    } catch (error) {
      console.error('❌ Erro na busca progressiva de disponibilidade:', error.message)

      // Log do erro usando ErrorLogger
      const context = {
        professional: conversation.extractedData?.professional,
        service: conversation.extractedData?.service,
        date: conversation.extractedData?.date,
        scenario: 'progressive_availability_fetch'
      }

      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, context)
      return null
    }
  }

  /**
   * 🆕 NOVA FUNÇÃO: Busca disponibilidade usando dados do schedulingData diretamente
   * Esta função otimiza o processo usando informações já coletadas na conversa
   *
   * @param {Object} conversation - Objeto da conversa com schedulingData
   * @param {Array} trinksApiCalls - Array para tracking de chamadas API
   */
  async fetchAvailabilityWithSchedulingData (conversation, trinksApiCalls = []) {
    console.log('🎯 Iniciando busca de disponibilidade com dados do schedulingData...')

    if (!conversation?.schedulingData) {
      console.log('❌ Nenhum schedulingData encontrado na conversa')
      return
    }

    const { date, professional, service, time } = conversation.schedulingData
    const trinksService = require('./trinks')
    const errorLogger = require('../utils/ErrorLogger')
    const { ERROR_CATEGORIES } = require('../utils/ErrorLogger')

    try {
      // CENÁRIO 1: Profissional específico + data (+ opcionalmente serviço)
      if (professional?.name && date) {
        console.log(`🎯 Cenário 1: Busca específica - Profissional: ${professional.name}, Data: ${date}, Serviço: ${service?.name || 'qualquer'}`)

        // Usar fetchSpecificProfessionalSlots com filtro de serviço se disponível
        await this.fetchSpecificProfessionalSlots(
          conversation,
          date,
          professional.name,
          service?.id || null,
          trinksApiCalls
        )

        // Verificar se dados foram populados
        if (conversation.availabilityContext?.hasAvailability) {
          console.log('✅ Disponibilidade obtida para profissional específico')
          return
        }
      }

      // CENÁRIO 2: Serviço específico + data (sem profissional definido)
      if (service?.id && date && !professional?.name) {
        console.log(`🎯 Cenário 2: Busca por serviço - Serviço: ${service.name}, Data: ${date}`)

        const startTime = Date.now()
        const availability = await trinksService.getProfessionalsAvailabilityReal(date, service.id)
        const endTime = Date.now()

        // Registrar chamada API
        trinksApiCalls.push({
          endpoint: '/v1/agendamentos/profissionais/data',
          method: 'GET',
          purpose: `Buscar disponibilidade geral filtrada por serviço ${service.name} (ID: ${service.id}) para ${date}`,
          responseTime: endTime - startTime,
          success: availability?.professionals?.length > 0,
          resultCount: availability?.professionals?.length || 0,
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(endTime).toISOString(),
          request: { date, serviceId: service.id },
          response: { status: 200, data: availability }
        })

        if (availability?.professionals?.length > 0) {
          console.log(`✅ Encontrados ${availability.professionals.length} profissionais disponíveis para ${service.name}`)

          // Preparar contexto de disponibilidade
          conversation.availabilityContext = {
            date,
            serviceId: service.id,
            serviceName: service.name,
            professionals: availability.professionals,
            totalSlots: availability.professionals.reduce((sum, prof) => sum + prof.totalSlots, 0),
            hasAvailability: true,
            specificProfessional: false,
            contextChangedAt: new Date().toISOString(),
            sentToAI: false
          }

          return
        }
      }

      // CENÁRIO 3: Apenas data (busca geral)
      if (date && !professional?.name && !service?.id) {
        console.log(`🎯 Cenário 3: Busca geral - Data: ${date}`)

        const startTime = Date.now()
        const availability = await trinksService.getProfessionalsAvailabilityReal(date)
        const endTime = Date.now()

        // Registrar chamada API
        trinksApiCalls.push({
          endpoint: '/v1/agendamentos/profissionais/data',
          method: 'GET',
          purpose: `Buscar disponibilidade geral para ${date}`,
          responseTime: endTime - startTime,
          success: availability?.professionals?.length > 0,
          resultCount: availability?.professionals?.length || 0,
          startTime: new Date(startTime).toISOString(),
          endTime: new Date(endTime).toISOString(),
          request: { date },
          response: { status: 200, data: availability }
        })

        if (availability?.professionals?.length > 0) {
          console.log(`✅ Disponibilidade geral encontrada: ${availability.professionals.length} profissionais`)

          // Preparar contexto de disponibilidade
          conversation.availabilityContext = {
            date,
            professionals: availability.professionals,
            totalSlots: availability.professionals.reduce((sum, prof) => sum + prof.totalSlots, 0),
            hasAvailability: true,
            specificProfessional: false,
            contextChangedAt: new Date().toISOString(),
            sentToAI: false
          }

          return
        }
      }

      console.log('❌ Nenhuma disponibilidade encontrada com os dados do schedulingData')
    } catch (error) {
      console.error('❌ Erro ao buscar disponibilidade com schedulingData:', error.message)

      // Log do erro
      const context = {
        function: 'fetchAvailabilityWithSchedulingData',
        professionalName: professional?.name,
        serviceId: service?.id,
        serviceName: service?.name,
        date,
        time
      }

      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, context)
    }
  }
}

module.exports = new AIService()
