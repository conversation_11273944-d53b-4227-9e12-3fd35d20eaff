const path = require('path');
const fs = require('fs');

// Import only supported providers
const WAHAProvider = require('./providers/WAHAProvider');
const CustomerAppProvider = require('./providers/CustomerAppProvider');

/**
 * Factory para criar instâncias de provedores WhatsApp
 * Suporta apenas: waha, twilio
 */
class WhatsAppProviderFactory {
  constructor() {
    this.providers = new Map();
    this.currentProvider = null;
    this.settings = this.loadSettings();
  }

  loadSettings() {
    try {
      const settingsPath = path.join(__dirname, '../config/settings.json');
      if (fs.existsSync(settingsPath)) {
        return JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      }
    } catch (error) {
      console.log('No settings found, using defaults');
    }
    
    return {
      whatsappEnabled: false,
      whatsappType: 'waha', // Default provider
      whatsappBusinessNumber: '',
      establishmentId: ''
    };
  }

  /**
   * Cria uma instância do provider WhatsApp baseado na configuração
   * @param {string} providerType - Tipo do provider (waha, twilio)
   * @returns {WhatsAppProviderInterface}
   */
  createProvider(providerType = null) {
    // Use provider type from parameter, settings, or default
    const type = providerType || this.settings.whatsappType || 'waha';
    
    console.log(`🏭 Creating WhatsApp provider: ${type}`);

    // Check if provider already exists in cache
    if (this.providers.has(type)) {
      console.log(`♻️ Reusing existing ${type} provider instance`);
      return this.providers.get(type);
    }

    let provider;

    switch (type.toLowerCase()) {
      case 'waha':
        provider = new WAHAProvider();
        break;

      case 'twilio':
        // Create Twilio provider
        provider = this.createTwilioProvider();
        break;

      case 'customer-app':
        // Customer App test mode - uses mock provider for testing
        provider = this.createCustomerAppProvider();
        break;

      default:
        console.error(`❌ Unknown WhatsApp provider type: ${type}`);
        console.log('📝 Available providers: waha, twilio, customer-app');
        throw new Error(`Unsupported WhatsApp provider: ${type}. Available: waha, twilio, customer-app`);
    }

    // Cache the provider instance
    this.providers.set(type, provider);
    
    console.log(`✅ ${type} provider created and cached successfully`);
    return provider;
  }

  /**
   * Retorna o provider atual ou cria um novo baseado nas configurações
   * @returns {WhatsAppProviderInterface}
   */
  getCurrentProvider() {
    if (!this.currentProvider) {
      this.currentProvider = this.createProvider();
    }
    return this.currentProvider;
  }

  /**
   * Altera o provider ativo
   * @param {string} providerType - Tipo do novo provider
   * @returns {WhatsAppProviderInterface}
   */
  switchProvider(providerType) {
    console.log(`🔄 Switching WhatsApp provider from ${this.settings.whatsappType} to ${providerType}`);
    
    // Disconnect current provider if connected
    if (this.currentProvider && this.currentProvider.isConnected) {
      console.log('🔌 Disconnecting current provider...');
      this.currentProvider.disconnect().catch(error => {
        console.error('⚠️ Error disconnecting current provider:', error);
      });
    }

    // Update settings
    this.settings.whatsappType = providerType;
    this.saveSettings();

    // Create new provider
    this.currentProvider = this.createProvider(providerType);
    
    console.log(`✅ Successfully switched to ${providerType} provider`);
    return this.currentProvider;
  }

  /**
   * Lista todos os providers disponíveis
   * @returns {Array<Object>}
   */
  getAvailableProviders() {
    return [
      {
        type: 'waha',
        name: 'WAHA (WhatsApp HTTP API)',
        description: 'API HTTP para WhatsApp com suporte a múltiplos formatos',
        features: ['QR Code', 'Mensagens de texto', 'Mensagens de mídia', 'Status em tempo real'],
        status: 'active'
      },
      {
        type: 'twilio',
        name: 'Twilio WhatsApp Business API',
        description: 'API oficial do WhatsApp Business via Twilio',
        features: ['Templates', 'Mensagens de texto', 'Mensagens de mídia', 'Webhooks'],
        status: 'premium'
      },
      {
        type: 'customer-app',
        name: 'Customer App Test Mode',
        description: 'Modo de teste para desenvolvimento sem WhatsApp real',
        features: ['Simulação de conversas', 'Análise de humor', 'Teste de recursos', 'Interface de cliente'],
        status: 'test'
      }
    ];
  }

  /**
   * Cria provider Twilio
   * @returns {WhatsAppProviderInterface}
   */
  createTwilioProvider() {
    // Import Twilio provider
    try {
      const TwilioProvider = require('./providers/TwilioProvider');
      return new TwilioProvider();
    } catch (error) {
      console.error('❌ Twilio provider not available:', error.message);
      console.log('💡 Falling back to WAHA provider');
      return new WAHAProvider();
    }
  }

  /**
   * Cria provider Customer App (modo teste)
   * @returns {WhatsAppProviderInterface}
   */
  createCustomerAppProvider() {
    // Import Customer App provider
    try {
      const CustomerAppProvider = require('./providers/CustomerAppProvider');
      return new CustomerAppProvider();
    } catch (error) {
      console.error('❌ Customer App provider not available:', error.message);
      console.log('💡 Creating mock provider for customer app mode');
      // Return a basic mock provider for customer app
      return this.createMockProvider();
    }
  }

  /**
   * Cria um provider mock básico para customer app
   * @returns {WhatsAppProviderInterface}
   */
  createMockProvider() {
    return {
      connectionType: 'customer-app',
      isConnected: true,
      connectionStatus: 'connected',
      phoneNumber: 'Customer App Test Mode',
      currentQRCode: null,

      getStatus() {
        return {
          isConnected: true,
          connectionStatus: 'connected',
          connectionType: 'customer-app',
          qrCode: null,
          phoneNumber: 'Customer App Test Mode'
        };
      },

      updateSettings(settings) {
        console.log('🧪 Customer App mock provider - settings updated:', Object.keys(settings));
      },

      async connect() {
        console.log('🧪 Customer App mock provider - connected instantly (test mode)');
        return Promise.resolve();
      },

      async disconnect() {
        console.log('🧪 Customer App mock provider - disconnected (test mode)');
        return Promise.resolve();
      },

      async sendMessage(phoneNumber, message, options = {}) {
        console.log(`🧪 Customer App mock provider - sending message to ${phoneNumber}:`, message);
        return Promise.resolve({ success: true, messageId: 'mock_' + Date.now() });
      },

      async simulateTyping(phoneNumber, duration = 3000) {
        console.log(`🧪 Customer App mock provider - simulating typing for ${phoneNumber} (${duration}ms)`);
        return Promise.resolve(true);
      },

      async loadHistoryFromWhatsApp(phoneNumber, limit = 10) {
        console.log(`🧪 Customer App mock provider - loading history for ${phoneNumber} (limit: ${limit})`);
        return Promise.resolve([]);
      },

      async loadAllChats() {
        console.log('🧪 Customer App mock provider - loading all chats');
        return Promise.resolve([]);
      },

      async clearSession() {
        console.log('🧪 Customer App mock provider - clearing session');
        return Promise.resolve({ success: true, message: 'Mock session cleared' });
      },

      setMessageHandler(handler) {
        console.log('🧪 Customer App mock provider - message handler set');
        this.messageHandler = handler;
      },

      sendQRToClient(socket) {
        console.log('🧪 Customer App mock provider - no QR code needed for test mode');
        return false;
      },

      emitLog(message) {
        console.log('🧪 Customer App mock provider log:', message);
      },

      emitStatus() {
        console.log('🧪 Customer App mock provider - emitting status');
      },

      normalizePhoneNumber(phoneNumber, forWhatsApp = false) {
        // Basic normalization for test mode
        let normalized = phoneNumber.replace(/\D/g, '');
        if (normalized.length === 11 && normalized.startsWith('21')) {
          normalized = '55' + normalized;
        } else if (normalized.length === 10) {
          normalized = '5521' + normalized;
        }
        
        if (forWhatsApp && !normalized.includes('@')) {
          normalized = normalized + '@c.us';
        }
        
        return normalized;
      }
    };
  }

  /**
   * Salva as configurações no arquivo
   */
  saveSettings() {
    try {
      const settingsPath = path.join(__dirname, '../config/settings.json');
      const currentSettings = fs.existsSync(settingsPath) 
        ? JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
        : {};
      
      const updatedSettings = { ...currentSettings, ...this.settings };
      fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2));
      console.log('✅ Provider settings saved successfully');
    } catch (error) {
      console.error('❌ Error saving provider settings:', error);
    }
  }

  /**
   * Atualiza configurações do factory
   * @param {Object} newSettings - Novas configurações
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    
    // If provider type changed, switch provider
    if (newSettings.whatsappType && newSettings.whatsappType !== this.settings.whatsappType) {
      this.switchProvider(newSettings.whatsappType);
    }
    
    // Update current provider settings
    if (this.currentProvider) {
      this.currentProvider.updateSettings(newSettings);
    }
    
    this.saveSettings();
  }

  /**
   * Limpa cache de providers
   */
  clearCache() {
    console.log('🧹 Clearing provider cache...');
    
    // Disconnect all cached providers
    for (const [type, provider] of this.providers) {
      if (provider.isConnected) {
        provider.disconnect().catch(error => {
          console.error(`⚠️ Error disconnecting ${type} provider:`, error);
        });
      }
    }
    
    this.providers.clear();
    this.currentProvider = null;
    
    console.log('✅ Provider cache cleared successfully');
  }

  /**
   * Retorna estatísticas dos providers
   * @returns {Object}
   */
  getProviderStats() {
    const stats = {
      currentProvider: this.settings.whatsappType,
      cachedProviders: Array.from(this.providers.keys()),
      totalProviders: this.providers.size,
      availableProviders: this.getAvailableProviders().length
    };

    if (this.currentProvider) {
      stats.currentStatus = this.currentProvider.getStatus();
    }

    return stats;
  }

  /**
   * Testa conectividade de um provider específico
   * @param {string} providerType - Tipo do provider para testar
   * @returns {Promise<Object>}
   */
  async testProvider(providerType) {
    console.log(`🧪 Testing ${providerType} provider...`);
    
    try {
      const provider = this.createProvider(providerType);
      
      // Test basic functionality without actually connecting
      const status = provider.getStatus();
      
      return {
        success: true,
        provider: providerType,
        status: status,
        features: this.getAvailableProviders().find(p => p.type === providerType)?.features || []
      };
      
    } catch (error) {
      console.error(`❌ Error testing ${providerType} provider:`, error);
      return {
        success: false,
        provider: providerType,
        error: error.message
      };
    }
  }

  /**
   * Método de conveniência para criar um provider com configuração específica
   * @param {string} providerType - Tipo do provider
   * @param {Object} config - Configuração específica do provider
   * @returns {WhatsAppProviderInterface}
   */
  createProviderWithConfig(providerType, config = {}) {
    const provider = this.createProvider(providerType);
    
    if (Object.keys(config).length > 0) {
      provider.updateSettings(config);
    }
    
    return provider;
  }
}

// Export singleton instance
const factory = new WhatsAppProviderFactory();

module.exports = factory;