/**
 * MessageBatcher - System to batch multiple rapid messages from same customer
 * This prevents multiple API calls to <PERSON> when users send multiple messages quickly
 */

class MessageBatcher {
  constructor(batchTimeoutMs = 5000) { // 5 seconds default
    this.batchTimeoutMs = batchTimeoutMs;
    this.pendingBatches = new Map(); // customerPhone -> batch data
    this.batchTimers = new Map(); // customerPhone -> timer ID
  }

  /**
   * Add a message to the batch for a customer
   * @param {string} customerPhone - Customer phone number
   * @param {Object} messageData - Message data to batch
   * @param {Function} processCallback - Function to call when batch is ready
   */
  addMessage(customerPhone, messageData, processCallback) {
    console.log(`📦 MessageBatcher: Adding message for ${customerPhone}`);
    
    // Get or create batch for this customer
    if (!this.pendingBatches.has(customerPhone)) {
      this.pendingBatches.set(customerPhone, {
        messages: [],
        processCallback: processCallback,
        firstMessageTime: Date.now()
      });
    }

    const batch = this.pendingBatches.get(customerPhone);
    
    // 🔍 VERIFICAR DUPLICATA - Comparar conteúdo da última mensagem
    if (batch.messages.length > 0) {
      const lastMessage = batch.messages[batch.messages.length - 1];
      const isTextDuplicate = messageData.content && 
        lastMessage.content === messageData.content &&
        messageData.type === lastMessage.type;
      
      // Ignorar duplicatas de texto idêntico em até 10 segundos
      const timeDiff = Date.now() - lastMessage.timestamp;
      if (isTextDuplicate && timeDiff < 10000) {
        console.log(`🔁 MessageBatcher: Mensagem duplicada ignorada para ${customerPhone}:`);
        console.log(`   Conteúdo: "${messageData.content}"`);
        console.log(`   Intervalo: ${timeDiff}ms`);
        return; // Não adicionar mensagem duplicada
      }
    }

    const messageToAdd = {
      ...messageData,
      timestamp: Date.now()
    };
    
    batch.messages.push(messageToAdd);
    console.log(`📦 MessageBatcher: Batch for ${customerPhone} now has ${batch.messages.length} message(s)`);

    // Clear existing timer if any
    if (this.batchTimers.has(customerPhone)) {
      clearTimeout(this.batchTimers.get(customerPhone));
    }

    // Set new timer
    const timer = setTimeout(() => {
      this.processBatch(customerPhone);
    }, this.batchTimeoutMs);

    this.batchTimers.set(customerPhone, timer);
    console.log(`⏰ MessageBatcher: Timer set for ${customerPhone} (${this.batchTimeoutMs}ms)`);
  }

  /**
   * Process the batch for a customer
   * @param {string} customerPhone - Customer phone number
   */
  async processBatch(customerPhone) {
    console.log(`🚀 MessageBatcher: Processing batch for ${customerPhone}`);
    
    const batch = this.pendingBatches.get(customerPhone);
    if (!batch || batch.messages.length === 0) {
      console.log(`❌ MessageBatcher: No batch found for ${customerPhone}`);
      return;
    }

    const { messages, processCallback } = batch;
    const totalMessages = messages.length;
    const batchDuration = Date.now() - batch.firstMessageTime;

    console.log(`📊 MessageBatcher: Processing ${totalMessages} messages for ${customerPhone} (batched over ${batchDuration}ms)`);

    // Clean up batch data
    this.pendingBatches.delete(customerPhone);
    this.batchTimers.delete(customerPhone);

    try {
      // If only one message, process normally
      if (totalMessages === 1) {
        console.log(`📝 MessageBatcher: Single message - processing normally`);
        await processCallback(messages[0]);
      } else {
        // Multiple messages - combine them
        console.log(`📝 MessageBatcher: Multiple messages (${totalMessages}) - combining for processing`);
        
        // Combine text messages intelligently - apresentar como conversa fluida
        // Sem timestamps para a IA processar como uma mensagem única e contextual
        const combinedContent = messages
          .map(msg => msg.content)
          .join(' ');

        // Create combined message object based on the most recent message
        const latestMessage = messages[messages.length - 1];
        const combinedMessage = {
          ...latestMessage,
          content: combinedContent,
          originalMessages: messages,
          isBatchedMessage: true,
          batchSize: totalMessages,
          batchDurationMs: batchDuration
        };

        console.log(`📝 MessageBatcher: Combined content for ${customerPhone}:\n${combinedContent}`);
        await processCallback(combinedMessage);
      }

      console.log(`✅ MessageBatcher: Batch processing completed for ${customerPhone}`);

    } catch (error) {
      console.error(`❌ MessageBatcher: Error processing batch for ${customerPhone}:`, error.message);
      
      // In case of error, still clean up
      this.pendingBatches.delete(customerPhone);
      this.batchTimers.delete(customerPhone);
      
      throw error; // Re-throw to let calling code handle it
    }
  }

  /**
   * Force process a batch immediately (useful for manual triggers)
   * @param {string} customerPhone - Customer phone number
   */
  async forceProcessBatch(customerPhone) {
    console.log(`🔥 MessageBatcher: Force processing batch for ${customerPhone}`);
    
    if (this.batchTimers.has(customerPhone)) {
      clearTimeout(this.batchTimers.get(customerPhone));
      this.batchTimers.delete(customerPhone);
    }
    
    await this.processBatch(customerPhone);
  }

  /**
   * Check if a customer has pending messages
   * @param {string} customerPhone - Customer phone number
   * @returns {boolean}
   */
  hasPendingMessages(customerPhone) {
    return this.pendingBatches.has(customerPhone) && 
           this.pendingBatches.get(customerPhone).messages.length > 0;
  }

  /**
   * Get statistics about current batches
   * @returns {Object} Statistics
   */
  getStats() {
    const totalCustomersWithPendingMessages = this.pendingBatches.size;
    const totalPendingMessages = Array.from(this.pendingBatches.values())
      .reduce((sum, batch) => sum + batch.messages.length, 0);

    return {
      totalCustomersWithPendingMessages,
      totalPendingMessages,
      batchTimeoutMs: this.batchTimeoutMs
    };
  }

  /**
   * Clear all pending batches (useful for cleanup)
   */
  clearAllBatches() {
    console.log(`🧹 MessageBatcher: Clearing all pending batches`);
    
    // Clear all timers
    for (const timer of this.batchTimers.values()) {
      clearTimeout(timer);
    }
    
    this.pendingBatches.clear();
    this.batchTimers.clear();
    
    console.log(`✅ MessageBatcher: All batches cleared`);
  }

  /**
   * Update batch timeout configuration
   * @param {number} timeoutMs - New timeout in milliseconds
   */
  updateBatchTimeout(timeoutMs) {
    console.log(`⚙️ MessageBatcher: Updating batch timeout from ${this.batchTimeoutMs}ms to ${timeoutMs}ms`);
    this.batchTimeoutMs = timeoutMs;
  }
}

module.exports = MessageBatcher;