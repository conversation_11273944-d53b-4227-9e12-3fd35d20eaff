const axios = require('axios');
const path = require('path');
const fs = require('fs');
const WhatsAppProviderInterface = require('../interfaces/WhatsAppProviderInterface');
const { whatsappLogger } = require('../../utils/logger');
const debugLogger = require('../debugLogger');

class WAHAProvider extends WhatsAppProviderInterface {
  constructor() {
    super();
    this.connectionType = 'waha';
    this.settings = this.loadSettings();
    // WAHA Core only supports 'default' session name
    this.instanceName = 'default';
    this.mockMode = false;
    // Initialize to current time to prevent processing historical messages on startup
    // Only process messages that arrive AFTER the service starts
    this.lastMessageTimestamp = Date.now();
    this.pollingInterval = null;
    this.processedMessageIds = new Set(); // Track processed message IDs to avoid duplicates
    this.isInitializing = true; // Flag to prevent processing historical messages during startup
    this.initializationComplete = false; // Flag to track when initialization is complete
    
    // Webhook status tracking
    this.currentWebhookConfig = null; // Stores current webhook configuration from WAHA
    this.lastWebhookCheck = null; // Timestamp of last webhook status check
    this.webhookPaused = false; // Flag to track if webhook is paused (application side)
    
    // WAHA API configuration
    this.apiUrl = this.settings.wahaApiUrl || process.env.WAHA_API_URL || 'https://waha.acoda.com.br';
    this.apiKey = this.settings.wahaApiKey || process.env.WAHA_API_KEY || 'flufluflu';
    this.webhookUrl = process.env.WAHA_WEBHOOK_URL || 'http://localhost:3001/webhook/waha';
    
    // Axios instance for API calls
    this.api = axios.create({
      baseURL: this.apiUrl,
      headers: {
        'Content-Type': 'application/json',
        'X-Api-Key': this.apiKey || ''
      },
      timeout: 30000
    });

    // Add debug logging interceptors
    this.setupApiInterceptors();

    console.log('🔧 WAHA Provider initialized');
    console.log(`🔗 API URL: ${this.apiUrl}`);
    console.log(`🔑 API Key: ${this.apiKey ? 'Set' : 'Not set'}`);
    console.log(`📡 Webhook URL: ${this.webhookUrl}`);
  }

  setupApiInterceptors() {
    // Request interceptor
    this.api.interceptors.request.use(
      (config) => {
        debugLogger.logAPIRequest('waha', config.method.toUpperCase(), config.url, config.data);
        return config;
      },
      (error) => {
        debugLogger.logAPIResponse('waha', error.config?.url, 0, null, error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.api.interceptors.response.use(
      (response) => {
        debugLogger.logAPIResponse('waha', response.config.url, response.status, response.data);
        return response;
      },
      (error) => {
        debugLogger.logAPIResponse('waha', error.config?.url, error.response?.status || 0, error.response?.data, error);
        return Promise.reject(error);
      }
    );
  }

  loadSettings() {
    try {
      const settingsPath = path.join(__dirname, '../../config/settings.json');
      if (fs.existsSync(settingsPath)) {
        return JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      }
    } catch (error) {
      console.log('No WAHA settings found, using defaults');
    }
    
    return {
      whatsappEnabled: false,
      whatsappType: 'waha',
      whatsappBusinessNumber: '',
      wahaApiUrl: process.env.WAHA_API_URL || 'https://waha.acoda.com.br',
      wahaApiKey: process.env.WAHA_API_KEY || 'flufluflu',
      establishmentId: ''
    };
  }

  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    
    // WAHA Core only supports 'default' session name - don't change it
    console.log(`🏢 Using WAHA default session (Core limitation)`);
    this.instanceName = 'default';
    
    // Update API configuration if WAHA settings changed
    if (newSettings.wahaApiUrl || newSettings.wahaApiKey) {
      this.apiUrl = newSettings.wahaApiUrl || this.apiUrl;
      
      // Only update API key if it's not masked
      if (newSettings.wahaApiKey && !newSettings.wahaApiKey.includes('••••')) {
        this.apiKey = newSettings.wahaApiKey;
      }
      
      // Update Axios instance with new configuration
      this.api = axios.create({
        baseURL: this.apiUrl,
        headers: {
          'Content-Type': 'application/json',
          'X-Api-Key': this.apiKey || ''
        },
        timeout: 30000
      });
    }
    
    // Update webhook URL if provided
    if (newSettings.webhookUrl !== undefined) {
      this.webhookUrl = newSettings.webhookUrl || process.env.WAHA_WEBHOOK_URL || 'http://localhost:3001/webhook/waha';
      console.log(`🔗 Webhook URL updated: ${this.webhookUrl}`);
    }
    
    // Re-setup debug interceptors
    this.setupApiInterceptors();
    
    console.log(`🔧 WAHA API configuration updated`);
    console.log(`🔗 API URL: ${this.apiUrl}`);
    console.log(`🔑 API Key: ${this.apiKey ? 'Updated' : 'Not set'}`);
    
    // Save settings to file
    try {
      const settingsPath = path.join(__dirname, '../../config/settings.json');
      const currentSettings = fs.existsSync(settingsPath) 
        ? JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
        : {};
      
      // Don't save masked keys
      const cleanSettings = { ...newSettings };
      if (cleanSettings.wahaApiKey && cleanSettings.wahaApiKey.includes('••••')) {
        cleanSettings.wahaApiKey = currentSettings.wahaApiKey;
      }
      
      const updatedSettings = { ...currentSettings, ...cleanSettings };
      fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2));
      console.log('✅ WAHA settings updated');
    } catch (error) {
      console.error('❌ Error saving WAHA settings:', error);
    }
  }

  extractWebhookConfig(sessionData) {
    try {
      this.lastWebhookCheck = Date.now();
      
      if (sessionData && sessionData.config && sessionData.config.webhooks) {
        this.currentWebhookConfig = sessionData.config.webhooks;
        console.log('🔗 Webhook configuration extracted:', JSON.stringify(this.currentWebhookConfig, null, 2));
      } else {
        this.currentWebhookConfig = [];
        console.log('📡 No webhook configuration found, session using polling');
      }
    } catch (error) {
      console.error('❌ Error extracting webhook config:', error);
      this.currentWebhookConfig = null;
    }
  }

  getWebhookStatus() {
    const expectedUrl = this.webhookUrl;
    const currentWebhooks = this.currentWebhookConfig || [];
    
    const status = {
      lastCheck: this.lastWebhookCheck,
      expectedUrl: expectedUrl,
      currentWebhooks: currentWebhooks,
      isConfigured: currentWebhooks.length > 0,
      isMatching: false,
      mode: 'polling', // default to polling, webhook mode determined below if not paused
      details: {},
      paused: this.webhookPaused
    };

    if (currentWebhooks.length > 0 && !this.webhookPaused) {
      const primaryWebhook = currentWebhooks[0];
      status.mode = 'webhook';
      status.details = {
        url: primaryWebhook.url,
        events: primaryWebhook.events || [],
        hmac: primaryWebhook.hmac,
        retries: primaryWebhook.retries
      };

      // Check if configured webhook matches expected
      if (expectedUrl && primaryWebhook.url === expectedUrl) {
        status.isMatching = true;
      }
    } else if (currentWebhooks.length > 0 && this.webhookPaused) {
      // Webhook is configured but paused - show webhook details but mark as polling mode
      const primaryWebhook = currentWebhooks[0];
      status.details = {
        url: primaryWebhook.url,
        events: primaryWebhook.events || [],
        hmac: primaryWebhook.hmac,
        retries: primaryWebhook.retries
      };
      // mode remains 'polling' due to pause
    }

    return status;
  }

  async connect() {
    console.log('\n=== WAHA API CONNECTION PROCESS STARTED ===');
    console.log('🔍 Environment WAHA_API_URL:', this.apiUrl);
    console.log('🔍 Settings whatsappEnabled:', this.settings.whatsappEnabled);
    
    if (!this.settings.whatsappEnabled) {
      console.log('❌ WhatsApp integration is DISABLED - aborting');
      throw new Error('WhatsApp integration is disabled');
    }

    try {
      this.connectionStatus = 'connecting';
      this.emitStatus();
      this.emitLog('🚀 Iniciando conexão com WAHA API...');

      // Check if WAHA API is running
      await this.checkAPIHealth();

      // Check if session exists first
      await this.syncWithExistingSession();

      console.log('✅ WAHA API connection process completed successfully');
      this.emitLog('✅ Conexão com WAHA API estabelecida com sucesso!');

    } catch (error) {
      console.error('❌ WAHA API connection failed:', error);
      this.connectionStatus = 'error';
      this.emitStatus();
      this.emitLog(`❌ Erro na conexão: ${error.message}`);
      throw error;
    }
  }

  async syncWithExistingSession() {
    try {
      console.log(`🔄 Syncing with existing WAHA session: ${this.instanceName}`);
      
      // First try to get existing session
      const response = await this.api.get(`/api/sessions/${this.instanceName}`);
      const sessionData = response.data;
      
      console.log(`📊 Found existing session with status: ${sessionData.status}`);
      
      // Extract webhook configuration from session data
      this.extractWebhookConfig(sessionData);
      
      if (sessionData.status === 'WORKING') {
        // Session is already connected
        this.isConnected = true;
        this.connectionStatus = 'connected';
        this.phoneNumber = sessionData.me?.id?.split('@')[0] || sessionData.me?.phone;
        this.currentQRCode = null;
        this.emitStatus();
        this.emitLog('✅ Sincronizado com sessão WAHA existente');
        console.log('✅ Synced with existing WAHA session');
        
        // Make sure webhook is configured
        await this.setWebhook();
        
        // Polling disabled - webhook is primary mechanism for receiving messages
        // If webhook fails, messages will be lost until webhook is restored
        console.log('📞 Webhook configured - polling disabled for better performance');

        // Mark initialization as complete after a brief delay to avoid processing startup messages
        setTimeout(() => {
          this.isInitializing = false;
          this.initializationComplete = true;
          console.log('✅ WAHA initialization complete - now processing new messages only');
        }, 5000); // 5 second delay to let any startup messages settle

        return;
      }
      
      // Session exists but not connected, check auth status
      await this.checkAuthStatus();
      
    } catch (error) {
      if (error.response?.status === 404) {
        // Session doesn't exist, create it
        console.log(`🔧 Session '${this.instanceName}' doesn't exist, creating...`);
        await this.startSession();
        await this.setWebhook();
        await this.checkAuthStatus();
      } else {
        throw error;
      }
    }
  }

  async checkAPIHealth() {
    try {
      console.log('🔍 Checking WAHA API health...');
      // WAHA doesn't have /api/health, so we check /api/sessions instead
      const response = await this.api.get('/api/sessions');
      console.log('✅ WAHA API is running');
      this.emitLog('✅ WAHA API está funcionando');
      return true;
    } catch (error) {
      console.error('❌ WAHA API health check failed:', error.message);
      console.log('🔄 Falling back to mock mode for development...');
      this.emitLog('🔄 Usando modo mock para desenvolvimento...');

      // Set mock mode
      this.mockMode = true;
      return true;
    }
  }

  async startSession() {
    if (this.mockMode) {
      console.log(`🧪 Mock mode: Starting session: ${this.instanceName}`);
      this.emitLog('🧪 Modo mock: Sessão iniciada');
      this.generateMockQRCode();
      return { success: true, mock: true };
    }

    try {
      // First check if session already exists
      console.log(`🔍 Checking if WAHA session exists: ${this.instanceName}`);
      const sessionCheck = await this.api.get(`/api/sessions/${this.instanceName}`);
      
      if (sessionCheck.data && sessionCheck.data.status) {
        console.log(`ℹ️ Session '${this.instanceName}' already exists with status: ${sessionCheck.data.status}`);
        this.emitLog('ℹ️ Sessão já existe, atualizando webhook...');
        
        // Update webhook configuration for existing session
        await this.setWebhook();
        return sessionCheck.data;
      }
    } catch (error) {
      // Session doesn't exist, continue to create it
      console.log(`🔧 Session '${this.instanceName}' not found, creating new session...`);
    }

    try {
      console.log(`🔧 Starting new WAHA session: ${this.instanceName}`);
      
      const sessionData = {
        name: this.instanceName,
        config: {
          webhooks: [
            {
              url: this.webhookUrl,
              events: ['message', 'session.status']
            }
          ]
        }
      };

      // WAHA uses /api/sessions/start endpoint for starting sessions
      const response = await this.api.post('/api/sessions/start', sessionData);
      
      console.log('✅ WAHA session started successfully');
      this.emitLog('✅ Sessão WAHA iniciada com sucesso');

      return response.data;
    } catch (error) {
      if (error.response?.status === 422) {
        console.log(`ℹ️ Session '${this.instanceName}' already started (422 error), continuing...`);
        this.emitLog('ℹ️ Sessão já iniciada, continuando...');
        return { alreadyStarted: true };
      }
      console.error('❌ Error starting session:', error.message);
      throw error;
    }
  }

  async setWebhook() {
    if (this.mockMode) {
      console.log('🧪 Mock mode: Webhook configuration set');
      this.emitLog('🧪 Modo mock: Webhook configurado');
      return { success: true, mock: true };
    }

    try {
      console.log(`🔗 Setting webhook for session: ${this.instanceName}`);
      console.log(`🌐 Webhook URL: ${this.webhookUrl || 'EMPTY (will use polling)'}`);
      this.emitLog('🔗 Configurando webhook...');
      
      // Check current session status first
      const currentSession = await this.getCurrentSessionStatus();
      if (!currentSession) {
        console.log('⚠️ No session exists, will create with webhook config');
        return await this.createSessionWithWebhookConfig();
      }

      // Check if we need to change webhook configuration
      const needsChange = await this.checkWebhookConfigChange(currentSession);
      if (!needsChange) {
        console.log('✅ Webhook configuration already correct, no changes needed');
        this.emitLog('✅ Configuração de webhook já está correta');
        return { success: true, unchanged: true };
      }

      // Try dynamic webhook configuration first (avoid session restart)
      const dynamicResult = await this.tryDynamicWebhookConfig();
      if (dynamicResult.success) {
        console.log('✅ Webhook configured dynamically without session restart');
        this.emitLog('✅ Webhook configurado sem reiniciar sessão');
        return dynamicResult;
      }

      // Fallback: restart session with new webhook config
      console.log('⚠️ Dynamic configuration failed, restarting session...');
      this.emitLog('⚠️ Configuração dinâmica falhou, reiniciando sessão...');
      
      const webhookConfig = this.webhookUrl && this.webhookUrl.trim() !== '' ? {
        webhooks: [
          {
            url: this.webhookUrl,
            events: ['message', 'message.any', 'state.change', 'group.join', 'group.leave']
          }
        ]
      } : null;

      return await this.restartSessionWithWebhookConfig(webhookConfig);
      
    } catch (error) {
      console.error('❌ Error setting webhook:', error.message);
      this.emitLog(`❌ Erro ao configurar webhook: ${error.message}`);
      
      // Don't throw error - webhook configuration is not critical for connection
      return { success: false, error: error.message };
    }
  }

  async setWebhookPaused(paused) {
    console.log(`🎛️ ${paused ? 'Pausing' : 'Resuming'} webhook processing`);
    
    this.webhookPaused = paused;
    
    if (paused) {
      console.log('⏸️ Webhook processing paused - switching to polling mode');
      this.emitLog('⏸️ Webhook pausado - usando polling');
      
      // Webhook paused - polling would be needed here but is disabled for performance
      // Messages will not be processed until webhook is unpaused
      console.log('⚠️ Webhook paused and polling disabled - messages will not be processed');
    } else {
      console.log('▶️ Webhook processing resumed - will use webhook if configured');
      this.emitLog('▶️ Webhook reativado');
      
      // Polling disabled for better performance - webhook is primary mechanism
      if (this.webhookUrl && this.webhookUrl.trim() !== '') {
        console.log('📞 Using webhook only - polling disabled');
      } else {
        console.log('⚠️ No webhook configured and polling disabled - no message processing available');
      }
    }
    
    console.log(`✅ Webhook pause state updated: ${paused ? 'PAUSED' : 'ACTIVE'}`);
    return { success: true, paused: this.webhookPaused };
  }

  async getCurrentSessionStatus() {
    try {
      const response = await this.api.get(`/api/sessions/${this.instanceName}`);
      return response.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // Session doesn't exist
      }
      throw error;
    }
  }

  async checkWebhookConfigChange(currentSession) {
    const currentWebhooks = currentSession.config?.webhooks || [];
    const desiredUrl = this.webhookUrl && this.webhookUrl.trim() !== '' ? this.webhookUrl : null;
    
    // If no webhooks configured and no URL desired
    if (currentWebhooks.length === 0 && !desiredUrl) {
      return false; // No change needed
    }
    
    // If webhooks configured but no URL desired (want to disable)
    if (currentWebhooks.length > 0 && !desiredUrl) {
      return true; // Need to remove webhooks
    }
    
    // If no webhooks but URL desired (want to enable)
    if (currentWebhooks.length === 0 && desiredUrl) {
      return true; // Need to add webhook
    }
    
    // If webhooks exist, check if URL matches
    const currentUrl = currentWebhooks[0]?.url;
    if (currentUrl !== desiredUrl) {
      return true; // URL changed
    }
    
    return false; // Configuration is the same
  }

  async tryDynamicWebhookConfig() {
    const desiredUrl = this.webhookUrl && this.webhookUrl.trim() !== '' ? this.webhookUrl : null;
    
    try {
      // Try Method 1: PUT webhook URL directly (for both setting and removing)
      console.log(`🔧 Trying dynamic webhook configuration via PUT... (${desiredUrl ? 'SET' : 'REMOVE'})`);
      try {
        const payload = desiredUrl ? {
          url: desiredUrl,
          events: ['message', 'message.any', 'state.change', 'group.join', 'group.leave']
        } : {
          url: '',  // Empty URL to disable webhook
          events: []
        };
        
        const response = await this.api.put(`/api/sessions/${this.instanceName}/webhook`, payload);
        console.log(`✅ Dynamic webhook ${desiredUrl ? 'configuration' : 'removal'} successful via PUT`);
        this.currentWebhookConfig = desiredUrl ? { url: desiredUrl } : null;
        return { success: true, method: 'PUT', data: response.data };
      } catch (putError) {
        console.log('⚠️ PUT webhook method failed:', putError.message);
      }

      // Try Method 2: PATCH session config
      console.log(`🔧 Trying dynamic webhook configuration via PATCH... (${desiredUrl ? 'SET' : 'REMOVE'})`);
      const configUpdate = desiredUrl ? {
        config: {
          webhooks: [
            {
              url: desiredUrl,
              events: ['message', 'message.any', 'state.change', 'group.join', 'group.leave']
            }
          ]
        }
      } : {
        config: {
          webhooks: []  // Empty array to disable webhooks
        }
      };

      try {
        const response = await this.api.patch(`/api/sessions/${this.instanceName}`, configUpdate);
        console.log(`✅ Dynamic webhook ${desiredUrl ? 'configuration' : 'removal'} successful via PATCH`);
        this.currentWebhookConfig = desiredUrl ? { url: desiredUrl } : null;
        return { success: true, method: 'PATCH', data: response.data };
      } catch (patchError) {
        console.log('⚠️ PATCH webhook method failed:', patchError.message);
      }

      // Try Method 3: POST config update (only for setting webhooks, not removing)
      if (desiredUrl) {
        console.log('🔧 Trying dynamic webhook configuration via POST config...');
        try {
          const response = await this.api.post(`/api/sessions/${this.instanceName}/config`, {
            webhooks: [
              {
                url: desiredUrl,
                events: ['message', 'message.any', 'state.change', 'group.join', 'group.leave']
              }
            ]
          });
          console.log('✅ Dynamic webhook configuration successful via POST config');
          this.currentWebhookConfig = { url: desiredUrl };
          return { success: true, method: 'POST_CONFIG', data: response.data };
        } catch (postError) {
          console.log('⚠️ POST config webhook method failed:', postError.message);
        }
      } else {
        console.log('ℹ️ Skipping POST config method for webhook removal (URL is empty)');
      }

      console.log('❌ All dynamic webhook configuration methods failed');
      return { success: false, error: 'All dynamic methods failed' };

    } catch (error) {
      console.log('❌ Error in dynamic webhook configuration:', error.message);
      return { success: false, error: error.message };
    }
  }

  async createSessionWithWebhookConfig() {
    const webhookConfig = this.webhookUrl && this.webhookUrl.trim() !== '' ? {
      webhooks: [
        {
          url: this.webhookUrl,
          events: ['message', 'message.any', 'state.change', 'group.join', 'group.leave']
        }
      ]
    } : {};

    const sessionConfig = {
      name: this.instanceName,
      config: webhookConfig
    };

    try {
      console.log('🚀 Creating new session with webhook config:', JSON.stringify(sessionConfig, null, 2));
      const response = await this.api.post('/api/sessions/start', sessionConfig);
      
      if (webhookConfig.webhooks) {
        this.currentWebhookConfig = webhookConfig.webhooks[0];
        console.log(`✅ New session created with webhook: ${this.webhookUrl}`);
        this.emitLog(`✅ Nova sessão criada com webhook: ${this.webhookUrl}`);
      } else {
        this.currentWebhookConfig = null;
        console.log('✅ New session created with polling mode');
        this.emitLog('✅ Nova sessão criada em modo polling');
      }

      return { success: true, created: true, data: response.data };
    } catch (error) {
      console.error('❌ Error creating session with webhook config:', error.message);
      return { success: false, error: error.message };
    }
  }

  async restartSessionWithWebhookConfig(webhookConfig) {
    try {
      console.log(`🔄 Restarting session ${this.instanceName} with webhook config...`);
      this.emitLog('🔄 Reiniciando sessão para configurar webhook...');
      
      // First, check if session exists and get its current state
      let sessionExists = false;
      let wasConnected = false;
      
      try {
        const response = await this.api.get(`/api/sessions/${this.instanceName}`);
        sessionExists = true;
        wasConnected = response.data.status === 'WORKING';
        console.log(`📊 Session exists with status: ${response.data.status}`);
      } catch (error) {
        if (error.response?.status !== 404) {
          throw error;
        }
        console.log('📊 Session does not exist, will create new one');
      }

      // If session exists, stop it first using correct WAHA endpoints
      if (sessionExists) {
        console.log(`🛑 Stopping existing session: ${this.instanceName}`);
        try {
          // Try different stop endpoints that WAHA supports
          try {
            await this.api.delete(`/api/sessions/${this.instanceName}`);
            console.log('✅ Session stopped successfully via DELETE /api/sessions/name');
          } catch (firstTry) {
            console.log('⚠️ First stop method failed, trying alternative...');
            try {
              await this.api.post(`/api/sessions/${this.instanceName}/stop`);
              console.log('✅ Session stopped successfully via POST /api/sessions/name/stop');
            } catch (secondTry) {
              console.log('⚠️ Second stop method failed, trying logout...');
              await this.api.post(`/api/sessions/${this.instanceName}/logout`);
              console.log('✅ Session logged out successfully');
            }
          }
          
          // Wait a bit for session to fully stop
          await new Promise(resolve => setTimeout(resolve, 3000));
        } catch (stopError) {
          console.log('⚠️ All session stop methods failed:', stopError.message);
          // Continue anyway, sometimes sessions can be restarted even if stop fails
        }
      }

      // Prepare session configuration
      const sessionConfig = {
        name: this.instanceName,
        config: {}
      };

      // Add webhook configuration if provided
      if (webhookConfig) {
        sessionConfig.config = webhookConfig;
        console.log('✅ Webhook configuration added to session config');
      }

      // Start session with webhook configuration
      console.log(`🚀 Starting session with configuration:`, JSON.stringify(sessionConfig, null, 2));
      
      try {
        const startResponse = await this.api.post('/api/sessions/start', sessionConfig);
        console.log('✅ Session started successfully with webhook configuration');
        this.emitLog('✅ Sessão reiniciada com configuração de webhook');
      } catch (startError) {
        if (startError.response?.status === 422) {
          console.log('⚠️ Session already exists, trying to update configuration...');
          // If session already exists, try to restart it
          try {
            await this.api.post(`/api/sessions/${this.instanceName}/restart`, sessionConfig.config);
            console.log('✅ Session restarted with new configuration');
            this.emitLog('✅ Sessão reiniciada com nova configuração');
          } catch (restartError) {
            console.log('⚠️ Session restart failed, trying alternative approach...');
            // As a fallback, just create the session normally
            try {
              await this.api.post('/api/sessions/', sessionConfig);
              console.log('✅ Session created with webhook configuration');
              this.emitLog('✅ Sessão criada com configuração de webhook');
            } catch (createError) {
              throw new Error(`Failed to configure webhook: ${createError.message}`);
            }
          }
        } else {
          throw startError;
        }
      }

      // Update our webhook tracking
      if (webhookConfig) {
        this.currentWebhookConfig = webhookConfig.webhooks[0];
        console.log(`✅ Webhook configured successfully: ${this.webhookUrl}`);
        this.emitLog(`✅ Webhook configurado: ${this.webhookUrl}`);
      } else {
        this.currentWebhookConfig = null;
        console.log('✅ Session configured for polling only');
        this.emitLog('✅ Sessão configurada para polling apenas');
      }

      // If session was previously connected, we may need to re-authenticate
      if (wasConnected) {
        console.log('📱 Session was previously connected, checking auth status...');
        // The session restart will handle re-authentication automatically
        // We'll check status in the next sync cycle
      }

      // Sync with the restarted session
      setTimeout(async () => {
        try {
          await this.syncWithExistingSession();
        } catch (error) {
          console.error('⚠️ Error syncing after session restart:', error.message);
        }
      }, 3000);

      return { 
        success: true, 
        restarted: true,
        webhook: webhookConfig ? this.webhookUrl : null,
        mode: webhookConfig ? 'webhook' : 'polling'
      };

    } catch (error) {
      console.error('❌ Error restarting session with webhook config:', error.message);
      this.emitLog(`❌ Erro ao reiniciar sessão: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async checkAuthStatus() {
    if (this.mockMode) {
      console.log(`🧪 Mock mode: Checking auth status: ${this.instanceName}`);

      // Simulate connection after 10 seconds
      setTimeout(() => {
        this.isConnected = true;
        this.connectionStatus = 'connected';
        this.phoneNumber = '5521994452153';
        this.currentQRCode = null;
        this.emitStatus();
        this.emitLog('✅ WhatsApp conectado com sucesso (modo mock)!');
        console.log('✅ WhatsApp connected successfully (mock mode)');
      }, 10000);

      return;
    }

    try {
      console.log(`🔍 Checking authentication status: ${this.instanceName}`);
      
      const response = await this.api.get(`/api/sessions/${this.instanceName}`);
      const status = response.data.status;
      const sessionData = response.data;
      
      console.log(`📊 Auth status: ${status}`);
      console.log(`📱 Session data:`, JSON.stringify(sessionData, null, 2));
      
      // Extract webhook configuration from session data
      this.extractWebhookConfig(sessionData);

      if (status === 'WORKING') {
        this.isConnected = true;
        this.connectionStatus = 'connected';
        this.phoneNumber = sessionData.me?.id?.split('@')[0] || sessionData.me?.phone;
        this.currentQRCode = null;
        this.emitStatus();
        this.emitLog('✅ WhatsApp já estava conectado');
        console.log('✅ WhatsApp already connected');
        console.log(`📱 Phone number: ${this.phoneNumber}`);

        // Mark initialization as complete after a brief delay to avoid processing startup messages
        setTimeout(() => {
          this.isInitializing = false;
          this.initializationComplete = true;
          console.log('✅ WAHA initialization complete - now processing new messages only');
        }, 5000); // 5 second delay to let any startup messages settle

        return;
      }

      // If not connected, try to get QR code
      if (status === 'SCAN_QR_CODE' || status === 'STARTING') {
        this.connectionStatus = 'connecting';
        this.emitStatus();
        await this.fetchQRCode();
      }

      // Wait for connection if not already connected
      if (status !== 'WORKING') {
        await this.waitForConnection();
      }

    } catch (error) {
      console.error('❌ Error checking auth status:', error.message);
      this.connectionStatus = 'error';
      this.emitStatus();
      throw error;
    }
  }

  async fetchQRCode() {
    try {
      console.log(`🔍 Fetching QR Code for session: ${this.instanceName}`);
      
      // WAHA uses /api/{session}/auth/qr endpoint that returns PNG image directly
      const response = await this.api.get(`/api/${this.instanceName}/auth/qr`, {
        responseType: 'arraybuffer'
      });
      
      if (response.data) {
        // Convert binary PNG to base64
        const base64 = Buffer.from(response.data, 'binary').toString('base64');
        this.currentQRCode = base64;
        this.qrCode = base64;
        
        console.log('📱 QR Code received from WAHA API (PNG format)');
        this.emitLog('📱 QR Code recebido - escaneie para conectar');
        
        // Emit QR code to frontend
        if (this.messageHandler && this.messageHandler.io) {
          this.messageHandler.io.emit('whatsapp_qr', { qr: this.currentQRCode });
        }
        
        this.emitStatus();
        return;
      }
      
      console.log('⏰ QR Code not available yet, will try again...');
      this.emitLog('⏰ QR Code será gerado em breve...');
      
    } catch (error) {
      console.error('Error fetching QR code:', error.message);
      this.emitLog('⚠️ Erro ao buscar QR Code - tentando novamente...');
    }
  }

  async waitForConnection() {
    console.log('⏰ Waiting for QR code scan or connection...');
    this.emitLog('⏰ Aguardando escaneamento do QR code...');
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Connection timeout after 90 seconds'));
      }, 90000);

      const checkStatus = async () => {
        try {
          const response = await this.api.get(`/api/sessions/${this.instanceName}`);
          const status = response.data.status;
          
          // Extract webhook configuration from session data
          this.extractWebhookConfig(response.data);
          
          if (status === 'WORKING') {
            clearTimeout(timeout);
            this.isConnected = true;
            this.connectionStatus = 'connected';
            this.phoneNumber = response.data.me?.phone || response.data.me?.id?.split('@')[0];
            this.currentQRCode = null;
            this.emitStatus();
            this.emitLog('✅ WhatsApp conectado com sucesso!');
            console.log('✅ WhatsApp connected successfully');

            // Mark initialization as complete after a brief delay to avoid processing startup messages
            setTimeout(() => {
              this.isInitializing = false;
              this.initializationComplete = true;
              console.log('✅ WAHA initialization complete - now processing new messages only');
            }, 5000); // 5 second delay to let any startup messages settle

            resolve();
            return;
          }

          // Check for QR code if still starting
          if (status === 'SCAN_QR_CODE' || status === 'STARTING') {
            await this.fetchQRCode();
          }

          // Continue checking
          setTimeout(checkStatus, 2000);
          
        } catch (error) {
          console.error('Error checking connection status:', error);
          setTimeout(checkStatus, 5000);
        }
      };

      checkStatus();
    });
  }

  async disconnect() {
    try {
      console.log('🔌 Disconnecting WAHA API...');
      
      if (this.instanceName) {
        await this.api.delete(`/api/sessions/${this.instanceName}/stop`);
      }
      
      this.isConnected = false;
      this.connectionStatus = 'disconnected';
      this.currentQRCode = null;
      this.phoneNumber = null;
      
      this.emitStatus();
      this.emitLog('🔌 Desconectado do WhatsApp');
      console.log('✅ Disconnected from WhatsApp');
      
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  }

  async sendMessage(phoneNumber, message, options = {}) {
    if (!this.isConnected) {
      throw new Error('WhatsApp not connected');
    }

    try {
      console.log(`📤 Sending message to ${phoneNumber}: ${message}`);
      this.emitLog(`📤 Enviando mensagem para ${phoneNumber}`);

      // Normalize phone number for WAHA format
      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);
      const chatId = `${normalizedPhone}@c.us`;

      const messageData = {
        chatId: chatId,
        text: message,
        session: this.instanceName
      };

      const response = await this.api.post('/api/sendText', messageData);

      console.log('✅ Message sent successfully via WAHA API');
      this.emitLog('✅ Mensagem enviada com sucesso');

      return {
        success: true,
        messageId: response.data.id,
        data: response.data
      };

    } catch (error) {
      console.error('❌ Error sending message via WAHA API:', error);
      this.emitLog(`❌ Erro ao enviar mensagem: ${error.message}`);
      throw error;
    }
  }

  async simulateTyping(phoneNumber, duration = 3000) {
    if (!this.isConnected) {
      return false;
    }

    try {
      console.log(`⌨️ Simulating typing to ${phoneNumber} for ${duration}ms...`);
      this.emitLog(`⌨️ Simulando digitação para ${phoneNumber}`);

      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);
      const chatId = `${normalizedPhone}@c.us`;

      // Send typing indicator
      await this.api.post('/api/sendSeen', {
        chatId: chatId,
        session: this.instanceName
      });

      // Wait for duration
      await new Promise(resolve => setTimeout(resolve, duration));

      console.log(`✅ Typing simulation completed for ${phoneNumber}`);
      this.emitLog(`✅ Simulação de digitação concluída`);
      return true;

    } catch (error) {
      console.error('❌ Error simulating typing:', error);
      return false;
    }
  }

  async loadHistoryFromWhatsApp(phoneNumber, limit = 10) {
    console.log(`📚 Loading WhatsApp history for ${phoneNumber} (limit: ${limit})...`);
    
    if (!this.isConnected) {
      console.log('❌ WhatsApp not connected, returning mock history for testing');
      return this.generateMockHistory(phoneNumber, limit);
    }

    return this._loadHistoryMessages(phoneNumber, limit);
  }

  // Internal method to load history without AI processing
  async _loadHistoryMessages(phoneNumber, limit = 10) {
    try {
      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);
      const chatId = `${normalizedPhone}@c.us`;

      // Fetch messages from WAHA API
      const response = await this.api.get(`/api/messages?chatId=${chatId}&limit=${limit}&session=${this.instanceName}`);

      const messages = response.data || [];
      const history = [];

      for (const msg of messages.reverse()) { // Reverse to get chronological order
        try {
          let messageContent = msg.text || msg.caption || '';
          let messageType = 'text';
          let audioData = null;
          let audioDuration = null;

          // Handle different message types
          // Check for audio messages - WAHA API may not set type correctly, so check for audio indicators
          const isAudioMessage = (
            msg.type === 'audio' ||
            msg.type === 'voice' ||
            (msg.hasMedia && msg.mediaUrl && (
              msg.mediaUrl.includes('.oga') ||
              msg.mediaUrl.includes('.ogg') ||
              msg.mediaUrl.includes('.mp3') ||
              msg.mediaUrl.includes('.wav') ||
              msg.mediaUrl.includes('.m4a')
            )) ||
            (msg.hasMedia && msg._data && msg._data.duration) // Duration indicates audio
          );

          if (isAudioMessage) {
            messageType = 'audio';
            if (msg.mediaUrl) {
              audioData = msg.mediaUrl;
              // Try to get duration from various sources
              audioDuration = msg.duration ||
                             (msg._data && msg._data.duration) ||
                             (msg.media && msg.media.duration) ||
                             10; // Default fallback
              messageContent = 'Mensagem de áudio';
              console.log(`🎤 Historical audio message detected: ${msg.id}, duration: ${audioDuration}s, url: ${msg.mediaUrl}`);
            }
          } else if (msg.type === 'image' || msg.type === 'document' || msg.type === 'video') {
            messageContent = `[${msg.type.toUpperCase()}]`;
            messageType = msg.type;
          }

          // Determine sender (customer vs salon)
          const isFromCustomer = !msg.fromMe;

          const historyMessage = {
            id: msg.id,
            content: messageContent,
            sender: isFromCustomer ? 'user' : 'ai',
            timestamp: new Date(msg.timestamp * 1000),
            status: 'delivered',
            type: messageType,
            whatsappMessage: true,
            isHistoricalMessage: true, // Mark as historical to prevent AI processing
            originalWhatsAppTimestamp: msg.timestamp // Keep original timestamp for reference
          };

          // Add audio fields if applicable
          if (messageType === 'audio' && audioData) {
            // Create unique audio ID and store the audio data
            const { v4: uuidv4 } = require('uuid');
            const audioId = uuidv4();

            // Store audio data for serving (assuming messageHandler is available)
            if (this.messageHandler && this.messageHandler.storeAudioData) {
              this.messageHandler.storeAudioData(audioId, audioData);
              historyMessage.audioUrl = `/api/audio/${audioId}`;
            } else {
              historyMessage.audioUrl = audioData; // Fallback to direct data
            }

            historyMessage.audioDuration = audioDuration;

            // Process transcription for historical audio messages
            if (isFromCustomer) { // Only transcribe customer audio messages
              try {
                console.log(`🎤 Processing transcription for historical audio message from ${normalizedPhone}`);
                const aiService = require('../ai');
                const aiResult = await aiService.processMessage(normalizedPhone, audioData, null, 'audio');

                if (aiResult.transcription) {
                  historyMessage.audioTranscription = aiResult.transcription;
                  console.log(`✅ Historical audio transcription: "${aiResult.transcription}"`);
                } else {
                  console.log(`⚠️ No transcription returned for historical audio message`);
                }
              } catch (transcriptionError) {
                console.warn(`⚠️ Error transcribing historical audio message: ${transcriptionError.message}`);
                // Don't fail the entire history load if transcription fails
              }
            }
          }

          history.push(historyMessage);

        } catch (msgError) {
          console.warn(`⚠️ Error processing history message: ${msgError.message}`);
        }
      }

      console.log(`✅ Loaded ${history.length} messages from WhatsApp history (marked as historical)`);
      return history;

    } catch (error) {
      console.error(`❌ Error loading WhatsApp history: ${error.message}`);
      return [];
    }
  }

  async loadAllChats() {
    console.log(`📱 loadAllChats called - Loading all WAHA chats...`);
    console.log(`📱 WhatsApp connection status: isConnected=${this.isConnected}, status=${this.connectionStatus}`);
    
    if (!this.isConnected) {
      console.log('❌ WhatsApp not connected, generating mock chats for testing');
      return this.generateMockChats();
    }

    try {
      // Fetch all chats from WAHA API
      const response = await this.api.get(`/api/${this.instanceName}/chats`);

      const chats = response.data || [];
      console.log(`📱 Found ${chats.length} chats in WAHA`);

      // Process chat data to extract relevant information
      const processedChats = chats.map(chat => {
        // Safely extract chat ID
        const chatId = chat.id || chat.chatId || '';
        const phoneNumber = typeof chatId === 'string' ? chatId.replace('@c.us', '') : '';
        
        return {
          id: phoneNumber,
          name: chat.name || chat.pushName || 'Cliente',
          lastMessageTime: chat.lastMessage?.timestamp ? new Date(chat.lastMessage.timestamp * 1000) : null,
          unreadCount: chat.unreadCount || 0,
          isGroup: typeof chatId === 'string' && chatId.includes('@g.us'),
          rawData: chat // Keep original data for debugging
        };
      }).filter(chat => {
        // Filter out groups and empty IDs
        return !chat.isGroup && chat.id && chat.id.length > 0;
      });

      console.log(`✅ Processed ${processedChats.length} individual chats from WAHA`);
      return processedChats;

    } catch (error) {
      console.error(`❌ Error loading WAHA chats: ${error.message}`);
      
      // If the API endpoint doesn't exist, try alternative endpoint
      if (error.response?.status === 404) {
        console.log('🔄 Trying alternative chats endpoint...');
        try {
          const alternativeResponse = await this.api.get(`/api/sessions/${this.instanceName}/chats`);
          const chats = alternativeResponse.data || [];
          console.log(`📱 Found ${chats.length} chats using alternative endpoint`);
          
          const processedChats = chats.map(chat => {
            const chatId = chat.id || chat.chatId || '';
            const phoneNumber = typeof chatId === 'string' ? chatId.replace('@c.us', '') : '';
            
            return {
              id: phoneNumber,
              name: chat.name || chat.pushName || 'Cliente',
              lastMessageTime: chat.lastMessage?.timestamp ? new Date(chat.lastMessage.timestamp * 1000) : null,
              unreadCount: chat.unreadCount || 0,
              isGroup: typeof chatId === 'string' && chatId.includes('@g.us'),
              rawData: chat
            };
          }).filter(chat => !chat.isGroup && chat.id && chat.id.length > 0);
          
          console.log(`✅ Processed ${processedChats.length} individual chats using alternative endpoint`);
          return processedChats;
          
        } catch (altError) {
          console.error(`❌ Alternative endpoint also failed: ${altError.message}`);
          return [];
        }
      }
      
      return [];
    }
  }

  async clearSession() {
    try {
      console.log('🧹 Clearing WhatsApp session...');
      this.emitLog('🧹 Limpando sessão do WhatsApp...');

      // Stop the session
      if (this.isConnected) {
        console.log('📱 Stopping WhatsApp session...');
        try {
          await this.api.post(`/api/sessions/${this.instanceName}/stop`);
          this.emitLog('📱 Sessão parada');
        } catch (error) {
          console.log('⚠️ Stop session failed, continuing with deletion...');
        }
      }

      // Delete the session
      console.log('🗑️ Deleting WhatsApp session...');
      try {
        await this.api.delete(`/api/sessions/${this.instanceName}`);
        this.emitLog('🗑️ Sessão removida');
      } catch (error) {
        console.log('⚠️ Session deletion failed:', error.message);
      }

      // Reset connection state
      this.isConnected = false;
      this.connectionStatus = 'disconnected';
      this.phoneNumber = null;
      this.currentQRCode = null;

      // Emit updated status
      this.emitStatus();
      console.log('✅ WhatsApp session cleared successfully');
      this.emitLog('✅ Sessão limpa com sucesso!');

      return {
        success: true,
        message: 'Sessão limpa com sucesso'
      };

    } catch (error) {
      console.error('❌ Error clearing WhatsApp session:', error);
      this.emitLog(`❌ Erro ao limpar sessão: ${error.message}`);
      throw error;
    }
  }

  // Handle incoming webhook from WAHA API
  async handleWebhook(webhookData) {
    try {
      console.log('📱 WAHA API webhook received:', JSON.stringify(webhookData, null, 2));
      debugLogger.logWebhookReceived('waha', webhookData);

      const { event, payload } = webhookData;

      switch (event) {
        case 'session.status':
          await this.handleSessionStatus(payload);
          break;
        case 'message':
          if (this.webhookPaused) {
            console.log('⏸️ Webhook paused - ignoring webhook message (will be processed by polling)');
            break;
          }
          await this.handleIncomingMessage(payload);
          break;
        case 'auth_failure':
          await this.handleAuthFailure(payload);
          break;
        case 'ready':
          await this.handleReady(payload);
          break;
        default:
          console.log(`📱 Unhandled webhook event: ${event}`);
          this.emitLog(`📱 Evento webhook não tratado: ${event}`);
      }

      debugLogger.logWebhookProcessed('waha', true);
    } catch (error) {
      console.error('❌ Error handling WAHA API webhook:', error);
      this.emitLog(`❌ Erro no webhook: ${error.message}`);
      debugLogger.logWebhookProcessed('waha', false, error);
    }
  }

  async handleSessionStatus(payload) {
    console.log('🔄 Session status webhook:', payload);
    
    const status = payload.status;
    
    if (status === 'WORKING') {
      this.isConnected = true;
      this.connectionStatus = 'connected';
      this.currentQRCode = null;
      this.phoneNumber = payload.me?.phone || payload.me?.id?.split('@')[0];
      
      console.log('✅ WhatsApp connected via webhook');
      this.emitLog('✅ WhatsApp conectado com sucesso!');
      
    } else if (status === 'SCAN_QR_CODE') {
      this.isConnected = false;
      this.connectionStatus = 'connecting';
      
      console.log('📱 QR code scan required via webhook');
      this.emitLog('📱 Escaneamento de QR code necessário');
      
      // Try to fetch QR code
      await this.fetchQRCode();
      
    } else if (status === 'FAILED' || status === 'STOPPED') {
      this.isConnected = false;
      this.connectionStatus = 'disconnected';
      this.phoneNumber = null;
      
      console.log('❌ WhatsApp disconnected via webhook');
      this.emitLog('❌ WhatsApp desconectado');
    }

    this.emitStatus();
  }

  async handleIncomingMessage(payload) {
    try {
      // Skip own messages
      if (payload.fromMe) {
        return;
      }

      console.log(`📱 WhatsApp message received from ${payload.from}`);

      // Extract phone number
      const phoneNumber = this.normalizePhoneNumber(payload.from.replace('@c.us', ''));

      // Extract message content and type
      let messageContent = payload.text || payload.body || payload.caption || '';
      let messageType = 'text';
      let audioData = null;
      let audioDuration = null;

      // Skip empty messages (first line of defense)
      if (messageType !== 'audio' && (!messageContent || messageContent.trim().length === 0)) {
        console.log(`📝 Empty message ignored from ${phoneNumber} - no processing needed`);
        return;
      }

      // Handle audio messages
      console.log(`🎤 AUDIO DEBUG: Checking message type - payload.type: "${payload.type}", hasMedia: ${payload.hasMedia}, mediaUrl: ${!!payload.mediaUrl}`);
      
      if (payload.type === 'audio' || payload.type === 'voice' || payload.type === 'ptt' ||
          (payload.hasMedia && (!payload.type || payload.type === 'undefined' || payload.type === undefined))) {
        console.log(`🎤 AUDIO DETECTED: Setting messageType to 'audio'`);
        messageType = 'audio';
        
        if (payload.mediaUrl) {
          audioData = payload.mediaUrl;
          audioDuration = payload.duration || 10;
          messageContent = 'Mensagem de áudio';
          console.log(`🎤 AUDIO DATA SET: audioData length: ${audioData?.length}, duration: ${audioDuration}`);
        } else if (payload.hasMedia && payload.media) {
          // Try to get audio from media object
          let mediaUrl = payload.media.url || payload.media.data;
          const mimetype = payload.media.mimetype || '';
          
          console.log(`🎤 WEBHOOK: Got media URL: ${!!mediaUrl}, mimetype: ${mimetype}`);
          
          // Check if we need to convert the audio format
          if (mediaUrl && !mimetype.includes('opus') && !mimetype.includes('ogg')) {
            console.log(`🎤 WEBHOOK: Audio format not browser-compatible, attempting conversion...`);
            try {
              const convertResponse = await this.api.post(`/api/${this.instanceName}/media/convert/voice`, {
                url: mediaUrl
              }, {
                headers: {
                  'Accept': 'application/json'
                }
              });
              
              if (convertResponse.data && convertResponse.data.data) {
                mediaUrl = `data:${convertResponse.data.mimetype};base64,${convertResponse.data.data}`;
                console.log(`🎤 WEBHOOK: Successfully converted audio to browser-compatible format`);
              }
            } catch (convertError) {
              console.log(`⚠️ WEBHOOK: Audio conversion failed: ${convertError.message}`);
            }
          }
          
          if (mediaUrl) {
            audioData = mediaUrl;
            audioDuration = payload.media.duration || payload.duration || 10;
            messageContent = 'Mensagem de áudio';
            console.log(`🎤 WEBHOOK: Audio data set from media object`);
          }
        } else {
          console.log(`🎤 WARNING: Audio message detected but no mediaUrl found`);
        }
      } else {
        console.log(`🎤 NOT AUDIO: Message type is "${payload.type}", treating as text`);
      }

      // Get customer name
      const customerName = payload.fromName || phoneNumber;

      // Send to message handler
      if (this.messageHandler) {
        console.log('🔄 Forwarding message to message handler...');
        await this.messageHandler.handleWhatsAppMessage({
          customerPhone: phoneNumber,
          customerName: customerName,
          content: messageContent,
          type: messageType,
          audioData: audioData,
          audioDuration: audioDuration,
          originalMessage: {
            ...payload,
            isHistoricalMessage: false, // This is a real-time message
            isNewMessage: true
          }
        });
        console.log('✅ Message forwarded to handler successfully');
      } else {
        console.log('❌ No message handler available - message will not be processed');
        this.emitLog('❌ Handler de mensagens não disponível');
      }

    } catch (error) {
      console.error('❌ Error handling incoming message from webhook:', error);
      this.emitLog(`❌ Erro ao processar mensagem: ${error.message}`);
    }
  }

  async handleAuthFailure(payload) {
    console.log('❌ Authentication failure:', payload);
    this.isConnected = false;
    this.connectionStatus = 'error';
    this.emitStatus();
    this.emitLog('❌ Falha na autenticação do WhatsApp');
  }

  async handleReady(payload) {
    console.log('✅ WhatsApp ready:', payload);
    this.isConnected = true;
    this.connectionStatus = 'connected';
    this.currentQRCode = null;
    this.phoneNumber = payload.me?.phone || payload.me?.id?.split('@')[0];
    this.emitStatus();
    this.emitLog('✅ WhatsApp pronto e conectado!');
  }

  // Generate mock QR code for testing
  generateMockQRCode() {
    // Generate a fake QR code string
    const mockQR = 'mock-qr-code-waha-' + Date.now();
    this.currentQRCode = mockQR;
    this.qrCode = mockQR;

    console.log('📱 Mock QR Code generated');
    this.emitLog('📱 QR Code mock gerado para teste');

    // Emit QR code to frontend
    if (this.messageHandler && this.messageHandler.io) {
      this.messageHandler.io.emit('whatsapp_qr', { qr: this.currentQRCode });
    }

    this.emitStatus();
  }

  // Generate mock history for testing
  generateMockHistory(phoneNumber, limit = 10) {
    console.log(`🧪 Generating ${limit} mock messages for ${phoneNumber}...`);

    const mockMessages = [];
    const now = Date.now();

    // Create a mix of text and audio messages for testing
    const messageTemplates = [
      { type: 'text', content: 'Olá! Gostaria de agendar um horário via WAHA', sender: 'user' },
      { type: 'text', content: 'Claro! Que tipo de serviço você gostaria?', sender: 'ai' },
      {
        type: 'audio',
        content: 'Mensagem de áudio',
        sender: 'user',
        audioUrl: 'data:audio/ogg;base64,SUQzAwAAAAAAI1RTU0UAAAAPAAADTGF2ZjU4LjI5LjEwMAAAAAAAAAAAAAAA',
        audioDuration: 8,
        audioTranscription: 'Gostaria de fazer um corte e barba via WAHA'
      },
      { type: 'text', content: 'Perfeito! Temos horários hoje à tarde', sender: 'ai' },
      { type: 'text', content: 'Agendado via WAHA! Até logo!', sender: 'ai' }
    ];

    // Generate messages up to the limit
    for (let i = 0; i < Math.min(limit, messageTemplates.length); i++) {
      const template = messageTemplates[i];
      const messageId = `mock-waha-${phoneNumber}-${i}`;
      const timestamp = new Date(now - (limit - i) * 60000);

      const message = {
        id: messageId,
        content: template.content,
        sender: template.sender,
        timestamp,
        status: 'delivered',
        type: template.type,
        whatsappMessage: true
      };

      // Add audio-specific fields
      if (template.type === 'audio') {
        message.audioUrl = template.audioUrl;
        message.audioDuration = template.audioDuration;
        message.audioTranscription = template.audioTranscription;
      }

      mockMessages.push(message);
    }

    console.log(`🧪 Generated ${mockMessages.length} mock WAHA messages`);
    return mockMessages;
  }

  // Generate mock chats for testing when not connected
  generateMockChats() {
    console.log(`🧪 Generating mock chats for testing...`);
    
    const mockChatsData = [
      {
        id: '5511999999001',
        name: 'Maria Silva',
        lastMessageTime: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        unreadCount: 2,
        isGroup: false
      },
      {
        id: '5511999999002', 
        name: 'João Santos',
        lastMessageTime: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        unreadCount: 0,
        isGroup: false
      },
      {
        id: '5511999999003',
        name: 'Ana Costa',
        lastMessageTime: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        unreadCount: 1,
        isGroup: false
      }
    ];

    console.log(`🧪 Generated ${mockChatsData.length} mock chats`);
    return mockChatsData;
  }

  // Auto-connect method for backward compatibility
  async tryAutoConnect() {
    try {
      console.log('🔍 Checking for auto-connect on startup...');

      if (this.settings.whatsappEnabled) {
        console.log('🚀 Auto-connecting WhatsApp with WAHA API...');
        await this.connect();
      } else {
        console.log('❌ WhatsApp integration is disabled');
      }
    } catch (error) {
      console.error('❌ Auto-connect failed:', error.message);
      throw error;
    }
  }

  // Polling system for WAHA Core webhook backup
  startPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
    }
    
    console.log('🔄 Starting message polling for WAHA Core backup...');
    debugLogger.logPollingStart('waha');
    
    // Poll every 5 seconds for new messages (increased frequency for better responsiveness)
    this.pollingInterval = setInterval(async () => {
      if (this.isConnected && !this.mockMode && !this.isInitializing) {
        try {
          // Check session status first
          const sessionResponse = await this.api.get(`/api/sessions/${this.instanceName}`);
          console.log(`🔄 [POLLING] Status da sessão: ${sessionResponse.data.status}`);
          
          // Extract webhook configuration from session data
          this.extractWebhookConfig(sessionResponse.data);
          
          if (sessionResponse.data.status !== 'WORKING') {
            console.log('⚠️ Session no longer working, reconnecting...');
            this.isConnected = false;
            this.connectionStatus = 'disconnected';
            this.emitStatus();
            debugLogger.logPollingResult('waha', false, []);
          } else {
            // Session is working, poll for new messages
            console.log(`📡 [POLLING] Sessão WORKING - verificando mensagens...`);
            await this.pollForNewMessages();
          }
        } catch (error) {
          console.error('❌ Polling error:', error.message);
          debugLogger.logPollingResult('waha', false, []);
        }
      } else {
        console.log(`🔄 [POLLING] Skipped - isConnected:${this.isConnected}, mockMode:${this.mockMode}, isInitializing:${this.isInitializing}`);
      }
    }, 5000); // Poll every 5 seconds instead of 30
  }

  stopPolling() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
      console.log('⏹️ Stopped message polling');
    }
  }

  async pollForNewMessages() {
    try {
      // Try different WAHA API endpoints to get messages
      
      // Method 1: Try to get all chats with unread messages
      try {
        const chatsResponse = await this.api.get(`/api/${this.instanceName}/chats`);
        const chats = chatsResponse.data || [];
        
        console.log(`📊 Found ${chats.length} chats to check`);
        
        // Check chats with unread count OR recent activity
        for (const chat of chats) {
          // Handle different ID formats from WAHA
          const chatId = typeof chat.id === 'object' ? chat.id._serialized : chat.id;
          const isGroup = chatId ? chatId.includes('@g.us') : false;
          
          console.log(`🔍 DETAILED DEBUG: Chat analysis - ID: ${chatId}, UnreadCount: ${chat.unreadCount}, IsGroup: ${isGroup}, Timestamp: ${chat.timestamp}`);
          
          if (chat.unreadCount && chat.unreadCount > 0 && !isGroup) {
            console.log(`📱 Chat ${chatId} has ${chat.unreadCount} unread messages - CALLING fetchMessagesFromChat`);
            await this.fetchMessagesFromChat(chatId, chat.unreadCount);
          } else if (!isGroup) {
            // Even if unreadCount is 0, check for recent messages in case unreadCount is not reliable
            console.log(`🔍 Checking recent messages for ${chatId} even with unreadCount=0 (timestamp: ${chat.timestamp})`);
            await this.fetchRecentMessagesFromChat(chatId, 5); // Check last 5 messages
          } else {
            console.log(`⏭️ Skipping group chat ${chatId}`);
          }
        }
        
        debugLogger.logPollingResult('waha', true, []);
        return;
      } catch (chatsError) {
        // Try alternative endpoints
        console.log('⚠️ Chats endpoint failed, trying alternative method...');
      }

      // Method 2: Try to get recent messages directly
      try {
        const messagesResponse = await this.api.get(`/api/${this.instanceName}/messages?limit=10`);
        const messages = messagesResponse.data || [];
        
        console.log(`📊 Found ${messages.length} recent messages`);
        
        for (const message of messages) {
          // Check if message is newer than our last processed timestamp
          const messageTime = message.timestamp * 1000; // Convert to milliseconds
          
          // Only process messages that are truly new (after initialization) and not from us
          if (messageTime > this.lastMessageTimestamp && !message.fromMe && this.initializationComplete) {
            console.log('📱 New message found via polling:', message.body || message.text);

            // Process the message
            await this.handleIncomingMessage({
              from: message.from || message.chatId,
              fromMe: message.fromMe,
              text: message.body || message.text,
              timestamp: message.timestamp,
              type: message.type || 'text',
              fromName: message.notifyName || message.pushName || 'Unknown'
            });

            // Update our timestamp
            this.lastMessageTimestamp = Math.max(this.lastMessageTimestamp, messageTime);
          } else if (!this.initializationComplete) {
            // During initialization, just update timestamp without processing
            this.lastMessageTimestamp = Math.max(this.lastMessageTimestamp, messageTime);
          }
        }
        
        debugLogger.logPollingResult('waha', true, messages);
        return;
      } catch (messagesError) {
        console.log('⚠️ Messages endpoint failed:', messagesError.message);
      }

      // If all methods fail, just log the polling attempt
      debugLogger.logPollingResult('waha', false, []);
      
    } catch (error) {
      console.error('❌ Polling error:', error.message);
      debugLogger.logPollingResult('waha', false, []);
    }
  }

  async fetchMessagesFromChat(chatId, unreadCount = 10) {
    try {
      console.log(`🔍 DETAILED DEBUG: fetchMessagesFromChat called with chatId=${chatId}, unreadCount=${unreadCount}`);
      
      // WAHA uses different endpoint format for getting messages from a specific chat
      const endpoint = `/api/${this.instanceName}/chats/${encodeURIComponent(chatId)}/messages?limit=${unreadCount + 5}`;
      console.log(`🔗 API endpoint: ${endpoint}`);
      
      const response = await this.api.get(endpoint);
      const messages = response.data || [];
      
      console.log(`📊 Found ${messages.length} messages from ${chatId}`);
      console.log(`📋 Messages structure:`, messages.map(m => ({
        id: m.id,
        body: m.body?.substring(0, 50),
        fromMe: m.fromMe,
        type: m.type,
        timestamp: m.timestamp
      })));
      
      // Take only the number of unread messages from the top (most recent)
      const unreadMessages = messages
        .filter(msg => !msg.fromMe) // Skip our own messages
        .slice(0, unreadCount); // Take only the unread count
      
      console.log(`📱 Processing ${unreadMessages.length} unread messages from ${chatId}`);
      
      for (const message of unreadMessages) {
        console.log(`📱 Processing message: "${message.body}" from ${message.from}`);

        // Check if we already processed this message ID
        if (this.processedMessageIds && this.processedMessageIds.has(message.id)) {
          console.log(`⏭️ Skipping already processed message ${message.id}`);
          continue;
        }

        // Skip processing messages during initialization to prevent historical message spam
        if (this.isInitializing || !this.initializationComplete) {
          console.log(`⏭️ Skipping message during initialization: ${message.id}`);
          // Still mark as processed and update timestamp to avoid reprocessing later
          if (!this.processedMessageIds) {
            this.processedMessageIds = new Set();
          }
          this.processedMessageIds.add(message.id);
          const messageTime = message.timestamp * 1000;
          this.lastMessageTimestamp = Math.max(this.lastMessageTimestamp, messageTime);
          continue;
        }
        
        // Extract audio/media information from message
        let messagePayload = {
          from: message.from || chatId,
          fromMe: message.fromMe,
          text: message.body || message.text,
          timestamp: message.timestamp,
          type: message.type || 'text',
          fromName: message._data?.notifyName || message.pushName || 'Unknown'
        };

        // Handle audio messages specifically
        if (message.type === 'audio' || message.type === 'voice' || message.type === 'ptt' ||
            (message.hasMedia && (!message.type || message.type === 'undefined' || message.type === undefined))) {
          messagePayload.type = 'audio';
          
          // Extract from _data object which contains WhatsApp raw data
          if (message._data && message._data.body) {
            messagePayload.mediaUrl = message._data.body; // WhatsApp audio content
            messagePayload.duration = message._data.duration || 10;
          } else if (message.media) {
            // If media object exists, extract URL and duration
            messagePayload.mediaUrl = message.media.url || message.media.data;
            messagePayload.duration = message.media.duration || message._data?.duration || 10;
          } else if (message.hasMedia) {
            // Try to fetch media from WAHA API
            try {
              const mediaResponse = await this.api.get(`/api/${this.instanceName}/messages/${message.id}/media`);
              if (mediaResponse.data) {
                messagePayload.mediaUrl = mediaResponse.data.url || mediaResponse.data.data;
                messagePayload.duration = mediaResponse.data.duration || 10;
              }
            } catch (mediaError) {
              console.log('⚠️ Could not fetch media for audio message:', mediaError.message);
            }
          }
          
          console.log(`🎤 Audio message detected - Type: ${message.type}, HasMedia: ${message.hasMedia}, MediaUrl: ${!!messagePayload.mediaUrl}`);
        }

        await this.handleIncomingMessage(messagePayload);
        
        // Mark message as processed
        if (!this.processedMessageIds) {
          this.processedMessageIds = new Set();
        }
        this.processedMessageIds.add(message.id);
        
        // Update our timestamp
        const messageTime = message.timestamp * 1000;
        this.lastMessageTimestamp = Math.max(this.lastMessageTimestamp, messageTime);
      }
      
      // Mark messages as read
      try {
        await this.api.post(`/api/${this.instanceName}/seen`, { chatId });
      } catch (seenError) {
        // Ignore if seen endpoint doesn't exist
      }
      
    } catch (error) {
      console.error(`❌ Error fetching messages from ${chatId}:`, error.message);
    }
  }

  async fetchRecentMessagesFromChat(chatId, limit = 5) {
    try {
      console.log(`🔍 DETAILED DEBUG: fetchRecentMessagesFromChat called with chatId=${chatId}, limit=${limit}`);
      
      // Use WAHA API to get ONLY incoming messages (fromMe=false) to find new user messages
      let endpoint = `/api/${this.instanceName}/chats/${encodeURIComponent(chatId)}/messages?limit=${limit}&filter.fromMe=false`;
      console.log(`🔗 Incoming messages API endpoint: ${endpoint}`);
      
      let response;
      let messages = [];
      
      try {
        response = await this.api.get(endpoint);
        messages = response.data || [];
        console.log(`✅ Successfully fetched incoming messages using filter.fromMe=false`);
      } catch (error) {
        console.log(`⚠️ Filtered endpoint failed, trying without filter...`);
        // Try alternative endpoint without filter if filtering is not supported
        endpoint = `/api/${this.instanceName}/chats/${encodeURIComponent(chatId)}/messages?limit=${limit}`;
        console.log(`🔗 All messages API endpoint (fallback): ${endpoint}`);
        response = await this.api.get(endpoint);
        messages = response.data || [];
        console.log(`⚠️ Using fallback - will filter fromMe=false manually`);
      }
      
      console.log(`📊 Found ${messages.length} recent messages from ${chatId}`);
      console.log(`📋 Recent messages structure:`, messages.map(m => ({
        id: m.id,
        body: m.body?.substring(0, 50),
        fromMe: m.fromMe,
        type: m.type,
        timestamp: m.timestamp,
        hasMedia: m.hasMedia,
        _data_exists: !!m._data,
        media_exists: !!m.media
      })));
      
      // Special detailed logging for audio messages
      messages.forEach(m => {
        if (m.type === 'audio' || m.type === 'voice' || m.type === 'ptt' || m.hasMedia) {
          console.log(`🎤 DETAILED AUDIO MESSAGE STRUCTURE:`, {
            id: m.id,
            type: m.type,
            hasMedia: m.hasMedia,
            _data: m._data ? {
              body_exists: !!m._data.body,
              body_length: m._data.body?.length,
              duration: m._data.duration
            } : 'no _data',
            media: m.media ? {
              url_exists: !!m.media.url,
              data_exists: !!m.media.data,
              duration: m.media.duration
            } : 'no media'
          });
        }
      });
      
      // Filter for new messages that we haven't processed yet (incoming messages only)
      const unprocessedMessages = messages.filter(message => {
        const messageTime = message.timestamp * 1000;
        const isNew = messageTime > this.lastMessageTimestamp;
        const notFromMe = !message.fromMe; // Only process incoming messages
        const notProcessed = !this.processedMessageIds || !this.processedMessageIds.has(message.id);
        
        console.log(`📋 Message ${message.id} - Time: ${messageTime}, LastProcessed: ${this.lastMessageTimestamp}, IsNew: ${isNew}, NotFromMe: ${notFromMe}, NotProcessed: ${notProcessed}`);
        
        // Only process messages that are: new, not from us, and not already processed
        return isNew && notFromMe && notProcessed;
      });
      
      console.log(`📱 Processing ${unprocessedMessages.length} unprocessed recent messages from ${chatId}`);
      
      for (const message of unprocessedMessages) {
        console.log(`📱 Processing recent message: "${message.body}" from ${message.from}`);
        
        // Extract audio/media information from message (same logic as fetchMessagesFromChat)
        let messagePayload = {
          from: message.from || chatId,
          fromMe: message.fromMe,
          text: message.body || message.text,
          timestamp: message.timestamp,
          type: message.type || 'text',
          fromName: message._data?.notifyName || message.pushName || 'Unknown'
        };

        // Handle audio messages specifically
        console.log(`🎤 POLLING AUDIO DEBUG: Message type: "${message.type}", hasMedia: ${message.hasMedia}, _data: ${!!message._data}, media: ${!!message.media}`);
        
        // Check if this could be an audio message - either by type or by having media
        const couldBeAudio = message.type === 'audio' || message.type === 'voice' || message.type === 'ptt' ||
                             (message.hasMedia && (!message.type || message.type === 'undefined' || message.type === undefined));
        
        if (couldBeAudio) {
          console.log(`🎤 POLLING AUDIO DETECTED: Setting type to 'audio'`);
          messagePayload.type = 'audio';
          
          // Try multiple methods to extract audio data
          let audioFound = false;
          
          // Method 1: Extract from _data object which contains WhatsApp raw data
          if (message._data && message._data.body) {
            messagePayload.mediaUrl = message._data.body;
            messagePayload.duration = message._data.duration || 10;
            audioFound = true;
            console.log(`🎤 POLLING: Got mediaUrl from _data.body, length: ${messagePayload.mediaUrl?.length}, duration: ${messagePayload.duration}`);
          }
          
          // Method 2: Extract from media object
          if (!audioFound && message.media && (message.media.url || message.media.data)) {
            messagePayload.mediaUrl = message.media.url || message.media.data;
            messagePayload.duration = message.media.duration || message._data?.duration || 10;
            audioFound = true;
            console.log(`🎤 POLLING: Got mediaUrl from media object, length: ${messagePayload.mediaUrl?.length}, duration: ${messagePayload.duration}`);
          }
          
          // Method 3: Fetch from WAHA API if hasMedia but no direct URL
          if (!audioFound && message.hasMedia) {
            console.log(`🎤 POLLING: Message has media but no direct access, fetching from API...`);
            try {
              const mediaResponse = await this.api.get(`/api/${this.instanceName}/messages/${message.id}/media`);
              console.log(`🎤 POLLING: Media API response:`, mediaResponse.data);
              if (mediaResponse.data && (mediaResponse.data.url || mediaResponse.data.data)) {
                let mediaUrl = mediaResponse.data.url || mediaResponse.data.data;
                
                // Check if the audio is in the correct format for browsers (OPUS/OGG)
                const mimetype = mediaResponse.data.mimetype || '';
                console.log(`🎤 POLLING: Media mimetype: ${mimetype}`);
                
                if (!mimetype.includes('opus') && !mimetype.includes('ogg')) {
                  console.log(`🎤 POLLING: Audio format not browser-compatible, attempting conversion...`);
                  // Try to convert using WAHA's conversion API
                  try {
                    const convertResponse = await this.api.post(`/api/${this.instanceName}/media/convert/voice`, {
                      url: mediaUrl
                    }, {
                      headers: {
                        'Accept': 'application/json'
                      }
                    });
                    
                    if (convertResponse.data && convertResponse.data.data) {
                      // Use the converted audio data
                      mediaUrl = `data:${convertResponse.data.mimetype};base64,${convertResponse.data.data}`;
                      console.log(`🎤 POLLING: Successfully converted audio to browser-compatible format`);
                    }
                  } catch (convertError) {
                    console.log(`⚠️ Audio conversion failed: ${convertError.message}`);
                  }
                }
                
                messagePayload.mediaUrl = mediaUrl;
                messagePayload.duration = mediaResponse.data.duration || 10;
                audioFound = true;
                console.log(`🎤 POLLING: Got mediaUrl from API, length: ${messagePayload.mediaUrl?.length}, duration: ${messagePayload.duration}`);
              }
            } catch (mediaError) {
              console.log('⚠️ Could not fetch media for audio message:', mediaError.message);
            }
          }
          
          if (!audioFound) {
            console.log(`🎤 POLLING WARNING: Audio message detected but no media data available, reverting to text`);
            messagePayload.type = 'text';
            messagePayload.text = 'Mensagem de áudio (não foi possível carregar)';
          }
          
          console.log(`🎤 POLLING FINAL: MediaUrl: ${!!messagePayload.mediaUrl}, Duration: ${messagePayload.duration}, Type: ${messagePayload.type}`);
        } else {
          console.log(`🎤 POLLING: Not an audio message, type is "${message.type}", hasMedia: ${message.hasMedia}`);
        }

        await this.handleIncomingMessage(messagePayload);
        
        // Mark message as processed
        if (!this.processedMessageIds) {
          this.processedMessageIds = new Set();
        }
        this.processedMessageIds.add(message.id);
        
        // Update our timestamp
        const messageTime = message.timestamp * 1000;
        this.lastMessageTimestamp = Math.max(this.lastMessageTimestamp, messageTime);
      }
      
    } catch (error) {
      console.error(`❌ Error fetching recent messages from ${chatId}:`, error.message);
    }
  }

  async checkChatForNewMessages(chatId) {
    try {
      const response = await this.api.get(`/api/messages?chatId=${chatId}&session=${this.instanceName}&limit=3`);
      const messages = response.data || [];
      
      for (const message of messages) {
        // Check if message is newer than our last processed timestamp
        const messageTime = message.timestamp * 1000; // Convert to milliseconds
        
        if (messageTime > this.lastMessageTimestamp && !message.fromMe) {
          console.log('📱 New message found via polling:', message.body || message.text);
          
          // Process the message
          await this.handleIncomingMessage({
            from: message.from,
            fromMe: message.fromMe,
            text: message.body || message.text,
            timestamp: message.timestamp,
            type: message.type || 'text',
            fromName: message.notifyName || message.pushName || message.from.split('@')[0]
          });
          
          // Update our timestamp
          this.lastMessageTimestamp = Math.max(this.lastMessageTimestamp, messageTime);
        }
      }
    } catch (error) {
      // Silently handle individual chat errors
      if (error.response?.status !== 404 && error.response?.status !== 400) {
        console.log(`⚠️ Error checking messages for ${chatId}:`, error.message);
      }
    }
  }

  async disconnect() {
    try {
      console.log('🔌 Disconnecting WAHA API...');
      
      // Stop polling
      this.stopPolling();
      
      if (this.instanceName) {
        await this.api.delete(`/api/sessions/${this.instanceName}/stop`);
      }
      
      this.isConnected = false;
      this.connectionStatus = 'disconnected';
      this.currentQRCode = null;
      this.phoneNumber = null;
      
      this.emitStatus();
      this.emitLog('🔌 Desconectado do WhatsApp');
      console.log('✅ Disconnected from WhatsApp');
      
    } catch (error) {
      console.error('Error disconnecting:', error);
    }
  }

  /**
   * Check WAHA server version and connectivity
   * @returns {Promise<Object>} Health check result with version info
   */
  async checkServerVersion() {
    try {
      console.log('🔍 Checking WAHA server version...');
      console.log(`📡 API URL: ${this.apiUrl}`);
      console.log(`🔑 API Key: ${this.apiKey ? 'Set' : 'Not set'}`);
      
      // Call WAHA's /api/server/version endpoint
      const response = await this.api.get('/api/server/version', {
        timeout: 5000 // 5 second timeout for health checks
      });
      
      if (response.data && response.data.version) {
        console.log(`✅ WAHA server version: ${response.data.version}`);
        
        return {
          success: true,
          version: response.data.version,
          data: response.data
        };
      } else {
        console.log('⚠️ WAHA responded but no version info found');
        return {
          success: false,
          error: 'Resposta inválida do servidor WAHA'
        };
      }
      
    } catch (error) {
      console.error('❌ WAHA server version check failed:', error.message);
      
      // Provide more specific error messages
      let errorMessage = 'Erro desconhecido';
      
      if (error.code === 'ECONNREFUSED') {
        errorMessage = 'Conexão recusada - verifique se o servidor WAHA está rodando';
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNRESET') {
        errorMessage = 'URL do servidor WAHA não encontrada ou inacessível';
      } else if (error.response?.status === 401) {
        errorMessage = 'API Key inválida ou não autorizada';
      } else if (error.response?.status === 403) {
        errorMessage = 'Acesso negado - verifique as permissões da API Key';
      } else if (error.response?.status === 404) {
        errorMessage = 'Endpoint /api/server/version não encontrado - versão WAHA incompatível?';
      } else if (error.code === 'ECONNABORTED') {
        errorMessage = 'Timeout - servidor WAHA demorou para responder';
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      return {
        success: false,
        error: errorMessage,
        statusCode: error.response?.status || null
      };
    }
  }
}

module.exports = WAHAProvider;