const path = require('path');
const fs = require('fs');
const WhatsAppProviderInterface = require('../interfaces/WhatsAppProviderInterface');

class TwilioProvider extends WhatsAppProviderInterface {
  constructor() {
    super();
    this.connectionType = 'twilio';
    this.settings = this.loadSettings();
    this.client = null;
    
    console.log('🔧 Twilio Provider initialized');
    console.log(`📞 Account SID: ${this.settings.twilioAccountSid ? 'Set' : 'Not set'}`);
    console.log(`🔑 Auth Token: ${this.settings.twilioAuthToken ? 'Set' : 'Not set'}`);
    console.log(`📱 From Number: ${this.settings.twilioFromNumber || 'Not set'}`);
  }

  loadSettings() {
    try {
      const settingsPath = path.join(__dirname, '../../config/settings.json');
      if (fs.existsSync(settingsPath)) {
        return JSON.parse(fs.readFileSync(settingsPath, 'utf8'));
      }
    } catch (error) {
      console.log('No Twilio settings found, using defaults');
    }
    
    return {
      whatsappEnabled: false,
      whatsappType: 'twilio',
      twilioAccountSid: process.env.TWILIO_ACCOUNT_SID || '',
      twilioAuthToken: process.env.TWILIO_AUTH_TOKEN || '',
      twilioFromNumber: process.env.TWILIO_FROM_NUMBER || ''
    };
  }

  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
    
    console.log(`📱 Twilio Provider settings updated`);
    
    // Reinitialize client if credentials changed
    if (newSettings.twilioAccountSid || newSettings.twilioAuthToken) {
      this.initializeClient();
    }
    
    // Save settings to file
    try {
      const settingsPath = path.join(__dirname, '../../config/settings.json');
      const currentSettings = fs.existsSync(settingsPath) 
        ? JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
        : {};
      
      // Don't save masked tokens
      const cleanSettings = { ...newSettings };
      if (cleanSettings.twilioAuthToken && cleanSettings.twilioAuthToken.includes('••••')) {
        cleanSettings.twilioAuthToken = currentSettings.twilioAuthToken;
      }
      
      const updatedSettings = { ...currentSettings, ...cleanSettings };
      fs.writeFileSync(settingsPath, JSON.stringify(updatedSettings, null, 2));
      console.log('✅ Twilio settings updated');
    } catch (error) {
      console.error('❌ Error saving Twilio settings:', error);
    }
  }

  initializeClient() {
    try {
      if (!this.settings.twilioAccountSid || !this.settings.twilioAuthToken) {
        console.log('⚠️ Twilio credentials not configured');
        return;
      }

      const twilio = require('twilio');
      this.client = twilio(this.settings.twilioAccountSid, this.settings.twilioAuthToken);
      console.log('✅ Twilio client initialized');
    } catch (error) {
      console.error('❌ Error initializing Twilio client:', error);
    }
  }

  async connect() {
    console.log('🔗 Connecting Twilio WhatsApp...');
    
    if (!this.settings.whatsappEnabled) {
      console.log('❌ WhatsApp integration is DISABLED');
      throw new Error('WhatsApp integration is disabled');
    }

    try {
      this.connectionStatus = 'connecting';
      this.emitStatus();
      this.emitLog('🚀 Iniciando conexão com Twilio WhatsApp...');

      this.initializeClient();

      if (!this.client) {
        throw new Error('Twilio client not initialized. Check credentials.');
      }

      if (!this.settings.twilioFromNumber) {
        throw new Error('Twilio from number not configured');
      }

      // Test connection by validating the from number
      await this.validateFromNumber();
      
      this.isConnected = true;
      this.connectionStatus = 'connected';
      this.phoneNumber = this.settings.twilioFromNumber;
      this.currentQRCode = null; // Twilio doesn't use QR codes
      
      this.emitStatus();
      this.emitLog('✅ Conectado ao Twilio WhatsApp com sucesso!');
      console.log('✅ Twilio WhatsApp connected successfully');

    } catch (error) {
      console.error('❌ Twilio connection failed:', error);
      this.connectionStatus = 'error';
      this.emitStatus();
      this.emitLog(`❌ Erro na conexão: ${error.message}`);
      throw error;
    }
  }

  async validateFromNumber() {
    try {
      // Validate that the from number is valid and active
      const phoneNumber = await this.client.incomingPhoneNumbers.list({
        phoneNumber: this.settings.twilioFromNumber
      });
      
      if (phoneNumber.length === 0) {
        throw new Error(`Phone number ${this.settings.twilioFromNumber} not found in your Twilio account`);
      }
      
      console.log(`✅ Validated Twilio from number: ${this.settings.twilioFromNumber}`);
    } catch (error) {
      console.error('❌ Error validating from number:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      console.log('🔌 Disconnecting Twilio WhatsApp...');
      
      this.isConnected = false;
      this.connectionStatus = 'disconnected';
      this.phoneNumber = null;
      
      this.emitStatus();
      this.emitLog('🔌 Desconectado do Twilio WhatsApp');
      console.log('✅ Disconnected from Twilio WhatsApp');
      
    } catch (error) {
      console.error('Error disconnecting Twilio:', error);
    }
  }

  async sendMessage(phoneNumber, message, options = {}) {
    if (!this.isConnected || !this.client) {
      throw new Error('Twilio WhatsApp not connected');
    }

    try {
      console.log(`📤 Sending Twilio message to ${phoneNumber}: ${message}`);
      this.emitLog(`📤 Enviando mensagem para ${phoneNumber}`);

      // Normalize phone number for Twilio WhatsApp format
      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);
      const toNumber = `whatsapp:+${normalizedPhone}`;
      const fromNumber = `whatsapp:${this.settings.twilioFromNumber}`;

      const messageData = {
        body: message,
        from: fromNumber,
        to: toNumber
      };

      const twilioMessage = await this.client.messages.create(messageData);

      console.log('✅ Twilio message sent successfully');
      this.emitLog('✅ Mensagem enviada com sucesso');

      return {
        success: true,
        messageId: twilioMessage.sid,
        data: twilioMessage
      };

    } catch (error) {
      console.error('❌ Error sending Twilio message:', error);
      this.emitLog(`❌ Erro ao enviar mensagem: ${error.message}`);
      throw error;
    }
  }

  async simulateTyping(phoneNumber, duration = 3000) {
    // Twilio doesn't support typing indicators, so we just wait
    console.log(`⌨️ Simulating typing for ${duration}ms (Twilio doesn't support typing indicators)`);
    this.emitLog(`⌨️ Aguardando ${duration}ms...`);
    
    await new Promise(resolve => setTimeout(resolve, duration));
    
    console.log(`✅ Typing simulation completed`);
    this.emitLog(`✅ Simulação de digitação concluída`);
    return true;
  }

  async loadHistoryFromWhatsApp(phoneNumber, limit = 10) {
    console.log(`📚 Loading Twilio message history for ${phoneNumber} (limit: ${limit})...`);
    
    if (!this.isConnected || !this.client) {
      console.log('❌ Twilio not connected, returning empty history');
      return [];
    }

    try {
      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);
      const toNumber = `whatsapp:+${normalizedPhone}`;
      
      // Fetch messages from Twilio API
      const messages = await this.client.messages.list({
        to: toNumber,
        from: `whatsapp:${this.settings.twilioFromNumber}`,
        limit: limit
      });

      const history = [];

      for (const msg of messages.reverse()) { // Reverse to get chronological order
        try {
          const historyMessage = {
            id: msg.sid,
            content: msg.body,
            sender: msg.direction === 'inbound' ? 'user' : 'ai',
            timestamp: new Date(msg.dateSent),
            status: msg.status,
            type: 'text',
            whatsappMessage: true
          };

          history.push(historyMessage);

        } catch (msgError) {
          console.warn(`⚠️ Error processing history message: ${msgError.message}`);
        }
      }

      console.log(`✅ Loaded ${history.length} messages from Twilio history`);
      return history;

    } catch (error) {
      console.error(`❌ Error loading Twilio history: ${error.message}`);
      return [];
    }
  }

  async clearSession() {
    try {
      console.log('🧹 Clearing Twilio session...');
      this.emitLog('🧹 Limpando sessão do Twilio...');

      // For Twilio, we just disconnect
      await this.disconnect();

      console.log('✅ Twilio session cleared successfully');
      this.emitLog('✅ Sessão limpa com sucesso!');

      return {
        success: true,
        message: 'Sessão Twilio limpa com sucesso'
      };

    } catch (error) {
      console.error('❌ Error clearing Twilio session:', error);
      this.emitLog(`❌ Erro ao limpar sessão: ${error.message}`);
      throw error;
    }
  }

  // Handle incoming webhook from Twilio
  async handleTwilioWebhook(webhookData) {
    try {
      console.log('📱 Twilio webhook received:', JSON.stringify(webhookData, null, 2));

      const from = webhookData.From; // e.g., whatsapp:+5521999888777
      const body = webhookData.Body;
      const messageId = webhookData.MessageSid;

      if (!from || !from.startsWith('whatsapp:')) {
        console.log('⚠️ Not a WhatsApp message, ignoring');
        return;
      }

      // Extract phone number from "whatsapp:+5521999888777" format
      const phoneNumber = from.replace('whatsapp:+', '').replace('whatsapp:', '');
      const normalizedPhone = this.normalizePhoneNumber(phoneNumber);

      // Get customer name (Twilio doesn't provide names, so use phone)
      const customerName = webhookData.ProfileName || normalizedPhone;

      // Send to message handler
      if (this.messageHandler) {
        console.log('🔄 Forwarding Twilio message to message handler...');
        await this.messageHandler.handleWhatsAppMessage({
          customerPhone: normalizedPhone,
          customerName: customerName,
          content: body,
          type: 'text',
          originalMessage: webhookData
        });
        console.log('✅ Twilio message forwarded to handler successfully');
      } else {
        console.log('❌ No message handler available - message will not be processed');
        this.emitLog('❌ Handler de mensagens não disponível');
      }

    } catch (error) {
      console.error('❌ Error handling Twilio webhook:', error);
      this.emitLog(`❌ Erro ao processar webhook: ${error.message}`);
    }
  }
}

module.exports = TwilioProvider;