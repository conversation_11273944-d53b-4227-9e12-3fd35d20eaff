const WhatsAppProviderInterface = require('../interfaces/WhatsAppProviderInterface');

/**
 * Provider para modo de teste Customer App
 * Simula funcionalidades WhatsApp para desenvolvimento e testes
 */
class CustomerAppProvider extends WhatsAppProviderInterface {
  constructor() {
    super();
    this.connectionType = 'customer-app';
    this.connectionStatus = 'ready'; // Customer app sempre está "pronto"
    this.isConnected = true; // Sempre conectado no modo teste
    this.phoneNumber = 'test-mode';
    this.testConversations = new Map(); // Armazena conversas de teste
    
    console.log('🧪 Customer App Provider initialized for test mode');
  }

  /**
   * Conecta ao modo teste (sempre bem-sucedido)
   */
  async connect() {
    console.log('🧪 Customer App Provider: Connecting to test mode...');
    
    this.isConnected = true;
    this.connectionStatus = 'ready';
    this.phoneNumber = 'test-mode';
    
    this.emitLog('Customer App test mode connected successfully');
    this.emitStatus();
    
    return { success: true, message: 'Test mode ready' };
  }

  /**
   * Desconecta do modo teste
   */
  async disconnect() {
    console.log('🧪 Customer App Provider: Disconnecting from test mode...');
    
    this.isConnected = false;
    this.connectionStatus = 'disconnected';
    this.phoneNumber = null;
    
    this.emitLog('Customer App test mode disconnected');
    this.emitStatus();
    
    return { success: true, message: 'Test mode disconnected' };
  }

  /**
   * Envia mensagem no modo teste
   * @param {string} phoneNumber - Número do telefone (simulado)
   * @param {string} message - Mensagem a ser enviada
   * @param {Object} options - Opções adicionais
   */
  async sendMessage(phoneNumber, message, options = {}) {
    console.log(`🧪 Customer App Provider: Sending test message to ${phoneNumber}`);
    console.log(`📝 Message: ${message}`);
    
    // Simula delay de envio
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Armazena a mensagem na conversa de teste
    if (!this.testConversations.has(phoneNumber)) {
      this.testConversations.set(phoneNumber, []);
    }
    
    const conversation = this.testConversations.get(phoneNumber);
    const messageData = {
      id: `test-${Date.now()}`,
      from: 'assistant',
      to: phoneNumber,
      message: message,
      timestamp: new Date().toISOString(),
      type: 'text',
      status: 'sent'
    };
    
    conversation.push(messageData);
    
    // Emite para o customer-app usando o evento que ele está esperando
    console.log(`🔍 CustomerAppProvider: Checking messageHandler availability...`);
    console.log(`🔍 MessageHandler exists: ${!!this.messageHandler}`);
    console.log(`🔍 MessageHandler.io exists: ${!!(this.messageHandler && this.messageHandler.io)}`);
    
    if (this.messageHandler && this.messageHandler.io) {
      console.log(`🔍 CustomerAppProvider: Searching for conversation with phone ${phoneNumber}...`);
      
      // Encontra o conversationId baseado no telefone
      let conversationId = null;
      let conversationCount = 0;
      
      if (this.messageHandler.activeConversations) {
        console.log(`🔍 CustomerAppProvider: Active conversations size: ${this.messageHandler.activeConversations.size}`);
        
        for (const [id, conv] of this.messageHandler.activeConversations) {
          conversationCount++;
          console.log(`🔍 CustomerAppProvider: Conversation ${conversationCount}: ID=${id}, Phone=${conv.customerPhone}`);
          
          if (conv.customerPhone === phoneNumber) {
            conversationId = id;
            console.log(`✅ CustomerAppProvider: Found matching conversation: ${conversationId}`);
            break;
          }
        }
      } else {
        console.log(`⚠️ CustomerAppProvider: activeConversations is null/undefined`);
      }
      
      if (conversationId) {
        // Emite salon_response que o customer-app está ouvindo
        this.messageHandler.io.emit('salon_response', {
          conversationId,
          message: {
            id: messageData.id,
            content: message,
            sender: 'ai', // Usar 'ai' para que apareça como mensagem do salão
            timestamp: new Date(),
            status: 'sent',
            type: 'text'
          }
        });
        
        console.log(`📤 Customer App Provider: Emitted salon_response for conversation ${conversationId}`);
      } else {
        console.log(`⚠️ Customer App Provider: No conversation found for ${phoneNumber} (searched ${conversationCount} conversations)`);
      }
      
      // Mantém o evento original para outros propósitos
      this.messageHandler.io.emit('test_message_sent', {
        phoneNumber,
        message,
        timestamp: new Date().toISOString()
      });
    }
    
    this.emitLog(`Test message sent to ${phoneNumber}: ${message.substring(0, 50)}...`);
    
    return {
      success: true,
      messageId: messageData.id,
      status: 'sent',
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Simula digitação no modo teste
   */
  async simulateTyping(phoneNumber, duration = 3000) {
    console.log(`🧪 Customer App Provider: Simulating typing for ${phoneNumber} (${duration}ms)`);
    
    // Emite evento de digitação para o frontend
    if (this.messageHandler && this.messageHandler.io) {
      this.messageHandler.io.emit('test_typing_start', {
        phoneNumber,
        duration
      });
      
      // Para a digitação após o tempo especificado
      setTimeout(() => {
        this.messageHandler.io.emit('test_typing_stop', {
          phoneNumber
        });
      }, duration);
    }
    
    return true;
  }

  /**
   * Carrega histórico de teste
   */
  async loadHistoryFromWhatsApp(phoneNumber, limit = 10) {
    console.log(`🧪 Customer App Provider: Loading test history for ${phoneNumber} (limit: ${limit})`);
    
    const conversation = this.testConversations.get(phoneNumber) || [];
    const history = conversation.slice(-limit).map(msg => ({
      id: msg.id,
      from: msg.from,
      to: msg.to,
      body: msg.message,
      timestamp: msg.timestamp,
      type: msg.type,
      isFromMe: msg.from === 'assistant'
    }));
    
    this.emitLog(`Loaded ${history.length} test messages for ${phoneNumber}`);
    
    return history;
  }

  /**
   * Limpa sessão de teste
   */
  async clearSession() {
    console.log('🧪 Customer App Provider: Clearing test session...');
    
    this.testConversations.clear();
    
    this.emitLog('Test session cleared - all conversations removed');
    
    return {
      success: true,
      message: 'Test session cleared successfully'
    };
  }

  /**
   * Simula recebimento de mensagem do customer app
   * @param {string} phoneNumber - Número do telefone simulado
   * @param {string} message - Mensagem recebida
   */
  async simulateIncomingMessage(phoneNumber, message) {
    console.log(`🧪 Customer App Provider: Simulating incoming message from ${phoneNumber}`);
    
    // Armazena a mensagem na conversa de teste
    if (!this.testConversations.has(phoneNumber)) {
      this.testConversations.set(phoneNumber, []);
    }
    
    const conversation = this.testConversations.get(phoneNumber);
    const messageData = {
      id: `test-incoming-${Date.now()}`,
      from: phoneNumber,
      to: 'assistant',
      message: message,
      timestamp: new Date().toISOString(),
      type: 'text',
      status: 'received'
    };
    
    conversation.push(messageData);
    
    // Processa a mensagem através do handler
    if (this.messageHandler && this.messageHandler.handleMessage) {
      const whatsappMessage = {
        id: messageData.id,
        from: phoneNumber,
        body: message,
        timestamp: Date.now(),
        type: 'chat',
        isFromMe: false,
        hasMedia: false
      };
      
      await this.messageHandler.handleMessage(whatsappMessage);
    }
    
    this.emitLog(`Processed incoming test message from ${phoneNumber}: ${message.substring(0, 50)}...`);
    
    return messageData;
  }

  /**
   * Retorna estatísticas do modo teste
   */
  getTestStats() {
    const totalConversations = this.testConversations.size;
    let totalMessages = 0;
    
    for (const conversation of this.testConversations.values()) {
      totalMessages += conversation.length;
    }
    
    return {
      totalConversations,
      totalMessages,
      activeConversations: Array.from(this.testConversations.keys()),
      status: this.getStatus()
    };
  }

  /**
   * Atualiza configurações do provider
   */
  updateSettings(newSettings) {
    console.log('🧪 Customer App Provider: Updating settings for test mode');
    // No modo teste, não há configurações específicas para atualizar
    this.emitLog('Test mode settings updated');
  }
}

module.exports = CustomerAppProvider;
