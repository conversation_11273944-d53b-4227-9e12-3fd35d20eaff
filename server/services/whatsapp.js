// WhatsApp Provider Factory Service
const WhatsAppProviderFactory = require('./WhatsAppProviderFactory');

// Unified WhatsApp Service that delegates to the configured provider
class WhatsAppService {
  constructor() {
    this.factory = WhatsAppProviderFactory;
    this.provider = null;
    
    // Get the current provider on initialization
    this.initializeProvider();
  }

  initializeProvider() {
    try {
      this.provider = this.factory.getCurrentProvider();
      console.log(`🔧 WhatsApp Service initialized with ${this.provider.connectionType} provider`);
    } catch (error) {
      console.error('❌ Error initializing WhatsApp provider:', error);
      throw error;
    }
  }

  // Provider management methods
  switchProvider(providerType) {
    this.provider = this.factory.switchProvider(providerType);
    return this.provider;
  }

  getCurrentProvider() {
    return this.provider;
  }

  getAvailableProviders() {
    return this.factory.getAvailableProviders();
  }

  getProviderStats() {
    return this.factory.getProviderStats();
  }

  async testProvider(providerType) {
    return await this.factory.testProvider(providerType);
  }

  // Delegate all WhatsApp operations to the current provider
  get isConnected() {
    return this.provider?.isConnected || false;
  }

  get connectionStatus() {
    return this.provider?.connectionStatus || 'disconnected';
  }

  get connectionType() {
    return this.provider?.connectionType || 'unknown';
  }

  get phoneNumber() {
    return this.provider?.phoneNumber || null;
  }

  get currentQRCode() {
    return this.provider?.currentQRCode || null;
  }

  setMessageHandler(handler) {
    if (this.provider) {
      return this.provider.setMessageHandler(handler);
    }
  }

  getStatus() {
    if (this.provider) {
      return this.provider.getStatus();
    }
    return {
      isConnected: false,
      connectionStatus: 'disconnected',
      connectionType: 'none',
      qrCode: null,
      phoneNumber: null
    };
  }

  updateSettings(newSettings) {
    // Update factory settings (may switch provider)
    this.factory.updateSettings(newSettings);
    
    // Update current provider
    if (this.provider) {
      this.provider.updateSettings(newSettings);
    }
  }

  async connect() {
    if (!this.provider) {
      this.initializeProvider();
    }
    return await this.provider.connect();
  }

  async disconnect() {
    if (this.provider) {
      return await this.provider.disconnect();
    }
  }

  async sendMessage(phoneNumber, message, options = {}) {
    if (!this.provider) {
      throw new Error('No WhatsApp provider available');
    }
    return await this.provider.sendMessage(phoneNumber, message, options);
  }

  async simulateTyping(phoneNumber, duration = 3000) {
    if (!this.provider) {
      return false;
    }
    return await this.provider.simulateTyping(phoneNumber, duration);
  }

  async loadHistoryFromWhatsApp(phoneNumber, limit = 10) {
    if (!this.provider) {
      return [];
    }
    return await this.provider.loadHistoryFromWhatsApp(phoneNumber, limit);
  }

  async loadAllChats() {
    if (!this.provider) {
      return [];
    }
    return await this.provider.loadAllChats();
  }

  async clearSession() {
    if (!this.provider) {
      return { success: false, message: 'No provider available' };
    }
    return await this.provider.clearSession();
  }

  sendQRToClient(socket) {
    if (this.provider) {
      return this.provider.sendQRToClient(socket);
    }
    return false;
  }

  emitLog(message) {
    if (this.provider) {
      return this.provider.emitLog(message);
    }
  }

  emitStatus() {
    if (this.provider) {
      return this.provider.emitStatus();
    }
  }

  // Webhook handling
  async handleWebhook(webhookData) {
    if (this.provider && typeof this.provider.handleWebhook === 'function') {
      return await this.provider.handleWebhook(webhookData);
    } else {
      console.log('⚠️ Current provider does not support webhooks');
    }
  }

  // Auto-connect method for backward compatibility
  async tryAutoConnect() {
    if (!this.provider) {
      this.initializeProvider();
    }
    
    if (this.provider && typeof this.provider.tryAutoConnect === 'function') {
      return await this.provider.tryAutoConnect();
    }
  }

  // Utility methods
  normalizePhoneNumber(phoneNumber, forWhatsApp = false) {
    if (this.provider) {
      return this.provider.normalizePhoneNumber(phoneNumber, forWhatsApp);
    }
    
    // Fallback normalization
    let normalized = phoneNumber.replace(/\D/g, '');
    if (normalized.length === 11 && normalized.startsWith('21')) {
      normalized = '55' + normalized;
    } else if (normalized.length === 10) {
      normalized = '5521' + normalized;
    }
    
    if (forWhatsApp && !normalized.includes('@')) {
      normalized = normalized + '@c.us';
    }
    
    return normalized;
  }
}

module.exports = new WhatsAppService();