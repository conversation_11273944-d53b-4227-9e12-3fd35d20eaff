/**
 * Interface comum para todos os provedores de WhatsApp
 * Define os métodos que todos os providers devem implementar
 */
class WhatsAppProviderInterface {
  constructor() {
    if (new.target === WhatsAppProviderInterface) {
      throw new TypeError("Cannot construct Abstract instances directly");
    }
    
    this.isConnected = false;
    this.connectionStatus = 'disconnected';
    this.connectionType = '';
    this.qrCode = null;
    this.currentQRCode = null;
    this.phoneNumber = null;
    this.messageHandler = null;
  }

  // Métodos obrigatórios que devem ser implementados por todos os providers
  
  /**
   * Conecta ao provedor WhatsApp
   * @returns {Promise<void>}
   */
  async connect() {
    throw new Error("Method 'connect()' must be implemented");
  }

  /**
   * Desconecta do provedor WhatsApp
   * @returns {Promise<void>}
   */
  async disconnect() {
    throw new Error("Method 'disconnect()' must be implemented");
  }

  /**
   * Envia mensagem
   * @param {string} phoneNumber - Número do telefone
   * @param {string} message - Mensagem a ser enviada
   * @param {Object} options - Opções adicionais
   * @returns {Promise<Object>}
   */
  async sendMessage(phoneNumber, message, options = {}) {
    throw new Error("Method 'sendMessage()' must be implemented");
  }

  /**
   * Simula digitação
   * @param {string} phoneNumber - Número do telefone
   * @param {number} duration - Duração em ms
   * @returns {Promise<boolean>}
   */
  async simulateTyping(phoneNumber, duration = 3000) {
    throw new Error("Method 'simulateTyping()' must be implemented");
  }

  /**
   * Carrega histórico de conversas
   * @param {string} phoneNumber - Número do telefone
   * @param {number} limit - Limite de mensagens
   * @returns {Promise<Array>}
   */
  async loadHistoryFromWhatsApp(phoneNumber, limit = 10) {
    throw new Error("Method 'loadHistoryFromWhatsApp()' must be implemented");
  }

  /**
   * Limpa sessão
   * @returns {Promise<Object>}
   */
  async clearSession() {
    throw new Error("Method 'clearSession()' must be implemented");
  }

  // Métodos comuns com implementação padrão

  /**
   * Configura o handler de mensagens
   * @param {Object} handler - Handler de mensagens
   */
  setMessageHandler(handler) {
    this.messageHandler = handler;
  }

  /**
   * Retorna o status atual da conexão
   * @returns {Object}
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      connectionStatus: this.connectionStatus,
      connectionType: this.connectionType,
      qrCode: this.currentQRCode,
      phoneNumber: this.phoneNumber
    };
  }

  /**
   * Atualiza as configurações
   * @param {Object} newSettings - Novas configurações
   */
  updateSettings(newSettings) {
    // Implementação padrão - pode ser sobrescrita
    console.log(`Updating settings for ${this.connectionType} provider`);
  }

  /**
   * Normaliza número de telefone
   * @param {string} phoneNumber - Número a ser normalizado
   * @param {boolean} forWhatsApp - Se é para formato WhatsApp
   * @returns {string}
   */
  normalizePhoneNumber(phoneNumber, forWhatsApp = false) {
    // Remove all non-numeric characters
    let normalized = phoneNumber.replace(/\D/g, '');

    // Add country code if missing (assuming Brazil +55)
    if (normalized.length === 11 && normalized.startsWith('21')) {
      normalized = '55' + normalized;
    } else if (normalized.length === 10) {
      normalized = '5521' + normalized;
    }

    // For WhatsApp format, add @c.us
    if (forWhatsApp && !normalized.includes('@')) {
      normalized = normalized + '@c.us';
    }

    return normalized;
  }

  /**
   * Envia QR code para cliente conectado
   * @param {Object} socket - Socket do cliente
   * @returns {boolean}
   */
  sendQRToClient(socket) {
    if (this.currentQRCode && !this.isConnected && this.connectionStatus === 'connecting') {
      socket.emit('whatsapp_qr', { qr: this.currentQRCode });
      socket.emit('whatsapp_status', this.getStatus());
      return true;
    }
    return false;
  }

  /**
   * Emite log para frontend
   * @param {string} message - Mensagem de log
   */
  emitLog(message) {
    if (this.messageHandler && this.messageHandler.io) {
      this.messageHandler.io.emit('whatsapp_log', {
        message,
        timestamp: new Date().toISOString()
      });
    }
    console.log(`[${this.connectionType.toUpperCase()}] ${message}`);
  }

  /**
   * Emite status para frontend
   */
  emitStatus() {
    const status = this.getStatus();
    if (this.messageHandler && this.messageHandler.io) {
      this.messageHandler.io.emit('whatsapp_status', status);
    }
  }
}

module.exports = WhatsAppProviderInterface;