const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

/**
 * ConversationLogger - Sistema de logging detalhado para conversas
 * 
 * Funcionalidades:
 * - Log de mensagens trocadas (para análise de fluxo)
 * - Log detalhado (prompts, respostas da <PERSON>, chamadas à API)
 * - Estrutura preparada para futuro RAG
 */
class ConversationLogger {
  constructor() {
    this.logsBasePath = path.join(__dirname, '../conversation_logs');
    this.activeConversations = new Map(); // phoneNumber -> conversationId
    this.ensureLogDirectory();
  }

  /**
   * Garante que o diretório de logs existe
   */
  ensureLogDirectory() {
    if (!fs.existsSync(this.logsBasePath)) {
      fs.mkdirSync(this.logsBasePath, { recursive: true });
      console.log(`📁 Created conversation logs directory: ${this.logsBasePath}`);
    }
  }

  /**
   * Inicia uma nova conversa ou recupera uma existente
   */
  startConversation(phoneNumber, customerData = null) {
    // Se já tem conversa ativa, continua usando a mesma
    if (this.activeConversations.has(phoneNumber)) {
      const conversationId = this.activeConversations.get(phoneNumber);
      console.log(`🔄 Continuing conversation ${conversationId} for ${phoneNumber}`);
      return conversationId;
    }

    // Cria nova conversa
    const conversationId = uuidv4();
    const conversationDir = path.join(this.logsBasePath, phoneNumber, conversationId);
    
    // Cria diretório da conversa
    fs.mkdirSync(conversationDir, { recursive: true });
    
    // Metadata inicial
    const metadata = {
      conversationId,
      phoneNumber,
      customerData: customerData ? {
        id: customerData.id,
        nome: customerData.nome,
        email: customerData.email,
        isNewCustomer: !customerData.ultimosServicos || customerData.ultimosServicos.length === 0
      } : null,
      startedAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      messageCount: 0,
      aiCallCount: 0,
      trinksApiCallCount: 0,
      totalTokensUsed: 0,
      errors: []
    };

    // Arquivos iniciais
    const messagesLog = {
      conversationId,
      phoneNumber,
      messages: []
    };

    const detailedLog = {
      conversationId,
      phoneNumber,
      events: []
    };

    // Salva arquivos iniciais
    this.saveToFile(conversationDir, 'metadata.json', metadata);
    this.saveToFile(conversationDir, 'messages.json', messagesLog);
    this.saveToFile(conversationDir, 'detailed.json', detailedLog);

    // Registra conversa ativa
    this.activeConversations.set(phoneNumber, conversationId);

    console.log(`🆕 Started new conversation ${conversationId} for ${phoneNumber}`);
    return conversationId;
  }

  /**
   * Registra uma mensagem enviada pelo cliente
   */
  logCustomerMessage(phoneNumber, message, messageType = 'text', transcription = null) {
    const conversationId = this.getActiveConversation(phoneNumber);
    if (!conversationId) return;

    const messageData = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      sender: 'customer',
      content: message,
      type: messageType,
      transcription: transcription
    };

    this.addMessage(phoneNumber, conversationId, messageData);
    console.log(`📥 Logged customer message for ${phoneNumber}: ${messageType}`);
  }

  /**
   * Registra uma mensagem enviada pela IA
   */
  logAIMessage(phoneNumber, message, stage = null, context = null) {
    const conversationId = this.getActiveConversation(phoneNumber);
    if (!conversationId) return;

    const messageData = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      sender: 'ai',
      content: message,
      stage: stage,
      context: context
    };

    this.addMessage(phoneNumber, conversationId, messageData);
    console.log(`📤 Logged AI message for ${phoneNumber}: stage=${stage}`);
  }

  /**
   * Registra uma chamada completa à Claude AI
   */
  logClaudeCall(phoneNumber, promptData, responseData, performanceMetrics = null) {
    const conversationId = this.getActiveConversation(phoneNumber);
    if (!conversationId) return;

    const eventData = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: 'claude_api_call',
      prompt: {
        systemPrompt: promptData.systemPrompt,
        conversationContext: promptData.conversationContext,
        model: promptData.model || 'claude-sonnet-4-20250514'
      },
      response: {
        content: responseData.content,
        rawResponse: responseData.rawResponse,
        tokensUsed: responseData.tokensUsed || null
      },
      performance: performanceMetrics,
      metadata: {
        stage: responseData.stage,
        context: responseData.context
      }
    };

    this.addDetailedEvent(phoneNumber, conversationId, eventData);
    this.updateMetrics(phoneNumber, conversationId, 'aiCallCount', 1);
    
    if (responseData.tokensUsed) {
      this.updateMetrics(phoneNumber, conversationId, 'totalTokensUsed', responseData.tokensUsed);
    }

    console.log(`🤖 Logged Claude API call for ${phoneNumber}`);
  }

  /**
   * Registra uma chamada à API Trinks
   */
  logTrinksApiCall(phoneNumber, endpoint, params, response, success = true, error = null) {
    const conversationId = this.getActiveConversation(phoneNumber);
    if (!conversationId) return;

    const eventData = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: 'trinks_api_call',
      endpoint: endpoint,
      params: params,
      response: response,
      success: success,
      error: error,
      duration: null // Pode ser adicionado depois
    };

    this.addDetailedEvent(phoneNumber, conversationId, eventData);
    this.updateMetrics(phoneNumber, conversationId, 'trinksApiCallCount', 1);

    console.log(`🔗 Logged Trinks API call for ${phoneNumber}: ${endpoint} (${success ? 'success' : 'error'})`);
  }

  /**
   * Registra um erro ocorrido na conversa
   */
  logError(phoneNumber, error, context = null) {
    const conversationId = this.getActiveConversation(phoneNumber);
    if (!conversationId) return;

    const errorData = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      type: 'error',
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      context: context
    };

    this.addDetailedEvent(phoneNumber, conversationId, errorData);
    
    // Adiciona erro à metadata
    const conversationDir = path.join(this.logsBasePath, phoneNumber, conversationId);
    const metadata = this.loadFromFile(conversationDir, 'metadata.json');
    if (metadata) {
      metadata.errors.push(errorData);
      this.saveToFile(conversationDir, 'metadata.json', metadata);
    }

    console.log(`❌ Logged error for ${phoneNumber}: ${error.message}`);
  }

  /**
   * Finaliza uma conversa
   */
  endConversation(phoneNumber, reason = 'completed') {
    const conversationId = this.getActiveConversation(phoneNumber);
    if (!conversationId) return;

    // Atualiza metadata
    const conversationDir = path.join(this.logsBasePath, phoneNumber, conversationId);
    const metadata = this.loadFromFile(conversationDir, 'metadata.json');
    if (metadata) {
      metadata.endedAt = new Date().toISOString();
      metadata.endReason = reason;
      metadata.duration = new Date() - new Date(metadata.startedAt);
      this.saveToFile(conversationDir, 'metadata.json', metadata);
    }

    // Remove da lista de conversas ativas
    this.activeConversations.delete(phoneNumber);

    console.log(`✅ Ended conversation ${conversationId} for ${phoneNumber}: ${reason}`);
  }

  /**
   * Obtém conversa ativa para um número
   */
  getActiveConversation(phoneNumber) {
    return this.activeConversations.get(phoneNumber);
  }

  /**
   * Lista todas as conversas de um número
   */
  getConversationHistory(phoneNumber, limit = 10) {
    const phoneDir = path.join(this.logsBasePath, phoneNumber);
    if (!fs.existsSync(phoneDir)) return [];

    const conversations = [];
    const conversationDirs = fs.readdirSync(phoneDir);

    for (const conversationId of conversationDirs) {
      const conversationDir = path.join(phoneDir, conversationId);
      const metadata = this.loadFromFile(conversationDir, 'metadata.json');
      
      if (metadata) {
        conversations.push({
          conversationId,
          phoneNumber,
          startedAt: metadata.startedAt,
          endedAt: metadata.endedAt,
          messageCount: metadata.messageCount,
          duration: metadata.duration
        });
      }
    }

    // Ordena por data mais recente
    conversations.sort((a, b) => new Date(b.startedAt) - new Date(a.startedAt));
    
    return conversations.slice(0, limit);
  }

  /**
   * Obtém dados completos de uma conversa
   */
  getConversationData(phoneNumber, conversationId) {
    const conversationDir = path.join(this.logsBasePath, phoneNumber, conversationId);
    if (!fs.existsSync(conversationDir)) return null;

    const metadata = this.loadFromFile(conversationDir, 'metadata.json');
    const messages = this.loadFromFile(conversationDir, 'messages.json');
    const detailed = this.loadFromFile(conversationDir, 'detailed.json');

    return {
      metadata,
      messages: messages?.messages || [],
      detailedEvents: detailed?.events || []
    };
  }

  /**
   * Exporta conversa em formato JSON
   */
  exportConversation(phoneNumber, conversationId, format = 'json') {
    const data = this.getConversationData(phoneNumber, conversationId);
    if (!data) return null;

    switch (format) {
      case 'json':
        return JSON.stringify(data, null, 2);
      case 'csv':
        return this.convertToCSV(data.messages);
      default:
        return data;
    }
  }

  /**
   * Métricas agregadas de todas as conversas
   */
  getAnalytics(days = 7) {
    const analytics = {
      totalConversations: 0,
      totalMessages: 0,
      totalAICalls: 0,
      totalTrinksApiCalls: 0,
      totalTokensUsed: 0,
      averageConversationDuration: 0,
      errorRate: 0,
      topCustomers: [],
      conversationsByDay: {}
    };

    // Implementar coleta de métricas aqui
    // Por agora retorna estrutura vazia

    return analytics;
  }

  // --- Métodos auxiliares ---

  addMessage(phoneNumber, conversationId, messageData) {
    const conversationDir = path.join(this.logsBasePath, phoneNumber, conversationId);
    const messagesLog = this.loadFromFile(conversationDir, 'messages.json');
    
    if (messagesLog) {
      messagesLog.messages.push(messageData);
      this.saveToFile(conversationDir, 'messages.json', messagesLog);
      this.updateMetrics(phoneNumber, conversationId, 'messageCount', 1);
    }
  }

  addDetailedEvent(phoneNumber, conversationId, eventData) {
    const conversationDir = path.join(this.logsBasePath, phoneNumber, conversationId);
    const detailedLog = this.loadFromFile(conversationDir, 'detailed.json');
    
    if (detailedLog) {
      detailedLog.events.push(eventData);
      this.saveToFile(conversationDir, 'detailed.json', detailedLog);
    }
  }

  updateMetrics(phoneNumber, conversationId, metric, value) {
    const conversationDir = path.join(this.logsBasePath, phoneNumber, conversationId);
    const metadata = this.loadFromFile(conversationDir, 'metadata.json');
    
    if (metadata) {
      if (typeof metadata[metric] === 'number') {
        metadata[metric] += value;
      } else {
        metadata[metric] = value;
      }
      metadata.lastActivity = new Date().toISOString();
      this.saveToFile(conversationDir, 'metadata.json', metadata);
    }
  }

  saveToFile(dir, filename, data) {
    try {
      const filePath = path.join(dir, filename);
      fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    } catch (error) {
      console.error(`❌ Error saving ${filename}:`, error);
    }
  }

  loadFromFile(dir, filename) {
    try {
      const filePath = path.join(dir, filename);
      if (!fs.existsSync(filePath)) return null;
      
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    } catch (error) {
      console.error(`❌ Error loading ${filename}:`, error);
      return null;
    }
  }

  convertToCSV(messages) {
    if (!messages || messages.length === 0) return '';

    const headers = ['timestamp', 'sender', 'content', 'type'];
    const csvContent = [
      headers.join(','),
      ...messages.map(msg => [
        msg.timestamp,
        msg.sender,
        `"${msg.content.replace(/"/g, '""')}"`, // Escape quotes
        msg.type || 'text'
      ].join(','))
    ].join('\n');

    return csvContent;
  }
}

// Singleton instance
const conversationLogger = new ConversationLogger();

module.exports = conversationLogger;