const { v4: uuidv4 } = require('uuid');
const aiService = require('./ai');
const trinksService = require('./trinks');
const whatsappService = require('./whatsapp');
const moodAnalysisService = require('./moodAnalysis');
const MessageBatcher = require('./MessageBatcher');
const { socketLogger } = require('../utils/logger');
const conversationLogger = require('../utils/ConversationLogger');

class MessageHandler {
  constructor() {
    this.activeConversations = new Map();
    this.aiModeConversations = new Map(); // Track AI mode per conversation
    this.audioDataStore = new Map(); // Store audio data for serving
    this.io = null;
    this.aiService = aiService; // Initialize AI service
    
    // Initialize message batcher with configurable timeout
    const batchTimeout = parseInt(process.env.MESSAGE_BATCH_TIMEOUT) || 5000; // 5 seconds default
    this.messageBatcher = new MessageBatcher(batchTimeout);
    console.log(`📦 MessageBatcher initialized with ${batchTimeout}ms timeout`);
    
    // Clear sessions on startup
    this.clearAllSessions();
  }
  
  /**
   * Clear all active sessions and conversations on server startup
   */
  clearAllSessions() {
    console.log('🧹 Clearing all active sessions on server startup...');
    
    const conversationCount = this.activeConversations.size;
    const aiModeCount = this.aiModeConversations.size;
    const audioCount = this.audioDataStore.size;
    
    this.activeConversations.clear();
    this.aiModeConversations.clear(); 
    this.audioDataStore.clear();
    
    // Clear API caches to ensure fresh data after server restart
    try {
      const professionalsCache = require('../utils/professionalsCache');
      const servicesCache = require('../utils/servicesCache');
      const availabilityCache = require('../utils/availabilityCache');
      
      professionalsCache.clearCache();
      servicesCache.clearCache();
      availabilityCache.clear();
      
      console.log('🧹 API caches cleared: professionals, services & availability');
    } catch (error) {
      console.warn('⚠️ Failed to clear API caches:', error.message);
    }
    
    console.log(`🧹 Sessions cleared: ${conversationCount} conversations, ${aiModeCount} AI modes, ${audioCount} audio files`);
    
    // Also clear mood analysis service data if needed (optional - may want to preserve mood history)
    // moodAnalysisService could have a clearAllData method if desired
  }

  /**
   * Extract response text from AI result object
   * Handles different response structures safely
   */
  extractResponseText(response) {
    if (typeof response === 'string') return response;
    if (response?.content && typeof response.content === 'string') return response.content;
    if (response?.response && typeof response.response === 'string') return response.response;
    return 'Desculpe, tive um problema para processar sua mensagem.';
  }

  initializeSocket(io) {
    this.io = io;
    console.log('🔧 MessageHandler initialized with Socket.io');
    
    // Set WhatsApp service message handler
    whatsappService.setMessageHandler(this);
    console.log('📱 WhatsApp service message handler configured');
    
    io.on('connection', (socket) => {
      const connectedClients = io.engine.clientsCount;
      const isDevelopment = process.env.NODE_ENV === 'development';
      const shouldLogVerbose = connectedClients > (isDevelopment ? 10 : 50) || isDevelopment;
      
      if (shouldLogVerbose) {
        console.log(`\n🔌 NEW CONNECTION: ${socket.id} (${connectedClients} total)`);
        
        // Check WhatsApp status apenas se necessário
        const whatsappStatus = whatsappService.getStatus();
        if (!whatsappStatus.isConnected) {
          console.log('🔍 WhatsApp status:', whatsappStatus.connectionStatus);
        }
      }
      
      // Send current QR code if available (for late joiners)
      if (whatsappService.sendCurrentQRToClient) {
        const qrSent = whatsappService.sendCurrentQRToClient(socket);
        if (qrSent && shouldLogVerbose) {
          console.log(`📱 QR code sent to: ${socket.id}`);
        }
      }
      
      if (shouldLogVerbose) {
        console.log('===================================\n');
      }

      // Handle new message from user
      socket.on('send_message', async (data) => {
        await this.handleUserMessage(socket, data);
      });

      // Handle audio message from user
      socket.on('send_audio', async (data) => {
        await this.handleUserAudio(socket, data);
      });

      // Handle customer search
      socket.on('search_customer', async (data) => {
        await this.handleCustomerSearch(socket, data);
      });

      // Handle conversation selection
      socket.on('select_conversation', (data) => {
        this.handleConversationSelection(socket, data);
      });

      // Handle simulated customer message
      socket.on('simulate_customer_message', async (data) => {
        await this.handleSimulatedCustomerMessage(socket, data);
      });

      // Handle AI mode toggle
      socket.on('toggle_ai_mode', (data) => {
        this.handleToggleAiMode(socket, data);
      });

      // Handle customer starting conversation
      socket.on('customer_start_conversation', async (data) => {
        await this.handleCustomerStartConversation(socket, data);
      });

      // Handle customer sending message
      socket.on('customer_send_message', async (data) => {
        await this.handleCustomerSendMessage(socket, data);
      });

      // Handle customer sending audio message
      socket.on('customer_send_audio', async (data) => {
        await this.handleCustomerSendAudio(socket, data);
      });

      // Handle typing events
      socket.on('user_typing', (data) => {
        socket.broadcast.emit('user_typing', data);
      });

      socket.on('user_stop_typing', (data) => {
        socket.broadcast.emit('user_stop_typing', data);
      });

      // WhatsApp connection events
      socket.on('whatsapp_connect', async (data) => {
        await this.handleWhatsAppConnect(socket, data);
      });

      socket.on('whatsapp_disconnect', async (data) => {
        await this.handleWhatsAppDisconnect(socket, data);
      });

      socket.on('whatsapp_get_status', (data) => {
        this.handleWhatsAppGetStatus(socket, data);
      });

      socket.on('whatsapp_load_history', async (data) => {
        await this.handleWhatsAppLoadHistory(socket, data);
      });

      socket.on('whatsapp_load_more_history', async (data) => {
        await this.handleLoadMoreWhatsAppHistory(socket, data);
      });

      socket.on('whatsapp_send_message', async (data) => {
        await this.handleWhatsAppSendMessage(socket, data);
      });

      socket.on('whatsapp_clear_session', async (data) => {
        await this.handleWhatsAppClearSession(socket, data);
      });

      // Handle conversation history loading
      socket.on('load_conversation_history', async (data) => {
        await this.handleLoadConversationHistory(socket, data);
      });

      // Load all WAHA chats (manually triggered only - removed auto-load on startup)
      socket.on('load_all_chats', async (data) => {
        console.log('📚 Manual load_all_chats requested - this should only be used for manual refresh');
        await this.handleLoadAllChats(socket, data);
      });

      // Manual appointment scheduling handlers
      socket.on('get_services', async (data) => {
        await this.handleGetServices(socket, data);
      });

      socket.on('get_availability', async (data) => {
        await this.handleGetAvailability(socket, data);
      });

      socket.on('create_manual_appointment', async (data) => {
        await this.handleCreateManualAppointment(socket, data);
      });

      // Test event for simulating WhatsApp messages
      socket.on('test_whatsapp_message', async (data) => {
        console.log('🧪 Test WhatsApp message received:', data);

        // Simulate the WhatsApp message processing
        if (this.whatsappService) {
          await this.whatsappService.handleIncomingMessage(data);
        } else {
          console.log('⚠️ WhatsApp service not available for test');
        }
      });

      // Handler for mood demo page
      socket.on('simulate_whatsapp_message', async (data) => {
        console.log('🎭 Simulating WhatsApp message for mood demo:', data);
        
        try {
          // Simulate incoming WhatsApp message format
          const simulatedPayload = {
            from: `${data.customerPhone}@c.us`,
            fromName: data.customerName || 'Cliente Demo',
            text: data.content,
            type: data.type || 'text',
            hasMedia: false,
            fromMe: false,
            timestamp: Date.now()
          };
          
          // Process the simulated message through the normal WhatsApp flow
          await this.handleWhatsAppMessage({
            customerPhone: data.customerPhone,
            customerName: data.customerName || 'Cliente Demo',
            content: data.content,
            type: data.type || 'text'
          });
          
          console.log('✅ Simulated message processed successfully');
        } catch (error) {
          console.error('❌ Error processing simulated message:', error.message);
        }
      });

      socket.on('disconnect', (reason) => {
        const connectedClients = io.engine.clientsCount;
        const isDevelopment = process.env.NODE_ENV === 'development';
        const shouldLogDisconnect = isDevelopment || 
          reason === 'transport error' || 
          reason === 'client namespace disconnect' ||
          connectedClients < 5; // Log quando há poucas conexões
        
        if (shouldLogDisconnect) {
          console.log(`🔌 DISCONNECT: ${socket.id} (${connectedClients} remain) - ${reason}`);
        }
      });
    });
  }

  async handleUserMessage(socket, data) {
    try {
      const { content, customerPhone, conversationId } = data;
      
      // Check if AI mode is enabled for this conversation
      const isAiMode = this.aiModeConversations.get(conversationId) ?? true;
      
      if (!isAiMode) {
        // AI mode is disabled - this message is from SALON STAFF to CUSTOMER
        console.log(`👨‍💼 Human agent message to ${customerPhone}: ${content}`);

        // Create salon/agent message (should appear on right side - sent)
        const salonMessage = {
          id: uuidv4(),
          content,
          sender: 'ai', // Use 'ai' so it appears on the right side (sent by salon)
          timestamp: new Date(),
          status: 'sent',
          type: 'text'
        };

        // Broadcast to all clients
        if (this.io) {
          this.io.emit('message_sent', {
            conversationId,
            message: salonMessage
          });
        }

        // Update conversation
        await this.updateConversation(conversationId, customerPhone, salonMessage);

        // Send to WhatsApp (attempt even if connection check fails)
        try {
          const whatsappService = require('./whatsapp');
          console.log(`📱 Attempting to send message to WhatsApp: ${customerPhone}`);
          console.log(`🔌 WhatsApp connection status: ${whatsappService.isConnected ? 'connected' : 'disconnected'}`);
          
          // Try to send message regardless of connection status check
          await whatsappService.sendMessage(customerPhone, content);
          console.log(`✅ Message sent to WhatsApp successfully`);
        } catch (whatsappError) {
          console.error(`❌ Failed to send message to WhatsApp:`, whatsappError.message);
          console.error(`🔍 WhatsApp service status:`, {
            isConnected: whatsappService.isConnected,
            connectionStatus: whatsappService.connectionStatus,
            connectionType: whatsappService.connectionType
          });
        }
        
        return;
      }
      
      // AI mode is enabled - this is a message FROM CUSTOMER to SALON
      console.log(`📨 Customer message from ${customerPhone}: ${content}`);

      // Create user message (should appear on left side - received)
      const userMessage = {
        id: uuidv4(),
        content,
        sender: 'user',
        timestamp: new Date(),
        status: 'delivered'
      };

      // Emit user message immediately
      socket.emit('message_received', {
        conversationId,
        message: userMessage
      });

      // Update conversation
      await this.updateConversation(conversationId, customerPhone, userMessage);

      // Performance metrics
      const metrics = {
        startTime: Date.now(),
        steps: {}
      };

      // Show typing indicator
      socket.emit('ai_typing_start', { conversationId });

      // Step 1: Get customer data
      metrics.steps.customerSearchStart = Date.now();
      const customerData = await trinksService.getCustomerByPhone(customerPhone);
      metrics.steps.customerSearchEnd = Date.now();
      
      console.log(`⏱️ Customer search took ${metrics.steps.customerSearchEnd - metrics.steps.customerSearchStart}ms`);

      // Update customer name if found
      if (customerData && !customerData._notFound && customerData.nome) {
        this.updateCustomerName(customerPhone, customerData.nome);
      }

      // Step 2: Process with AI for response
      metrics.steps.aiProcessingStart = Date.now();
      const aiResult = await aiService.processMessage(customerPhone, content, customerData);
      metrics.steps.aiProcessingEnd = Date.now();
      
      console.log(`⏱️ AI processing took ${metrics.steps.aiProcessingEnd - metrics.steps.aiProcessingStart}ms`);
      
      // Calculate typing duration based on response length
      const responseText = this.extractResponseText(aiResult.response);
      const typingDuration = this.calculateTypingDuration(responseText);
      
      // Start typing with realistic delay
      setTimeout(() => {
        socket.emit('ai_typing_start', { conversationId });
        console.log(`⌨️ AI typing for ${typingDuration}ms...`);
      }, 300); // Small delay before starting to type
      
      // Send response after typing duration
      setTimeout(async () => {
        try {
          // Create AI response message
          const aiMessage = {
            id: uuidv4(),
            content: this.extractResponseText(aiResult.response),
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent',
            debugInfo: aiResult.debugInfo
          };

          // 📝 Log resposta do sistema no ConversationLogger
          const processingTime = metrics.steps.aiProcessingEnd - metrics.steps.aiProcessingStart;
          await conversationLogger.logSystemResponse(customerPhone, aiMessage.content, 'text', {
            usedTemplate: aiResult.debugInfo?.usedAvailabilityTemplate || aiResult.debugInfo?.templateUsed,
            duration: processingTime,
            stage: aiResult.stage || 'unknown'
          });

          // Stop typing indicator
          socket.emit('ai_typing_stop', { conversationId });

          // Send AI response
          socket.emit('message_sent', {
            conversationId,
            message: aiMessage
          });

          // Update conversation
          await this.updateConversation(conversationId, customerPhone, aiMessage);

          console.log(`🤖 AI Response to ${customerPhone}: ${aiResult.response}`);
          
          // Log performance metrics
          const totalTime = Date.now() - metrics.startTime;
          logger.info('📊 Message processing performance', {
            totalTime: `${totalTime}ms`,
            customerSearchTime: `${metrics.steps.customerSearchEnd - metrics.steps.customerSearchStart}ms`,
            aiProcessingTime: `${metrics.steps.aiProcessingEnd - metrics.steps.aiProcessingStart}ms`,
            typingSimulationTime: `${typingDuration}ms`,
            phone: customerPhone,
            conversationId
          });
          
          // Alert if too slow
          if (totalTime > 3000) {
            logger.warn(`⚠️ SLOW RESPONSE: Message processing took ${totalTime}ms (threshold: 3000ms)`, {
              phone: customerPhone,
              conversationId
            });
          }

        } catch (error) {
          console.error('Error processing AI response:', error);
          
          socket.emit('ai_typing_stop', { conversationId });
          socket.emit('message_sent', {
            conversationId,
            message: {
              id: uuidv4(),
              content: "Desculpe, tive um problema técnico. Pode repetir sua mensagem?",
              sender: 'ai',
              timestamp: new Date(),
              status: 'sent'
            }
          });
        }
      }, processingDelay);

    } catch (error) {
      console.error('Error handling user message:', error);
      socket.emit('error', { message: 'Erro ao processar mensagem' });
    }
  }

  async handleUserAudio(socket, data) {
    try {
      const { audioData, duration, customerPhone, conversationId } = data;
      
      console.log(`🎤 Audio from ${customerPhone}: ${duration}s`);
      console.log(`🔍 Audio data type: ${typeof audioData}`);
      console.log(`🔍 Audio data length: ${audioData ? audioData.length : 'null'}`);
      console.log(`🔍 Audio data starts with: ${audioData ? audioData.substring(0, 50) : 'null'}`);

      // Create unique audio ID and store the audio data
      const audioId = uuidv4();
      this.storeAudioData(audioId, audioData);

      // Create audio message with proper URL
      const audioMessage = {
        id: uuidv4(),
        content: 'Mensagem de áudio',
        sender: 'user',
        timestamp: new Date(),
        status: 'delivered',
        type: 'audio',
        audioUrl: `/api/audio/${audioId}`, // Use proper audio serving endpoint
        audioDuration: duration,
        isProcessing: true
      };

      // Emit audio message immediately to all clients
      console.log(`📤 Broadcasting audio message to all clients`);
      if (this.io) {
        this.io.emit('message_received', {
          conversationId,
          message: audioMessage
        });
      }

      // Update conversation
      await this.updateConversation(conversationId, customerPhone, audioMessage);

      // Show processing indicator
      socket.emit('ai_typing_start', { conversationId });

      // Get customer data
      const customerData = await trinksService.getCustomerByPhone(customerPhone);

      // Process with AI (longer delay for audio processing)
      const processingDelay = Math.max(2000, duration * 500); // At least 2s, or 0.5s per second of audio
      
      setTimeout(async () => {
        try {
          // Process audio with AI (transcribe + understand intent)
          const aiResult = await aiService.processMessage(customerPhone, audioData, customerData, 'audio');
          
          // Update audio message as processed with transcription
          const processedAudioMessage = {
            ...audioMessage,
            isProcessing: false,
            audioUrl: audioMessage.audioUrl, // Keep the proper URL endpoint
            audioTranscription: aiResult.transcription || null
          };
          
          // Broadcast updated audio message to all clients
          console.log(`📤 Broadcasting updated audio message to all clients`);
          if (this.io) {
            this.io.emit('message_updated', {
              conversationId,
              message: processedAudioMessage
            });
          }

          // Create AI response message
          const aiMessage = {
            id: uuidv4(),
            content: this.extractResponseText(aiResult.response),
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent',
            type: 'text',
            debugInfo: aiResult.debugInfo
          };

          // Stop typing indicator
          socket.emit('ai_typing_stop', { conversationId });

          // Send AI response
          socket.emit('message_sent', {
            conversationId,
            message: aiMessage
          });

          // Update conversation
          await this.updateConversation(conversationId, customerPhone, aiMessage);

          console.log(`🤖 AI Response to audio from ${customerPhone}: ${aiResult.response}`);

        } catch (error) {
          console.error('Error processing AI audio response:', error);
          
          socket.emit('ai_typing_stop', { conversationId });
          socket.emit('message_sent', {
            conversationId,
            message: {
              id: uuidv4(),
              content: "Desculpe, tive dificuldade para processar seu áudio. Pode repetir ou digitar sua mensagem?",
              sender: 'ai',
              timestamp: new Date(),
              status: 'sent',
              type: 'text'
            }
          });
        }
      }, processingDelay);

    } catch (error) {
      console.error('Error handling user audio:', error);
      socket.emit('error', { message: 'Erro ao processar áudio' });
    }
  }

  async handleCustomerSearch(socket, data) {
    try {
      const { phone } = data;
      console.log(`🔍 Searching customer: ${phone}`);

      const customer = await trinksService.getCustomerByPhone(phone);

      if (customer) {
        // Create or get existing conversation
        const conversationId = await this.getOrCreateConversation(phone, customer.nome);
        const conversation = this.activeConversations.get(conversationId);

        // Load historical messages if conversation is new or has no messages
        if (conversation && conversation.messages.length === 0 && !conversation.whatsappHistoryLoaded) {
          console.log(`📚 Loading WhatsApp history for customer search: ${phone}`);
          try {
            conversation.whatsappHistoryLoaded = true; // Mark as loading to prevent duplicates

            const history = await whatsappService.loadHistoryFromWhatsApp(phone, 10);
            if (history.length > 0) {
              console.log(`✅ Loaded ${history.length} messages from WhatsApp history for customer search`);

              // Add historical messages to conversation
              conversation.messages = [...history];
              conversation.lastMessage = history.length > 0 ? history[0].timestamp : new Date();

              // Update conversation in memory
              this.activeConversations.set(conversationId, conversation);

              // Broadcast conversation update to all clients
              if (this.io) {
                this.io.emit('conversation_updated', {
                  conversationId,
                  conversation
                });
              }

              console.log(`📤 Broadcasting ${history.length} historical messages to clients`);
            } else {
              console.log(`📭 No historical messages found for ${phone}`);
            }
          } catch (historyError) {
            console.error('Error loading WhatsApp history for customer search:', historyError);
            // Don't fail the search if history loading fails
          }
        }

        socket.emit('customer_found', {
          customer,
          conversationId
        });
      } else {
        // Create conversation for new customer
        const conversationId = await this.getOrCreateConversation(phone, 'Cliente Novo');

        socket.emit('customer_not_found', {
          phone,
          conversationId
        });
      }

    } catch (error) {
      console.error('Error searching customer:', error);
      socket.emit('error', { message: 'Erro ao buscar cliente' });
    }
  }

  handleConversationSelection(socket, data) {
    const { conversationId } = data;
    const conversation = this.activeConversations.get(conversationId);
    
    if (conversation) {
      socket.emit('conversation_selected', {
        conversationId,
        conversation
      });
    }
  }

  async handleSimulatedCustomerMessage(socket, data) {
    try {
      const { phone, message } = data;
      console.log(`🎭 Simulated customer message from ${phone}: ${message}`);

      // Get or create conversation
      let conversationId = null;
      for (const [id, conversation] of this.activeConversations) {
        if (conversation.customerPhone === phone) {
          conversationId = id;
          break;
        }
      }

      if (!conversationId) {
        // If no conversation exists, create one first
        const customer = await trinksService.getCustomerByPhone(phone);
        const customerName = customer ? customer.nome : 'Cliente Novo';
        conversationId = await this.getOrCreateConversation(phone, customerName);
      }

      // Create customer message
      const customerMessage = {
        id: uuidv4(),
        content: message,
        sender: 'user',
        timestamp: new Date(),
        status: 'delivered',
        type: 'text'
      };

      // Emit customer message immediately
      socket.emit('message_received', {
        conversationId,
        message: customerMessage
      });

      // Update conversation
      await this.updateConversation(conversationId, phone, customerMessage);

      // Check if AI mode is enabled for this conversation
      const isAiMode = this.aiModeConversations.get(conversationId) ?? true;
      
      if (!isAiMode) {
        // AI mode is disabled, don't send automatic response
        console.log(`🤖 AI disabled for conversation ${conversationId}, not responding automatically`);
        return;
      }

      // Show typing indicator
      socket.emit('ai_typing_start', { conversationId });

      // Get customer data
      const customerData = await trinksService.getCustomerByPhone(phone);

      // Process with AI (with realistic delay)
      const processingDelay = this.calculateResponseDelay(message);
      
      setTimeout(async () => {
        try {
          const aiResult = await aiService.processMessage(phone, message, customerData);
          
          // Create AI response message
          const aiMessage = {
            id: uuidv4(),
            content: this.extractResponseText(aiResult.response),
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent',
            debugInfo: aiResult.debugInfo
          };

          // Stop typing indicator
          socket.emit('ai_typing_stop', { conversationId });

          // Send AI response
          socket.emit('message_sent', {
            conversationId,
            message: aiMessage
          });

          // Update conversation
          await this.updateConversation(conversationId, phone, aiMessage);

          console.log(`🤖 AI Response to simulated customer ${phone}: ${aiResult.response}`);

        } catch (error) {
          console.error('Error processing simulated AI response:', error);
          
          socket.emit('ai_typing_stop', { conversationId });
          socket.emit('message_sent', {
            conversationId,
            message: {
              id: uuidv4(),
              content: "Desculpe, tive um problema técnico. Pode repetir sua mensagem?",
              sender: 'ai',
              timestamp: new Date(),
              status: 'sent'
            }
          });
        }
      }, processingDelay);

    } catch (error) {
      console.error('Error handling simulated customer message:', error);
      socket.emit('error', { message: 'Erro ao processar mensagem simulada' });
    }
  }

  handleToggleAiMode(socket, data) {
    try {
      const { conversationId, isAiMode } = data;
      console.log(`🔄 Toggling AI mode for conversation ${conversationId}: ${isAiMode}`);
      
      // Update AI mode for conversation
      this.aiModeConversations.set(conversationId, isAiMode);
      
      // Create system message about the transfer
      const systemMessage = {
        id: uuidv4(),
        content: isAiMode 
          ? "🤖 Conversa retornada para IA automática"
          : "👤 Conversa transferida para atendente humano",
        sender: 'system',
        timestamp: new Date(),
        status: 'sent',
        type: 'text'
      };

      // Send system message to all clients
      if (this.io) {
        this.io.emit('message_sent', {
          conversationId,
          message: systemMessage
        });
      }

      // Update conversation with system message
      const conversation = this.activeConversations.get(conversationId);
      if (conversation) {
        conversation.messages.push(systemMessage);
        conversation.lastMessage = systemMessage.timestamp;
        this.activeConversations.set(conversationId, conversation);
      }
      
      // Broadcast AI mode change to all clients
      if (this.io) {
        this.io.emit('ai_mode_toggled', {
          conversationId,
          isAiMode
        });
      }
      
    } catch (error) {
      console.error('Error toggling AI mode:', error);
      socket.emit('error', { message: 'Erro ao alterar modo de IA' });
    }
  }

  async handleCustomerStartConversation(socket, data) {
    try {
      const { phone, name } = data;
      console.log(`👤 Cliente ${name} (${phone}) iniciando conversa`);

      // Get customer data from Trinks API
      const customerData = await trinksService.getCustomerByPhone(phone);
      const customerName = customerData ? customerData.nome : name;

      // Update customer name if found
      if (customerData && !customerData._notFound && customerData.nome) {
        this.updateCustomerName(phone, customerData.nome);
      }

      // Create or get existing conversation
      const conversationId = await this.getOrCreateConversation(phone, customerName);
      
      // Confirm to customer that conversation started
      socket.emit('conversation_started', {
        conversationId,
        customer: customerData || { nome: name, telefone: phone }
      });

      // Notify all clients about new customer conversation
      console.log(`📤 Broadcasting customer_found to all clients`);
      if (this.io) {
        this.io.emit('customer_found', {
          customer: customerData || { nome: name, telefone: phone },
          conversationId
        });
      }

      console.log(`💬 Conversa iniciada: ${conversationId} para ${customerName || name || phone}`);

    } catch (error) {
      console.error('Erro ao iniciar conversa do cliente:', error);
      socket.emit('error', { message: 'Erro ao iniciar conversa' });
    }
  }

  async handleCustomerSendMessage(socket, data) {
    try {
      const { conversationId, content, customerPhone } = data;
      console.log(`👤 Mensagem do cliente ${customerPhone}: ${content}`);
      console.log(`📡 Broadcasting to salon - conversationId: ${conversationId}`);

      // Create customer message
      const customerMessage = {
        id: uuidv4(),
        content,
        sender: 'user',
        timestamp: new Date(),
        status: 'delivered',
        type: 'text'
      };

      // Update conversation
      await this.updateConversation(conversationId, customerPhone, customerMessage);

      // Perform mood analysis on customer message (only for text messages)
      let moodAnalysis = null;
      if (content && content.trim().length > 0) {
        try {
          console.log(`🎭 [CUSTOMER DEBUG] Starting mood analysis for customer app message from ${customerPhone}`);
          console.log(`🎭 [CUSTOMER DEBUG] Message content: "${content}"`);
          console.log(`🎭 [CUSTOMER DEBUG] Conversation ID: ${conversationId}`);
          
          const conversationHistory = this.activeConversations.get(conversationId)?.messages || [];
          console.log(`🎭 [CUSTOMER DEBUG] Conversation history length: ${conversationHistory.length}`);
          
          moodAnalysis = await moodAnalysisService.analyzeMessageMood(content, customerPhone, conversationHistory);
          console.log(`🎭 [CUSTOMER DEBUG] Mood analysis result:`, moodAnalysis);
          
          // Check if conversation should be escalated based on mood
          const escalationCheck = moodAnalysisService.shouldEscalateConversation(customerPhone);
          if (escalationCheck.escalate) {
            console.log(`🚨 MOOD ESCALATION RECOMMENDED for ${customerPhone}:`, escalationCheck);
            
            // Emit escalation alert to all clients
            if (this.io) {
              this.io.emit('mood_escalation_alert', {
                conversationId,
                customerPhone,
                escalationReason: escalationCheck.reason,
                moodCategory: escalationCheck.moodCategory || 'unknown',
                confidence: escalationCheck.confidence || 0,
                timestamp: new Date().toISOString()
              });
            }
          }
          
          // Store mood in customer message for reference
          customerMessage.moodAnalysis = moodAnalysis;
          
          // Emit mood update to clients for analytics/UI
          if (this.io) {
            const moodUpdatePayload = {
              conversationId,
              customerPhone,
              moodAnalysis: {
                category: moodAnalysis.moodCategory,
                score: moodAnalysis.moodScore,
                confidence: moodAnalysis.confidence,
                escalationRecommended: moodAnalysis.escalationRecommended,
                trend: moodAnalysis.conversationTrend,
                timestamp: new Date().toISOString()
              }
            };
            
            console.log(`🎭 [CUSTOMER DEBUG] Emitting mood update to ${this.io.engine.clientsCount} connected clients:`, moodUpdatePayload);
            this.io.emit('customer_mood_update', moodUpdatePayload);
            console.log(`🎭 [CUSTOMER DEBUG] Mood update emitted successfully`);
          } else {
            console.log(`🎭 [CUSTOMER DEBUG] No socket.io instance available to emit mood update`);
          }
          
        } catch (moodError) {
          console.error(`❌ Error analyzing mood for customer app message from ${customerPhone}:`, moodError.message);
        }
      }

      // Emit to all clients
      console.log(`📤 Emitting message_received to all clients`);
      if (this.io) {
        this.io.emit('message_received', {
          conversationId,
          message: customerMessage
        });
      }

      // Check if AI mode is enabled for this conversation
      const isAiMode = this.aiModeConversations.get(conversationId) ?? true;
      
      if (!isAiMode) {
        // AI mode is disabled, don't send automatic response
        console.log(`🤖 AI disabled for conversation ${conversationId}, not responding automatically`);
        return;
      }

      // Show typing indicator to customer
      socket.emit('salon_typing', { conversationId, isTyping: true });
      
      // Broadcast typing to salon
      socket.broadcast.emit('ai_typing_start', { conversationId });

      // Get customer data
      const customerData = await trinksService.getCustomerByPhone(customerPhone);

      // Update customer name if found
      if (customerData && !customerData._notFound && customerData.nome) {
        this.updateCustomerName(customerPhone, customerData.nome);
      }

      // Process with AI (with realistic delay)
      const processingDelay = this.calculateResponseDelay(content);
      
      setTimeout(async () => {
        try {
          const aiResult = await aiService.processMessage(customerPhone, content, customerData);
          
          // Create AI response message
          const aiMessage = {
            id: uuidv4(),
            content: this.extractResponseText(aiResult.response),
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent',
            debugInfo: aiResult.debugInfo
          };

          // Stop typing indicators
          socket.emit('salon_typing', { conversationId, isTyping: false });
          socket.broadcast.emit('ai_typing_stop', { conversationId });

          // Send AI response to customer
          socket.emit('salon_response', {
            conversationId,
            message: aiMessage
          });

          // Send AI response to salon
          socket.broadcast.emit('message_sent', {
            conversationId,
            message: aiMessage
          });

          // Update conversation
          await this.updateConversation(conversationId, customerPhone, aiMessage);

          const responseText = typeof aiResult.response === 'string' ? aiResult.response : String(aiResult.response);
          console.log(`🤖 AI Response sent to customer ${customerPhone}: ${responseText}`);

        } catch (error) {
          console.error('Error processing customer AI response:', error);
          
          // Stop typing indicators
          socket.emit('salon_typing', { conversationId, isTyping: false });
          socket.broadcast.emit('ai_typing_stop', { conversationId });
          
          const errorMessage = {
            id: uuidv4(),
            content: "Desculpe, tive um problema técnico. Pode repetir sua mensagem?",
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent'
          };

          socket.emit('salon_response', {
            conversationId,
            message: errorMessage
          });
        }
      }, processingDelay);

    } catch (error) {
      console.error('Error handling customer message:', error);
      socket.emit('error', { message: 'Erro ao processar mensagem' });
    }
  }

  async handleCustomerSendAudio(socket, data) {
    try {
      const { conversationId, audioData, duration, customerPhone } = data;
      console.log(`🎙️ Audio message from customer ${customerPhone} - Duration: ${duration}s`);
      console.log(`📡 Broadcasting audio to salon - conversationId: ${conversationId}`);

      // Generate unique ID for audio
      const audioId = uuidv4();
      
      // Store audio data for serving
      this.audioDataStore.set(audioId, audioData);
      
      // Clean up old audio data after 1 hour
      setTimeout(() => {
        this.audioDataStore.delete(audioId);
      }, 3600000); // 1 hour

      // Create audio URL
      const audioUrl = `/api/audio/${audioId}`;

      // Transcribe audio
      console.log('🎤 Starting audio transcription for customer message...');
      let transcription = null;
      try {
        if (this.aiService) {
          transcription = await this.aiService.transcribeAudio(audioData);
          console.log('✅ Audio transcription successful:', transcription);
        } else {
          console.warn('⚠️ AI Service not available for transcription');
        }
      } catch (error) {
        console.error('❌ Error during audio transcription:', error.message);
        console.error('❌ Transcription error stack:', error.stack);
      }

      // Create customer audio message
      const customerAudioMessage = {
        id: uuidv4(),
        content: `Mensagem de voz (${duration}s)`,
        sender: 'user',
        timestamp: new Date(),
        status: 'delivered',
        type: 'audio',
        audioUrl: audioUrl,
        audioDuration: duration,
        audioTranscription: transcription // Use audioTranscription to match frontend
      };

      // Update conversation
      await this.updateConversation(conversationId, customerPhone, customerAudioMessage);

      // Broadcast to salon
      console.log(`📤 Emitting audio message to salon clients`);
      console.log('🔍 Audio message being sent to salon:', JSON.stringify({
        conversationId,
        message: {
          ...customerAudioMessage,
          audioUrl: customerAudioMessage.audioUrl.substring(0, 20) + '...' // Truncate for readability
        }
      }, null, 2));
      
      // Emit to all clients
      if (this.io) {
        this.io.emit('message_received', {
          conversationId,
          message: customerAudioMessage
        });
      }

      // Check if AI mode is enabled for this conversation
      const isAiMode = this.aiModeConversations.get(conversationId) ?? true;
      
      if (!isAiMode) {
        // AI mode is disabled, don't send automatic response
        console.log(`🤖 AI disabled for conversation ${conversationId}, not responding to audio automatically`);
        return;
      }

      // Show typing indicator to customer
      socket.emit('salon_typing', { conversationId, isTyping: true });
      
      // Broadcast typing to salon
      socket.broadcast.emit('ai_typing_start', { conversationId });

      // Process the transcribed audio with AI
      setTimeout(async () => {
        try {
          let aiResponseContent;
          let aiResponse = null; // Initialize aiResponse outside the if block
          
          if (transcription && this.aiService) {
            console.log(`🤖 Processing transcribed message: "${transcription}"`);
            console.log(`🔍 AI Service available:`, !!this.aiService);
            // Use the transcription as the message content for AI processing
            aiResponse = await this.aiService.processMessage(
              customerPhone,
              transcription,
              null, // customerData - will be fetched by AI service
              'text' // Process as text since we have transcription
            );
            // Extract the response text from the AI response object
            aiResponseContent = this.extractResponseText(aiResponse);
            console.log('🤖 AI response extracted:', aiResponseContent);
          } else {
            // Fallback if no transcription available
            if (!transcription) console.log('⚠️ No transcription available for AI processing');
            if (!this.aiService) console.log('⚠️ AI Service not available');
            aiResponseContent = 'Recebi sua mensagem de voz! Nossa equipe irá ouvi-la em breve. 🎧';
          }

          const aiMessage = {
            id: uuidv4(),
            content: aiResponseContent,
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent',
            debugInfo: aiResponse && typeof aiResponse === 'object' ? aiResponse.debugInfo : null
          };

          // Stop typing indicators
          socket.emit('salon_typing', { conversationId, isTyping: false });
          socket.broadcast.emit('ai_typing_stop', { conversationId });

          // Send AI response to customer
          socket.emit('salon_response', {
            conversationId,
            message: aiMessage
          });

          // Send AI response to salon
          socket.broadcast.emit('message_sent', {
            conversationId,
            message: aiMessage
          });

          // Update conversation
          await this.updateConversation(conversationId, customerPhone, aiMessage);

          console.log(`🤖 AI Response sent for audio message from ${customerPhone}`);

        } catch (error) {
          console.error('Error processing customer audio response:', error);
          
          // Stop typing indicators
          socket.emit('salon_typing', { conversationId, isTyping: false });
          socket.broadcast.emit('ai_typing_stop', { conversationId });
        }
      }, 2000);

    } catch (error) {
      console.error('❌ Error handling customer audio message:', error);
      socket.emit('error', { message: 'Erro ao processar mensagem de áudio' });
    }
  }

  async getOrCreateConversation(customerPhone, customerName) {
    // Check if conversation already exists
    for (const [id, conversation] of this.activeConversations) {
      if (conversation.customerPhone === customerPhone) {
        return id;
      }
    }

    // Ensure customerName is never undefined
    const safeName = customerName || customerPhone || 'Cliente desconhecido';

    // Create new conversation
    const conversationId = uuidv4();
    const conversation = {
      id: conversationId,
      customerPhone,
      customerName: safeName,
      messages: [],
      lastMessage: new Date(),
      unreadCount: 0
    };

    this.activeConversations.set(conversationId, conversation);
    
    // 📝 Log início de nova sessão no ConversationLogger
    await conversationLogger.logSessionStart(customerPhone, conversationId);
    
    // Broadcast new conversation to all clients
    if (this.io) {
      this.io.emit('new_conversation', conversation);
    }

    return conversationId;
  }

  async updateConversation(conversationId, customerPhone, message) {
    let conversation = this.activeConversations.get(conversationId);
    
    if (!conversation) {
      console.warn(`⚠️ Conversation ${conversationId} not found for updateConversation. Creating basic conversation.`);
      // Create basic conversation without async logging (to avoid complexity)
      conversation = {
        id: conversationId,
        customerPhone,
        customerName: 'Cliente desconhecido',
        messages: [],
        lastMessage: new Date(),
        unreadCount: 0
      };
      this.activeConversations.set(conversationId, conversation);
    }

    conversation.messages.push(message);
    conversation.lastMessage = message.timestamp;
    
    if (message.sender === 'user') {
      conversation.unreadCount++;
    }

    this.activeConversations.set(conversationId, conversation);

    // Broadcast conversation update
    if (this.io) {
      this.io.emit('conversation_updated', {
        conversationId,
        conversation
      });
    }
  }

  // Update customer name in existing conversation
  updateCustomerName(customerPhone, newName) {
    for (const [id, conversation] of this.activeConversations) {
      if (conversation.customerPhone === customerPhone) {
        conversation.customerName = newName;
        this.activeConversations.set(id, conversation);
        
        // Broadcast conversation update with new name
        if (this.io) {
          this.io.emit('conversation_updated', {
            conversationId: id,
            conversation
          });
          console.log(`📝 Updated customer name for ${customerPhone}: ${newName}`);
        }
        break;
      }
    }
  }

  calculateResponseDelay(message) {
    // Simulate realistic response times based on message complexity
    const baseDelay = 800; // 0.8 second minimum (more natural)
    
    // Calculate words and complexity
    const words = message.split(/\s+/);
    const wordsCount = words.length;
    const hasNumbers = /\d/.test(message);
    const hasQuestions = /\?/.test(message);
    const hasSpecialWords = /(quando|onde|como|quanto|qual|que dia|horário)/i.test(message);
    
    // Base delay on reading time (average human reads 200-250 words per minute)
    const readingDelay = (wordsCount / 250) * 60 * 1000; // Convert to milliseconds
    
    // Add complexity delays
    let complexityDelay = 0;
    if (hasNumbers) complexityDelay += 300; // Numbers require checking
    if (hasQuestions) complexityDelay += 500; // Questions require thinking
    if (hasSpecialWords) complexityDelay += 700; // Specific queries need lookup
    
    // Add thinking time (0.5 to 1.5 seconds)
    const thinkingDelay = Math.random() * 1000 + 500;
    
    // Total delay with realistic bounds
    const totalDelay = baseDelay + readingDelay + complexityDelay + thinkingDelay;
    
    // Keep within reasonable bounds (1 to 4 seconds)
    return Math.max(1000, Math.min(4000, totalDelay));
  }

  calculateTypingDuration(message) {
    // Simulate realistic typing speed: 40-60 WPM (words per minute)
    // Average word length: 5 characters
    // Add some randomness to make it feel human
    
    const messageLength = message.length;
    const wordsCount = Math.ceil(messageLength / 5);
    const baseTime = (wordsCount / 50) * 60 * 1000; // 50 WPM in milliseconds
    
    // Add variations for more natural feel
    const randomFactor = 0.3; // ±30% variation
    const variation = baseTime * randomFactor * (Math.random() - 0.5) * 2;
    
    // Add pauses for punctuation
    const punctuationCount = (message.match(/[.,!?;:]/g) || []).length;
    const punctuationPause = punctuationCount * 150; // 150ms per punctuation
    
    // Add pause for new lines
    const newlineCount = (message.match(/\n/g) || []).length;
    const newlinePause = newlineCount * 300; // 300ms per new line
    
    const totalTime = baseTime + variation + punctuationPause + newlinePause;
    
    // Keep within reasonable bounds (1.5-8 seconds)
    return Math.max(1500, Math.min(8000, totalTime));
  }

  // Get all active conversations
  getAllConversations() {
    return Array.from(this.activeConversations.values());
  }

  // Clear conversation (for testing)
  clearConversation(conversationId) {
    this.activeConversations.delete(conversationId);
  }

  // WhatsApp event handlers
  async handleWhatsAppConnect(socket, data) {
    try {
      const { type, settings } = data; // type: 'web' or 'twilio'
      console.log(`🔗 Connecting WhatsApp via ${type}...`);
      
      // Update WhatsApp settings
      if (settings) {
        whatsappService.updateSettings({
          whatsappEnabled: true,
          whatsappType: type,
          ...settings
        });
      }
      
      await whatsappService.connect();
      
      socket.emit('whatsapp_connect_response', {
        success: true,
        status: whatsappService.getStatus()
      });
      
    } catch (error) {
      console.error('Error connecting WhatsApp:', error);
      socket.emit('whatsapp_connect_response', {
        success: false,
        error: error.message
      });
    }
  }

  async handleWhatsAppDisconnect(socket, data) {
    try {
      console.log('🔌 Disconnecting WhatsApp...');
      await whatsappService.disconnect();
      
      socket.emit('whatsapp_disconnect_response', {
        success: true
      });
      
    } catch (error) {
      console.error('Error disconnecting WhatsApp:', error);
      socket.emit('whatsapp_disconnect_response', {
        success: false,
        error: error.message
      });
    }
  }

  handleWhatsAppGetStatus(socket, data) {
    const status = whatsappService.getStatus();
    socket.emit('whatsapp_status_response', status);
  }

  async handleWhatsAppLoadHistory(socket, data) {
    try {
      const { phoneNumber, limit = 50 } = data;
      console.log(`📚 Loading WhatsApp history for ${phoneNumber}...`);
      
      const history = await whatsappService.loadHistoryFromWhatsApp(phoneNumber, limit);
      
      socket.emit('whatsapp_history_response', {
        success: true,
        phoneNumber,
        history
      });
      
    } catch (error) {
      console.error('Error loading WhatsApp history:', error);
      socket.emit('whatsapp_history_response', {
        success: false,
        error: error.message
      });
    }
  }

  async handleLoadMoreWhatsAppHistory(socket, data) {
    try {
      const { phoneNumber, limit = 10 } = data;
      console.log(`📚 Loading MORE WhatsApp history for ${phoneNumber} (limit: ${limit})`);
      
      // Find conversation to get current message count
      let conversationId = null;
      let currentMessageCount = 0;
      
      for (const [id, conversation] of this.activeConversations) {
        if (conversation.customerPhone === phoneNumber) {
          conversationId = id;
          currentMessageCount = conversation.messages.filter(msg => msg.whatsappMessage).length;
          break;
        }
      }
      
      if (!conversationId) {
        console.log(`❌ No conversation found for ${phoneNumber}`);
        return;
      }
      
      // Load more history (current + additional)
      const totalToLoad = currentMessageCount + limit;
      const allHistory = await whatsappService.loadHistoryFromWhatsApp(phoneNumber, totalToLoad);
      
      // Get only the new messages (the additional ones we haven't loaded yet)
      const newMessages = allHistory.slice(0, allHistory.length - currentMessageCount);
      
      if (newMessages.length > 0) {
        const conversation = this.activeConversations.get(conversationId);
        
        // Add new messages to the beginning of existing messages
        conversation.messages = [...newMessages, ...conversation.messages];
        
        // Emit to all clients
        if (this.io) {
          this.io.emit('whatsapp_history_loaded', {
            conversationId,
            phoneNumber,
            history: newMessages,
            totalHistoryMessages: allHistory.length,
            isLoadMore: true
          });
        }
        
        console.log(`✅ Loaded ${newMessages.length} additional WhatsApp messages`);
      } else {
        console.log('📚 No additional WhatsApp messages found');
      }
      
    } catch (error) {
      console.error('Error loading more WhatsApp history:', error);
    }
  }

  async handleWhatsAppSendMessage(socket, data) {
    try {
      const { phoneNumber, message, conversationId } = data;
      console.log(`📤 Sending WhatsApp message to ${phoneNumber}: ${message}`);
      
      const result = await whatsappService.sendMessage(phoneNumber, message);
      
      // Create message for UI
      const sentMessage = {
        id: uuidv4(),
        content: message,
        sender: 'staff',
        timestamp: new Date(),
        status: 'sent',
        type: 'text',
        whatsappId: result.id || result.sid
      };

      // Update conversation if exists
      if (conversationId) {
        await this.updateConversation(conversationId, phoneNumber, sentMessage);
      }

      socket.emit('whatsapp_send_response', {
        success: true,
        message: sentMessage,
        whatsappResult: result
      });
      
    } catch (error) {
      console.error('Error sending WhatsApp message:', error);
      socket.emit('whatsapp_send_response', {
        success: false,
        error: error.message
      });
    }
  }

  async handleWhatsAppClearSession(socket, data) {
    try {
      console.log('🧹 Clearing WhatsApp session...');
      
      await whatsappService.clearSession();
      
      socket.emit('whatsapp_clear_session_response', {
        success: true,
        message: 'Sessão limpa com sucesso'
      });
      
      // Emit status update
      socket.emit('whatsapp_status', whatsappService.getStatus());
      
    } catch (error) {
      console.error('Error clearing WhatsApp session:', error);
      socket.emit('whatsapp_clear_session_response', {
        success: false,
        error: error.message
      });
    }
  }

  // Handle incoming WhatsApp messages (called by WhatsApp service)
  async handleWhatsAppMessage(messageData) {
    try {
      const { customerPhone, customerName, content, type, audioData, audioDuration, originalMessage } = messageData;
      
      // Skip empty messages (second line of defense)
      if (type !== 'audio' && (!content || content.trim().length === 0)) {
        console.log(`📝 Empty message ignored from ${customerPhone} - second line of defense`);
        return;
      }
      
      console.log(`📱 Processing WhatsApp message from ${customerPhone}: ${content}`);
      
      // Get or create conversation
      let conversationId = null;
      for (const [id, conversation] of this.activeConversations) {
        if (conversation.customerPhone === customerPhone) {
          conversationId = id;
          break;
        }
      }

      if (!conversationId) {
        // Create new conversation
        conversationId = await this.getOrCreateConversation(customerPhone, customerName);
      }

      // Create customer message
      const customerMessage = {
        id: uuidv4(),
        content: content,
        sender: 'user',
        timestamp: new Date(),
        status: 'delivered',
        type: type || 'text',
        whatsappMessage: true
      };
      
      // Add audio-specific fields if it's an audio message
      if (type === 'audio' && audioData) {
        // Create unique audio ID and store the audio data
        const audioId = uuidv4();
        this.storeAudioData(audioId, audioData);

        customerMessage.audioUrl = `/api/audio/${audioId}`; // Use proper audio serving endpoint
        customerMessage.audioDuration = audioDuration || 10;
        console.log(`🎤 WhatsApp audio message: ${audioDuration}s, stored as ${audioId}`);
      }

      // 📝 Log mensagem do cliente no ConversationLogger
      await conversationLogger.logClientMessage(customerPhone, content, type);

      // Emit customer message to all clients
      if (this.io) {
        this.io.emit('message_received', {
          conversationId,
          message: customerMessage
        });
      }

      // Update conversation
      await this.updateConversation(conversationId, customerPhone, customerMessage);

      // Perform mood analysis on customer message (only for text messages for now)
      let moodAnalysis = null;
      if (type !== 'audio' && content && content.trim().length > 0) {
        try {
          console.log(`🎭 [MOOD DEBUG] Starting mood analysis for message from ${customerPhone}`);
          console.log(`🎭 [MOOD DEBUG] Message content: "${content}"`);
          console.log(`🎭 [MOOD DEBUG] Message type: ${type}`);
          console.log(`🎭 [MOOD DEBUG] Conversation ID: ${conversationId}`);
          
          const conversationHistory = this.activeConversations.get(conversationId)?.messages || [];
          console.log(`🎭 [MOOD DEBUG] Conversation history length: ${conversationHistory.length}`);
          
          moodAnalysis = await moodAnalysisService.analyzeMessageMood(content, customerPhone, conversationHistory);
          console.log(`🎭 [MOOD DEBUG] Mood analysis result:`, moodAnalysis);
          
          // Check if conversation should be escalated based on mood
          const escalationCheck = moodAnalysisService.shouldEscalateConversation(customerPhone);
          if (escalationCheck.escalate) {
            console.log(`🚨 MOOD ESCALATION RECOMMENDED for ${customerPhone}:`, escalationCheck);
            
            // Emit escalation alert to all clients
            if (this.io) {
              this.io.emit('mood_escalation_alert', {
                conversationId,
                customerPhone,
                escalationReason: escalationCheck.reason,
                moodCategory: escalationCheck.moodCategory || 'unknown',
                confidence: escalationCheck.confidence || 0,
                timestamp: new Date().toISOString()
              });
            }
          }
          
          // Store mood in customer message for reference
          customerMessage.moodAnalysis = moodAnalysis;
          
          // Emit mood update to clients for analytics/UI
          if (this.io) {
            const moodUpdatePayload = {
              conversationId,
              customerPhone,
              moodAnalysis: {
                category: moodAnalysis.moodCategory,
                score: moodAnalysis.moodScore,
                confidence: moodAnalysis.confidence,
                escalationRecommended: moodAnalysis.escalationRecommended,
                trend: moodAnalysis.conversationTrend,
                timestamp: new Date().toISOString()
              }
            };
            
            console.log(`🎭 [MOOD DEBUG] Emitting mood update to ${this.io.engine.clientsCount} connected clients:`, moodUpdatePayload);
            this.io.emit('customer_mood_update', moodUpdatePayload);
            console.log(`🎭 [MOOD DEBUG] Mood update emitted successfully`);
          } else {
            console.log(`🎭 [MOOD DEBUG] No socket.io instance available to emit mood update`);
          }
          
        } catch (moodError) {
          console.error(`❌ Error analyzing mood for ${customerPhone}:`, moodError.message);
        }
      }

      // Load conversation history from WhatsApp ONLY if this is truly a NEW conversation
      // A conversation is considered new if:
      // 1. It was just created (has exactly 1 message - the current one)
      // 2. History hasn't been loaded yet
      // 3. The conversation was created recently (within last 5 minutes to avoid loading on server restart)
      const conversation = this.activeConversations.get(conversationId);
      const conversationAge = Date.now() - new Date(conversation?.lastMessage || 0).getTime();
      const isRecentlyCreated = conversationAge < 5 * 60 * 1000; // 5 minutes
      const isNewConversation = conversation &&
                                conversation.messages.length === 1 &&
                                !conversation.whatsappHistoryLoaded &&
                                isRecentlyCreated;
      
      if (isNewConversation) {
        console.log(`🆕 NEW conversation detected for ${customerPhone} (age: ${Math.round(conversationAge/1000)}s), loading last 10 messages...`);
        try {
          // Mark as loading to prevent duplicate loads
          conversation.whatsappHistoryLoaded = true;
          
          const history = await whatsappService.loadHistoryFromWhatsApp(customerPhone, 10);
          if (history.length > 0) {
            console.log(`✅ Loaded ${history.length} messages from WhatsApp history for new conversation`);
            
            // Filter out the current message and any duplicates
            const historyToAdd = history.filter(histMsg => 
              histMsg.id !== customerMessage.id && 
              !conversation.messages.some(existingMsg => existingMsg.id === histMsg.id)
            );
            
            if (historyToAdd.length > 0) {
              // Insert history messages at the beginning of the conversation
              conversation.messages.unshift(...historyToAdd);
              
              // Emit history to clients
              if (this.io) {
                this.io.emit('whatsapp_history_loaded', {
                  conversationId,
                  phoneNumber: customerPhone,
                  history: historyToAdd,
                  totalHistoryMessages: history.length,
                  isNewConversation: true
                });
              }
            }
          } else {
            console.log('📚 No additional history messages found for new conversation');
          }
        } catch (error) {
          console.error('Error loading WhatsApp history for new conversation:', error);
          // Reset flag on error so it can be retried
          conversation.whatsappHistoryLoaded = false;
        }
      } else {
        const reason = !conversation ? 'no conversation' :
                      conversation.messages.length !== 1 ? `${conversation.messages.length} messages` :
                      conversation.whatsappHistoryLoaded ? 'history already loaded' :
                      !isRecentlyCreated ? `conversation too old (${Math.round(conversationAge/1000)}s)` :
                      'unknown reason';
        console.log(`🔄 Skipping history load for ${customerPhone}: ${reason}`);
      }

      // Skip AI processing for historical messages
      if (originalMessage?.isHistoricalMessage === true) {
        console.log(`📚 Skipping AI processing for historical message from ${customerPhone}`);
        return;
      }
      
      // Only process with AI if this is explicitly marked as a new message
      console.log(`🔍 DEBUG originalMessage:`, originalMessage);
      console.log(`🔍 DEBUG isNewMessage:`, originalMessage?.isNewMessage);
      if (originalMessage?.isNewMessage !== true) {
        console.log(`🤖 Skipping AI processing - not marked as new message from ${customerPhone}`);
        return;
      }

      // Check if AI mode is enabled for this conversation
      const isAiMode = this.aiModeConversations.get(conversationId) ?? true;
      
      if (!isAiMode) {
        console.log(`🤖 AI disabled for conversation ${conversationId}, not responding automatically`);
        return;
      }

      // INTEGRATION: Use MessageBatcher for AI processing
      console.log(`📦 Adding message to batch for AI processing: ${customerPhone}`);
      
      // Prepare message data for batching
      const messageForBatching = {
        customerPhone,
        customerName,
        content,
        type,
        audioData,
        audioDuration,
        originalMessage,
        conversationId,
        customerMessage: customerMessage,
        moodAnalysis: moodAnalysis
      };

      // Add to batch - the callback will be called when batch is ready for processing
      this.messageBatcher.addMessage(customerPhone, messageForBatching, (batchedMessage) => {
        this.processBatchedMessage(batchedMessage);
      });

      console.log(`📦 Message added to batch, will be processed after timeout or when more messages arrive`);
    } catch (error) {
      console.error(`❌ Error handling WhatsApp message from ${customerPhone}:`, error.message);
    }
  }

  // New method to process batched messages
  async processBatchedMessage(messageData) {
    try {
      const { 
        customerPhone, 
        customerName, 
        content, 
        type, 
        audioData, 
        audioDuration, 
        originalMessage,
        conversationId,
        customerMessage,
        moodAnalysis,
        isBatchedMessage,
        batchSize
      } = messageData;

      if (isBatchedMessage) {
        console.log(`🚀 Processing batched message from ${customerPhone} (${batchSize} messages combined)`);
      } else {
        console.log(`🚀 Processing single message from ${customerPhone}`);
      }

      // Show typing indicator
      if (this.io) {
        this.io.emit('ai_typing_start', { conversationId });
      }

      // Get customer data
      const customerData = await trinksService.getCustomerByPhone(customerPhone);

      // Update customer name if found
      if (customerData && !customerData._notFound && customerData.nome) {
        this.updateCustomerName(customerPhone, customerData.nome);
      }

      // Process with AI (with realistic delay)
      const processingDelay = this.calculateResponseDelay(content);
      
      setTimeout(async () => {
        try {
          // For audio messages, pass the audio data for transcription
          const messageToProcess = type === 'audio' && audioData ? audioData : content;
          
          // Get mood-based response adjustments if mood analysis was performed
          let moodBasedAdjustments = null;
          if (moodAnalysis) {
            moodBasedAdjustments = moodAnalysisService.getMoodBasedResponseAdjustments(customerPhone);
            console.log(`🎭 Using mood-based response adjustments:`, moodBasedAdjustments);
          }
          
          const aiResult = await aiService.processMessage(
            customerPhone, 
            messageToProcess, 
            customerData, 
            type, 
            moodBasedAdjustments,
            isBatchedMessage
          );

          // If this was an audio message and we got a transcription, update the original message
          if (type === 'audio' && aiResult.transcription) {
            const updatedCustomerMessage = {
              ...customerMessage,
              audioTranscription: aiResult.transcription
            };

            // Update the conversation
            const conversation = this.activeConversations.get(conversationId);
            if (conversation) {
              const messageIndex = conversation.messages.findIndex(msg => msg.id === customerMessage.id);
              if (messageIndex !== -1) {
                conversation.messages[messageIndex] = updatedCustomerMessage;
              }
            }

            // Emit updated message to all clients
            if (this.io) {
              this.io.emit('message_updated', {
                conversationId,
                message: updatedCustomerMessage
              });
            }

            console.log(`🎤 Audio transcription added: "${aiResult.transcription}"`);
          }

          // Check if AI returned an empty response (from empty message handling)
          const responseText = this.extractResponseText(aiResult.response);
          if (aiResult.debugInfo?.emptyMessage === true || !responseText || responseText.trim().length === 0) {
            console.log(`📝 Empty AI response detected - not sending to WhatsApp or UI`);
            
            // Stop typing indicator only
            if (this.io) {
              this.io.emit('ai_typing_stop', { conversationId });
            }
            return; // Exit without sending any message
          }

          // Create AI response message
          const aiMessage = {
            id: uuidv4(),
            content: responseText,
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent',
            debugInfo: aiResult.debugInfo
          };

          // Stop typing indicator
          if (this.io) {
            this.io.emit('ai_typing_stop', { conversationId });
          }

          // Send AI response back to WhatsApp with enhanced error handling first
          try {
            // Check if WhatsApp is connected before attempting to send
            const whatsappStatus = whatsappService.getStatus();
            console.log(`📊 WhatsApp Status before sending: ${whatsappStatus.connectionStatus}`);

            if (whatsappStatus.isConnected && whatsappStatus.connectionStatus === 'connected') {
              // Simulate typing before sending response
              console.log(`⌨️ Starting typing simulation for ${customerPhone}...`);
              const typingDuration = whatsappService.calculateTypingDuration ?
                whatsappService.calculateTypingDuration(aiResult.response.length) : 3000;

              // Start typing indicator
              const typingPromise = whatsappService.simulateTyping ?
                whatsappService.simulateTyping(customerPhone, typingDuration) :
                new Promise(resolve => setTimeout(resolve, typingDuration));

              // Wait for typing simulation to complete
              await typingPromise;
              console.log(`✅ Typing simulation completed`);

              // Send the actual message with enhanced logging
              console.log(`📤 Sending AI response to WhatsApp: ${customerPhone}`);
              console.log(`📝 Message content: ${aiResult.response.substring(0, 100)}...`);

              const sendResult = await whatsappService.sendMessage(customerPhone, aiResult.response);

              if (sendResult && sendResult.success) {
                aiMessage.status = 'delivered';
                console.log(`✅ Message successfully delivered to ${customerPhone}`);
                console.log(`📋 Send result:`, sendResult);
              } else {
                throw new Error('Send result indicates failure');
              }

            } else {
              console.log(`⚠️ WhatsApp not connected (${whatsappStatus.connectionStatus}), cannot send message`);
              aiMessage.status = 'failed';
              aiMessage.failureReason = `WhatsApp not connected: ${whatsappStatus.connectionStatus}`;

              // Log the message that couldn't be sent
              console.log(`📝 Message that couldn't be sent: ${aiResult.response}`);
            }

          } catch (whatsappError) {
            console.error('❌ Error sending AI response to WhatsApp:', whatsappError);
            console.error('❌ Error details:', {
              message: whatsappError.message,
              stack: whatsappError.stack,
              customerPhone,
              responseLength: aiResult.response.length
            });

            aiMessage.status = 'failed';
            aiMessage.failureReason = whatsappError.message;
          }

          // Update conversation with the AI message
          await this.updateConversation(conversationId, customerPhone, aiMessage);

          // Send AI response to salon interface AFTER WhatsApp send and conversation update
          if (this.io) {
            this.io.emit('message_sent', {
              conversationId,
              message: aiMessage
            });
          }

          // WEBSOCKET INTEGRATION: Emit appointment/cancellation events for sidebar updates
          if (aiResult.debugInfo) {
            // Handle appointment creation
            if (aiResult.debugInfo.appointmentCreated && aiResult.debugInfo.appointmentCreated.success) {
              const appointmentData = aiResult.debugInfo.appointmentCreated.appointment;
              console.log(`📅 Emitting appointment_created event for sidebar update`);
              
              this.io.emit('appointment_created', {
                success: true,
                appointment: appointmentData,
                customerPhone: customerPhone,
                conversationId: conversationId,
                source: 'ai_chat'
              });
            }

            // Handle appointment cancellation (support both legacy and new debugInfo shapes)
            const cancelDebug = aiResult.debugInfo.appointmentCanceled;
            const cancellationResult = aiResult.debugInfo.cancellationResult;
            const hasCancelled = (cancelDebug && cancelDebug.success) || (cancellationResult && cancellationResult.cancelled);

            if (hasCancelled) {
              const cancelledAppointment = cancelDebug?.agendamento 
                || cancellationResult?.cancelledAppointment 
                || { id: cancellationResult?.cancelledAppointmentId };

              console.log(`🚫 Emitting appointment_cancelled event for sidebar update`);
              this.io.emit('appointment_cancelled', {
                success: true,
                appointment: cancelledAppointment,
                customerPhone: customerPhone,
                conversationId: conversationId,
                source: 'ai_chat'
              });
            }
          }

          console.log(`🤖 AI Response sent to WhatsApp ${customerPhone}: ${aiResult.response}`);

        } catch (error) {
          console.error('Error processing batched WhatsApp AI response:', error);
          
          if (this.io) {
            this.io.emit('ai_typing_stop', { conversationId });
          }
          
          const errorMessage = {
            id: uuidv4(),
            content: "Desculpe, tive um problema técnico. Pode repetir sua mensagem?",
            sender: 'ai',
            timestamp: new Date(),
            status: 'sent'
          };

          // Update conversation with error message first
          await this.updateConversation(conversationId, customerPhone, errorMessage);

          if (this.io) {
            this.io.emit('message_sent', {
              conversationId,
              message: errorMessage
            });
          }

          // Try to send error message to WhatsApp
          try {
            await whatsappService.sendMessage(customerPhone, errorMessage.content);
          } catch (whatsappError) {
            console.error('Error sending error message to WhatsApp:', whatsappError);
          }
        }
      }, processingDelay);

    } catch (error) {
      console.error('Error processing batched message:', error);
    }
  }

  async handleLoadConversationHistory(socket, data) {
    try {
      const { phoneNumber, limit = 10 } = data;
      console.log(`📚 Loading conversation history for ${phoneNumber} (limit: ${limit})...`);

      // Try to load from WhatsApp first
      const history = await whatsappService.loadHistoryFromWhatsApp(phoneNumber, limit);

      socket.emit('conversation_history_response', {
        success: true,
        phoneNumber,
        history,
        message: `Loaded ${history.length} messages from conversation history`
      });

      console.log(`✅ Loaded ${history.length} messages from conversation history for ${phoneNumber}`);

    } catch (error) {
      console.error('Error loading conversation history:', error);
      socket.emit('conversation_history_response', {
        success: false,
        phoneNumber: data.phoneNumber,
        history: [],
        error: error.message
      });
    }
  }

  async handleLoadAllChats(socket, data) {
    try {
      console.log('📚 handleLoadAllChats called - Loading all WAHA chats WITHOUT conversation history...');
      console.log('📚 Data received:', data);

      // Get all chats from WAHA
      const chatsData = await whatsappService.loadAllChats();

      if (!chatsData || !Array.isArray(chatsData)) {
        console.log('❌ No chats data received from WAHA');
        socket.emit('all_chats_loaded', {
          success: false,
          error: 'No chats data available'
        });
        return;
      }

      console.log(`📱 Found ${chatsData.length} chats in WAHA`);

      // Process each chat but DO NOT load conversation history automatically
      // History will only be loaded when new messages arrive or manually requested
      const processedChats = [];

      for (const chat of chatsData) {
        try {
          const phoneNumber = chat.id;
          console.log(`📞 Processing chat for ${phoneNumber} (without loading history)...`);

          // Create or get existing conversation WITHOUT loading history
          const customerName = chat.name || phoneNumber;
          const conversationId = await this.getOrCreateConversation(phoneNumber, customerName);
          const conversation = this.activeConversations.get(conversationId);

          if (conversation) {
            processedChats.push({
              conversationId,
              phoneNumber,
              customerName,
              messagesCount: conversation.messages.length,
              lastMessage: conversation.lastMessage || new Date(),
              conversation
            });
          }
          
        } catch (chatError) {
          console.error(`❌ Error processing chat ${chat.id}:`, chatError.message);
        }
      }
      
      console.log(`✅ Processed ${processedChats.length} chats (without loading conversation history)`);
      
      // Send all processed chats to frontend (without conversation history)
      socket.emit('all_chats_loaded', {
        success: true,
        chats: processedChats,
        totalChats: chatsData.length,
        chatsWithoutHistory: processedChats.length,
        message: 'Chats loaded without conversation history - history will load when new messages arrive'
      });
      
      // Also broadcast updated conversations to all clients
      if (this.io) {
        for (const chatData of processedChats) {
          this.io.emit('conversation_updated', {
            conversationId: chatData.conversationId,
            conversation: chatData.conversation
          });
        }
      }
      
    } catch (error) {
      console.error('❌ Error loading all chats:', error);
      socket.emit('all_chats_loaded', {
        success: false,
        error: error.message
      });
    }
  }
  // Manual appointment scheduling handlers
  async handleGetServices(socket, data) {
    try {
      console.log('📎 Getting services for socket:', socket.id);
      
      const services = await trinksService.getServices();
      
      socket.emit('services_response', {
        success: true,
        data: services
      });
      
      console.log('📎 Services retrieved successfully:', { count: services?.length || 0 });
      
    } catch (error) {
      console.error('❌ Error getting services:', error);
      socket.emit('services_response', {
        success: false,
        error: error.message || 'Failed to retrieve services'
      });
    }
  }

  async handleGetAvailability(socket, data) {
    try {
      const { date, serviceId } = data || {};
      
      // Input validation
      if (!date) {
        socket.emit('availability_response', {
          success: false,
          error: 'Date is required'
        });
        return;
      }

      // Validate date format (YYYY-MM-DD)
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(date)) {
        socket.emit('availability_response', {
          success: false,
          error: 'Invalid date format. Use YYYY-MM-DD'
        });
        return;
      }

      // Validate date is not in the past
      const selectedDate = new Date(date + 'T00:00:00');
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (selectedDate < today) {
        socket.emit('availability_response', {
          success: false,
          error: 'Cannot search availability for past dates'
        });
        return;
      }

      console.log('📋 Getting availability for date:', { 
        date, 
        serviceId: serviceId || 'all services', 
        socket: socket.id 
      });
      
      // Pass serviceId to filter availability by specific service
      const availability = await trinksService.getProfessionalsAvailability(date, serviceId);
      
      socket.emit('availability_response', {
        success: true,
        data: availability
      });
      
      console.log('📋 Availability retrieved successfully:', { 
        date, 
        serviceId: serviceId || 'all services',
        professionalsCount: availability?.professionals?.length || 0 
      });
      
    } catch (error) {
      console.error('❌ Error getting availability:', error);
      socket.emit('availability_response', {
        success: false,
        error: error.message || 'Failed to retrieve availability'
      });
    }
  }

  async handleCreateManualAppointment(socket, data) {
    try {
      const { appointmentData, customerData } = data || {};
      
      // Validate required data structure
      if (!appointmentData) {
        socket.emit('appointment_created', {
          success: false,
          error: 'Appointment data is required'
        });
        return;
      }

      if (!customerData) {
        socket.emit('appointment_created', {
          success: false,
          error: 'Customer data is required'
        });
        return;
      }

      // Validate appointment data fields
      const requiredAppointmentFields = ['servicoId', 'profissionalId', 'data', 'hora'];
      for (const field of requiredAppointmentFields) {
        if (!appointmentData[field]) {
          socket.emit('appointment_created', {
            success: false,
            error: `Missing required field: ${field}`
          });
          return;
        }
      }

      // Validate customer data
      if (!customerData.nome || customerData.nome.trim().length < 2) {
        socket.emit('appointment_created', {
          success: false,
          error: 'Customer name must be at least 2 characters'
        });
        return;
      }

      // Validate date format
      const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
      if (!dateRegex.test(appointmentData.data)) {
        socket.emit('appointment_created', {
          success: false,
          error: 'Invalid date format. Use YYYY-MM-DD'
        });
        return;
      }

      // Validate time format
      const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
      if (!timeRegex.test(appointmentData.hora)) {
        socket.emit('appointment_created', {
          success: false,
          error: 'Invalid time format. Use HH:MM'
        });
        return;
      }

      // Sanitize customer name (XSS prevention)
      const sanitizedCustomerData = {
        ...customerData,
        nome: customerData.nome.trim().replace(/[<>]/g, '').substring(0, 50)
      };

      console.log('🎆 Creating manual appointment:', { 
        appointmentData: {
          ...appointmentData,
          // Don't log sensitive customer data
          customerName: sanitizedCustomerData.nome
        },
        socket: socket.id 
      });
      
      const result = await trinksService.createAppointmentWithCustomer(appointmentData, sanitizedCustomerData);
      
      socket.emit('appointment_created', {
        success: true,
        data: result
      });
      
      // Broadcast to other clients for real-time updates
      socket.broadcast.emit('appointment_updated', {
        type: 'new_appointment',
        data: {
          id: result.appointment?.id || result.id,
          date: appointmentData.data,
          time: appointmentData.hora,
          customerName: sanitizedCustomerData.nome
        }
      });
      
      console.log('🎆 Manual appointment created successfully:', { 
        appointmentId: result.appointment?.id || result.id,
        customerId: result.customer?.id || sanitizedCustomerData.id
      });
      
    } catch (error) {
      console.error('❌ Error creating manual appointment:', error);
      socket.emit('appointment_created', {
        success: false,
        error: error.message || 'Failed to create appointment'
      });
    }
  }

  // Audio data management methods
  storeAudioData(audioId, audioData) {
    this.audioDataStore.set(audioId, audioData);
    // Clean up old audio data after 1 hour
    setTimeout(() => {
      this.audioDataStore.delete(audioId);
    }, 60 * 60 * 1000);
  }

  getAudioData(audioId) {
    return this.audioDataStore.get(audioId);
  }
}

module.exports = new MessageHandler();
