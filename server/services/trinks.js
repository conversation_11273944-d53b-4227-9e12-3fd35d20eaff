// Trinks API Service - Real API integration only

const { v4: uuidv4 } = require('uuid')
const axios = require('axios')
const rateLimit = require('axios-rate-limit')
const path = require('path')
const messageLogger = require('../utils/messageLogger')
const { errorLogger, ERROR_CATEGORIES } = require('../utils/ErrorLogger')

// Cache é gerenciado pelos módulos dedicados em /utils/

// Função utilitária para buscar ID do profissional pelo nome via API
async function getProfessionalIdByName (name, trinksService) {
  if (!name || !trinksService) return null

  try {
    // Usar cache dedicado de profissionais
    const professionalsCache = require('../utils/professionalsCache')
    const professionals = await professionalsCache.getProfessionals()

    if (!professionals || professionals.length === 0) {
      console.log('❌ Nenhum profissional encontrado na API')
      return null
    }

    const normalizedName = name.toLowerCase().trim()

    // Busca profissional por nome (case insensitive e partial match)
    const professional = professionals.find(prof =>
      prof.nome && prof.nome.toLowerCase().includes(normalizedName)
    )

    if (professional) {
      console.log(`✅ Profissional encontrado: ${professional.nome} (ID: ${professional.id})`)
      return professional.id
    }

    console.log(`❌ Profissional "${name}" não encontrado na lista da API`)
    return null
  } catch (error) {
    console.error('❌ Erro ao buscar ID do profissional:', error.message)
    return null
  }
}

class TrinksService {
  constructor () {
    this.config = this.loadConfig()
    this.axiosInstance = null
    this.initializeAPI()
  }

  loadConfig () {
    // SEMPRE usar variáveis de ambiente
    const apiKey = process.env.TRINKS_API_KEY
    const estabelecimentoId = process.env.TRINKS_ESTABLISHMENT_ID || '188253'

    if (!apiKey) {
      throw new Error(
        '🚨 TRINKS_API_KEY não configurada! Configure no arquivo .env:\n' +
        'TRINKS_API_KEY=sua_chave_aqui\n' +
        'TRINKS_ESTABLISHMENT_ID=188253'
      )
    }

    console.log('🔗 Trinks API initialized:', 'https://api.trinks.com')
    console.log('🔗 API Key:', apiKey.substring(0, 10) + '...')
    console.log('🔗 Establishment ID:', estabelecimentoId)

    return {
      enabled: true, // Sempre habilitado
      apiUrl: 'https://api.trinks.com',
      apiKey,
      estabelecimentoId
    }
  }

  initializeAPI () {
    // API sempre habilitada - sem mais verificação de enabled
    if (this.config.apiKey) {
      // Criar instância axios com rate limiting de 1 segundo
      const baseInstance = axios.create({
        baseURL: this.config.apiUrl,
        headers: {
          'X-Api-Key': this.config.apiKey,
          'Content-Type': 'application/json'
        },
        timeout: 10000
      })

      // Aplicar rate limiting: máximo 1 requisição por segundo
      this.axiosInstance = rateLimit(baseInstance, {
        maxRequests: 1, // máximo 1 requisição
        perMilliseconds: 1000, // por segundo (1000ms)
        maxRPS: 1 // 1 request per second
      })

      // Add request interceptor for logging
      this.axiosInstance.interceptors.request.use(
        (config) => {
          // Adicionar timestamp para calcular duração
          config.metadata = { startTime: Date.now() }

          console.log(`🔥 TRINKS API REQUEST: ${config.method.toUpperCase()} ${config.url}`)
          console.log('🔥 Headers:', config.headers)
          if (config.data) {
            console.log('🔥 Body:', JSON.stringify(config.data, null, 2))
          }
          if (config.params) {
            console.log('🔥 Params:', config.params)
          }

          // Log para messageLogger se estiver ativo
          if (messageLogger.isCapturing) {
            messageLogger.logApiCall(
              config.method.toUpperCase(),
              config.url,
              0, // Duration será preenchido no response
              null, // Success será preenchido no response
              {
                purpose: 'Trinks API Request',
                headers: config.headers,
                data: config.data,
                params: config.params,
                phase: 'REQUEST'
              }
            )
          }

          return config
        },
        (error) => {
          console.error('🔥 TRINKS API REQUEST ERROR:', error)
          throw error
        }
      )

      // Capturar referência do config para usar dentro do interceptor
      const trinksConfig = this.config

      // Add response interceptor for logging
      this.axiosInstance.interceptors.response.use(
        (response) => {
          console.log(`🔥 TRINKS API RESPONSE: ${response.status} ${response.config.method.toUpperCase()} ${response.config.url}`)
          console.log('🔥 Response Data:', JSON.stringify(response.data, null, 2))

          // Log para messageLogger se estiver ativo
          if (messageLogger.isCapturing) {
            messageLogger.logApiCall(
              response.config.method.toUpperCase(),
              response.config.url,
              response.config.metadata?.duration || 0,
              response.status >= 200 && response.status < 300,
              {
                purpose: 'Trinks API Response',
                status: response.status,
                statusText: response.statusText,
                dataSize: JSON.stringify(response.data).length,
                phase: 'RESPONSE',
                responsePreview: typeof response.data === 'object'
                  ? Object.keys(response.data).join(', ')
                  : String(response.data).substring(0, 100)
              }
            )
          }

          return response
        },
        async (error) => {
          // Capturar timestamp imediatamente para preservar timing exato
          const errorTimestamp = new Date().toISOString()

          // Calcular duração se disponível
          const duration = error.config?.metadata?.startTime
            ? Date.now() - error.config.metadata.startTime
            : 0

          // PRESERVAR o erro original completo ANTES de qualquer processamento
          const originalError = {
            ...error,
            // Preservar stack trace original
            originalStack: error.stack,
            // Preservar todas as propriedades originais
            originalProperties: Object.getOwnPropertyNames(error).reduce((props, prop) => {
              try {
                props[prop] = error[prop]
              } catch (e) {
                props[prop] = `[Error accessing property: ${e.message}]`
              }
              return props
            }, {}),
            // Timestamp exato da captura
            capturedAt: errorTimestamp
          }

          // Extract detailed error information (mantendo lógica existente)
          const errorDetails = {
            status: error.response?.status || 0,
            statusText: error.response?.statusText || 'Unknown Status',
            message: error.message || 'Unknown error',
            code: error.code || 'UNKNOWN_ERROR',
            data: error.response?.data || null,
            url: error.config?.url || 'UNKNOWN_URL',
            method: error.config?.method?.toUpperCase() || 'UNKNOWN_METHOD'
          }

          // Create comprehensive error message
          let detailedErrorMessage = `${errorDetails.message}`
          if (errorDetails.status) {
            detailedErrorMessage = `HTTP ${errorDetails.status} (${errorDetails.statusText}): ${errorDetails.message}`
          }
          if (errorDetails.data && typeof errorDetails.data === 'object' && errorDetails.data.message) {
            detailedErrorMessage += ` - API Response: ${errorDetails.data.message}`
          }
          if (errorDetails.code && errorDetails.code !== 'UNKNOWN_ERROR') {
            detailedErrorMessage += ` (${errorDetails.code})`
          }

          // ENHANCED: Console logging com diagnóstico detalhado
          const networkDiagnostic = this.generateNetworkDiagnostic(originalError, errorDetails)

          console.error(`🔥 TRINKS API ERROR: ${errorDetails.status} ${errorDetails.method} ${errorDetails.url}`)
          console.error(`🔥 Error Type: ${networkDiagnostic.errorType}`)
          console.error(`🔥 Network Phase: ${networkDiagnostic.phase}`)
          console.error(`🔥 Root Cause: ${networkDiagnostic.rootCause}`)
          console.error(`🔥 Detailed Error: ${detailedErrorMessage}`)

          // Logs específicos por tipo de erro
          if (networkDiagnostic.errorType === 'DNS_RESOLUTION_FAILURE') {
            console.error('🔥 DNS Issues:', {
              hostname: originalError.hostname || errorDetails.url,
              suggestion: 'Verifique conectividade de rede e configuração de DNS'
            })
          } else if (networkDiagnostic.errorType === 'CONNECTION_TIMEOUT') {
            console.error('🔥 Connection Details:', {
              timeout: error.config?.timeout || 'default',
              duration,
              suggestion: 'API pode estar sobrecarregada ou indisponível'
            })
          } else if (networkDiagnostic.errorType === 'SSL_CERTIFICATE_ERROR') {
            console.error('🔥 SSL Issues:', {
              suggestion: 'Verificar certificados SSL da API Trinks'
            })
          } else if (networkDiagnostic.errorType === 'NETWORK_UNREACHABLE') {
            console.error('🔥 Network Issues:', {
              suggestion: 'Verifique conectividade com api.trinks.com'
            })
          }

          // Request configuration para debug
          if (error.config) {
            console.error('🔥 Request Config:', {
              baseURL: error.config.baseURL,
              timeout: error.config.timeout,
              headers: error.config.headers ? Object.keys(error.config.headers) : [],
              params: error.config.params ? Object.keys(error.config.params) : []
            })
          }

          // Response data se disponível
          if (errorDetails.data) {
            console.error('🔥 API Response Data:', JSON.stringify(errorDetails.data, null, 2))
          }

          // Timing information
          console.error('🔥 Timing:', {
            duration: `${duration}ms`,
            phase: duration < 100 ? 'DNS/Connection' : duration < 1000 ? 'Request/Response' : 'Timeout'
          })

          // Log para messageLogger se estiver ativo
          if (messageLogger.isCapturing) {
            messageLogger.logApiCall(
              errorDetails.method,
              errorDetails.url,
              duration,
              false,
              {
                purpose: 'Trinks API Error',
                status: errorDetails.status,
                errorMessage: detailedErrorMessage,
                errorData: errorDetails.data,
                errorCode: errorDetails.code,
                phase: 'ERROR',
                // ENHANCED: Diagnóstico detalhado de rede
                networkDiagnostic,
                // Manter compatibilidade com sistema existente
                originalErrorType: originalError.name,
                originalErrorCode: originalError.code,
                isNetworkError: networkDiagnostic.hasNetworkIssue,
                isDnsError: networkDiagnostic.isDnsIssue,
                isTimeoutError: networkDiagnostic.isTimeoutIssue,
                isSslError: networkDiagnostic.isSslIssue,
                isConnectionError: networkDiagnostic.isConnectionIssue
              }
            )
          }

          // ENHANCED: ErrorLogger com diagnóstico completo e debugging detalhado
          try {
            console.log('🔍 Tentando salvar erro detalhado via ErrorLogger...')

            const errorLogResult = await errorLogger.logError(originalError, ERROR_CATEGORIES.TRINKS_ERROR, {
              // Contexto da requisição
              requestDetails: {
                method: error.config?.method,
                url: error.config?.url,
                baseURL: error.config?.baseURL,
                timeout: error.config?.timeout,
                headers: error.config?.headers,
                params: error.config?.params,
                data: error.config?.data
              },
              // Contexto da resposta
              responseDetails: error.response
                ? {
                    status: error.response.status,
                    statusText: error.response.statusText,
                    headers: error.response.headers,
                    data: error.response.data
                  }
                : null,
              // Contexto timing
              timingDetails: {
                duration,
                startTime: error.config?.metadata?.startTime,
                errorTimestamp,
                wasTimeout: originalError.code === 'ETIMEDOUT'
              },
              // ENHANCED: Contexto com diagnóstico detalhado
              networkDiagnostic,
              // Contexto da API Trinks
              trinksContext: {
                estabelecimentoId: trinksConfig.estabelecimentoId,
                apiKey: trinksConfig.apiKey?.substring(0, 10) + '...',
                apiUrl: trinksConfig.apiUrl,
                detailedErrorMessage,
                // Usar diagnóstico mais avançado
                errorType: networkDiagnostic.errorType,
                networkPhase: networkDiagnostic.phase,
                rootCause: networkDiagnostic.rootCause
              }
            })

            // Log de sucesso com informações do arquivo gerado
            console.log('✅ Log de erro salvo com sucesso:', {
              errorId: errorLogResult.errorId,
              filename: errorLogResult.filename,
              category: errorLogResult.category,
              sessionId: errorLogResult.sessionId
            })
          } catch (logError) {
            console.error('❌ FALHA ao salvar log detalhado:')
            console.error('❌ Erro do ErrorLogger:', logError.message)
            console.error('❌ Stack do ErrorLogger:', logError.stack)
            console.error('❌ Detalhes do erro original que tentamos logar:', {
              originalErrorName: originalError.name,
              originalErrorCode: originalError.code,
              originalErrorMessage: originalError.message,
              hasResponse: !!error.response,
              sessionId: errorLogger.currentSessionId
            })

            // Implementar fallback logging em caso de falha do ErrorLogger
            try {
              const fallbackFilename = `fallback-error-${Date.now()}.json`
              const fallbackPath = path.join(__dirname, '../logs/errors', fallbackFilename)
              const fallbackData = {
                timestamp: new Date().toISOString(),
                originalError: {
                  name: originalError.name,
                  message: originalError.message,
                  code: originalError.code,
                  stack: originalError.stack
                },
                networkDiagnostic,
                errorLoggerFailure: {
                  message: logError.message,
                  stack: logError.stack
                }
              }

              require('fs').writeFileSync(fallbackPath, JSON.stringify(fallbackData, null, 2))
              console.log(`💾 Fallback log salvo: ${fallbackFilename}`)
            } catch (fallbackError) {
              console.error('❌ Falha até mesmo no fallback logging:', fallbackError.message)
            }
          }

          // Preservar e re-throw o erro original
          throw originalError
        }
      )

      console.log('🔗 Trinks API initialized:', this.config.apiUrl)
      console.log('🔗 API Key:', this.config.apiKey.substring(0, 8) + '...')
      console.log('🔗 Establishment ID:', this.config.estabelecimentoId)
      console.log('⏱️ Rate limiting aplicado: 1 requisição por segundo')
    } else {
      console.log('❌ API não configurada - sistema não funcionará sem chaves válidas')
      console.log('🎭 Config enabled:', this.config.enabled)
      console.log('🎭 Has API key:', !!this.config.apiKey)
    }
  }

  /**
   * Gera diagnóstico detalhado de erros de rede para debugging eficaz
   */
  generateNetworkDiagnostic (originalError, errorDetails) {
    // Classificação detalhada de erros de rede
    let errorType = 'UNKNOWN_ERROR'
    let phase = 'Unknown'
    let rootCause = 'Erro não classificado'

    // Análise do código de erro
    if (originalError.code) {
      switch (originalError.code) {
        case 'ENOTFOUND':
          errorType = 'DNS_RESOLUTION_FAILURE'
          phase = 'DNS Resolution'
          rootCause = 'Não foi possível resolver o nome do host api.trinks.com'
          break
        case 'ECONNREFUSED':
          errorType = 'CONNECTION_REFUSED'
          phase = 'TCP Connection'
          rootCause = 'Conexão recusada pelo servidor (porta fechada ou firewall)'
          break
        case 'ETIMEDOUT':
          errorType = 'CONNECTION_TIMEOUT'
          phase = 'TCP/HTTP Timeout'
          rootCause = 'Timeout na conexão ou resposta do servidor'
          break
        case 'ECONNRESET':
          errorType = 'CONNECTION_RESET'
          phase = 'Data Transfer'
          rootCause = 'Conexão resetada pelo servidor durante transferência'
          break
        case 'CERT_HAS_EXPIRED':
        case 'UNABLE_TO_VERIFY_LEAF_SIGNATURE':
        case 'SELF_SIGNED_CERT_IN_CHAIN':
          errorType = 'SSL_CERTIFICATE_ERROR'
          phase = 'SSL Handshake'
          rootCause = 'Problema com certificado SSL do servidor'
          break
        case 'ENETUNREACH':
          errorType = 'NETWORK_UNREACHABLE'
          phase = 'Network Routing'
          rootCause = 'Rede de destino inacessível (problema de roteamento)'
          break
        case 'EHOSTUNREACH':
          errorType = 'HOST_UNREACHABLE'
          phase = 'Network Routing'
          rootCause = 'Host de destino inacessível'
          break
        default:
          errorType = 'NETWORK_ERROR'
          phase = 'Network Layer'
          rootCause = `Erro de rede: ${originalError.code}`
      }
    } else if (errorDetails.status > 0) {
      // Erros HTTP com status code
      errorType = 'HTTP_ERROR'
      phase = 'HTTP Response'

      if (errorDetails.status >= 400 && errorDetails.status < 500) {
        rootCause = `Erro do cliente HTTP ${errorDetails.status}: ${errorDetails.statusText}`
      } else if (errorDetails.status >= 500) {
        rootCause = `Erro do servidor HTTP ${errorDetails.status}: ${errorDetails.statusText}`
      } else {
        rootCause = `Resposta HTTP inesperada: ${errorDetails.status}`
      }
    } else if (originalError.message) {
      // Análise baseada na mensagem de erro
      const message = originalError.message.toLowerCase()

      if (message.includes('timeout')) {
        errorType = 'REQUEST_TIMEOUT'
        phase = 'Request Timeout'
        rootCause = 'Timeout na requisição HTTP'
      } else if (message.includes('network')) {
        errorType = 'NETWORK_ERROR'
        phase = 'Network Layer'
        rootCause = 'Problema genérico de rede'
      } else {
        errorType = 'UNKNOWN_ERROR'
        phase = 'Unknown'
        rootCause = originalError.message
      }
    }

    return {
      errorType,
      phase,
      rootCause,
      hasNetworkIssue: !errorDetails.status || errorDetails.status === 0,
      hasHttpResponse: errorDetails.status > 0,
      isDnsIssue: originalError.code === 'ENOTFOUND',
      isTimeoutIssue: originalError.code === 'ETIMEDOUT' || originalError.message?.includes('timeout'),
      isSslIssue: originalError.code?.includes('CERT') || originalError.code?.includes('SSL'),
      isConnectionIssue: ['ECONNREFUSED', 'ECONNRESET', 'ENETUNREACH', 'EHOSTUNREACH'].includes(originalError.code)
    }
  }

  updateConfig (newConfig) {
    this.config = { ...this.config, ...newConfig }
    this.initializeAPI()
  }

  // Normalize phone number format
  normalizePhoneNumber (telefone) {
    if (!telefone || typeof telefone !== 'string') return telefone

    // Remove all non-numeric characters and spaces
    let cleanPhone = telefone.replace(/\D/g, '')

    // If it starts with 55 (country code), remove it
    if (cleanPhone.startsWith('55') && cleanPhone.length > 11) {
      cleanPhone = cleanPhone.substring(2)
    }

    // Ensure we have at least 10-11 digits (DDD + number)
    if (cleanPhone.length < 10) {
      console.warn(`Phone number too short: ${telefone} -> ${cleanPhone}`)
    }

    return cleanPhone
  }

  // Get customer by phone number
  async getCustomerByPhone (telefone) {
    // Normalize phone number for consistent searching
    const normalizedPhone = this.normalizePhoneNumber(telefone)
    console.log(`🔍 Normalized phone: ${telefone} -> ${normalizedPhone}`)

    return await this.getCustomerByPhoneReal(normalizedPhone)
  }

  async getCustomerByPhoneReal (telefone) {
    const startTime = Date.now()

    try {
      console.log(`🔍 [getCustomerByPhoneReal] Buscando cliente por telefone: ${telefone}`)
      console.log('🔍 [getCustomerByPhoneReal] API URL:', this.config.apiUrl)

      // Phone should already be normalized by getCustomerByPhone
      const cleanPhone = telefone

      // Use the correct Trinks endpoint: GET /v1/clientes with telefone parameter
      // Based on https://trinks.readme.io/reference/get_v1-clientes
      const requestConfig = {
        params: {
          telefone: cleanPhone
        },
        headers: {
          ...this.axiosInstance.defaults.headers,
          estabelecimentoId: this.config.estabelecimentoId
        }
      }

      const fullUrl = `${this.config.apiUrl}/v1/clientes`
      console.log(`🔍 [getCustomerByPhoneReal] Tentando GET ${fullUrl}`)
      console.log('🔍 [getCustomerByPhoneReal] Headers:', {
        'X-Api-Key': this.config.apiKey ? this.config.apiKey.substring(0, 10) + '...' : 'NOT SET',
        estabelecimentoId: requestConfig.headers.estabelecimentoId
      })
      console.log('🔍 [getCustomerByPhoneReal] Params:', requestConfig.params)

      console.log(`🔍 Searching customer with phone: ${cleanPhone} using GET /v1/clientes`)
      const response = await this.axiosInstance.get('/v1/clientes', requestConfig)

      console.log(`✅ [getCustomerByPhoneReal] Response status: ${response.status}`)
      const responseTime = Date.now() - startTime

      // Handle Trinks API response structure for /v1/clientes
      let customers = []
      if (response.data) {
        // According to API docs, /v1/clientes returns array of cliente objects
        if (Array.isArray(response.data)) {
          customers = response.data
        } else if (response.data.data && Array.isArray(response.data.data)) {
          customers = response.data.data
        }
      }

      console.log('🔍 API Response structure:', {
        hasData: !!response.data,
        responseKeys: response.data ? Object.keys(response.data) : [],
        customersFound: customers.length,
        firstCustomer: customers[0] ? Object.keys(customers[0]) : []
      })

      const debugInfo = {
        apiUsed: true,

        endpoint: '/v1/clientes',
        method: 'GET',
        requestParams: requestConfig.params,
        requestHeaders: this.axiosInstance.defaults.headers,
        responseStatus: response.status,
        responseHeaders: response.headers,
        responseTime,
        found: customers.length > 0,
        timestamp: new Date().toISOString(),
        apiUrl: this.config.apiUrl
      }

      if (customers.length > 0) {
        const customer = customers[0]

        // Get customer appointments using the correct endpoint
        let ultimosServicos = []
        let appointmentsData = null
        try {
          appointmentsData = await this.getCustomerAppointmentsReal(customer.id)
          // Handle new structure with past/future appointments
          if (appointmentsData && appointmentsData.past) {
            ultimosServicos = appointmentsData.past // Use past appointments for ultimosServicos
          } else {
            ultimosServicos = appointmentsData || []
          }
        } catch (appointmentError) {
          console.warn('Error fetching customer appointments:', appointmentError.message)
        }

        return {
          id: customer.id.toString(),
          nome: customer.nome || customer.name || 'Nome não informado',
          telefone: customer.telefone || customer.phone || telefone,
          email: customer.email || 'Email não informado',
          ultimosServicos,
          appointmentsData, // Include full appointment data
          preferencias: customer.observacoes || customer.preferencias || 'Cliente da API Trinks',
          _debugInfo: debugInfo
        }
      }

      return {
        _debugInfo: debugInfo,
        _notFound: true
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      console.error('❌ [getCustomerByPhoneReal] ERRO detalhado:')
      console.error(`   - Telefone pesquisado: ${telefone}`)
      console.error(`   - Tempo de resposta: ${responseTime}ms`)
      console.error(`   - Erro: ${error.message}`)
      console.error(`   - Status: ${error.response?.status}`)
      console.error('   - Response data:', error.response?.data)
      console.error('   - Request config:', error.config
        ? {
            url: error.config.url,
            method: error.config.method,
            params: error.config.params
          }
        : 'N/A')

      // Log detalhado do erro
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'getCustomerByPhoneReal',
        telefone,
        endpoint: '/v1/clientes',
        method: 'GET',
        responseTime
      })

      // Sempre forçar erro quando API falha
      throw new Error(`Erro na API Trinks: ${error.message}`)
    }
  }

  // Get availability for a specific date
  async getAvailability (data, servico = null) {
    // Sempre usar API real
    return await (() => this.getAvailabilityReal(data, servico))
  }

  // Get professionals availability for a specific date (real API)
  // IMPORTANTE: Sempre busca em tempo real, sem cache
  async getProfessionalsAvailability (data, serviceId = null) {
    console.log(`🔄 Real-time availability check for date: ${data}, service: ${serviceId || 'all'}`)

    // Sempre usar API real
    return await (() => this.getProfessionalsAvailabilityReal(data, serviceId))
  }

  async getAvailabilityReal (data, servico = null) {
    try {
      const params = { date: data }
      if (servico) params.service = servico

      const response = await this.axiosInstance.get('/availability', { params })

      // Transform API response to match our expected format
      return {
        data,
        horarios: response.data.available_slots || response.data.horarios || []
      }
    } catch (error) {
      console.error('Error fetching availability from Trinks API:', error.message)

      // Log detalhado do erro
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'getAvailabilityReal',
        data,
        servico,
        endpoint: '/availability',
        method: 'GET'
      })

      // Sempre forçar erro quando API falha
      throw new Error(`Erro na API Trinks: ${error.message}`)
    }
  }

  // Nova função para buscar slots específicos de um profissional
  async getProfessionalSlots (data, professionalId, serviceId = null) {
    const startTime = Date.now()

    try {
      console.log(`🔍 [getProfessionalSlots] Buscando slots específicos - Data: ${data}, Profissional ID: ${professionalId}, Serviço ID: ${serviceId}`)
      console.log('🔍 [getProfessionalSlots] API URL:', this.config.apiUrl)

      const requestConfig = {
        params: {
          data,
          profissionalId: professionalId,
          excecoes: false,
          excluirExcecoesDeAgendamentoOnline: true
        },
        headers: {
          ...this.axiosInstance.defaults.headers,
          estabelecimentoId: this.config.estabelecimentoId
        }
      }

      // Se um serviço específico foi solicitado, adicionar ao request
      if (serviceId) {
        requestConfig.params.servicoId = serviceId
        console.log(`🔍 [getProfessionalSlots] Filtering by service ID: ${serviceId}`)
      }

      const fullUrl = `${this.config.apiUrl}/v1/agendamentos/profissionais/${data}`
      console.log(`🔍 [getProfessionalSlots] Tentando GET ${fullUrl}`)
      console.log('🔍 [getProfessionalSlots] Headers:', {
        'X-Api-Key': this.config.apiKey ? this.config.apiKey.substring(0, 10) + '...' : 'NOT SET',
        estabelecimentoId: requestConfig.headers.estabelecimentoId
      })
      console.log('🔍 [getProfessionalSlots] Params:', requestConfig.params)

      const response = await this.axiosInstance.get(`/v1/agendamentos/profissionais/${data}`, requestConfig)

      console.log(`✅ [getProfessionalSlots] Response status: ${response.status}`)
      const responseTime = Date.now() - startTime

      console.log(`✅ API Response for professional slots: ${JSON.stringify(response.data, null, 2)}`)

      if (response.data && response.data.data && response.data.data.length > 0) {
        const professional = response.data.data[0] // Pega o primeiro (deve ser apenas um)
        const slots = professional.horariosVagos || []

        console.log(`✅ Slots encontrados para profissional ${professionalId}: ${slots.length}`)

        return {
          professionalId,
          professionalName: professional.nome,
          data,
          slots,
          totalSlots: slots.length,
          hasAvailability: slots.length > 0,
          responseTime
        }
      } else {
        console.log(`❌ Nenhum slot encontrado para profissional ${professionalId} em ${data}`)
        return {
          professionalId,
          professionalName: null,
          data,
          slots: [],
          totalSlots: 0,
          hasAvailability: false,
          responseTime
        }
      }
    } catch (error) {
      const responseTime = Date.now() - startTime
      console.error('❌ [getProfessionalSlots] ERRO detalhado:')
      console.error(`   - Profissional ID: ${professionalId}`)
      console.error(`   - Data solicitada: ${data}`)
      console.error(`   - Serviço ID: ${serviceId}`)
      console.error(`   - Tempo de resposta: ${responseTime}ms`)
      console.error(`   - Erro: ${error.message}`)
      console.error(`   - Status: ${error.response?.status}`)
      console.error('   - Response data:', error.response?.data)
      console.error('   - Request config:', error.config
        ? {
            url: error.config.url,
            method: error.config.method,
            params: error.config.params
          }
        : 'N/A')

      return {
        professionalId,
        professionalName: null,
        data,
        slots: [],
        totalSlots: 0,
        hasAvailability: false,
        error: error.message,
        responseTime
      }
    }
  }

  async getProfessionalsAvailabilityReal (data, serviceId = null) {
    const startTime = Date.now()

    try {
      const requestConfig = {
        params: {
          data,
          excecoes: false,
          excluirExcecoesDeAgendamentoOnline: true
        },
        headers: {
          ...this.axiosInstance.defaults.headers,
          estabelecimentoId: this.config.estabelecimentoId
        }
      }

      // Se um serviço específico foi solicitado, adicionar ao request
      if (serviceId) {
        requestConfig.params.servicoId = serviceId
        console.log(`🎯 Filtering availability by service ID: ${serviceId}`)
      }

      const response = await this.axiosInstance.get('/v1/agendamentos/profissionais/data', requestConfig)
      const responseTime = Date.now() - startTime

      const debugInfo = {
        apiUsed: true,
        endpoint: '/v1/agendamentos/profissionais/data',
        method: 'GET',
        requestParams: requestConfig.params,
        responseTime,
        professionalsCount: response.data?.data?.length || 0,
        timestamp: new Date().toISOString()
      }

      if (response.data && response.data.data && response.data.data.length > 0) {
        const availability = response.data.data.map(profissional => ({
          id: profissional.id.toString(),
          nome: profissional.nome,
          horariosVagos: profissional.horariosVagos || [],
          intervalosVagos: profissional.intervalosVagos || [],
          totalSlots: profissional.horariosVagos ? profissional.horariosVagos.length : 0,
          _debugInfo: debugInfo
        }))

        // Sort professionals by most available slots first
        availability.sort((a, b) => b.totalSlots - a.totalSlots)

        console.log(`✅ Fetched availability for ${availability.length} professionals on ${data}`)
        return {
          data,
          professionals: availability,
          totalProfessionals: availability.length,
          _debugInfo: debugInfo
        }
      }

      console.warn('No availability found for date:', data)
      return {
        data,
        professionals: [],
        totalProfessionals: 0,
        _debugInfo: debugInfo
      }
    } catch (error) {
      console.error('Error fetching professionals availability from Trinks API:', error.message)

      // Log detalhado do erro
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'getProfessionalsAvailabilityReal',
        data,
        serviceId,
        endpoint: '/v1/agendamentos/profissionais/data',
        method: 'GET'
      })

      // Sempre forçar erro quando API falha
      throw new Error(`Erro na API Trinks: ${error.message}`)
    }
  }

  /**
   * Validates if a professional can perform a specific service on a given date
   * Uses the Trinks API to check service-professional compatibility in real time
   * @param {string} professionalId - ID of the professional
   * @param {string} serviceId - ID of the service
   * @param {string} date - Date in YYYY-MM-DD format
   * @returns {Object} Validation result with compatibility status and alternatives
   */
  async validateProfessionalService (professionalId, serviceId, date) {
    console.log(`🔍 Validating compatibility: Professional ${professionalId} + Service ${serviceId} on ${date}`)

    try {
      // Busca disponibilidade filtrando por serviço específico
      const availability = await this.getProfessionalsAvailability(date, serviceId)

      if (!availability || !availability.data) {
        console.warn('⚠️ No availability data returned from API')
        return {
          compatible: false,
          reason: 'API_ERROR',
          message: 'Não foi possível verificar a disponibilidade no momento',
          alternatives: []
        }
      }

      // Verifica se o profissional solicitado está na lista de profissionais
      // que podem realizar esse serviço
      const professional = availability.data.find(p => p.id.toString() === professionalId.toString())

      if (!professional) {
        console.log(`❌ Professional ${professionalId} cannot perform service ${serviceId}`)

        // Retorna profissionais alternativos que podem fazer o serviço
        const alternatives = availability.data.map(p => ({
          id: p.id,
          nome: p.nome,
          horariosVagos: p.horariosVagos || [],
          totalSlots: p.horariosVagos?.length || 0
        }))

        return {
          compatible: false,
          reason: 'PROFESSIONAL_INCOMPATIBLE',
          message: 'Este profissional não realiza esse serviço',
          alternatives,
          suggestedProfessionals: alternatives.slice(0, 3) // Top 3 alternatives
        }
      }

      console.log(`✅ Professional ${professionalId} can perform service ${serviceId}`)
      return {
        compatible: true,
        professional: {
          id: professional.id,
          nome: professional.nome,
          horariosVagos: professional.horariosVagos || [],
          totalSlots: professional.horariosVagos?.length || 0
        }
      }
    } catch (error) {
      console.error('❌ Error validating professional-service compatibility:', error.message)
      return {
        compatible: false,
        reason: 'VALIDATION_ERROR',
        message: 'Erro ao verificar compatibilidade',
        error: error.message,
        alternatives: []
      }
    }
  }

  // Create new customer - always use real API
  async createCustomer (customerData) {
    return await this.createCustomerReal(customerData)
  }

  async createCustomerReal (customerData) {
    const startTime = Date.now()

    try {
      // Clean phone number for Trinks API (remove +55 prefix)
      const cleanPhone = customerData.telefone?.startsWith('+55')
        ? customerData.telefone.replace('+55', '')
        : customerData.telefone

      // Parse phone number for DDI, DDD, and number
      const phoneMatch = cleanPhone.match(/^(\d{2})(\d{8,9})$/)
      const ddd = phoneMatch ? phoneMatch[1] : '21'
      const numero = phoneMatch ? phoneMatch[2] : cleanPhone.substring(2)

      const requestBody = {
        nome: customerData.nome || customerData.name,
        genero: this.mapGenderToTrinksFormat(customerData.genero || customerData.gender),
        telefones: [
          {
            ddi: '55',
            ddd,
            numero,
            tipoId: 1
          }
        ]
      }

      const requestConfig = {
        headers: {
          ...this.axiosInstance.defaults.headers,
          estabelecimentoId: this.config.estabelecimentoId
        }
      }

      const response = await this.axiosInstance.post('/v1/clientes', requestBody, requestConfig)
      const responseTime = Date.now() - startTime

      const debugInfo = {
        apiUsed: true,
        endpoint: '/v1/clientes',
        method: 'POST',
        requestBody,
        responseTime,
        responseStatus: response.status,
        timestamp: new Date().toISOString(),
        apiUrl: this.config.apiUrl
      }

      if (response.data) {
        const customer = {
          id: response.data.id?.toString() || uuidv4(),
          nome: response.data.nome || customerData.nome,
          telefone: response.data.telefone || customerData.telefone,
          email: response.data.email || customerData.email,
          ultimosServicos: [],
          preferencias: response.data.observacoes || customerData.observacoes || 'Cliente da API Trinks',
          created_at: response.data.criadoEm || new Date().toISOString(),
          _debugInfo: debugInfo
        }

        console.log(`✅ Customer created via Trinks API: ${customer.id} - ${customer.nome}`)
        return customer
      }

      const error = new Error('Invalid response from Trinks API')
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'createCustomer',
        customerData,
        response: response?.data,
        endpoint: '/v1/clientes',
        method: 'POST'
      })
      throw error
    } catch (error) {
      const responseTime = Date.now() - startTime

      console.error('Error creating customer via Trinks API:', error.message)

      // Log apenas se não foi já logado acima
      if (!error.message.includes('Invalid response from Trinks API')) {
        await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
          function: 'createCustomer',
          customerData,
          endpoint: '/v1/clientes',
          method: 'POST',
          responseTime,
          originalError: error.message
        })
      }

      // Sempre forçar erro quando API falha
      throw new Error(`Erro na API Trinks: ${error.message}`)
    }
  }

  // Create new appointment - Always use real API
  async createAppointment (appointmentData) {
    if (!this.config.enabled || !this.axiosInstance) {
      throw new Error(
        '🚨 API Trinks não configurada! Verificar:\n' +
        `- config.enabled: ${this.config.enabled}\n` +
        `- axiosInstance: ${!!this.axiosInstance}\n` +
        '- TRINKS_API_KEY no .env\n' +
        'Sistema requer API válida para funcionar'
      )
    }

    console.log('📅 Criando agendamento via API Trinks (REAL)')
    return await this.createAppointmentReal(appointmentData)
  }

  async createAppointmentReal (appointmentData) {
    const startTime = Date.now()

    try {
      // Validate required fields
      const clienteId = appointmentData.clienteId || appointmentData.cliente_id
      const servicoId = appointmentData.servicoId || appointmentData.servico_id
      const data = appointmentData.data
      const hora = appointmentData.hora || appointmentData.horario

      if (!clienteId || !servicoId || !data || !hora) {
        throw new Error('Campos obrigatórios faltando: clienteId, servicoId, data, hora')
      }

      // Get service data for duration and price
      let duracaoEmMinutos = appointmentData.duracaoEmMinutos || 60
      let valor = appointmentData.valor || 0

      try {
        const services = await this.getServices()
        const service = services.find(s => s.id.toString() === servicoId.toString())
        if (service) {
          duracaoEmMinutos = service.duracao || 60
          valor = service.preco || 0
          console.log(`✅ Found service data: ${service.nome} - ${duracaoEmMinutos}min - R$${valor}`)
        } else {
          console.warn(`⚠️ Service not found: ${servicoId}, using defaults`)
        }
      } catch (serviceError) {
        console.warn('Error fetching service data:', serviceError.message)
      }

      // Combine date and time into ISO format for dataHoraInicio
      const dataHoraInicio = `${data}T${hora}:00`

      // Transform our internal format to Trinks API format (matching exact API spec)
      const requestBody = {
        clienteId: parseInt(clienteId),
        servicoId: parseInt(servicoId),
        dataHoraInicio,
        duracaoEmMinutos: parseInt(duracaoEmMinutos),
        valor: parseFloat(valor),
        profissionalId: appointmentData.profissionalId ? parseInt(appointmentData.profissionalId) : null,
        observacoes: appointmentData.observacoes || appointmentData.notes || null
      }

      const requestConfig = {
        headers: {
          ...this.axiosInstance.defaults.headers,
          estabelecimentoId: this.config.estabelecimentoId
        }
      }

      const response = await this.axiosInstance.post('/v1/agendamentos', requestBody, requestConfig)
      const responseTime = Date.now() - startTime

      const debugInfo = {
        apiUsed: true,
        endpoint: '/v1/agendamentos',
        method: 'POST',
        requestBody,
        responseTime,
        responseStatus: response.status,
        timestamp: new Date().toISOString(),
        apiUrl: this.config.apiUrl
      }

      if (response.data) {
        const appointment = {
          id: response.data.id?.toString() || uuidv4(),
          clienteId: response.data.clienteId || appointmentData.clienteId,
          profissionalId: response.data.profissionalId || appointmentData.profissionalId,
          servicoId: response.data.servicoId || appointmentData.servicoId,
          data: response.data.data || appointmentData.data,
          hora: response.data.hora || appointmentData.hora,
          status: response.data.status || 'confirmado',
          observacoes: response.data.observacoes || appointmentData.observacoes,
          valor: response.data.valor || appointmentData.valor,
          created_at: response.data.criadoEm || new Date().toISOString(),
          _debugInfo: debugInfo
        }

        console.log(`✅ Appointment created via Trinks API: ${appointment.id}`)
        return appointment
      }

      throw new Error('Invalid response from Trinks API')
    } catch (error) {
      console.error('Error creating appointment via Trinks API:', error.message)

      // Sempre forçar erro quando API falha
      throw new Error(`Erro na API Trinks: ${error.message}`)
    }
  }

  // Create appointment with customer verification/creation
  async createAppointmentWithCustomer (appointmentData, customerData = null) {
    try {
      let customerId = appointmentData.clienteId || appointmentData.cliente_id

      // If no customer ID provided, try to find or create customer
      if (!customerId && customerData) {
        console.log(`🔍 Searching for customer: ${customerData.telefone}`)

        // First, try to find existing customer
        const existingCustomer = await this.getCustomerByPhone(customerData.telefone)

        if (existingCustomer && !existingCustomer._notFound) {
          customerId = existingCustomer.id
          console.log(`✅ Found existing customer: ${existingCustomer.id} - ${existingCustomer.nome}`)
        } else {
          // Customer not found, create new one
          console.log(`➕ Creating new customer: ${customerData.nome || customerData.telefone}`)
          const newCustomer = await this.createCustomer(customerData)
          customerId = newCustomer.id
          console.log(`✅ Created new customer: ${newCustomer.id} - ${newCustomer.nome}`)
        }
      }

      if (!customerId) {
        const error = new Error('Cliente ID é obrigatório para criar agendamento')
        await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
          function: 'createAppointmentWithCustomer',
          appointmentData,
          customerData,
          hasCustomerId: !!customerId
        })
        throw error
      }

      // Update appointment data with customer ID
      const completeAppointmentData = {
        ...appointmentData,
        clienteId: customerId,
        cliente_id: customerId
      }

      // Create the appointment
      console.log(`📅 Creating appointment for customer: ${customerId}`)
      const appointment = await this.createAppointment(completeAppointmentData)

      return {
        appointment,
        customerId,
        wasCustomerCreated: !appointmentData.clienteId && customerData
      }
    } catch (error) {
      console.error('Error creating appointment with customer:', error.message)

      // Log apenas se não foi já logado
      if (!error.message.includes('Cliente ID é obrigatório')) {
        await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
          function: 'createAppointmentWithCustomer',
          appointmentData,
          customerData,
          originalError: error.message
        })
      }

      throw error
    }
  }

  // Get professional services - Always use real API
  async getProfessionalServices (professionalId) {
    const professionalServicesCache = require('../utils/professionalServicesCache')
    const startTime = Date.now()

    try {
      // Verificar cache primeiro
      const cachedServices = professionalServicesCache.get(professionalId)
      if (cachedServices) {
        return {
          success: true,
          services: cachedServices,
          fromCache: true,
          professionalId
        }
      }

      console.log(`🔍 Buscando serviços do profissional ${professionalId} na API...`)

      // Implementar paginação completa (igual getServicesReal e getProfessionalsReal)
      let allServices = []
      let currentPage = 1
      let totalPages = 1

      // Fetch all pages of professional services
      do {
        const requestConfig = {
          params: {
            page: currentPage,
            pageSize: 50 // Máximo permitido pela API Trinks
          },
          headers: {
            ...this.axiosInstance.defaults.headers,
            estabelecimentoId: this.config.estabelecimentoId
          }
        }

        const response = await this.axiosInstance.get(`/v1/profissionais/${professionalId}/servicos`, requestConfig)

        if (response.data && response.data.data && response.data.data.length > 0) {
          allServices = allServices.concat(response.data.data)
          totalPages = response.data.totalPages || 1
          currentPage++

          console.log(`📄 Página ${currentPage - 1}/${totalPages} processada: ${response.data.data.length} serviços`)
        } else {
          break
        }
      } while (currentPage <= totalPages)

      const responseTime = Date.now() - startTime

      console.log(`✅ Serviços do profissional ${professionalId} coletados: ${allServices.length} serviços em ${totalPages} página(s)`)

      if (allServices.length > 0) {
        // Armazenar no cache
        professionalServicesCache.set(professionalId, allServices)

        return {
          success: true,
          services: allServices,
          fromCache: false,
          professionalId,
          responseTime,
          totalServices: allServices.length,
          totalPages
        }
      } else {
        console.warn(`⚠️ Nenhum serviço encontrado para profissional ${professionalId}`)
        return {
          success: false,
          services: [],
          professionalId,
          message: 'Nenhum serviço encontrado'
        }
      }
    } catch (error) {
      console.error(`❌ Erro ao buscar serviços do profissional ${professionalId}:`, error.message)

      // Log detalhado do erro
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'getProfessionalServices',
        professionalId,
        endpoint: `/v1/profissionais/${professionalId}/servicos`,
        method: 'GET'
      })

      return {
        success: false,
        error: error.message,
        services: [],
        professionalId
      }
    }
  }

  // Get all services with caching
  async getServices () {
    const servicesCache = require('../utils/servicesCache')
    return await servicesCache.getServices()
  }

  async getServicesReal () {
    const startTime = Date.now()

    console.log('🔍 [getServicesReal] Iniciando busca de serviços...')
    console.log('🔍 [getServicesReal] API URL:', this.config.apiUrl)
    console.log('🔍 [getServicesReal] Establishment ID:', this.config.estabelecimentoId)

    try {
      let allServices = []
      let currentPage = 1
      let totalPages = 1

      // Fetch all pages of services
      do {
        const requestConfig = {
          params: {
            somenteVisiveisCliente: true, // Only services visible to clients
            page: currentPage,
            pageSize: 50
          },
          headers: {
            ...this.axiosInstance.defaults.headers,
            estabelecimentoId: this.config.estabelecimentoId
          }
        }

        const fullUrl = `${this.config.apiUrl}/v1/servicos`
        console.log(`🔍 [getServicesReal] Tentando GET ${fullUrl} (página ${currentPage})`)
        console.log('🔍 [getServicesReal] Headers:', {
          'X-Api-Key': this.config.apiKey ? this.config.apiKey.substring(0, 10) + '...' : 'NOT SET',
          estabelecimentoId: requestConfig.headers.estabelecimentoId
        })
        console.log('🔍 [getServicesReal] Params:', requestConfig.params)

        const response = await this.axiosInstance.get('/v1/servicos', requestConfig)

        console.log(`✅ [getServicesReal] Response status: ${response.status}`)
        console.log('✅ [getServicesReal] Response data structure:', {
          hasData: !!response.data,
          hasDataArray: !!response.data?.data,
          dataLength: response.data?.data?.length || 0,
          totalPages: response.data?.totalPages
        })

        if (response.data && response.data.data && response.data.data.length > 0) {
          allServices = allServices.concat(response.data.data)
          totalPages = response.data.totalPages || 1
          currentPage++
        } else {
          break
        }
      } while (currentPage <= totalPages)

      const responseTime = Date.now() - startTime

      const debugInfo = {
        apiUsed: true,
        endpoint: '/v1/servicos',
        method: 'GET',
        responseTime,
        servicesCount: allServices.length,
        totalPages,
        timestamp: new Date().toISOString()
      }

      if (allServices.length > 0) {
        const services = allServices.map(servico => ({
          id: servico.id.toString(),
          nome: servico.nome,
          duracao: servico.duracaoEmMinutos || 60,
          preco: parseFloat(servico.preco || 0),
          descricao: servico.descricao || '',
          categoria: servico.categoria || 'Geral',
          ativo: servico.ativo !== false,
          visivel_cliente: servico.visivelParaCliente !== false,
          _debugInfo: debugInfo
        }))

        const filteredServices = services.filter(service => service.ativo && service.visivel_cliente)
        console.log(`✅ Fetched ${filteredServices.length} services from Trinks API across ${totalPages} pages`)
        return filteredServices
      }

      console.warn('No services found in Trinks API response')
      throw new Error('Nenhum serviço encontrado na API Trinks')
    } catch (error) {
      console.error('❌ [getServicesReal] ERRO ao buscar serviços:', error.message)
      console.error('❌ [getServicesReal] Tipo de erro:', error.code || 'UNKNOWN')
      console.error('❌ [getServicesReal] Stack:', error.stack)

      // Detalhes adicionais para debug de conectividade
      if (error.code === 'ENOTFOUND') {
        console.error('❌ [getServicesReal] ERRO DNS: Não foi possível resolver api.trinks.com')
      } else if (error.code === 'ETIMEDOUT') {
        console.error('❌ [getServicesReal] TIMEOUT: Conexão expirou')
      } else if (error.code === 'ECONNREFUSED') {
        console.error('❌ [getServicesReal] CONEXÃO RECUSADA: Servidor não está respondendo')
      }

      // Log detalhado do erro
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'getServicesReal',
        endpoint: '/v1/servicos',
        method: 'GET'
      })

      // Sempre forçar erro quando API falha
      throw new Error(`Erro na API Trinks: ${error.message}`)
    }
  }

  // Get all professionals
  async getProfessionals () {
    return await this.getProfessionalsReal()
  }

  async getProfessionalsReal () {
    const startTime = Date.now()

    console.log('🔍 [getProfessionalsReal] Iniciando busca de profissionais...')
    console.log('🔍 [getProfessionalsReal] API URL:', this.config.apiUrl)
    console.log('🔍 [getProfessionalsReal] Establishment ID:', this.config.estabelecimentoId)

    try {
      let allProfessionals = []
      let currentPage = 1
      let totalPages = 1

      // Fetch all pages of professionals
      do {
        const requestConfig = {
          params: {
            page: currentPage,
            pageSize: 50
          },
          headers: {
            ...this.axiosInstance.defaults.headers,
            estabelecimentoId: this.config.estabelecimentoId
          }
        }

        const fullUrl = `${this.config.apiUrl}/v1/profissionais`
        console.log(`🔍 [getProfessionalsReal] Tentando GET ${fullUrl} (página ${currentPage})`)
        console.log('🔍 [getProfessionalsReal] Headers:', {
          'X-Api-Key': this.config.apiKey ? this.config.apiKey.substring(0, 10) + '...' : 'NOT SET',
          estabelecimentoId: requestConfig.headers.estabelecimentoId
        })
        console.log('🔍 [getProfessionalsReal] Params:', requestConfig.params)

        const response = await this.axiosInstance.get('/v1/profissionais', requestConfig)

        console.log(`✅ [getProfessionalsReal] Response status: ${response.status}`)
        console.log('✅ [getProfessionalsReal] Response data structure:', {
          hasData: !!response.data,
          hasDataArray: !!response.data?.data,
          dataLength: response.data?.data?.length || 0,
          totalPages: response.data?.totalPages
        })

        if (response.data && response.data.data && response.data.data.length > 0) {
          allProfessionals = allProfessionals.concat(response.data.data)
          totalPages = response.data.totalPages || 1
          currentPage++
        } else {
          break
        }
      } while (currentPage <= totalPages)

      const responseTime = Date.now() - startTime

      const debugInfo = {
        apiUsed: true,
        endpoint: '/v1/profissionais',
        method: 'GET',
        responseTime,
        professionalsCount: allProfessionals.length,
        totalPages,
        timestamp: new Date().toISOString()
      }

      if (allProfessionals.length > 0) {
        const professionals = allProfessionals.map(profissional => ({
          id: profissional.id.toString(),
          nome: profissional.apelido || profissional.nome || profissional.name,
          nomeCompleto: profissional.nome || profissional.name,
          cpf: profissional.cpf,
          apelido: profissional.apelido,
          especialidades: [], // This would need another API call to get specialties
          _debugInfo: debugInfo
        }))

        console.log(`✅ Fetched ${professionals.length} professionals from Trinks API across ${totalPages} pages`)
        return professionals
      }

      console.warn('No professionals found in Trinks API response')
      throw new Error('Nenhum profissional encontrado na API Trinks')
    } catch (error) {
      console.error('❌ [getProfessionalsReal] ERRO ao buscar profissionais:', error.message)
      console.error('❌ [getProfessionalsReal] Tipo de erro:', error.code || 'UNKNOWN')
      console.error('❌ [getProfessionalsReal] Stack:', error.stack)

      // Detalhes adicionais para debug de conectividade
      if (error.code === 'ENOTFOUND') {
        console.error('❌ [getProfessionalsReal] ERRO DNS: Não foi possível resolver api.trinks.com')
      } else if (error.code === 'ETIMEDOUT') {
        console.error('❌ [getProfessionalsReal] TIMEOUT: Conexão expirou')
      } else if (error.code === 'ECONNREFUSED') {
        console.error('❌ [getProfessionalsReal] CONEXÃO RECUSADA: Servidor não está respondendo')
      }

      // Log detalhado do erro
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'getProfessionalsReal',
        endpoint: '/v1/profissionais',
        method: 'GET',
        errorCode: error.code,
        apiUrl: this.config.apiUrl
      })

      // Sempre forçar erro quando API falha
      throw new Error(`Erro na API Trinks: ${error.message}`)
    }
  }

  // Get customer appointments - always use real API
  async getCustomerAppointments (customerId) {
    const result = await this.getCustomerAppointmentsReal(customerId)
    // If result has the new structure with past/future, return it
    if (result && typeof result === 'object' && result.all) {
      return result
    }
    // Otherwise, return as legacy format
    return result || []
  }

  // Get customer appointments from real API
  // Uses the correct endpoint: GET /v1/agendamentos with clienteId
  // Based on https://trinks.readme.io/reference/get_v1-agendamentos
  async getCustomerAppointmentsReal (customerId) {
    const startTime = Date.now()

    try {
      console.log(`🔍 [getCustomerAppointmentsReal] Buscando agendamentos do cliente: ${customerId}`)
      console.log('🔍 [getCustomerAppointmentsReal] API URL:', this.config.apiUrl)

      // Calculate date range: 30 days before and 30 days after today
      const today = new Date()
      const thirtyDaysBefore = new Date(today)
      thirtyDaysBefore.setDate(today.getDate() - 30)
      const thirtyDaysAfter = new Date(today)
      thirtyDaysAfter.setDate(today.getDate() + 30)

      const dataInicio = thirtyDaysBefore.toLocaleDateString('sv-SE') // YYYY-MM-DD format
      const dataFim = thirtyDaysAfter.toLocaleDateString('sv-SE') // YYYY-MM-DD format

      const params = {
        clienteId: parseInt(customerId),
        dataInicio,
        dataFim
      }

      const requestConfig = {
        params,
        headers: {
          ...this.axiosInstance.defaults.headers,
          estabelecimentoId: this.config.estabelecimentoId
        }
      }

      const fullUrl = `${this.config.apiUrl}/v1/agendamentos`
      console.log(`🔍 [getCustomerAppointmentsReal] Tentando GET ${fullUrl}`)
      console.log('🔍 [getCustomerAppointmentsReal] Headers:', {
        'X-Api-Key': this.config.apiKey ? this.config.apiKey.substring(0, 10) + '...' : 'NOT SET',
        estabelecimentoId: requestConfig.headers.estabelecimentoId
      })
      console.log('🔍 [getCustomerAppointmentsReal] Params:', requestConfig.params)

      console.log(`🔍 Fetching appointments for customer ${customerId} from ${dataInicio} to ${dataFim}`)
      const response = await this.axiosInstance.get('/v1/agendamentos', requestConfig)

      console.log(`✅ [getCustomerAppointmentsReal] Response status: ${response.status}`)

      // Handle Trinks API response structure for /v1/agendamentos
      // According to API docs, this returns array of agendamento objects
      let agendamentos = []
      if (response.data) {
        if (Array.isArray(response.data)) {
          agendamentos = response.data
        } else if (response.data.data && Array.isArray(response.data.data)) {
          agendamentos = response.data.data
        }
      }

      console.log(`🔍 Found ${agendamentos.length} appointments for customer ${customerId}`)

      if (agendamentos.length > 0) {
        // Transform API response to match our expected format
        // Separate past and future appointments based on today's date

        const formattedAppointments = agendamentos.map(agendamento => {
          const appointmentDate = agendamento.dataHoraInicio ? agendamento.dataHoraInicio.split('T')[0] : agendamento.data
          const appointmentTime = agendamento.dataHoraInicio ? agendamento.dataHoraInicio.split('T')[1]?.substring(0, 5) : agendamento.hora

          // Create full datetime for accurate comparison (use Brazil timezone)
          const appointmentDateTimeStr = `${appointmentDate}T${appointmentTime || '00:00'}:00`
          const appointmentDateTime = new Date(appointmentDateTimeStr)
          const nowInBrazil = new Date()

          // Adjust to Brazil timezone for accurate comparison
          const nowBrazilTime = new Date(nowInBrazil.getTime() - (nowInBrazil.getTimezoneOffset() * 60000) + (-3 * 3600000)) // UTC-3 (Brazil)

          const isFutureAccurate = appointmentDateTime > nowBrazilTime
          const isPastAccurate = appointmentDateTime <= nowBrazilTime

          return {
            id: agendamento.id,
            data: appointmentDate,
            hora: appointmentTime,
            servico: agendamento.servico?.nome || agendamento.servicoNome || 'Serviço não informado',
            profissional: agendamento.profissional?.nome || agendamento.profissionalNome || 'Profissional não informado',
            valor: parseFloat(agendamento.valor || 0),
            status: agendamento.status?.nome || agendamento.status || 'Confirmado',
            observacoes: agendamento.observacoesDoCliente || agendamento.observacoes || '',
            // Use accurate datetime comparison that considers both date AND time
            isPast: isPastAccurate,
            isFuture: isFutureAccurate,
            _debugInfo: {
              appointmentDateTime: appointmentDateTimeStr,
              nowBrazilTime: nowBrazilTime.toISOString(),
              comparison: {
                isFuture: isFutureAccurate,
                isPast: isPastAccurate
              }
            }
          }
        })

        // Sort by date, newest first
        const sortedAppointments = formattedAppointments.sort((a, b) => new Date(b.data) - new Date(a.data))

        console.log(`✅ Found ${formattedAppointments.length} total appointments (${formattedAppointments.filter(a => a.isPast).length} past, ${formattedAppointments.filter(a => a.isFuture).length} future)`)

        // Filter out duplicates - keep the one with professional assigned or the earliest created
        const uniqueAppointments = this.removeDuplicateAppointments(sortedAppointments)

        // Filter out cancelled appointments using multiple approaches
        const activeAppointments = uniqueAppointments.filter(a => {
          const status = (a.status || '').toLowerCase()

          // Common cancelled status patterns (multiple languages/variations)
          const cancelledPatterns = [
            'cancelado', 'canceled', 'cancelled',
            'desmarcado', 'anulado', 'cancelou',
            'não compareceu', 'faltou', 'ausente'
          ]

          const isCancelled = cancelledPatterns.some(pattern => status.includes(pattern))
          return !isCancelled
        })

        console.log(`🔍 After filtering cancelled appointments: ${activeAppointments.length}/${uniqueAppointments.length} active appointments`)

        return {
          all: uniqueAppointments, // Keep all for historical purposes
          past: activeAppointments.filter(a => a.isPast).slice(0, 3), // Last 3 past appointments (active only)
          future: activeAppointments.filter(a => a.isFuture), // All future ACTIVE appointments only
          total: activeAppointments.length // Count only active appointments
        }
      }

      console.log('No appointments found for customer')
      return []
    } catch (error) {
      const responseTime = Date.now() - startTime
      console.error('❌ [getCustomerAppointmentsReal] ERRO detalhado:')
      console.error(`   - Cliente ID: ${customerId}`)
      console.error(`   - Tempo de resposta: ${responseTime}ms`)
      console.error(`   - Erro: ${error.message}`)
      console.error(`   - Status: ${error.response?.status}`)
      console.error('   - Response data:', error.response?.data)
      console.error('   - Request config:', error.config
        ? {
            url: error.config.url,
            method: error.config.method,
            params: error.config.params
          }
        : 'N/A')

      // Log detalhado do erro
      await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
        function: 'getCustomerAppointmentsReal',
        customerId,
        endpoint: '/v1/agendamentos',
        method: 'GET',
        responseTime
      })

      return []
    }
  }

  /**
   * Remove duplicate appointments, keeping the one with professional assigned or earliest created
   * @param {Array} appointments - Array of appointments to deduplicate
   * @returns {Array} - Array without duplicates
   */
  removeDuplicateAppointments (appointments) {
    if (!Array.isArray(appointments) || appointments.length === 0) {
      return appointments
    }

    const uniqueMap = new Map()

    appointments.forEach(appointment => {
      // Create a unique key based on date, time and service
      const key = `${appointment.data}-${appointment.hora}-${appointment.servicoNome || appointment.service || 'unknown'}`

      const existing = uniqueMap.get(key)

      if (!existing) {
        // First appointment with this key
        uniqueMap.set(key, appointment)
      } else {
        // Duplicate found - decide which one to keep
        const shouldReplace = this.shouldReplaceAppointment(existing, appointment)
        if (shouldReplace) {
          uniqueMap.set(key, appointment)
        }
      }
    })

    const result = Array.from(uniqueMap.values())

    if (result.length !== appointments.length) {
      console.log(`🗑️ Removed ${appointments.length - result.length} duplicate appointments`)
    }

    return result
  }

  /**
   * Helper method to decide which appointment to keep when duplicates are found
   * Priority: professional assigned > earlier created date
   * @param {Object} existing - Current appointment
   * @param {Object} candidate - New appointment candidate
   * @returns {boolean} - Whether to replace existing with candidate
   */
  shouldReplaceAppointment (existing, candidate) {
    // Prefer appointment with professional assigned
    const existingHasProfessional = !!(existing.profissionalNome || existing.professional)
    const candidateHasProfessional = !!(candidate.profissionalNome || candidate.professional)

    if (candidateHasProfessional && !existingHasProfessional) {
      return true // Replace with one that has professional
    }

    if (!candidateHasProfessional && existingHasProfessional) {
      return false // Keep the one with professional
    }

    // If both have professional or both don't, prefer the one created earlier
    const existingCreated = new Date(existing.criadoEm || existing.createdAt || 0)
    const candidateCreated = new Date(candidate.criadoEm || candidate.createdAt || 0)

    return candidateCreated < existingCreated // Earlier created wins
  }
}

const trinksService = new TrinksService()

// Adicionar função utilitária como propriedade do serviço (bound para ter acesso ao this)
trinksService.getProfessionalIdByName = (name) => getProfessionalIdByName(name, trinksService)

// Adicionar método para buscar ID de serviço por nome usando o cache
trinksService.getServiceIdByName = async (serviceName, trinksApiCalls = []) => {
  const servicesCache = require('../utils/servicesCache')
  return await servicesCache.getServiceIdByName(serviceName, trinksService, trinksApiCalls)
}

/**
 * 📅 SISTEMA DE BUSCA PROGRESSIVA DE DISPONIBILIDADE
 * Métodos para buscar slots nos 3 cenários:
 * 1. Profissional + Serviço + Data
 * 2. Profissional + Data
 * 3. Serviço + Data
 */

/**
 * Cenário 1: Buscar slots específicos de um profissional para um serviço em uma data
 * @param {string} professionalName Nome do profissional
 * @param {string} serviceName Nome do serviço
 * @param {string} date Data no formato YYYY-MM-DD
 * @returns {Object} Dados de disponibilidade
 */
trinksService.getProfessionalServiceSlots = async function (professionalName, serviceName, date) {
  try {
    console.log(`🔍 [Cenário 1] Buscando slots: ${professionalName} + ${serviceName} + ${date}`)

    // Buscar ID do profissional
    const professionalId = trinksService.getProfessionalIdByName(professionalName)
    if (!professionalId) {
      console.log(`❌ Profissional "${professionalName}" não encontrado`)
      return { slots: [], error: 'Professional not found' }
    }

    // Buscar ID do serviço
    const serviceId = await trinksService.getServiceIdByName(serviceName)
    if (!serviceId) {
      console.log(`❌ Serviço "${serviceName}" não encontrado`)
      return { slots: [], error: 'Service not found' }
    }

    // Buscar slots do profissional na data
    const response = await trinksService.axiosInstance.get(`/v1/agendamentos/profissionais/${date}`, {
      params: {
        profissionalId: professionalId,
        servicoId: serviceId
      },
      headers: {
        estabelecimentoId: trinksService.config.estabelecimentoId
      }
    })

    const slots = response.data?.horarios || []

    return {
      slots,
      professionalName,
      professionalId,
      serviceName,
      serviceId,
      date,
      formattedSlots: slots.join(', ')
    }
  } catch (error) {
    console.error('❌ Erro ao buscar slots profissional+serviço+data:', error.message)

    // Log detalhado do erro
    errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
      function: 'getProfessionalServiceSlots',
      professionalName,
      serviceName,
      date,
      endpoint: `/v1/agendamentos/profissionais/${date}`,
      method: 'GET'
    }).catch(logError => {
      console.error('❌ Erro ao salvar log:', logError.message)
    })

    return { slots: [], error: error.message }
  }
}

/**
 * Cenário 3: Buscar slots de todos os profissionais que fazem um serviço em uma data
 * @param {string} serviceName Nome do serviço
 * @param {string} date Data no formato YYYY-MM-DD
 * @returns {Object} Dados de disponibilidade
 */
trinksService.getServiceSlots = async function (serviceName, date) {
  try {
    console.log(`🔍 [Cenário 3] Buscando slots: ${serviceName} + ${date}`)

    // Buscar ID do serviço
    const serviceId = await trinksService.getServiceIdByName(serviceName)
    if (!serviceId) {
      console.log(`❌ Serviço "${serviceName}" não encontrado`)
      return { slots: [], error: 'Service not found' }
    }

    // Buscar slots de todos os profissionais que fazem o serviço
    const response = await trinksService.axiosInstance.get(`/v1/agendamentos/profissionais/${date}`, {
      params: {
        servicoId: serviceId
      },
      headers: {
        estabelecimentoId: trinksService.config.estabelecimentoId
      }
    })

    // Agrupar slots por profissional
    const professionalSlots = response.data?.profissionais || []
    const allSlots = []
    const professionalData = []

    professionalSlots.forEach(prof => {
      if (prof.horarios && prof.horarios.length > 0) {
        prof.horarios.forEach(slot => {
          allSlots.push(`${prof.nome} às ${slot}`)
        })
        professionalData.push({
          name: prof.nome,
          id: prof.id,
          slots: prof.horarios
        })
      }
    })

    return {
      slots: allSlots,
      serviceName,
      serviceId,
      date,
      professionals: professionalData,
      formattedSlots: allSlots.join(', ')
    }
  } catch (error) {
    console.error('❌ Erro ao buscar slots serviço+data:', error.message)
    return { slots: [], error: error.message }
  }
}

/**
 * Get future appointments for a customer by phone number
 * @param {string} customerPhone - Customer phone number
 * @returns {Promise<Object>} - Object with success flag and appointments data
 */
trinksService.getFutureAppointments = async function (customerPhone) {
  try {
    console.log(`🔍 Buscando agendamentos futuros para telefone: ${customerPhone}`)

    // First get customer data by phone
    const customerData = await this.getCustomerByPhone(customerPhone)

    if (!customerData || customerData._notFound) {
      console.log('❌ Cliente não encontrado para buscar agendamentos futuros')
      return {
        success: false,
        error: 'Cliente não encontrado',
        appointments: []
      }
    }

    // Get all appointments for the customer
    const allAppointments = await this.getCustomerAppointments(customerData.id)

    if (!allAppointments) {
      console.log('❌ Erro ao buscar agendamentos do cliente')
      return {
        success: false,
        error: 'Erro ao buscar agendamentos',
        appointments: []
      }
    }

    // Extract future appointments
    let futureAppointments = []

    if (allAppointments.future && Array.isArray(allAppointments.future)) {
      futureAppointments = allAppointments.future
    } else if (Array.isArray(allAppointments)) {
      // Legacy format - filter by date
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      futureAppointments = allAppointments.filter(appointment => {
        const appointmentDate = new Date(appointment.data)
        return appointmentDate >= today
      })
    }

    console.log(`✅ Encontrados ${futureAppointments.length} agendamentos futuros`)

    return {
      success: true,
      appointments: futureAppointments,
      count: futureAppointments.length
    }
  } catch (error) {
    console.error('❌ Erro ao buscar agendamentos futuros:', error.message)

    // Log detailed error
    await errorLogger.logError(error, ERROR_CATEGORIES.TRINKS_ERROR, {
      function: 'getFutureAppointments',
      customerPhone,
      method: 'GET'
    })

    return {
      success: false,
      error: error.message,
      appointments: []
    }
  }
}

module.exports = trinksService
