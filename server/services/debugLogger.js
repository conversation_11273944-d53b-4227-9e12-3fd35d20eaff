class DebugLogger {
  constructor() {
    this.logs = [];
    this.maxLogs = 50; // Keep last 50 logs
  }

  addLog(type, event, data) {
    const log = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      type, // 'webhook', 'polling', 'api_request', 'api_response'
      event, // 'incoming_webhook', 'outgoing_request', etc.
      data: this.sanitizeData(data)
    };

    this.logs.unshift(log);
    
    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }

    // Safely log to console with error handling
    try {
      console.log(`🐛 Debug Log [${type}/${event}]:`, data);
    } catch (error) {
      // Ignore EPIPE errors (broken pipe) when console output is closed
      if (error.code !== 'EPIPE') {
        console.error('Debug logger error:', error.message);
      }
    }
  }

  sanitizeData(data) {
    try {
      // Remove sensitive information
      const sanitized = JSON.parse(JSON.stringify(data));
      
      // Remove API keys, tokens, etc.
      if (sanitized.headers && sanitized.headers.Authorization) {
        sanitized.headers.Authorization = '••••••••';
      }
      
      if (sanitized.apiKey) {
        sanitized.apiKey = '••••••••';
      }

      return sanitized;
    } catch (error) {
      return { error: 'Failed to sanitize data', original: String(data) };
    }
  }

  getLogs(limit = 10) {
    return this.logs.slice(0, limit);
  }

  clearLogs() {
    this.logs = [];
  }

  // Webhook specific methods
  logWebhookReceived(provider, webhookData) {
    this.addLog('webhook', 'received', {
      provider,
      body: webhookData,
      timestamp: new Date().toISOString()
    });
  }

  logWebhookProcessed(provider, success, error = null) {
    this.addLog('webhook', 'processed', {
      provider,
      success,
      error: error?.message,
      timestamp: new Date().toISOString()
    });
  }

  // API request/response methods
  logAPIRequest(provider, method, url, data = null) {
    this.addLog('api_request', 'outgoing', {
      provider,
      method,
      url,
      data,
      timestamp: new Date().toISOString()
    });
  }

  logAPIResponse(provider, url, status, data = null, error = null) {
    this.addLog('api_response', 'received', {
      provider,
      url,
      status,
      success: !error,
      data,
      error: error?.message,
      timestamp: new Date().toISOString()
    });
  }

  // Polling methods
  logPollingStart(provider) {
    this.addLog('polling', 'started', {
      provider,
      timestamp: new Date().toISOString()
    });
  }

  logPollingResult(provider, found, messages = []) {
    this.addLog('polling', 'completed', {
      provider,
      messagesFound: found,
      messageCount: messages.length,
      messages: messages.map(msg => ({
        id: msg.id,
        from: msg.from,
        type: msg.type,
        timestamp: msg.timestamp
      })),
      timestamp: new Date().toISOString()
    });
  }

  // Connection events
  logConnection(provider, event, data = null) {
    this.addLog('connection', event, {
      provider,
      data,
      timestamp: new Date().toISOString()
    });
  }
}

// Export singleton instance
module.exports = new DebugLogger();