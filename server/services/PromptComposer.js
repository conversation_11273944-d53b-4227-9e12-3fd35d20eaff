const fs = require('fs')
const path = require('path')

/**
 * Sistema de Composição Dinâmica de Prompts
 *
 * Responsável por:
 * 1. Carregar templates modulares de prompt
 * 2. Selecionar contextos apropriados baseados no cliente
 * 3. <PERSON>mpor prompts otimizados dinamicamente
 * 4. Validar guardrails e regras
 * 5. Manter cache de templates carregados
 */
class PromptComposer {
  constructor () {
    this.promptsPath = path.join(__dirname, '../prompts')
    this.templateCache = new Map()
    this.guardrails = null
    this.loadGuardrails()
  }

  /**
   * Carrega regras de guardrails do arquivo JSON
   */
  loadGuardrails () {
    try {
      const guardrailsPath = path.join(this.promptsPath, 'guardrails/validation-rules.json')
      const guardrailsData = fs.readFileSync(guardrailsPath, 'utf8')
      this.guardrails = JSON.parse(guardrailsData)
      console.log('✅ Guardrails carregados com sucesso')
    } catch (error) {
      console.error('❌ Erro ao carregar guardrails:', error.message)
      this.guardrails = { version: 'fallback' } // Fallback básico
    }
  }

  /**
   * Extrai apenas o primeiro nome do profissional
   */
  extractFirstName (fullName) {
    if (!fullName) return 'Profissional'

    // Remove títulos comuns no início
    const cleanName = fullName.replace(/^(Sr\.|Sra\.|Dr\.|Dra\.|Prof\.|Profª\.)\s*/i, '')

    // Pega apenas o primeiro nome (primeira palavra)
    const firstName = cleanName.split(' ')[0]

    return firstName || 'Profissional'
  }

  /**
   * 🆕 Analisa preferências históricas do cliente por profissional e serviço
   * Conta frequência de agendamentos para identificar profissionais preferidos
   */
  analyzeCustomerProfessionalPreferences (customerData) {
    if (!customerData) {
      return {}
    }

    // Consolidar dados históricos de diferentes fontes
    const historicalServices = []

    // Fonte 1: ultimosServicos (formato legado)
    if (customerData.ultimosServicos && customerData.ultimosServicos.length > 0) {
      customerData.ultimosServicos.forEach(service => {
        if (service.profissional && service.servico) {
          historicalServices.push({
            professional: service.profissional,
            service: service.servico,
            date: service.data
          })
        }
      })
    }

    // Fonte 2: appointmentsData.past (formato novo)
    if (customerData.appointmentsData && customerData.appointmentsData.past && customerData.appointmentsData.past.length > 0) {
      customerData.appointmentsData.past.forEach(appointment => {
        if (appointment.profissional && appointment.servico) {
          historicalServices.push({
            professional: appointment.profissional,
            service: appointment.servico,
            date: appointment.data
          })
        }
      })
    }

    // Analisar frequências por serviço
    const servicePreferences = {}
    const professionalFrequency = {}

    historicalServices.forEach(({ professional, service }) => {
      // Normalizar nomes para comparação
      const normalizedService = service.toLowerCase().trim()
      const normalizedProfessional = this.extractFirstName(professional)

      // Contar por serviço específico
      if (!servicePreferences[normalizedService]) {
        servicePreferences[normalizedService] = {}
      }
      if (!servicePreferences[normalizedService][normalizedProfessional]) {
        servicePreferences[normalizedService][normalizedProfessional] = 0
      }
      servicePreferences[normalizedService][normalizedProfessional]++

      // Contar frequência geral de profissionais
      if (!professionalFrequency[normalizedProfessional]) {
        professionalFrequency[normalizedProfessional] = 0
      }
      professionalFrequency[normalizedProfessional]++
    })

    // Determinar profissional preferido para cada serviço
    const preferences = {}
    for (const [service, professionals] of Object.entries(servicePreferences)) {
      let maxCount = 0
      let preferredProfessional = null

      for (const [professional, count] of Object.entries(professionals)) {
        if (count > maxCount) {
          maxCount = count
          preferredProfessional = professional
        }
      }

      if (preferredProfessional && maxCount >= 1) { // Pelo menos 1 agendamento
        preferences[service] = {
          professional: preferredProfessional,
          count: maxCount,
          confidence: maxCount >= 2 ? 'high' : 'medium' // Alta confiança com 2+ agendamentos
        }
      }
    }

    console.log(`🎯 Analisadas preferências para ${Object.keys(preferences).length} tipos de serviço`)
    return preferences
  }

  /**
   * Carrega um template de prompt do sistema de arquivos com cache
   */
  loadTemplate (templatePath) {
    if (this.templateCache.has(templatePath)) {
      return this.templateCache.get(templatePath)
    }

    try {
      const fullPath = path.join(this.promptsPath, templatePath)
      const content = fs.readFileSync(fullPath, 'utf8')

      // Cache o template carregado
      this.templateCache.set(templatePath, content)
      console.log(`📄 Template carregado: ${templatePath}`)
      return content
    } catch (error) {
      console.error(`❌ Erro ao carregar template ${templatePath}:`, error.message)

      // FALLBACK ROBUSTO: Para templates críticos, retornar conteúdo básico
      if (templatePath.includes('availability-context')) {
        console.warn('🚨 Template de disponibilidade falhou - usando fallback crítico')
        return this.getAvailabilityFallback()
      }

      if (templatePath.includes('base-identity')) {
        console.warn('🚨 Template de identidade falhou - usando fallback crítico')
        return this.getIdentityFallback()
      }

      return '' // Fallback vazio para outros templates
    }
  }

  /**
   * Determina o tipo de cliente baseado no histórico
   */
  determineCustomerType (customerData) {
    if (!customerData) {
      return 'new'
    }

    let totalServices = 0

    // Verifica estrutura nova (appointmentsData.past)
    if (customerData.appointmentsData?.past) {
      totalServices = customerData.appointmentsData.past.length
    }
    // Fallback para estrutura antiga (ultimosServicos)
    else if (customerData.ultimosServicos) {
      totalServices = customerData.ultimosServicos.length
    }

    // Classificação baseada no número de serviços
    if (totalServices === 0) return 'new'
    if (totalServices >= 5) return 'frequent'
    return 'returning'
  }

  /**
   * Analisa padrões de serviços para clientes frequentes
   */
  analyzeServicePatterns (customerData) {
    if (!customerData || !customerData.appointmentsData?.past) {
      return {
        mostFrequent: { name: 'Nenhum', count: 0 },
        preferredProfessional: { name: 'Nenhum', count: 0 }
      }
    }

    const pastServices = customerData.appointmentsData.past
    const serviceCount = {}
    const professionalCount = {}

    pastServices.forEach(service => {
      // Conta serviços
      const serviceName = service.servico || 'Serviço não informado'
      serviceCount[serviceName] = (serviceCount[serviceName] || 0) + 1

      // Conta profissionais
      const professionalName = service.profissional || 'Profissional não informado'
      if (professionalName !== 'Profissional não informado') {
        professionalCount[professionalName] = (professionalCount[professionalName] || 0) + 1
      }
    })

    // Encontra mais frequentes
    const mostFrequentService = Object.keys(serviceCount).length > 0
      ? Object.keys(serviceCount).reduce((a, b) => serviceCount[a] > serviceCount[b] ? a : b)
      : 'Nenhum'

    const preferredProfessional = Object.keys(professionalCount).length > 0
      ? Object.keys(professionalCount).reduce((a, b) => professionalCount[a] > professionalCount[b] ? a : b)
      : 'Nenhum'

    return {
      mostFrequent: {
        name: mostFrequentService,
        count: serviceCount[mostFrequentService] || 0
      },
      preferredProfessional: {
        name: preferredProfessional,
        count: professionalCount[preferredProfessional] || 0
      },
      totalServices: pastServices.length
    }
  }

  /**
   * Substitui variáveis no template por valores reais
   */
  replaceTemplateVariables (template, variables) {
    let result = template

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g')
      result = result.replace(regex, value || '')
    }

    return result
  }

  /**
   * 🌎 CONTEXTO TEMPORAL E GEOGRÁFICO COMPLETO
   * Adiciona contexto rico de data, hora, região e timezone ao prompt
   */
  getCurrentDateContext () {
    const dateUtils = require('../utils/dateUtils')
    const now = dateUtils.getCurrentDateBR()

    // Obter data atual em formato brasileiro
    const today = dateUtils.getDateStringBR(now)

    // Calcular amanhã usando timezone brasileiro
    const tomorrow = new Date(now)
    tomorrow.setDate(tomorrow.getDate() + 1)
    const tomorrowStr = dateUtils.getDateStringBR(tomorrow)

    // Obter dia da semana no timezone do Brasil
    const weekdays = ['domingo', 'segunda-feira', 'terça-feira', 'quarta-feira', 'quinta-feira', 'sexta-feira', 'sábado']
    const todayWeekday = weekdays[now.getDay()]
    const tomorrowWeekday = weekdays[tomorrow.getDay()]

    // Obter horário atual no Brasil usando função correta
    const { hour: currentHour, timeString: currentTime } = dateUtils.getHourMinuteBR(now)

    // Determinar período do dia usando hora brasileira correta
    let periodOfDay = ''
    if (currentHour >= 6 && currentHour < 12) {
      periodOfDay = 'manhã'
    } else if (currentHour >= 12 && currentHour < 18) {
      periodOfDay = 'tarde'
    } else if (currentHour >= 18 && currentHour < 22) {
      periodOfDay = 'início da noite'
    } else {
      periodOfDay = 'noite'
    }

    // Contexto geográfico e cultural
    const contextString = `## 🌎 CONTEXTO TEMPORAL E GEOGRÁFICO

**LOCALIZAÇÃO E FUSO HORÁRIO:**
- Você está operando no Rio de Janeiro, Brasil
- Timezone: America/Sao_Paulo (UTC-3 ou UTC-2 durante horário de verão)
- Região: Sudeste do Brasil, cultura carioca/brasileira

**DATA E HORÁRIO ATUAIS:**
- Data de hoje: ${today} (${todayWeekday})
- Amanhã será: ${tomorrowStr} (${tomorrowWeekday})
- Horário atual: ${currentTime} (${periodOfDay})
- Este é o horário local do salão e dos clientes

**CONTEXTO CULTURAL:**
- Use expressões brasileiras naturais do Rio de Janeiro
- Considere que é ${periodOfDay}, adapte cumprimentos se necessário
- Horário comercial típico: 9h às 18h (segunda a sábado)
- Clientes falam português brasileiro com gírias cariocas

**IMPORTANTE:** Todas as referências temporais (hoje, amanhã, manhã, tarde) são relativas ao horário atual ${currentTime} no timezone Brasil/São Paulo.`

    // 🗓️ LOG CRÍTICO para debugging
    console.log('🗓️ PromptComposer: Contexto temporal/geográfico gerado:')
    console.log(`   Hoje: ${today} (${todayWeekday})`)
    console.log(`   Amanhã: ${tomorrowStr} (${tomorrowWeekday})`)
    console.log(`   Horário: ${currentTime} (${periodOfDay})`)
    console.log(`   Timestamp: ${now.toISOString()}`)

    return contextString
  }

  /**
   * Formata dados de disponibilidade para apresentação com detalhes completos
   */
  formatAvailabilityData (availabilityContext) {
    if (!availabilityContext) return ''

    const { availableSlots, professionals, professionalsCount, date, error, noAvailability, hasAvailability, specificProfessional, professionalName, formattedSlots } = availabilityContext

    if (error) {
      return `⚠️ ERRO: Não foi possível verificar disponibilidade. ${availabilityContext.errorMessage || ''}`
    }

    if (noAvailability || !hasAvailability) {
      return `❌ SEM DISPONIBILIDADE: Não há horários livres para ${date}.`
    }

    // 🎯 PROFISSIONAL ESPECÍFICO: Mostrar horários detalhados
    if (specificProfessional && professionalName && formattedSlots) {
      return `📋 AGENDA ESPECÍFICA - ${this.extractFirstName(professionalName)} em ${date}:
${formattedSlots}

🎯 INSTRUÇÕES PARA RESPOSTA:
- Use EXATAMENTE estes horários nas suas respostas
- NUNCA invente outros horários
- Responda diretamente: "${this.extractFirstName(professionalName)} está livre às ${formattedSlots}!"`
    }

    // 🏢 DISPONIBILIDADE GERAL: Múltiplos profissionais
    if (Array.isArray(professionals) && professionals.length > 0) {
      let formatted = `✅ HORÁRIOS DISPONÍVEIS CONFIRMADOS (${date}):\n`

      professionals.forEach(prof => {
        if (prof.horariosVagos && prof.horariosVagos.length > 0) {
          const firstName = this.extractFirstName(prof.nome)
          const times = prof.horariosVagos.sort()
          formatted += `👨‍💼 ${firstName}: ${times.join(', ')}\n`
        }
      })

      formatted += `\n🎯 INSTRUÇÕES PARA RESPOSTA:
- Use APENAS estes horários confirmados
- NUNCA invente outros horários  
- Organize por profissional como mostrado acima
- Destaque os horários mais populares (manhã/tarde)`

      return formatted
    }

    // 📋 FORMATO LEGACY: Array de slots
    if (Array.isArray(availableSlots)) {
      // Agrupa slots por profissional
      const slotsByProfessional = {}
      availableSlots.forEach(slot => {
        const profName = this.extractFirstName(slot.professional?.nome || 'Profissional')
        if (!slotsByProfessional[profName]) {
          slotsByProfessional[profName] = []
        }
        slotsByProfessional[profName].push(slot.time)
      })

      let formatted = `✅ DISPONÍVEIS (${professionalsCount || Object.keys(slotsByProfessional).length} profissionais):\n`
      Object.keys(slotsByProfessional).forEach(profName => {
        const times = slotsByProfessional[profName].sort()
        formatted += `👨‍💼 ${profName}: ${times.join(', ')}\n`
      })

      formatted += '\n🎯 Use APENAS estes horários nas suas respostas!'
      return formatted
    }

    // Fallback para formato de string
    return availableSlots || 'Consulte nossa agenda'
  }

  /**
   * Compõe o prompt final baseado no contexto do cliente e conversa
   */
  async composePrompt (customerData, conversation, availabilityContext, establishmentInfo, appointmentResult = null, cancellationResult = null) {
    console.log('🔧 Iniciando composição de prompt...')

    // 1. Carrega identidade base e adiciona contexto de data
    const baseIdentityTemplate = this.loadTemplate('system/base-identity.md')
    const baseIdentity = this.replaceTemplateVariables(baseIdentityTemplate, {
      current_date_context: this.getCurrentDateContext()
    })

    // 1.5. Adicionar seção de serviços por profissional (NOVA FUNCIONALIDADE + PREFERÊNCIAS HISTÓRICAS)
    const professionalServicesSection = await this.buildProfessionalServicesSection(conversation, establishmentInfo, customerData)

    // 2. Determina tipo de cliente e carrega template apropriado
    const customerType = this.determineCustomerType(customerData)
    console.log(`👤 Tipo de cliente detectado: ${customerType}`)

    let customerTemplate = ''
    const templateVariables = {}

    switch (customerType) {
      case 'new':
        customerTemplate = this.loadTemplate('context-templates/customer-new.md')
        templateVariables.customer_name = customerData?.nome || 'Cliente'
        templateVariables.customer_gender = customerData?.genero || 'Não informado'
        break

      case 'returning':
        customerTemplate = this.loadTemplate('context-templates/customer-returning.md')
        const totalServices = customerData?.appointmentsData?.past?.length || customerData?.ultimosServicos?.length || 0
        const lastService = customerData?.appointmentsData?.past?.[0] || customerData?.ultimosServicos?.[0]

        templateVariables.customer_name = customerData?.nome || 'Cliente'
        templateVariables.customer_gender = customerData?.genero || 'Não informado'
        templateVariables.total_services = totalServices
        templateVariables.last_service = lastService?.servico || 'Serviço anterior'
        templateVariables.last_professional = lastService?.profissional || 'Profissional'
        templateVariables.service_history = `${totalServices} serviços realizados`
        break

      case 'frequent':
        customerTemplate = this.loadTemplate('context-templates/customer-frequent.md')
        const patterns = this.analyzeServicePatterns(customerData)

        templateVariables.customer_name = customerData?.nome || 'Cliente'
        templateVariables.customer_gender = customerData?.genero || 'Não informado'
        templateVariables.total_services = patterns.totalServices
        templateVariables.most_frequent_service = patterns.mostFrequent.name
        templateVariables.service_count = patterns.mostFrequent.count
        templateVariables.preferred_professional = this.extractFirstName(patterns.preferredProfessional.name)
        templateVariables.professional_count = patterns.preferredProfessional.count
        templateVariables.service_patterns = `Prefere ${patterns.mostFrequent.name} com ${this.extractFirstName(patterns.preferredProfessional.name)}`
        break
    }

    // 3. Adiciona contexto de disponibilidade se necessário
    let availabilityTemplate = ''
    if (availabilityContext) {
      // NOVO: Controle inteligente de envio de disponibilidade
      const contextType = availabilityContext.contextType || 'full'

      if (contextType === 'reference') {
        console.log('📋 Usando contexto de disponibilidade por referência (economizando tokens)')
        availabilityTemplate = this.createReferenceAvailabilityTemplate(availabilityContext)
      } else {
        availabilityTemplate = this.loadTemplate('context-templates/availability-context.md')
        templateVariables.target_date = availabilityContext.date || 'Data não especificada'
        templateVariables.available_professionals_count = availabilityContext.professionalsCount || 0
        templateVariables.availability_status = availabilityContext.error ? 'error' : 'available'
        templateVariables.hasAvailability = availabilityContext.hasAvailability

        // NOVO: Suporte para profissional específico
        if (availabilityContext.specificProfessional) {
          templateVariables.specific_professional = true
          templateVariables.professional_name = this.extractFirstName(availabilityContext.professionalName)
          templateVariables.formatted_slots = availabilityContext.formattedSlots || 'Nenhum horário disponível'
          templateVariables.formatted_availability = `PROFISSIONAL ESPECÍFICO: ${this.extractFirstName(availabilityContext.professionalName)}\nHorários: ${availabilityContext.formattedSlots}`
        } else {
          templateVariables.specific_professional = false
          templateVariables.formatted_availability = this.formatAvailabilityData(availabilityContext)
        }

        // FALLBACK CRÍTICO: Se template vazio, força informações de disponibilidade
        if (!availabilityTemplate.trim()) {
          console.warn('🚨 Template de disponibilidade vazio - forçando informações críticas')
          availabilityTemplate = this.createAvailabilityFallbackTemplate(availabilityContext)
        }
      }

      // 🔍 DEBUG: Rastrear envio de disponibilidade para IA
      if (availabilityContext) {
        console.log('📊 DEBUG DISPONIBILIDADE PARA IA:')
        console.log(`   Context Type: ${availabilityContext.contextType}`)
        console.log(`   Must Use Data: ${availabilityContext.mustUseData || false}`)
        console.log(`   Has Availability: ${availabilityContext.hasAvailability}`)
        console.log(`   Professional: ${availabilityContext.professionalName || 'N/A'}`)
        console.log(`   Date: ${availabilityContext.date}`)
        console.log(`   Slots Count: ${availabilityContext.availableSlots?.length || 0}`)
        console.log(`   Template Size: ${availabilityTemplate.length} chars`)
      }
    }

    // 3.5 CORREÇÃO CRÍTICA: Sempre incluir dados coletados E identificação do cliente
    let schedulingDataTemplate = ''

    // 🔥 CORREÇÃO: Identificar se cliente já está identificado para não pedir telefone/nome novamente
    const clienteIdentificado = !!(customerData && (customerData.nome || customerData.telefone))
    const nomeCliente = customerData?.nome || 'Cliente'
    const telefoneCliente = customerData?.telefone || null

    // Dados de agendamento coletados
    const schedulingData = conversation?.schedulingData
    const hasSchedulingData = schedulingData && (schedulingData.professional?.name || schedulingData.service?.name || schedulingData.date || schedulingData.time)

    // Se há dados coletados OU cliente identificado, incluir no prompt
    if (hasSchedulingData || clienteIdentificado) {
      console.log('📋 CRÍTICO: Incluindo contexto completo no prompt:', {
        clienteIdentificado,
        nomeCliente,
        hasTelefone: !!telefoneCliente,
        dadosAgendamento: hasSchedulingData
          ? {
              professional: schedulingData.professional?.name,
              service: schedulingData.service?.name,
              date: schedulingData.date,
              time: schedulingData.time
            }
          : 'nenhum'
      })

      schedulingDataTemplate = `

## 📋 DADOS JÁ COLETADOS NESTA CONVERSA

${clienteIdentificado ? `**CLIENTE IDENTIFICADO**: ${nomeCliente}${telefoneCliente ? ` (${telefoneCliente})` : ''}` : ''}

${hasSchedulingData
? `Cliente já forneceu as seguintes informações:
${schedulingData.professional?.name ? `• **Profissional escolhido**: ${schedulingData.professional.name}` : ''}
${schedulingData.service?.name ? `• **Serviço desejado**: ${schedulingData.service.name}${schedulingData.service.price ? ` (R$${schedulingData.service.price})` : ''}` : ''}
${schedulingData.date ? `• **Data preferida**: ${schedulingData.date}` : ''}
${schedulingData.time ? `• **Horário preferido**: ${schedulingData.time}` : ''}`
: ''}

**IMPORTANTE**: Use essas informações para contextualizar sua resposta. Se o cliente já escolheu algo, não pergunte novamente.
${clienteIdentificado ? `**CRÍTICO**: Cliente JÁ está identificado como ${nomeCliente}. NUNCA pergunte nome ou telefone novamente!` : ''}
`
    }

    // 4. Verifica incompatibilidades
    let compatibilityTemplate = ''
    if (conversation?.context?.incompatibleProfessional) {
      compatibilityTemplate = this.loadTemplate('context-templates/compatibility-issues.md')
      templateVariables.requested_service = conversation.context.selectedService || 'Serviço solicitado'
      templateVariables.requested_professional = conversation.context.incompatibleProfessional
      templateVariables.incompatibility_reason = conversation.context.incompatibilityReason || 'Especialidade diferente'

      const alternatives = conversation.context.suggestedAlternatives || []
      templateVariables.alternative_professionals = alternatives.map(p => p.nome).join(', ')
      templateVariables.alternative_professionals_formatted = alternatives.map(p => `• ${p.nome}`).join('\\n')
    }

    // 5. Aplica variáveis nos templates
    const processedCustomerTemplate = this.replaceTemplateVariables(customerTemplate, templateVariables)
    const processedAvailabilityTemplate = this.replaceTemplateVariables(availabilityTemplate, templateVariables)
    const processedCompatibilityTemplate = this.replaceTemplateVariables(compatibilityTemplate, templateVariables)

    // 5.5. Adiciona contexto de agendamentos futuros e finalizados hoje do cliente
    let futureAppointmentsTemplate = ''
    let todayCompletedAppointmentsTemplate = ''

    if (conversation?.futureAppointmentsContext) {
      const futureContext = conversation.futureAppointmentsContext

      // Contexto de agendamentos realmente futuros
      if (futureContext.hasAppointments) {
        const appointmentsList = futureContext.appointments.map(apt => {
          const date = new Date(apt.dataHora)
          const dateStr = date.toLocaleDateString('pt-BR')
          const timeStr = date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
          return `• ${dateStr} às ${timeStr} - ${apt.servico} com ${apt.profissional} (ID: ${apt.id})`
        }).join('\n')

        futureAppointmentsTemplate = `
## 📅 PRÓXIMOS AGENDAMENTOS
O cliente tem ${futureContext.count} agendamento(s) confirmado(s):
${appointmentsList}

**IMPORTANTE:** 
- Se cliente quer agendar algo novo, informe sobre os existentes
- Para remarcar, pergunte se quer cancelar algum dos existentes primeiro
- Para cancelar, use os IDs dos agendamentos acima
`
      } else {
        futureAppointmentsTemplate = `
## 📅 PRÓXIMOS AGENDAMENTOS
O cliente NÃO tem agendamentos futuros.

**IMPORTANTE:** 
- Cliente está livre para novos agendamentos
- Não há conflitos de horário
- Pode sugerir qualquer horário disponível
`
      }

      // Novo contexto de agendamentos finalizados hoje
      if (futureContext.todayCompleted?.hasAppointments) {
        const completedList = futureContext.todayCompleted.appointments.map(apt => {
          const date = new Date(apt.dataHora)
          const timeStr = date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })
          return `• ${apt.servico} com ${apt.profissional} às ${timeStr}`
        }).join('\n')

        todayCompletedAppointmentsTemplate = `
## 📅 ATENDIMENTOS REALIZADOS HOJE
O cliente teve ${futureContext.todayCompleted.count} atendimento(s) hoje:
${completedList}
`
      }
    }

    // 6. Adiciona informações do estabelecimento
    const establishmentContext = `
## 📍 INFORMAÇÕES DO ESTABELECIMENTO
Nome: ${establishmentInfo?.establishmentName || 'Salão Trinks'}
Horário: ${establishmentInfo?.workingHours || '9h às 18h, Segunda a Sábado'}
`

    // 6. Adiciona informações sobre resultado do agendamento (se disponível)
    let appointmentContext = ''
    if (appointmentResult) {
      if (appointmentResult.success) {
        const schedulingData = conversation?.schedulingData
        const customerName = customerData?.nome || 'Cliente'
        const serviceInfo = schedulingData ? `${schedulingData.service.name} - R$${schedulingData.service.price}` : 'Serviço'
        const professionalName = this.extractFirstName(schedulingData?.professional?.name)
        const appointmentDate = schedulingData?.date || 'Data'
        const appointmentTime = schedulingData?.time || 'Horário'

        appointmentContext = `
## ✅ AGENDAMENTO CRIADO COM SUCESSO
- ID do agendamento: ${appointmentResult.appointmentId}
- Status: CONFIRMADO
- Agendamento foi criado automaticamente na API Trinks
${appointmentResult.customerCreated ? '- Cliente novo foi cadastrado automaticamente' : '- Cliente já existia no sistema'}
- Cliente: ${customerName}
- Profissional: ${professionalName} 
- Serviço: ${serviceInfo}
- Data: ${appointmentDate}
- Horário: ${appointmentTime}

**IMPORTANTE:** Confirme o agendamento para o cliente usando as informações reais acima. Use o ID do agendamento na resposta.
**RESPOSTA OBRIGATÓRIA:** Deve incluir todas as informações do agendamento e o ID ${appointmentResult.appointmentId}.`
      } else {
        appointmentContext = `
## ❌ FALHA NA CRIAÇÃO DO AGENDAMENTO
- Erro: ${appointmentResult.error}
- Status: NÃO CONFIRMADO
- Agendamento NÃO foi criado na API Trinks

**IMPORTANTE:** Informe o cliente sobre o problema e peça para tentar novamente ou ajustar os dados.
**RESPOSTA OBRIGATÓRIA:** Explique o problema e peça para o cliente tentar novamente ou ajustar as informações.`
      }
    }

    // 7. Adiciona informações sobre resultado do cancelamento (se disponível)
    let cancellationContext = ''
    if (cancellationResult) {
      if (cancellationResult.success) {
        const cancelledAppointment = cancellationResult.cancelledAppointment
        const customerName = customerData?.nome || 'Cliente'
        const appointmentDate = new Date(cancelledAppointment.dataHoraInicio).toLocaleDateString('pt-BR')
        const appointmentTime = new Date(cancelledAppointment.dataHoraInicio).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' })

        cancellationContext = `
## ✅ AGENDAMENTO CANCELADO COM SUCESSO
- ID do agendamento cancelado: ${cancellationResult.cancelledAppointmentId}
- Status: CANCELADO
- Cancelamento foi processado na API Trinks
- Cliente: ${customerName}
- Profissional: ${cancelledAppointment.profissionalNome}
- Serviço: ${cancelledAppointment.servicoNome}
- Data: ${appointmentDate}
- Horário: ${appointmentTime}

**IMPORTANTE:** Confirme o cancelamento para o cliente usando as informações reais acima.
**RESPOSTA OBRIGATÓRIA:** Deve confirmar que o agendamento foi cancelado com sucesso e incluir o ID ${cancellationResult.cancelledAppointmentId}.`
      } else if (cancellationResult.noAppointments) {
        cancellationContext = `
## ❌ NENHUM AGENDAMENTO PARA CANCELAR
- Status: SEM AGENDAMENTOS FUTUROS
- O cliente não possui agendamentos futuros para cancelar

**IMPORTANTE:** Informe o cliente que ele não possui agendamentos futuros para cancelar.
**RESPOSTA OBRIGATÓRIA:** Explique que não há agendamentos futuros e pergunte se deseja fazer um novo agendamento.`
      } else {
        cancellationContext = `
## ❌ FALHA NO CANCELAMENTO
- Erro: ${cancellationResult.error}
- Status: NÃO CANCELADO
- O cancelamento NÃO foi processado na API Trinks

**IMPORTANTE:** Informe o cliente sobre o problema e peça para tentar novamente.
**RESPOSTA OBRIGATÓRIA:** Explique o problema e peça para o cliente tentar novamente ou entre em contato diretamente.`
      }
    }

    // 8. Monta prompt final
    const finalPrompt = [
      baseIdentity,
      processedCustomerTemplate,
      schedulingDataTemplate, // CORREÇÃO: Incluir dados já coletados
      processedAvailabilityTemplate,
      processedCompatibilityTemplate,
      todayCompletedAppointmentsTemplate, // NOVO: Agendamentos finalizados hoje
      futureAppointmentsTemplate,
      establishmentContext,
      professionalServicesSection, // NOVA SEÇÃO: Serviços por profissional
      appointmentContext,
      cancellationContext,
      '## 🎯 INSTRUÇÕES DE REFERÊNCIA TEMPORAL',
      '',
      '**Para agendamentos que já ocorreram hoje:**',
      '- Se acabou de terminar (menos de 1h): "Você acabou de sair daqui!", "Vi que você acabou de fazer..."',
      '- Se foi há algumas horas: "Como foi o [serviço] de hoje?", "Gostou do resultado?"',
      '- Demonstre que sabe que o cliente esteve no salão hoje',
      '',
      '**Para próximos agendamentos:**',
      '- Se é em breve (próxima hora): "Tá chegando a hora!", "Daqui a pouquinho você tem..."',
      '- Se é amanhã: "Te vejo amanhã para...", "Amanhã você tem..."',
      '- Se é mais distante: Mencione normalmente a data',
      '',
      '**IMPORTANTE:** Use seu julgamento para decidir quando e como mencionar. Seja natural!',
      '',
      '## 🎯 INSTRUÇÕES FINAIS',
      '',
      '🚨 **VERIFICAÇÃO OBRIGATÓRIA ANTES DE RESPONDER:**',
      '1. **SERVIÇO**: Se cliente menciona serviço, verifique QUAIS profissionais fazem esse serviço no mapeamento acima',
      '2. **PROFISSIONAL**: Só sugira profissionais que REALMENTE fazem o serviço solicitado',
      '3. **DISPONIBILIDADE**: Use apenas horários que estão explicitamente fornecidos nos dados',
      '4. **VALIDAÇÃO**: NUNCA sugira Ágata para MASSAGEM QUICK (ela só faz BARBA 15!)',
      '',
      '✅ **COMPORTAMENTO CORRETO:**',
      '- Responda de forma natural e brasileira',
      '- Use no máximo 2-3 linhas como WhatsApp',
      '- JAMAIS invente horários, preços ou informações',
      '- NUNCA sugira ações sem ter integração real com API',
      '- SEMPRE verifique agendamentos futuros antes de sugerir novos',
      '- SÓ confirme cancelamentos após sucesso na API Trinks',
      '- Mantenha tom caloroso e profissional',
      '',
      '❌ **ERROS CRÍTICOS A EVITAR:**',
      '- Sugerir profissional que NÃO faz o serviço solicitado',
      '- Ignorar dados de mapeamento fornecidos',
      '- Inventar horários não fornecidos nos dados'
    ].filter(section => section.trim().length > 0).join('\\n\\n')

    const tokenCount = Math.ceil(finalPrompt.length / 4) // Aproximação: 4 chars = 1 token
    console.log(`📊 Prompt composto: ${tokenCount} tokens aproximados`)

    if (tokenCount > 2000) {
      console.warn('⚠️ Prompt muito longo, considere otimização')
    }

    return finalPrompt
  }

  /**
   * Valida resposta contra guardrails definidos
   */
  validateResponse (response) {
    if (!this.guardrails || !response) return { valid: true, warnings: [] }

    const warnings = []
    const forbidden = this.guardrails.forbidden_phrases

    // Verifica frases proibidas
    if (forbidden) {
      Object.keys(forbidden.categories).forEach(category => {
        const phrases = forbidden.categories[category]
        phrases.forEach(phrase => {
          if (response.toLowerCase().includes(phrase.toLowerCase())) {
            warnings.push(`Frase proibida detectada (${category}): "${phrase}"`)
          }
        })
      })
    }

    // Verifica comprimento da resposta
    const lines = response.split('\\n').length
    const maxLines = this.guardrails.response_limits?.max_lines || 5

    if (lines > maxLines) {
      warnings.push(`Resposta muito longa: ${lines} linhas (máximo: ${maxLines})`)
    }

    return {
      valid: warnings.length === 0,
      warnings
    }
  }

  /**
   * Limpa cache de templates (útil para desenvolvimento)
   */
  clearCache () {
    this.templateCache.clear()
    console.log('🧹 Cache de templates limpo')
  }

  /**
   * Fallback crítico para disponibilidade quando template falha
   */
  getAvailabilityFallback () {
    return `## 📅 INFORMAÇÕES DE DISPONIBILIDADE (FALLBACK)

**REGRA CRÍTICA:** NUNCA diga "vou verificar" ou "aguarde". 
Sempre responda com dados que possui AGORA.

{{#specific_professional}}
**{{professional_name}}** em {{target_date}}:
{{#hasAvailability}}
✅ DISPONÍVEL: {{formatted_slots}}
Resposta: "{{professional_name}} está livre em {{formatted_slots}}!"
{{/hasAvailability}}
{{^hasAvailability}}
❌ OCUPADO: Sem horários livres
Resposta: "{{professional_name}} não tem horário livre em {{target_date}}"
{{/hasAvailability}}
{{/specific_professional}}

{{^specific_professional}}
**DISPONIBILIDADE GERAL:**
{{formatted_availability}}
{{/specific_professional}}`
  }

  /**
   * Constrói seção de serviços por profissional para prompt + preferências históricas
   */
  async buildProfessionalServicesSection (conversation, establishmentInfo, customerData = null) {
    try {
      console.log('🗺️ Usando novo sistema de mapeamento profissional → serviços...')

      // 1. Buscar mapeamento completo de profissionais → serviços
      const professionalServicesMapping = require('../utils/professionalServicesMapping')
      const basicPromptText = await professionalServicesMapping.generatePromptText()

      // 2. 🆕 NOVA FUNCIONALIDADE: Analisar preferências históricas do cliente
      const preferences = this.analyzeCustomerProfessionalPreferences(customerData)
      let preferencesSection = '\n## 🎯 PREFERÊNCIAS HISTÓRICAS DO CLIENTE\n\n'

      if (Object.keys(preferences).length > 0) {
        preferencesSection += '**CLIENTE TEM HISTÓRICO**: Baseado em agendamentos anteriores, este cliente tem preferência por:\n\n'

        for (const [service, data] of Object.entries(preferences)) {
          const confidenceEmoji = data.confidence === 'high' ? '🔥' : '⭐'
          preferencesSection += `• **${service.toUpperCase()}**: ${confidenceEmoji} **${data.professional}** (${data.count} vez${data.count > 1 ? 'es' : ''})\n`
        }

        preferencesSection += '\n**🚨 INSTRUÇÃO CRÍTICA**: Ao sugerir profissionais para serviços, SEMPRE PRIORIZE o profissional preferido do cliente!\n'
        preferencesSection += 'EXEMPLO: "Opa! Barba de novo? O João que você sempre vai tá livre às 15h! R$30 como sempre. Quer com ele? 😊"\n\n'
      } else {
        preferencesSection += '**CLIENTE NOVO**: Sem preferências históricas definidas.\n'
        preferencesSection += 'INSTRUÇÃO: Sugira todos profissionais disponíveis de forma amigável e deixe cliente escolher.\n'
        preferencesSection += 'EXEMPLO: "Opa! Barba? Temos o João e Jailson que são demais! João R$30, Jailson R$50. Qual prefere? 🔥"\n\n'
      }

      // 3. Combinar mapeamento básico + preferências históricas
      const combinedPromptText = basicPromptText + preferencesSection

      console.log(`🎯 Seção de preferências gerada: ${Object.keys(preferences).length} serviços com preferência`)
      return combinedPromptText
    } catch (error) {
      console.error('❌ Erro ao construir seção de mapeamento profissional → serviços:', error.message)

      // Fallback: usar sistema antigo se novo falhar
      try {
        console.log('🔄 Tentando fallback para sistema antigo...')

        // Verificar se há profissional específico sendo considerado
        const selectedProfessional = conversation?.context?.selectedProfessional
        const selectedProfessionalId = conversation?.context?.selectedProfessionalId

        if (selectedProfessional && selectedProfessionalId) {
          console.log(`🔍 Buscando serviços específicos do profissional: ${selectedProfessional} (ID: ${selectedProfessionalId})`)

          const trinksService = require('./trinks')
          const professionalServicesResult = await trinksService.getProfessionalServices(selectedProfessionalId)

          if (professionalServicesResult.success && professionalServicesResult.services.length > 0) {
            const services = professionalServicesResult.services
            const servicesFormatted = services.map(service =>
              `• ${service.nome} (${service.duracaoEmMinutos}min) - R$${service.preco}`
            ).join('\n')

            return `
## 👨‍💼 SERVIÇOS ESPECÍFICOS DO PROFISSIONAL (FALLBACK)

**${selectedProfessional}** pode realizar:
${servicesFormatted}

**IMPORTANTE**: Ofereça APENAS os serviços listados acima para ${selectedProfessional}.
**JAMAIS** sugira serviços que não estão nesta lista.
`
          }
        }

        // Fallback completo: Seção geral de estabelecimento
        return `
## 💇‍♀️ SERVIÇOS DISPONÍVEIS NO SALÃO (FALLBACK)

${establishmentInfo?.servicesInfo || 'Serviços variados disponíveis'}

**PROFISSIONAIS DISPONÍVEIS**: ${establishmentInfo?.professionals || 'Equipe qualificada'}

**DICA**: Quando cliente mencionar profissional específico, ofereça apenas os serviços que ele pode realizar.
`
      } catch (fallbackError) {
        console.error('❌ Erro também no sistema de fallback:', fallbackError.message)

        // Fallback básico final em caso de erro total
        return `
## 📍 INFORMAÇÕES BÁSICAS DO ESTABELECIMENTO (FALLBACK)

**NOTA**: Sistema de mapeamento detalhado temporariamente indisponível.
`
      }
    }
  }

  /**
   * Fallback crítico para identidade quando template falha
   */
  getIdentityFallback () {
    return `## 👋 ASSISTENTE DO SALÃO (FALLBACK)

Você é uma recepcionista brasileira calorosa de salão de beleza.

**REGRAS FUNDAMENTAIS:**
- NUNCA diga: "vou verificar", "aguarde", "deixe-me consultar"
- SEMPRE responda diretamente com dados disponíveis
- Use linguagem brasileira: "E aí!", "Que massa!", "Show!"
- Máximo 2-3 linhas por resposta
- Tom caloroso mas profissional

**Data atual:** {{current_date_context}}`
  }

  /**
   * Cria template de disponibilidade inline quando necessário
   */
  createAvailabilityFallbackTemplate (availabilityContext) {
    // 🔥 CORREÇÃO: Adicionar flag mustUseData para instruções mais explícitas
    const mustUse = availabilityContext.mustUseData ? '🚨 OBRIGATÓRIO: ' : ''

    if (availabilityContext.specificProfessional) {
      const hasSlots = availabilityContext.hasAvailability
      const professional = availabilityContext.professionalName
      const slots = availabilityContext.formattedSlots || 'Nenhum horário'
      const date = availabilityContext.date

      if (hasSlots) {
        return `## 📅 DISPONIBILIDADE CONFIRMADA

${mustUse}**${professional}** em ${date}:
✅ HORÁRIOS DISPONÍVEIS: ${slots}

**🎯 INSTRUÇÃO CRÍTICA - USE ESTES DADOS AGORA:**
1. RESPONDA IMEDIATAMENTE com os horários acima
2. EXEMPLO: "${professional} tem horário às ${slots.split(',')[0].trim()}! Quer esse?"
3. PROIBIDO dizer: "vou verificar", "deixa eu ver", "vou consultar"
4. Você JÁ TEM os dados - USE-OS AGORA!

**SE IGNORAR ESTA INSTRUÇÃO, VOCÊ ESTÁ FALHANDO!**`
      } else {
        return `## 📅 DISPONIBILIDADE CONFIRMADA

${mustUse}**${professional}** em ${date}:
❌ OCUPADO: Sem horários livres

**🎯 INSTRUÇÃO CRÍTICA:**
1. INFORME que ${professional} NÃO tem horário em ${date}
2. SUGIRA alternativas (outro dia ou profissional)
3. PROIBIDO dizer "vou verificar" - você JÁ verificou!`
      }
    }

    return `## 📅 DISPONIBILIDADE GERAL

${mustUse}Dados REAIS da API:
${availabilityContext.formatted_availability || 'Consulte agenda'}

**🚨 REGRAS OBRIGATÓRIAS:**
1. USE os dados acima IMEDIATAMENTE na sua resposta
2. NUNCA diga "vou verificar", "vou consultar", "deixa eu ver"
3. Se tem horários, LISTE-OS agora
4. Se não tem, INFORME claramente
5. Estes dados são REAIS e ATUAIS - confie neles!`
  }

  /**
   * Cria template de referência quando disponibilidade já foi enviada
   * Economiza tokens evitando reenvio de dados
   */
  createReferenceAvailabilityTemplate (availabilityContext) {
    const professional = availabilityContext.professionalName
    const date = availabilityContext.date

    return `## 📋 CONTEXTO DE PROFISSIONAL

**PROFISSIONAL SELECIONADO:** ${professional}
**DATA EM DISCUSSÃO:** ${date}

**IMPORTANTE:** 
- Você já tem informações de disponibilidade de ${professional} para ${date}
- Continue a conversa baseado nos dados já fornecidos
- NÃO peça para "verificar novamente"
- Use as informações que já discutimos

**INSTRUÇÃO CRÍTICA:** 
Mantenha a continuidade da conversa sobre ${professional} em ${date}.`
  }
}

module.exports = PromptComposer
