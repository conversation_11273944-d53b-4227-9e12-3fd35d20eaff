const aiService = require('./ai');
const messageLogger = require('../utils/messageLogger');

class MoodAnalysisService {
  constructor() {
    this.conversationMoodHistory = new Map();
    this.moodCategories = {
      VERY_POSITIVE: { 
        score: 0.8, 
        label: 'Super Feliz', 
        escalate: false,
        trinksDescription: 'Empreendedor radiante com a parceria Trinks!',
        supportLevel: 'manter_energia_positiva',
        responseStyle: 'celebrar_conquista'
      },
      POSITIVE: { 
        score: 0.4, 
        label: 'Contente', 
        escalate: false,
        trinksDescription: 'Empreendedor satisfeito e colaborativo',
        supportLevel: 'aproveitar_momentum',
        responseStyle: 'reforcar_parceria'
      },
      NEUTRAL: { 
        score: 0.0, 
        label: 'Tranquilo', 
        escalate: false,
        trinksDescription: 'Empreendedor buscando informações para crescer',
        supportLevel: 'orientacao_clara',
        responseStyle: 'ser_prestativa'
      },
      FRUSTRATED: { 
        score: -0.4, 
        label: 'Preocupado', 
        escalate: true,
        trinksDescription: 'Empreendedor precisando de mais suporte da Trinks',
        supportLevel: 'atencao_redobrada',
        responseStyle: 'acolher_e_resolver'
      },
      ANGRY: { 
        score: -0.8, 
        label: 'Chateado', 
        escalate: true,
        trinksDescription: 'Empreendedor com insatisfação - vamos resolver juntos',
        supportLevel: 'intervencao_rapida',
        responseStyle: 'empatia_e_acao'
      },
      VERY_ANGRY: { 
        score: -1.0, 
        label: 'Muito Chateado', 
        escalate: true,
        trinksDescription: 'Empreendedor necessitando atenção especial da Trinks',
        supportLevel: 'escalacao_imediata',
        responseStyle: 'cuidado_especial'
      }
    };
  }

  /**
   * Analyzes the mood of a customer message
   * @param {string} message - Customer message text
   * @param {string} customerPhone - Customer phone number
   * @param {Array} conversationHistory - Previous messages for context
   * @returns {Promise<Object>} Mood analysis result
   */
  async analyzeMessageMood(message, customerPhone, conversationHistory = []) {
    try {
      console.log(`🎭 A Trinks está analisando o humor do empreendedor ${customerPhone}: "${message.substring(0, 100)}..."`);
      
      // Get conversation context for better analysis
      const recentMessages = conversationHistory.slice(-5).map(msg => ({
        sender: msg.sender,
        content: msg.content,
        timestamp: msg.timestamp
      }));
      
      const moodAnalysisPrompt = `Você é a especialista em análise emocional da Trinks, parceira de gestão e tecnologia para negócios de beleza e bem-estar. Analise o humor/emoção do empreendedor cliente baseado em sua mensagem e contexto da conversa.

CONTEXTO TRINKS: A Trinks transforma sonhos de empreendedores em negócios de sucesso. Nossa missão é "dar um up" no negócio deles.

MENSAGEM ATUAL DO EMPREENDEDOR:
"${message}"

CONTEXTO DA CONVERSA (últimas 5 mensagens):
${recentMessages.map(msg => `${msg.sender}: ${msg.content}`).join('\n')}

CATEGORIAS DE HUMOR (adequadas ao universo de beleza e bem-estar):
1. VERY_POSITIVE - Empreendedor super feliz, satisfeito, elogiando a Trinks (score: 0.8)
2. POSITIVE - Empreendedor contente, colaborativo, confiante (score: 0.4)  
3. NEUTRAL - Empreendedor tranquilo, buscando informações, neutro (score: 0.0)
4. FRUSTRATED - Empreendedor preocupado, confuso, precisando de mais suporte (score: -0.4)
5. ANGRY - Empreendedor chateado, reclamando, insatisfeito com algo (score: -0.8)
6. VERY_ANGRY - Empreendedor muito chateado, frustrado com a experiência (score: -1.0)

INDICADORES EMOCIONAIS A CONSIDERAR:
- Palavras de frustração: "demora", "problema", "erro", "não funciona"
- Palavras de raiva: "péssimo", "ridículo", "absurdo", "inaceitável"  
- Linguagem urgente: "URGENTE", "IMEDIATAMENTE", "JÁ", múltiplas exclamações
- Linguagem positiva: "obrigado", "perfeito", "ótimo", "adorei"
- Linguagem neutra: perguntas diretas, informações factuais
- Caps lock excessivo, múltiplos pontos de exclamação/interrogação
- Tom de cobrança vs pedido de ajuda
- Escalação de tensão vs resolução

ANÁLISE REQUERIDA:
Retorne EXATAMENTE neste formato JSON:
{
  "moodCategory": "CATEGORIA_ESCOLHIDA",
  "moodScore": número_entre_-1_e_1,
  "confidence": número_entre_0_e_1,
  "emotionalIndicators": ["indicador1", "indicador2"],
  "escalationRecommended": true/false,
  "reasoning": "breve explicação da análise",
  "conversationTrend": "improving/declining/stable"
}

Seja preciso e considere o contexto brasileiro de atendimento ao cliente.`;

      const startTime = Date.now();
      const claudeResponse = await aiService.anthropic.messages.create({
        model: "claude-sonnet-4-20250514",
        max_tokens: 400,
        temperature: 0.2, // Lower temperature for more consistent analysis
        messages: [{
          role: 'user',
          content: moodAnalysisPrompt
        }]
      });
      const duration = Date.now() - startTime;

      // Parse AI response with proper error handling
      if (!claudeResponse.content || !claudeResponse.content[0] || typeof claudeResponse.content[0].text !== 'string') {
        console.error('❌ Invalid Claude API response structure in moodAnalysis:', JSON.stringify(claudeResponse, null, 2));
        throw new Error('Invalid Claude API response structure: missing content[0].text');
      }
      
      const responseText = claudeResponse.content[0].text.trim();
      
      // Log da chamada da IA
      messageLogger.logAIInteraction(
        'moodAnalysis',
        moodAnalysisPrompt,
        responseText,
        duration,
        'claude-sonnet-4-20250514'
      );
      
      console.log(`⏱️ Mood analysis took ${duration}ms`);
      let moodAnalysis;
      
      try {
        // Try to extract JSON from the response
        const jsonMatch = responseText.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          moodAnalysis = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (parseError) {
        console.error('❌ Failed to parse mood analysis JSON:', parseError.message);
        console.error('Raw response:', responseText);
        
        // Fallback analysis
        moodAnalysis = this.fallbackMoodAnalysis(message);
      }

      // Validate and clean the analysis
      moodAnalysis = this.validateMoodAnalysis(moodAnalysis);
      
      // Store in conversation mood history
      this.updateConversationMoodHistory(customerPhone, moodAnalysis, message);
      
      console.log(`🎭 Mood Analysis Result:`, {
        category: moodAnalysis.moodCategory,
        score: moodAnalysis.moodScore,
        confidence: moodAnalysis.confidence,
        escalate: moodAnalysis.escalationRecommended,
        trend: moodAnalysis.conversationTrend
      });

      return {
        ...moodAnalysis,
        analysisTime: analysisTime,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      console.error('❌ Error in mood analysis:', error.message);
      
      // Return neutral fallback
      return this.fallbackMoodAnalysis(message);
    }
  }

  /**
   * Provides fallback mood analysis when AI analysis fails
   */
  fallbackMoodAnalysis(message) {
    const text = message.toLowerCase();
    
    // Simple keyword-based fallback
    const angryWords = ['péssimo', 'ridículo', 'absurdo', 'inaceitável', 'horrível'];
    const frustratedWords = ['demora', 'problema', 'erro', 'não funciona', 'dificuldade'];
    const positiveWords = ['obrigado', 'obrigada', 'perfeito', 'ótimo', 'adorei', 'excelente'];
    
    let score = 0;
    let category = 'NEUTRAL';
    let escalate = false;
    
    if (angryWords.some(word => text.includes(word)) || text.includes('!!!') || /[A-Z]{3,}/.test(message)) {
      score = -0.8;
      category = 'ANGRY';
      escalate = true;
    } else if (frustratedWords.some(word => text.includes(word))) {
      score = -0.4;
      category = 'FRUSTRATED';
      escalate = true;
    } else if (positiveWords.some(word => text.includes(word))) {
      score = 0.4;
      category = 'POSITIVE';
    }
    
    return {
      moodCategory: category,
      moodScore: score,
      confidence: 0.6, // Lower confidence for fallback
      emotionalIndicators: ['fallback_analysis'],
      escalationRecommended: escalate,
      reasoning: 'Análise baseada em palavras-chave (fallback)',
      conversationTrend: 'stable',
      fallback: true
    };
  }

  /**
   * Validates and cleans mood analysis result
   */
  validateMoodAnalysis(analysis) {
    const validCategories = Object.keys(this.moodCategories);
    
    return {
      moodCategory: validCategories.includes(analysis.moodCategory) ? analysis.moodCategory : 'NEUTRAL',
      moodScore: Math.max(-1, Math.min(1, parseFloat(analysis.moodScore) || 0)),
      confidence: Math.max(0, Math.min(1, parseFloat(analysis.confidence) || 0.5)),
      emotionalIndicators: Array.isArray(analysis.emotionalIndicators) ? analysis.emotionalIndicators : [],
      escalationRecommended: Boolean(analysis.escalationRecommended),
      reasoning: analysis.reasoning || 'No reasoning provided',
      conversationTrend: ['improving', 'declining', 'stable'].includes(analysis.conversationTrend) ? 
        analysis.conversationTrend : 'stable'
    };
  }

  /**
   * Updates mood history for a conversation
   */
  updateConversationMoodHistory(customerPhone, moodAnalysis, message) {
    if (!this.conversationMoodHistory.has(customerPhone)) {
      this.conversationMoodHistory.set(customerPhone, {
        history: [],
        currentMood: null,
        trends: {
          avgScore: 0,
          volatility: 0,
          escalationCount: 0
        }
      });
    }

    const conversationMood = this.conversationMoodHistory.get(customerPhone);
    
    // Add to history
    conversationMood.history.push({
      timestamp: new Date(),
      message: message.substring(0, 100), // First 100 chars for context
      ...moodAnalysis
    });
    
    // Keep only last 10 mood entries
    if (conversationMood.history.length > 10) {
      conversationMood.history = conversationMood.history.slice(-10);
    }
    
    // Update current mood
    conversationMood.currentMood = moodAnalysis;
    
    // Calculate trends
    this.updateMoodTrends(conversationMood);
    
    this.conversationMoodHistory.set(customerPhone, conversationMood);
  }

  /**
   * Updates mood trends for analytics
   */
  updateMoodTrends(conversationMood) {
    const history = conversationMood.history;
    if (history.length === 0) return;
    
    // Calculate average mood score
    const avgScore = history.reduce((sum, entry) => sum + entry.moodScore, 0) / history.length;
    
    // Calculate mood volatility (standard deviation)
    const variance = history.reduce((sum, entry) => sum + Math.pow(entry.moodScore - avgScore, 2), 0) / history.length;
    const volatility = Math.sqrt(variance);
    
    // Count escalation recommendations
    const escalationCount = history.filter(entry => entry.escalationRecommended).length;
    
    conversationMood.trends = {
      avgScore: Math.round(avgScore * 100) / 100,
      volatility: Math.round(volatility * 100) / 100,
      escalationCount,
      totalEntries: history.length
    };
  }

  /**
   * Gets current mood state for a customer
   */
  getCurrentMood(customerPhone) {
    const conversationMood = this.conversationMoodHistory.get(customerPhone);
    return conversationMood ? conversationMood.currentMood : null;
  }

  /**
   * Gets complete mood history for a customer
   */
  getMoodHistory(customerPhone) {
    return this.conversationMoodHistory.get(customerPhone) || null;
  }

  /**
   * Checks if conversation should be escalated based on mood patterns
   */
  shouldEscalateConversation(customerPhone) {
    const conversationMood = this.conversationMoodHistory.get(customerPhone);
    if (!conversationMood) return false;
    
    const { currentMood, trends, history } = conversationMood;
    
    // Immediate escalation triggers
    if (currentMood && currentMood.escalationRecommended) {
      console.log(`🤝 A Trinks identifica que o empreendedor ${customerPhone} precisa de atenção especial: ${currentMood.moodCategory}`);
      return {
        escalate: true,
        reason: 'empreendedor_precisa_atencao_especial',
        moodCategory: currentMood.moodCategory,
        confidence: currentMood.confidence,
        trinksMessage: 'Vamos dar atenção especial a este empreendedor - parceria em ação!',
        supportAction: 'Hora de mostrar o que a Trinks faz de melhor: cuidar do empreendedor',
        urgencyLevel: 'imediata',
        partnershipFocus: true
      };
    }
    
    // Pattern-based escalation
    if (history.length >= 3) {
      const recentNegative = history.slice(-3).every(entry => entry.moodScore < -0.2);
      if (recentNegative) {
        console.log(`🤝 A Trinks percebe que o empreendedor ${customerPhone} precisa de mais suporte: padrão de preocupação`);
        return {
          escalate: true,
          reason: 'padrao_preocupacao_continuada',
          avgScore: trends.avgScore,
          volatility: trends.volatility,
          trinksMessage: 'Este empreendedor está passando por um momento difícil - vamos acompanhar de perto',
          supportAction: 'Hora da Trinks mostrar que é parceira de verdade',
          urgencyLevel: 'alta',
          partnershipFocus: true
        };
      }
    }
    
    // High volatility escalation
    if (trends.volatility > 0.6 && trends.escalationCount >= 2) {
      console.log(`🤝 A Trinks identifica que o empreendedor ${customerPhone} precisa de estabilidade: oscilações emocionais`);
      return {
        escalate: true,
        reason: 'instabilidade_emocional_alta',
        volatility: trends.volatility,
        escalationCount: trends.escalationCount,
        trinksMessage: 'Empreendedor passando por altos e baixos - vamos oferecer suporte mais próximo',
        supportAction: 'Momento de a Trinks ser a âncora que este empreendedor precisa',
        urgencyLevel: 'alta',
        partnershipFocus: true
      };
    }
    
    return { escalate: false };
  }

  /**
   * Gets mood-appropriate response adjustments
   */
  getMoodBasedResponseAdjustments(customerPhone) {
    const currentMood = this.getCurrentMood(customerPhone);
    if (!currentMood) return null;
    
    const adjustments = {
      moodCategory: currentMood.moodCategory,
      suggestions: []
    };
    
    switch (currentMood.moodCategory) {
      case 'VERY_ANGRY':
      case 'ANGRY':
        adjustments.suggestions = [
          'Use a linguagem acolhedora da Trinks: "A gente entende sua preocupação e está aqui para resolver"',
          'Reconheça imediatamente: "Vamos dar um jeito nessa situação juntos"', 
          'Ofereça soluções práticas para o negócio de beleza/bem-estar',
          'Evite explicações técnicas - foque na solução',
          'Tom parceiro: "A Trinks está com você, vem que a gente resolve!"',
          'Demonstre urgência genuína: "Sua satisfação é nossa prioridade"'
        ];
        adjustments.responseStyle = 'acolhedor_solucionador';
        adjustments.trinksEssence = 'Hora de mostrar que somos parceiros de verdade';
        break;
        
      case 'FRUSTRATED':
        adjustments.suggestions = [
          'Demonstre parceria: "A Trinks está do seu lado para superar isso"',
          'Ofereça orientação clara: "Vamos te guiar passo a passo"',
          'Use tom próximo: "Vamos dar um up nessa situação do seu negócio?"',
          'Mantenha expectativas realistas mas otimistas',
          'Reconheça o desafio: "Sabemos que gerir negócio de beleza tem seus perrengues"'
        ];
        adjustments.responseStyle = 'parceiro_prestativo';
        adjustments.trinksEssence = 'Parceria que resolve e orienta';
        break;
        
      case 'POSITIVE':
      case 'VERY_POSITIVE':
        adjustments.suggestions = [
          'Celebre junto: "Que alegria ver seu negócio prosperando!"',
          'Reforce o crescimento: "A Trinks adora ver empreendedor realizado!"',
          'Ofereça mais valor: "Que tal mais um up no seu negócio?"',
          'Use expressões Trinks: "Isso que é dar um up no negócio!"',
          'Aproveite o momento: "Agora é hora de potencializar ainda mais!"'
        ];
        adjustments.responseStyle = 'entusiasmado_parceiro';
        adjustments.trinksEssence = 'Celebrando o sucesso do empreendedor';
        break;
        
      default:
        adjustments.suggestions = [
          'Mantenha o tom profissional e próximo da Trinks',
          'Seja clara: "A Trinks está aqui para te ajudar"',
          'Demonstre parceria: "Juntos vamos fazer seu negócio crescer"',
          'Foque no crescimento do negócio de beleza/bem-estar',
          'Use linguagem do universo: "Vamos dar aquele up no seu estabelecimento?"'
        ];
        adjustments.responseStyle = 'profissional_proximo';
        adjustments.trinksEssence = 'Profissional, prestativa e parceira';
    }
    
    return adjustments;
  }

  /**
   * Gets analytics summary for mood tracking
   */
  getMoodAnalytics() {
    const analytics = {
      totalCustomers: this.conversationMoodHistory.size,
      moodDistribution: {},
      escalationRate: 0,
      averageVolatility: 0,
      timestamp: new Date().toISOString()
    };
    
    let totalEscalations = 0;
    let totalVolatility = 0;
    let totalCustomers = 0;
    
    // Initialize mood distribution
    Object.keys(this.moodCategories).forEach(category => {
      analytics.moodDistribution[category] = 0;
    });
    
    this.conversationMoodHistory.forEach((conversationMood, customerPhone) => {
      totalCustomers++;
      
      if (conversationMood.currentMood) {
        analytics.moodDistribution[conversationMood.currentMood.moodCategory]++;
      }
      
      totalEscalations += conversationMood.trends.escalationCount;
      totalVolatility += conversationMood.trends.volatility;
    });
    
    analytics.escalationRate = totalCustomers > 0 ? Math.round((totalEscalations / totalCustomers) * 100) / 100 : 0;
    analytics.averageVolatility = totalCustomers > 0 ? Math.round((totalVolatility / totalCustomers) * 100) / 100 : 0;
    
    return analytics;
  }

  /**
   * Clears mood history for a customer (privacy/GDPR compliance)
   */
  clearCustomerMoodData(customerPhone) {
    const deleted = this.conversationMoodHistory.delete(customerPhone);
    console.log(`🗑️ Cleared mood data for ${customerPhone}: ${deleted ? 'success' : 'not found'}`);
    return deleted;
  }
}

module.exports = new MoodAnalysisService();