const { guardrailsManager } = require('../core/guardrails');

class GuardrailsMiddleware {
  constructor() {
    this.enabled = true;
    this.logViolations = true;
  }

  async processResponse(response, context = {}) {
    if (!this.enabled) {
      return {
        success: true,
        response: response,
        bypassed: true
      };
    }

    try {
      const result = await guardrailsManager.checkResponse(response, context);
      
      return {
        success: true,
        ...result,
        originalResponse: response,
        processed: true
      };
    } catch (error) {
      console.error('Error in guardrails middleware:', error);
      
      // En caso de erro, permitir a resposta original como fallback
      return {
        success: true,
        response: response,
        error: error.message,
        bypassed: true
      };
    }
  }

  // Middleware para Express
  express() {
    return async (req, res, next) => {
      // Interceptar respostas da IA
      const originalSend = res.send;
      
      res.send = async function(data) {
        if (typeof data === 'object' && data.response) {
          const context = {
            customerPhone: req.body?.customerPhone,
            agent: req.body?.agent,
            intent: req.body?.intent,
            userAgent: req.get('User-Agent'),
            ip: req.ip
          };
          
          const result = await guardrailsManager.checkResponse(data.response, context);
          
          if (!result.allowed) {
            data.response = result.suggestedResponse || 'Desculpe, não posso ajudar com isso.';
            data.guardrailsBlocked = true;
            data.violationReason = result.reason;
          } else if (result.modified) {
            data.response = result.response;
            data.guardrailsModified = true;
          }
          
          if (result.violations && result.violations.length > 0) {
            data.guardrailsViolations = result.violations.length;
          }
        }
        
        return originalSend.call(this, data);
      };
      
      next();
    };
  }

  // Middleware para WebSocket/Socket.io
  socketIO() {
    return (socket, next) => {
      const originalEmit = socket.emit;
      
      socket.emit = async function(event, data, ...args) {
        if (event === 'ai_response' && data && data.response) {
          const context = {
            socketId: socket.id,
            customerPhone: data.customerPhone,
            agent: data.agent,
            intent: data.intent
          };
          
          const result = await guardrailsManager.checkResponse(data.response, context);
          
          if (!result.allowed) {
            data.response = result.suggestedResponse || 'Desculpe, não posso ajudar com isso.';
            data.guardrailsBlocked = true;
            data.violationReason = result.reason;
          } else if (result.modified) {
            data.response = result.response;
            data.guardrailsModified = true;
          }
          
          if (result.violations && result.violations.length > 0) {
            data.guardrailsViolations = result.violations.length;
          }
        }
        
        return originalEmit.call(this, event, data, ...args);
      };
      
      next();
    };
  }

  // Método para uso direto com agentes
  async checkAgentResponse(agent, response, context = {}) {
    const enhancedContext = {
      ...context,
      agent: agent.name,
      agentType: agent.constructor.name,
      timestamp: new Date()
    };

    const result = await this.processResponse(response, enhancedContext);
    
    if (!result.allowed) {
      // Log the blocked response for review
      console.warn(`Response blocked by guardrails for agent ${agent.name}:`, {
        original: response,
        reason: result.reason,
        violations: result.violations
      });
    }

    return result;
  }

  enable() {
    this.enabled = true;
    console.log('Guardrails middleware enabled');
  }

  disable() {
    this.enabled = false;
    console.log('Guardrails middleware disabled');
  }

  getStatus() {
    return {
      enabled: this.enabled,
      logViolations: this.logViolations,
      guardrailsStats: guardrailsManager.getStatistics()
    };
  }
}

// Create singleton instance
const guardrailsMiddleware = new GuardrailsMiddleware();

module.exports = {
  GuardrailsMiddleware,
  guardrailsMiddleware
};