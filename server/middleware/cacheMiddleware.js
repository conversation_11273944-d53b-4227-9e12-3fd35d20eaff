const { aiResponseCache, embeddingCache, promptCache } = require('../core/cache');

class CacheMiddleware {
  constructor() {
    this.enabled = true;
    this.useResponseCache = true;
    this.useEmbeddingCache = true;
    this.usePromptCache = true;
    this.stats = {
      responseCacheHits: 0,
      responseCacheMisses: 0,
      embeddingCacheHits: 0,
      embeddingCacheMisses: 0,
      promptCacheHits: 0,
      promptCacheMisses: 0
    };
  }

  async cacheAIResponse(input, context, responseGenerator) {
    if (!this.enabled || !this.useResponseCache) {
      const response = await responseGenerator();
      this.stats.responseCacheMisses++;
      return { ...response, fromCache: false };
    }

    try {
      const result = await aiResponseCache.cacheAIResponse(input, context, responseGenerator);
      
      if (result.fromCache) {
        this.stats.responseCacheHits++;
      } else {
        this.stats.responseCacheMisses++;
      }
      
      return result;
    } catch (error) {
      console.error('Error in AI response cache:', error);
      // Fallback to direct generation
      const response = await responseGenerator();
      this.stats.responseCacheMisses++;
      return { ...response, fromCache: false, cacheError: error.message };
    }
  }

  async cacheEmbedding(text, model, embeddingGenerator) {
    if (!this.enabled || !this.useEmbeddingCache) {
      const embedding = await embeddingGenerator();
      this.stats.embeddingCacheMisses++;
      return { embedding, fromCache: false };
    }

    try {
      const result = await embeddingCache.cacheEmbedding(text, model, embeddingGenerator);
      
      if (result.fromCache) {
        this.stats.embeddingCacheHits++;
      } else {
        this.stats.embeddingCacheMisses++;
      }
      
      return result;
    } catch (error) {
      console.error('Error in embedding cache:', error);
      // Fallback to direct generation
      const embedding = await embeddingGenerator();
      this.stats.embeddingCacheMisses++;
      return { embedding, fromCache: false, cacheError: error.message };
    }
  }

  async cachePrompt(promptKey, variables, promptGenerator) {
    if (!this.enabled || !this.usePromptCache) {
      const prompt = await promptGenerator();
      this.stats.promptCacheMisses++;
      return { prompt, fromCache: false };
    }

    try {
      const cacheKey = promptCache.generateKey(promptKey, variables);
      
      let cachedPrompt = await promptCache.get(cacheKey);
      if (cachedPrompt) {
        this.stats.promptCacheHits++;
        return { prompt: cachedPrompt.prompt, fromCache: true };
      }
      
      const prompt = await promptGenerator();
      
      await promptCache.set(cacheKey, {
        prompt,
        promptKey,
        variables,
        timestamp: new Date()
      });
      
      this.stats.promptCacheMisses++;
      return { prompt, fromCache: false };
    } catch (error) {
      console.error('Error in prompt cache:', error);
      // Fallback to direct generation
      const prompt = await promptGenerator();
      this.stats.promptCacheMisses++;
      return { prompt, fromCache: false, cacheError: error.message };
    }
  }

  // Wrapper for agent execution with caching
  async wrapAgentExecution(agent, input, context, originalExecute) {
    if (!this.enabled) {
      return await originalExecute.call(agent, input, context);
    }

    const cacheContext = {
      agent: agent.name,
      systemPrompt: agent.systemPrompt,
      model: context.model,
      temperature: context.temperature,
      maxTokens: context.maxTokens
    };

    try {
      const result = await this.cacheAIResponse(
        input,
        cacheContext,
        async () => await originalExecute.call(agent, input, context)
      );

      // Add cache info to the response
      const response = result.response || result;
      if (typeof response === 'object') {
        response.cache = {
          fromCache: result.fromCache,
          cacheKey: result.cacheKey
        };
      }

      return response;
    } catch (error) {
      console.error('Error in agent execution cache wrapper:', error);
      // Fallback to direct execution
      return await originalExecute.call(agent, input, context);
    }
  }

  // Invalidation methods
  invalidateAgent(agentName) {
    const responseInvalidated = aiResponseCache.invalidateByAgent(agentName);
    console.log(`Invalidated ${responseInvalidated} cache entries for agent ${agentName}`);
    return responseInvalidated;
  }

  invalidateOldResponses(hours = 24) {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    const invalidated = aiResponseCache.invalidateOlderThan(cutoff);
    console.log(`Invalidated ${invalidated} cache entries older than ${hours} hours`);
    return invalidated;
  }

  invalidateAllResponses() {
    aiResponseCache.clear();
    console.log('All AI response cache cleared');
  }

  invalidateAllEmbeddings() {
    embeddingCache.clear();
    console.log('All embedding cache cleared');
  }

  invalidateAllPrompts() {
    promptCache.clear();
    console.log('All prompt cache cleared');
  }

  // Configuration methods
  enable() {
    this.enabled = true;
    console.log('Cache middleware enabled');
  }

  disable() {
    this.enabled = false;
    console.log('Cache middleware disabled');
  }

  enableResponseCache() {
    this.useResponseCache = true;
  }

  disableResponseCache() {
    this.useResponseCache = false;
  }

  enableEmbeddingCache() {
    this.useEmbeddingCache = true;
  }

  disableEmbeddingCache() {
    this.useEmbeddingCache = false;
  }

  enablePromptCache() {
    this.usePromptCache = true;
  }

  disablePromptCache() {
    this.usePromptCache = false;
  }

  // Statistics and monitoring
  getStatistics() {
    const responseStats = aiResponseCache.getStatistics();
    const embeddingStats = embeddingCache.getStatistics();
    const promptStats = promptCache.getStatistics();

    const totalResponseRequests = this.stats.responseCacheHits + this.stats.responseCacheMisses;
    const totalEmbeddingRequests = this.stats.embeddingCacheHits + this.stats.embeddingCacheMisses;
    const totalPromptRequests = this.stats.promptCacheHits + this.stats.promptCacheMisses;

    return {
      enabled: this.enabled,
      responseCache: {
        enabled: this.useResponseCache,
        hits: this.stats.responseCacheHits,
        misses: this.stats.responseCacheMisses,
        hitRate: totalResponseRequests > 0 ? this.stats.responseCacheHits / totalResponseRequests : 0,
        size: responseStats.size,
        memoryUsage: responseStats.memoryUsageFormatted
      },
      embeddingCache: {
        enabled: this.useEmbeddingCache,
        hits: this.stats.embeddingCacheHits,
        misses: this.stats.embeddingCacheMisses,
        hitRate: totalEmbeddingRequests > 0 ? this.stats.embeddingCacheHits / totalEmbeddingRequests : 0,
        size: embeddingStats.size,
        memoryUsage: embeddingStats.memoryUsageFormatted
      },
      promptCache: {
        enabled: this.usePromptCache,
        hits: this.stats.promptCacheHits,
        misses: this.stats.promptCacheMisses,
        hitRate: totalPromptRequests > 0 ? this.stats.promptCacheHits / totalPromptRequests : 0,
        size: promptStats.size,
        memoryUsage: promptStats.memoryUsageFormatted
      },
      overall: {
        totalRequests: totalResponseRequests + totalEmbeddingRequests + totalPromptRequests,
        totalHits: this.stats.responseCacheHits + this.stats.embeddingCacheHits + this.stats.promptCacheHits,
        totalMisses: this.stats.responseCacheMisses + this.stats.embeddingCacheMisses + this.stats.promptCacheMisses
      }
    };
  }

  calculateCostSavings(costPerAICall = 0.01, costPerEmbedding = 0.001) {
    const responseSavings = this.stats.responseCacheHits * costPerAICall;
    const embeddingSavings = this.stats.embeddingCacheHits * costPerEmbedding;
    
    return {
      responseCacheSavings: responseSavings,
      embeddingCacheSavings: embeddingSavings,
      totalSavings: responseSavings + embeddingSavings,
      currency: 'USD'
    };
  }

  // Express middleware
  express() {
    return (req, res, next) => {
      // Add cache methods to request object
      req.cache = {
        ai: (input, context, generator) => this.cacheAIResponse(input, context, generator),
        embedding: (text, model, generator) => this.cacheEmbedding(text, model, generator),
        prompt: (key, variables, generator) => this.cachePrompt(key, variables, generator)
      };
      
      next();
    };
  }

  reset() {
    this.stats = {
      responseCacheHits: 0,
      responseCacheMisses: 0,
      embeddingCacheHits: 0,
      embeddingCacheMisses: 0,
      promptCacheHits: 0,
      promptCacheMisses: 0
    };
  }
}

// Create singleton instance
const cacheMiddleware = new CacheMiddleware();

module.exports = {
  CacheMiddleware,
  cacheMiddleware
};