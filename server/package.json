{"name": "trinks-ia-server", "version": "1.0.0", "description": "Backend for Trinks IA scheduling system POC", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "dev:whatsapp": "WHATSAPP_REAL=true nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "test:fernando": "node test-fernando-humanized-flow.js", "test:humanization": "node test-fernando-humanized-flow.js", "test:integration": "node tests/integration/test-runner.js", "test:integration:new-customer": "node tests/integration/test-runner.js --scenario=new-customer", "test:integration:existing-customer": "node tests/integration/test-runner.js --scenario=existing-customer", "test:integration:cancellation": "node tests/integration/test-runner.js --scenario=cancellation", "test:integration:availability": "node tests/integration/test-runner.js --scenario=availability", "test:integration:html": "node tests/integration/test-runner.js --report=html", "test:integration:verbose": "node tests/integration/test-runner.js --verbose", "test:integration:critical": "node tests/integration/test-runner.js --critical-only", "test:integration:performance": "node tests/integration/test-runner.js --performance-only", "test:integration:repeat": "node tests/integration/test-runner.js --repeat=5", "test:performance": "node test-timezone-performance.js", "test:all": "npm run test:performance && npm run test:fernando", "test:setup": "echo 'Setting up integration tests...' && mkdir -p tests/integration/reports/{html,json}", "test:config-check": "node -e \"console.log('✅ Environment Check:'); console.log('ANTHROPIC_API_KEY:', process.env.ANTHROPIC_API_KEY ? 'SET' : 'MISSING'); console.log('TRINKS_API_KEY:', process.env.TRINKS_API_KEY ? 'SET' : 'MISSING'); console.log('TRINKS_ESTABLISHMENT_ID:', process.env.TRINKS_ESTABLISHMENT_ID || 'MISSING');\"", "validate:humanization": "node -e \"const validator = require('./tests/integration/validators/humanization.js'); console.log('✅ Humanization validator loaded successfully');\""}, "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@langchain/core": "^0.3.19", "@langchain/langgraph": "^0.0.34", "@supabase/supabase-js": "^2.56.0", "axios": "^1.11.0", "axios-rate-limit": "^1.4.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "openai": "^5.11.0", "pg": "^8.16.3", "qrcode": "^1.5.4", "qrcode-terminal": "^0.12.0", "rimraf": "^6.0.1", "socket.io": "^4.7.2", "twilio": "^5.8.0", "uuid": "^9.0.0", "whatsapp-web.js": "^1.31.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "zod": "^3.22.4"}, "devDependencies": {"chai": "^4.3.10", "jest": "^29.7.0", "nodemon": "^3.0.1", "sinon": "^17.0.1"}, "keywords": ["ai", "scheduling", "whatsapp", "trinks"], "author": "Trinks IA Team", "license": "MIT"}