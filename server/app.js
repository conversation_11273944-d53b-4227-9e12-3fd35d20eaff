const express = require('express');
const cors = require('cors');
const path = require('path');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');
require('dotenv').config();

// CRITICAL: Set Brazil timezone for all date operations
process.env.TZ = 'America/Sao_Paulo';
console.log(`🌎 Timezone configured: ${process.env.TZ} (Current time: ${new Date().toLocaleString('pt-BR')})`);

// Import refactored components
const { logger, apiLogger } = require('./utils/logger');
const SocketStateMachineAdapter = require('./adapters/SocketStateMachineAdapter');

const app = express();
const server = createServer(app);

// Initialize Socket.io with optimized connection management
const io = new Server(server, {
  cors: {
    origin: [
      "http://localhost:3000", 
      "http://localhost:3002",
      "http://localhost:3001"
    ],
    methods: ["GET", "POST"],
    credentials: true
  },
  // Configurações otimizadas para reduzir conexões órfãs
  transports: ['websocket'], // Apenas websocket para conexões mais estáveis
  pingTimeout: 20000, // 20s - timeout mais rápido
  pingInterval: 10000, // 10s - ping mais frequente
  connectTimeout: 20000, // 20s - conexão mais rápida
  maxHttpBufferSize: 1e6, // 1MB buffer máximo
  allowEIO3: false, // Desabilitar compatibilidade antiga
  
  // Configurações específicas para desenvolvimento
  ...(process.env.NODE_ENV === 'development' && {
    pingTimeout: 15000, // 15s em dev
    pingInterval: 5000,  // 5s em dev
    connectTimeout: 15000, // 15s em dev
    // Limite de conexões por IP em desenvolvimento
    perMessageDeflate: false, // Desabilitar compressão para reduzir overhead
    httpCompression: false
  }),
  
  // Limitar reconexões automáticas
  connectionStateRecovery: {
    maxDisconnectionDuration: 2 * 60 * 1000, // 2 minutos
    skipMiddlewares: true,
  }
});

const PORT = process.env.PORT || 3001;

// Security middleware
app.use(helmet({
  crossOriginEmbedderPolicy: false,
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "ws:", "wss:"]
    }
  }
}));

app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: [
    "http://localhost:3000", 
    "http://localhost:3002",
    "http://localhost:3001"
  ],
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Make Socket.io instance available to routes
app.set('io', io);

// Request logging middleware
app.use((req, res, next) => {
  const start = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - start;
    const logMessage = `${req.method} ${req.originalUrl} ${res.statusCode} - ${duration}ms - ${req.ip}`;
    
    if (res.statusCode >= 400) {
      apiLogger.error(logMessage);
    } else {
      apiLogger.info(logMessage);
    }
  });
  
  next();
});

// Static files
app.use(express.static(path.join(__dirname, 'public')));

// Initialize Socket adapter for state machine integration
const socketAdapter = new SocketStateMachineAdapter();
socketAdapter.initialize(io);

// API Routes
app.use('/api/conversation', require('./routes/conversation'));
app.use('/api/prompts', require('./routes/prompts'));
app.use('/api', require('./routes/trinks')); // Legacy Trinks API routes
app.use('/api/monitoring', require('./routes/monitoring'));
app.use('/api/rag', require('./routes/rag'));

// Legacy API routes (for backward compatibility)
app.use('/api', require('./routes/api'));

// Health check
app.get('/health', (req, res) => {
  const metrics = socketAdapter.getMetrics();
  const activeSessions = socketAdapter.getActiveSessions();
  
  res.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0.0',
    architecture: 'refactored',
    activeSessions: activeSessions.totalSessions,
    metrics: {
      totalConversations: metrics.totalConversations,
      averageResponseTime: metrics.averageResponseTime
    }
  });
});

// Admin endpoints for socket management
app.get('/api/admin/sessions', (req, res) => {
  try {
    const sessions = socketAdapter.getActiveSessions();
    res.json({
      success: true,
      data: sessions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

app.post('/api/admin/inject-message', async (req, res) => {
  try {
    const { sessionId, message, fromAdmin = true } = req.body;
    
    if (!sessionId || !message) {
      return res.status(400).json({
        success: false,
        error: 'Session ID and message are required'
      });
    }

    const result = await socketAdapter.injectMessage(sessionId, message, fromAdmin);
    
    res.json({
      success: true,
      data: result
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Metrics endpoint
app.get('/api/metrics', (req, res) => {
  try {
    const stateMachineMetrics = socketAdapter.getMetrics();
    const activeSessions = socketAdapter.getActiveSessions();
    
    res.json({
      conversations: {
        total: stateMachineMetrics.totalConversations,
        active: activeSessions.totalSessions,
        sessions: activeSessions.sessions.length
      },
      averageResponseTime: stateMachineMetrics.averageResponseTime,
      intentClassifications: stateMachineMetrics.intentClassifications,
      errorRate: stateMachineMetrics.errorRate,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// WebSocket connection info endpoint
app.get('/api/websocket/info', (req, res) => {
  res.json({
    endpoint: `ws://localhost:${PORT}`,
    transports: ['websocket', 'polling'],
    events: {
      client: [
        'customer_start_conversation',
        'customer_send_message', 
        'customer_send_audio'
      ],
      server: [
        'conversation_started',
        'salon_response',
        'salon_typing',
        'error'
      ]
    }
  });
});

// Legacy webhook endpoints (maintain compatibility)
app.post('/webhook/waha', async (req, res) => {
  try {
    logger.info('Legacy WAHA webhook received');
    
    // Import legacy handler
    const whatsappService = require('./services/whatsapp');
    await whatsappService.handleWebhook(req.body);
    
    res.status(200).json({ success: true });
  } catch (error) {
    logger.error('Legacy webhook error:', error);
    res.status(500).json({ error: error.message });
  }
});

// Audio serving endpoint
app.get('/api/audio/:audioId', async (req, res) => {
  try {
    const { audioId } = req.params;
    
    // This could be enhanced to work with the new storage system
    res.status(404).json({ error: 'Audio endpoint needs implementation' });
  } catch (error) {
    res.status(500).json({ error: 'Internal server error' });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  logger.error('Unhandled error:', error);
  
  res.status(500).json({
    success: false,
    error: process.env.NODE_ENV === 'production' 
      ? 'Internal server error' 
      : error.message
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Endpoint not found'
  });
});

// Sistema otimizado de limpeza de conexões
const CONNECTION_CLEANUP_INTERVAL = process.env.NODE_ENV === 'development' ? 30000 : 60000; // 30s dev, 1min prod
const INACTIVE_TIMEOUT = 30 * 60 * 1000; // 30 minutos de inatividade
const MAX_CONNECTIONS_WARNING = process.env.NODE_ENV === 'development' ? 20 : 100;

// Rastrear última atividade de cada socket
const socketActivity = new Map();

// Store references for debug endpoints
app.set('socketActivity', socketActivity);

setInterval(() => {
  const connectedClients = io.engine.clientsCount;
  const connectedSockets = io.sockets.sockets.size;
  const currentTime = Date.now();
  
  let removedCount = 0;
  let inactiveCount = 0;
  
  // Limpar conexões desconectadas e inativas
  io.sockets.sockets.forEach((socket) => {
    const lastActivity = socketActivity.get(socket.id) || currentTime;
    const isInactive = currentTime - lastActivity > INACTIVE_TIMEOUT;
    
    if (!socket.connected) {
      console.log(`🗑️ Removendo socket desconectado: ${socket.id}`);
      socket.disconnect(true);
      socketActivity.delete(socket.id);
      removedCount++;
    } else if (isInactive) {
      // Remover sockets inativos tanto em dev quanto em produção
      const isDevelopment = process.env.NODE_ENV === 'development';
      const shouldRemoveInactive = isDevelopment || (currentTime - lastActivity) > (2 * 60 * 60 * 1000); // 2h em prod
      
      if (shouldRemoveInactive) {
        console.log(`⏰ Removendo socket inativo (${Math.round((currentTime - lastActivity) / 60000)}min): ${socket.id}`);
        socket.disconnect(true);
        socketActivity.delete(socket.id);
        inactiveCount++;
      }
    }
  });
  
  // Log apenas quando necessário
  const shouldLog = removedCount > 0 || inactiveCount > 0 || connectedClients > MAX_CONNECTIONS_WARNING;
  
  if (shouldLog) {
    console.log(`🧹 Limpeza: ${connectedClients} clients, ${connectedSockets} sockets${removedCount > 0 ? `, removidos: ${removedCount}` : ''}${inactiveCount > 0 ? `, inativos: ${inactiveCount}` : ''}`);
  }
  
  // Alerta para muitas conexões
  if (connectedClients > MAX_CONNECTIONS_WARNING) {
    console.log(`⚠️ Muitas conexões detectadas: ${connectedClients} (limite recomendado: ${MAX_CONNECTIONS_WARNING})`);
  }
}, CONNECTION_CLEANUP_INTERVAL);

// Rastrear atividade do socket
io.on('connection', (socket) => {
  socketActivity.set(socket.id, Date.now());
  
  // Atualizar atividade em qualquer evento
  const updateActivity = () => socketActivity.set(socket.id, Date.now());
  
  socket.onAny(updateActivity);
  
  socket.on('disconnect', () => {
    socketActivity.delete(socket.id);
  });
});

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  server.close(() => {
    logger.info('Server closed');
    process.exit(0);
  });
});

/**
 * Executa diagnósticos completos de conectividade durante a inicialização
 */
async function runConnectivityDiagnostics() {
  console.log('\n🔍 ===== INICIANDO DIAGNÓSTICOS DE CONECTIVIDADE =====');
  
  const dns = require('dns').promises;
  const axios = require('axios');
  const { errorLogger, ERROR_CATEGORIES } = require('./utils/ErrorLogger');
  
  const diagnostics = {
    dns: { status: 'PENDING', details: null },
    apiConnection: { status: 'PENDING', details: null },
    apiAuthentication: { status: 'PENDING', details: null },
    systemInfo: {},
    timing: { start: Date.now(), end: null }
  };

  try {
    // 1. DIAGNÓSTICO DE DNS
    console.log('🌐 Testando resolução DNS para api.trinks.com...');
    try {
      const dnsResults = await dns.lookup('api.trinks.com');
      diagnostics.dns.status = 'SUCCESS';
      diagnostics.dns.details = {
        address: dnsResults.address,
        family: dnsResults.family,
        resolvedAt: new Date().toISOString()
      };
      console.log(`✅ DNS OK: api.trinks.com → ${dnsResults.address} (IPv${dnsResults.family})`);
    } catch (dnsError) {
      diagnostics.dns.status = 'FAILED';
      diagnostics.dns.details = {
        error: dnsError.message,
        code: dnsError.code,
        errno: dnsError.errno,
        syscall: dnsError.syscall
      };
      console.log(`❌ DNS FALHOU: ${dnsError.message} (${dnsError.code})`);
      
      // Log detalhado do erro DNS
      await errorLogger.logError(dnsError, ERROR_CATEGORIES.SYSTEM_ERROR, {
        diagnostic: 'DNS_RESOLUTION',
        hostname: 'api.trinks.com',
        phase: 'STARTUP_DIAGNOSTICS'
      });
    }

    // 2. TESTE DE CONECTIVIDADE BÁSICA
    console.log('🔌 Testando conectividade básica com API Trinks...');
    try {
      const connectivityTest = axios.create({
        timeout: 5000,
        validateStatus: () => true // Aceitar qualquer status code
      });

      const connectStart = Date.now();
      const response = await connectivityTest.get('https://api.trinks.com');
      const connectDuration = Date.now() - connectStart;

      diagnostics.apiConnection.status = 'SUCCESS';
      diagnostics.apiConnection.details = {
        status: response.status,
        statusText: response.statusText,
        headers: {
          'content-type': response.headers['content-type'],
          'server': response.headers.server,
          'connection': response.headers.connection
        },
        duration: connectDuration,
        connectedAt: new Date().toISOString()
      };

      console.log(`✅ CONECTIVIDADE OK: HTTP ${response.status} em ${connectDuration}ms`);
    } catch (connectError) {
      diagnostics.apiConnection.status = 'FAILED';
      diagnostics.apiConnection.details = {
        error: connectError.message,
        code: connectError.code,
        errno: connectError.errno,
        syscall: connectError.syscall,
        hostname: connectError.hostname
      };
      console.log(`❌ CONECTIVIDADE FALHOU: ${connectError.message}`);

      // Log detalhado do erro de conectividade
      await errorLogger.logError(connectError, ERROR_CATEGORIES.API_ERROR, {
        diagnostic: 'API_CONNECTIVITY',
        url: 'https://api.trinks.com',
        phase: 'STARTUP_DIAGNOSTICS'
      });
    }

    // 3. TESTE DE AUTENTICAÇÃO DA API
    const apiKey = process.env.TRINKS_API_KEY;
    const establishmentId = process.env.TRINKS_ESTABLISHMENT_ID;

    if (apiKey && establishmentId) {
      console.log('🔐 Testando autenticação na API Trinks...');
      try {
        const authTest = axios.create({
          timeout: 10000,
          headers: {
            'X-Api-Key': apiKey,
            'Content-Type': 'application/json'
          },
          validateStatus: (status) => status < 500 // 401/403 são esperados se auth falhar
        });

        const authStart = Date.now();
        const authResponse = await authTest.get('/v1/estabelecimentos', {
          params: { estabelecimentoId }
        });
        const authDuration = Date.now() - authStart;

        if (authResponse.status === 200) {
          diagnostics.apiAuthentication.status = 'SUCCESS';
          diagnostics.apiAuthentication.details = {
            status: authResponse.status,
            establishmentName: authResponse.data?.nome || 'Unknown',
            apiKeyValid: true,
            duration: authDuration,
            authenticatedAt: new Date().toISOString()
          };
          console.log(`✅ AUTENTICAÇÃO OK: Estabelecimento "${authResponse.data?.nome}" em ${authDuration}ms`);
        } else {
          diagnostics.apiAuthentication.status = 'FAILED';
          diagnostics.apiAuthentication.details = {
            status: authResponse.status,
            statusText: authResponse.statusText,
            apiKeyValid: false,
            error: 'Authentication failed - Invalid API key or establishment ID'
          };
          console.log(`❌ AUTENTICAÇÃO FALHOU: HTTP ${authResponse.status} - Chave API ou ID do estabelecimento inválidos`);
        }
      } catch (authError) {
        diagnostics.apiAuthentication.status = 'FAILED';
        diagnostics.apiAuthentication.details = {
          error: authError.message,
          code: authError.code,
          apiKeyValid: false
        };
        console.log(`❌ AUTENTICAÇÃO FALHOU: ${authError.message}`);

        // Log detalhado do erro de autenticação
        await errorLogger.logError(authError, ERROR_CATEGORIES.API_ERROR, {
          diagnostic: 'API_AUTHENTICATION',
          url: '/v1/estabelecimentos',
          establishmentId: establishmentId,
          hasApiKey: !!apiKey,
          phase: 'STARTUP_DIAGNOSTICS'
        });
      }
    } else {
      diagnostics.apiAuthentication.status = 'SKIPPED';
      diagnostics.apiAuthentication.details = {
        reason: 'Missing API credentials',
        hasApiKey: !!apiKey,
        hasEstablishmentId: !!establishmentId
      };
      console.log('⚠️ AUTENTICAÇÃO IGNORADA: Credenciais da API não configuradas');
    }

    // 4. INFORMAÇÕES DO SISTEMA
    diagnostics.systemInfo = {
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
      memory: {
        total: Math.round(process.memoryUsage().rss / 1024 / 1024) + 'MB',
        heap: Math.round(process.memoryUsage().heapUsed / 1024 / 1024) + 'MB'
      },
      uptime: Math.round(process.uptime()) + 's',
      timezone: process.env.TZ,
      environment: process.env.NODE_ENV || 'development'
    };

  } catch (generalError) {
    console.log(`❌ ERRO GERAL NO DIAGNÓSTICO: ${generalError.message}`);
    await errorLogger.logError(generalError, ERROR_CATEGORIES.SYSTEM_ERROR, {
      diagnostic: 'GENERAL_DIAGNOSTICS',
      phase: 'STARTUP_DIAGNOSTICS'
    });
  } finally {
    diagnostics.timing.end = Date.now();
    diagnostics.timing.duration = diagnostics.timing.end - diagnostics.timing.start;
  }

  // RESUMO DOS DIAGNÓSTICOS
  console.log('\n📊 ===== RESUMO DOS DIAGNÓSTICOS =====');
  console.log(`🌐 DNS Resolution: ${diagnostics.dns.status}`);
  console.log(`🔌 API Connectivity: ${diagnostics.apiConnection.status}`);
  console.log(`🔐 API Authentication: ${diagnostics.apiAuthentication.status}`);
  console.log(`⏱️ Total Duration: ${diagnostics.timing.duration}ms`);
  console.log(`💻 System: ${diagnostics.systemInfo.platform} ${diagnostics.systemInfo.arch} (Node ${diagnostics.systemInfo.nodeVersion})`);
  console.log(`🧠 Memory: ${diagnostics.systemInfo.memory.heap} / ${diagnostics.systemInfo.memory.total}`);

  // Determinar status geral
  const criticalFailures = [diagnostics.dns.status, diagnostics.apiConnection.status].filter(s => s === 'FAILED').length;
  const authFailure = diagnostics.apiAuthentication.status === 'FAILED';

  if (criticalFailures > 0) {
    console.log('🚨 DIAGNÓSTICO: FALHAS CRÍTICAS DETECTADAS - Sistema pode não funcionar corretamente');
  } else if (authFailure) {
    console.log('⚠️ DIAGNÓSTICO: Conectividade OK, mas autenticação falhou - Verifique credenciais da API');
  } else {
    console.log('✅ DIAGNÓSTICO: Todos os sistemas operacionais');
  }

  console.log('🔍 ===== FIM DOS DIAGNÓSTICOS =====\n');

  // Salvar diagnóstico completo em arquivo
  try {
    const fs = require('fs');
    const diagnosticFile = path.join(__dirname, 'logs', `startup-diagnostic-${Date.now()}.json`);
    await fs.promises.writeFile(diagnosticFile, JSON.stringify(diagnostics, null, 2));
    console.log(`📄 Diagnóstico salvo em: ${diagnosticFile}`);
  } catch (saveError) {
    console.log(`⚠️ Não foi possível salvar diagnóstico: ${saveError.message}`);
  }

  return diagnostics;
}

server.listen(PORT, async () => {
  const message = `🚀 Trinks IA Server v2.0 (Refactored) running on port ${PORT}`;
  const socketMessage = `📱 Socket.io integrated with State Machine`;
  const apiMessage = `🔗 API endpoints available at http://localhost:${PORT}/api`;
  
  console.log(message);
  console.log(socketMessage);
  console.log(apiMessage);
  
  logger.info(`Server started on port ${PORT}`);
  logger.info('Socket.io integrated with refactored state machine');
  logger.info('All new architecture components loaded');
  
  // RUN CONNECTIVITY DIAGNOSTICS FIRST
  try {
    await runConnectivityDiagnostics();
  } catch (diagnosticError) {
    logger.error('Connectivity diagnostics failed:', diagnosticError);
  }

  // Initialize core services
  try {
    const { cacheManager } = require('./core/cache');
    await cacheManager.initialize();
    logger.info('Cache manager initialized');
    
    const { ragManager } = require('./core/rag');
    await ragManager.initialize();
    logger.info('RAG manager initialized');
    
    logger.info('✅ All systems ready');
  } catch (error) {
    logger.error('Initialization error:', error);
  }
});

module.exports = { app, server, io, socketAdapter };