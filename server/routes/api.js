const express = require('express');
const router = express.Router();
const fs = require('fs');
const path = require('path');
const { logger } = require('../utils/logger');
const { errorLogger, ERROR_CATEGORIES } = require('../utils/ErrorLogger');

// Helper function to update .env file
function updateEnvFile(updates) {
  try {
    const envPath = path.join(__dirname, '..', '.env');
    let envContent = '';
    
    // Read current .env file
    if (fs.existsSync(envPath)) {
      envContent = fs.readFileSync(envPath, 'utf8');
    }
    
    // Parse existing variables
    const envVars = {};
    const lines = envContent.split('\n');
    
    lines.forEach(line => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          envVars[key.trim()] = valueParts.join('=').trim();
        }
      }
    });
    
    // Update with new values
    Object.keys(updates).forEach(key => {
      if (updates[key] !== undefined && updates[key] !== '') {
        envVars[key] = updates[key];
      }
    });
    
    // Rebuild .env content
    let newEnvContent = '# Trinks IA Environment Configuration\n';
    newEnvContent += 'NODE_ENV=development\n';
    newEnvContent += 'PORT=3001\n\n';
    
    newEnvContent += '# WhatsApp Configuration\n';
    newEnvContent += `WHATSAPP_REAL=${envVars.WHATSAPP_REAL || 'true'}\n`;
    newEnvContent += `WHATSAPP_FORCE_VISIBLE=${envVars.WHATSAPP_FORCE_VISIBLE || 'true'}\n`;
    newEnvContent += `WHATSAPP_HEADLESS=${envVars.WHATSAPP_HEADLESS || 'false'}\n\n`;
    
    newEnvContent += '# WAHA Configuration\n';
    newEnvContent += `WAHA_API_URL=${envVars.WAHA_API_URL || 'https://waha.acoda.com.br'}\n`;
    newEnvContent += `WAHA_API_KEY=${envVars.WAHA_API_KEY || 'flufluflu'}\n`;
    newEnvContent += `WAHA_WEBHOOK_URL=${envVars.WAHA_WEBHOOK_URL || ''}\n\n`;
    
    newEnvContent += '# API Keys (OBRIGATÓRIO: Configure com suas chaves reais)\n';
    newEnvContent += '# Para usar IA real do Claude, configure sua chave da Anthropic\n';
    newEnvContent += '# Obtenha em: https://console.anthropic.com/\n';
    newEnvContent += `ANTHROPIC_API_KEY=${envVars.ANTHROPIC_API_KEY || ''}\n\n`;
    
    newEnvContent += '# Para transcrição de áudio usando OpenAI Whisper\n';
    newEnvContent += '# Obtenha em: https://platform.openai.com/api-keys\n';
    newEnvContent += `OPENAI_API_KEY=${envVars.OPENAI_API_KEY || ''}\n\n`;
    
    newEnvContent += '# Para integração com Trinks (configuração real)\n';
    newEnvContent += `TRINKS_API_KEY=${envVars.TRINKS_API_KEY || ''}\n`;
    newEnvContent += `TRINKS_ESTABLISHMENT_ID=${envVars.TRINKS_ESTABLISHMENT_ID || '188253'}\n\n`;
    
    newEnvContent += '# Debug\n';
    newEnvContent += `DEBUG=${envVars.DEBUG || 'puppeteer:*'}\n`;
    newEnvContent += `CHROME_DEBUG=${envVars.CHROME_DEBUG || '1'}\n`;
    
    // Write updated .env file
    fs.writeFileSync(envPath, newEnvContent, 'utf8');
    
    logger.info('✅ .env file updated successfully');
    return true;
  } catch (error) {
    logger.error('❌ Error updating .env file:', error);
    return false;
  }
}

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'trinks-ia-server'
  });
});

// Placeholder for other API routes
router.get('/status', (req, res) => {
  res.json({
    status: 'running',
    environment: process.env.NODE_ENV || 'development',
    hasAnthropicKey: !!process.env.ANTHROPIC_API_KEY,
    hasTrinksKey: !!process.env.TRINKS_API_KEY
  });
});


// Debug endpoint for professional services mapping
router.get('/debug/professional-services', async (req, res) => {
  try {
    const professionalServicesMapping = require('../utils/professionalServicesMapping');
    
    // Get cache stats
    const stats = professionalServicesMapping.getCacheStats();
    
    // Get full mapping
    const mapping = await professionalServicesMapping.getMapping();
    
    // Generate prompt text for testing
    const promptText = await professionalServicesMapping.generatePromptText();
    
    res.json({
      stats: stats,
      totalProfessionals: Object.keys(mapping).length,
      professionals: Object.values(mapping).map(p => ({
        id: p.id,
        nome: p.nome,
        apelido: p.apelido,
        servicosCount: p.servicos.length,
        servicos: p.servicos.map(s => `${s.nome} (${s.duracaoEmMinutos}min - R$${s.preco})`)
      })),
      promptPreview: promptText.substring(0, 1000) + (promptText.length > 1000 ? '...' : '')
    });
    
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    res.status(500).json({
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Force update professional services cache
router.post('/debug/professional-services/update', async (req, res) => {
  try {
    const professionalServicesMapping = require('../utils/professionalServicesMapping');
    
    console.log('🔄 Forçando atualização do cache via API...');
    await professionalServicesMapping.forceUpdate();
    
    const stats = professionalServicesMapping.getCacheStats();
    
    res.json({
      message: 'Cache atualizado com sucesso',
      stats: stats
    });
    
  } catch (error) {
    console.error('Error updating cache:', error);
    res.status(500).json({
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// Test specific professional services
router.get('/debug/professional/:name/services', async (req, res) => {
  try {
    const professionalServicesMapping = require('../utils/professionalServicesMapping');
    const professionalName = req.params.name;
    
    const professional = await professionalServicesMapping.findProfessionalByName(professionalName);
    
    if (!professional) {
      return res.status(404).json({
        error: `Profissional "${professionalName}" não encontrado`,
        availableProfessionals: Object.values(await professionalServicesMapping.getMapping()).map(p => ({
          nome: p.nome,
          apelido: p.apelido
        }))
      });
    }
    
    res.json({
      professional: {
        id: professional.id,
        nome: professional.nome,
        apelido: professional.apelido,
        servicos: professional.servicos
      }
    });
    
  } catch (error) {
    console.error('Error finding professional:', error);
    res.status(500).json({
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// WebSocket connection monitoring endpoint
router.get('/debug/websocket/connections', (req, res) => {
  try {
    const io = req.app.get('io'); // Socket.io instance
    
    if (!io) {
      return res.status(500).json({
        success: false,
        error: 'Socket.io instance not found'
      });
    }
    
    const connectedClients = io.engine.clientsCount;
    const connectedSockets = io.sockets.sockets.size;
    
    // Get detailed socket info
    const socketDetails = [];
    io.sockets.sockets.forEach((socket) => {
      socketDetails.push({
        id: socket.id,
        connected: socket.connected,
        handshake: {
          address: socket.handshake.address,
          time: socket.handshake.time,
          url: socket.handshake.url,
          userAgent: socket.handshake.headers['user-agent']
        },
        rooms: Array.from(socket.rooms)
      });
    });
    
    res.json({
      success: true,
      data: {
        summary: {
          engineClients: connectedClients,
          sockets: connectedSockets,
          timestamp: new Date().toISOString()
        },
        sockets: socketDetails
      }
    });
    
  } catch (error) {
    console.error('❌ Error getting WebSocket connections:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Enhanced WebSocket cleanup and management endpoints
router.post('/debug/websocket/cleanup', (req, res) => {
  try {
    const io = req.app.get('io');
    const { force = false, maxAge = 1800000 } = req.body; // 30 minutos padrão
    
    if (!io) {
      return res.status(500).json({
        success: false,
        error: 'Socket.io instance not found'
      });
    }
    
    const beforeClients = io.engine.clientsCount;
    const beforeSockets = io.sockets.sockets.size;
    const currentTime = Date.now();
    
    let removedDisconnected = 0;
    let removedInactive = 0;
    const socketActivity = req.app.get('socketActivity') || new Map();
    
    // Force cleanup of disconnected and inactive sockets
    io.sockets.sockets.forEach((socket) => {
      const lastActivity = socketActivity.get(socket.id) || currentTime;
      const isInactive = currentTime - lastActivity > maxAge;
      
      if (!socket.connected) {
        socket.disconnect(true);
        socketActivity.delete(socket.id);
        removedDisconnected++;
      } else if (force && isInactive) {
        console.log(`🧹 Força removendo socket inativo: ${socket.id} (${Math.round((currentTime - lastActivity) / 60000)}min)`);
        socket.disconnect(true);
        socketActivity.delete(socket.id);
        removedInactive++;
      }
    });
    
    const afterClients = io.engine.clientsCount;
    const afterSockets = io.sockets.sockets.size;
    
    res.json({
      success: true,
      data: {
        before: { engineClients: beforeClients, sockets: beforeSockets },
        after: { engineClients: afterClients, sockets: afterSockets },
        removed: {
          disconnected: removedDisconnected,
          inactive: removedInactive,
          total: removedDisconnected + removedInactive
        },
        cleanup: { force, maxAgeMinutes: Math.round(maxAge / 60000) },
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('❌ Error cleaning up WebSocket connections:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Disconnect specific socket by ID
router.post('/debug/websocket/disconnect/:socketId', (req, res) => {
  try {
    const io = req.app.get('io');
    const { socketId } = req.params;
    const { reason = 'admin_disconnect' } = req.body;
    
    if (!io) {
      return res.status(500).json({ success: false, error: 'Socket.io instance not found' });
    }
    
    const socket = io.sockets.sockets.get(socketId);
    
    if (!socket) {
      return res.status(404).json({ success: false, error: 'Socket not found' });
    }
    
    socket.disconnect(true);
    console.log(`🔌 Admin disconnected socket: ${socketId} - reason: ${reason}`);
    
    res.json({
      success: true,
      data: {
        socketId,
        reason,
        wasConnected: socket.connected,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    console.error('❌ Error disconnecting socket:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Get connection statistics with IP tracking
router.get('/debug/websocket/stats', (req, res) => {
  try {
    const io = req.app.get('io');
    
    if (!io) {
      return res.status(500).json({ success: false, error: 'Socket.io instance not found' });
    }
    
    const socketActivity = req.app.get('socketActivity') || new Map();
    const currentTime = Date.now();
    const stats = {
      total: {
        engineClients: io.engine.clientsCount,
        sockets: io.sockets.sockets.size
      },
      activity: {
        active: 0,
        inactive: 0,
        disconnected: 0
      },
      connections: [],
      ips: {}
    };
    
    // Analyze each socket
    io.sockets.sockets.forEach((socket) => {
      const lastActivity = socketActivity.get(socket.id) || currentTime;
      const minutesInactive = Math.round((currentTime - lastActivity) / 60000);
      const clientIp = socket.handshake.address;
      
      // Count by IP
      stats.ips[clientIp] = (stats.ips[clientIp] || 0) + 1;
      
      if (!socket.connected) {
        stats.activity.disconnected++;
      } else if (minutesInactive > 30) {
        stats.activity.inactive++;
      } else {
        stats.activity.active++;
      }
      
      stats.connections.push({
        id: socket.id,
        connected: socket.connected,
        ip: clientIp,
        minutesInactive,
        connectTime: socket.handshake.time,
        transport: socket.conn.transport.name
      });
    });
    
    // Sort connections by activity
    stats.connections.sort((a, b) => b.minutesInactive - a.minutesInactive);
    
    res.json({ success: true, data: stats });
    
  } catch (error) {
    console.error('❌ Error getting WebSocket stats:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Settings endpoints
router.get('/settings', async (req, res) => {
  try {
    // Check WAHA health to get version info
    let wahaHealth = {
      isHealthy: false,
      version: null,
      message: 'Não verificado'
    };
    
    try {
      const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
      const provider = WhatsAppProviderFactory.getCurrentProvider();
      
      if (provider.connectionType === 'waha') {
        const healthResult = await provider.checkServerVersion();
        wahaHealth = {
          isHealthy: healthResult.success,
          version: healthResult.version,
          message: healthResult.success 
            ? `Conectado (v${healthResult.version})`
            : healthResult.error || 'Problema ao conectar'
        };
      }
    } catch (error) {
      console.log('Warning: Could not check WAHA health for settings:', error.message);
    }
    
    // Return current environment variables and settings
    const settings = {
      establishmentName: 'Salão Trinks',
      services: ['Corte Feminino', 'Corte + Escova', 'Pintura + Corte', 'Hidratação', 'Corte Masculino', 'Corte + Barba', 'Manicure', 'Pedicure'],
      professionals: ['Ana', 'Carla', 'Roberto', 'Paula'],
      workingHours: '9:00 às 18:00, Segunda a Sábado',
      customPrompt: '',
      anthropicApiKey: process.env.ANTHROPIC_API_KEY || '',
      openaiApiKey: process.env.OPENAI_API_KEY || '',
      trinksApiUrl: 'https://api.trinks.com',
      trinksApiKey: process.env.TRINKS_API_KEY || '',
      trinksEnabled: !!process.env.TRINKS_API_KEY,
      trinksEstabelecimentoId: process.env.TRINKS_ESTABLISHMENT_ID || '188253',
      operationMode: 'whatsapp',
      whatsappEnabled: true,
      whatsappType: 'waha',
      twilioAccountSid: '',
      twilioAuthToken: '',
      twilioPhoneNumber: '',
      wahaApiUrl: process.env.WAHA_API_URL || 'https://waha.acoda.com.br',
      wahaApiKey: process.env.WAHA_API_KEY || 'flufluflu',
      wahaWebhookUrl: process.env.WAHA_WEBHOOK_URL || '',
      wahaHealth: wahaHealth
    };

    res.json(settings);
  } catch (error) {
    console.error('Error loading settings:', error);
    res.status(500).json({
      error: error.message
    });
  }
});

router.post('/settings', async (req, res) => {
  try {
    const settings = req.body;
    
    // Prepare environment variables to update
    const envUpdates = {};
    
    if (settings.anthropicApiKey) {
      envUpdates.ANTHROPIC_API_KEY = settings.anthropicApiKey;
    }
    
    if (settings.openaiApiKey) {
      envUpdates.OPENAI_API_KEY = settings.openaiApiKey;
    }
    
    if (settings.trinksApiKey) {
      envUpdates.TRINKS_API_KEY = settings.trinksApiKey;
    }
    
    if (settings.trinksEstabelecimentoId) {
      envUpdates.TRINKS_ESTABLISHMENT_ID = settings.trinksEstabelecimentoId;
    }
    
    // WAHA Configuration
    if (settings.wahaApiUrl) {
      envUpdates.WAHA_API_URL = settings.wahaApiUrl;
    }
    
    if (settings.wahaApiKey) {
      envUpdates.WAHA_API_KEY = settings.wahaApiKey;
    }
    
    if (settings.wahaWebhookUrl !== undefined) {
      envUpdates.WAHA_WEBHOOK_URL = settings.wahaWebhookUrl;
    }
    
    // Update .env file first
    const envUpdated = updateEnvFile(envUpdates);
    
    if (!envUpdated) {
      throw new Error('Failed to update .env file');
    }
    
    // Update process.env for immediate effect
    Object.keys(envUpdates).forEach(key => {
      process.env[key] = envUpdates[key];
    });
    
    // Update WhatsApp service settings
    if (settings.wahaApiUrl || settings.wahaApiKey || settings.wahaWebhookUrl !== undefined) {
      const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
      const currentProvider = WhatsAppProviderFactory.getCurrentProvider();
      if (currentProvider) {
        currentProvider.updateSettings({
          apiUrl: settings.wahaApiUrl,
          apiKey: settings.wahaApiKey,
          webhookUrl: settings.wahaWebhookUrl
        });
      }
    }
    
    // Import AI service to update runtime settings
    const aiService = require('../services/ai');
    
    // Update AI service with new settings
    aiService.updateSettings({
      anthropicApiKey: settings.anthropicApiKey,
      openaiApiKey: settings.openaiApiKey,
      trinksApiKey: settings.trinksApiKey,
      trinksEnabled: settings.trinksEnabled,
      trinksEstabelecimentoId: settings.trinksEstabelecimentoId
    });

    logger.info('Settings updated successfully:', {
      hasAnthropicKey: !!settings.anthropicApiKey,
      hasOpenAIKey: !!settings.openaiApiKey,  
      hasTrinksKey: !!settings.trinksApiKey,
      trinksEnabled: settings.trinksEnabled,
      establishmentId: settings.trinksEstabelecimentoId,
      envFileUpdated: envUpdated
    });

    res.json({ 
      success: true,
      message: 'Configurações atualizadas com sucesso! As chaves foram salvas permanentemente.',
      envFileUpdated: envUpdated
    });
  } catch (error) {
    console.error('Error saving settings:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// WhatsApp API endpoints
router.get('/whatsapp/status', async (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.getCurrentProvider();
    
    const status = provider.getStatus();
    
    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    console.error('Error getting WhatsApp status:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      status: {
        isConnected: false,
        connectionStatus: 'error',
        connectionType: null,
        qrCode: null,
        phoneNumber: null
      }
    });
  }
});

router.post('/whatsapp/connect', async (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.getCurrentProvider();
    
    console.log(`🔌 Manual WhatsApp connection requested via API`);
    
    await provider.connect();
    
    const status = provider.getStatus();
    
    res.json({
      success: true,
      message: 'Conexão WhatsApp iniciada',
      status: status
    });
  } catch (error) {
    console.error('Error connecting WhatsApp:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.post('/whatsapp/disconnect', async (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.getCurrentProvider();
    
    console.log(`🔌 Manual WhatsApp disconnection requested via API`);
    
    await provider.disconnect();
    
    const status = provider.getStatus();
    
    res.json({
      success: true,
      message: 'WhatsApp desconectado',
      status: status
    });
  } catch (error) {
    console.error('Error disconnecting WhatsApp:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.get('/whatsapp/providers', (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const providers = WhatsAppProviderFactory.getAvailableProviders();
    const stats = WhatsAppProviderFactory.getProviderStats();
    
    res.json({
      success: true,
      availableProviders: providers,
      currentProvider: stats.currentProvider,
      stats: stats
    });
  } catch (error) {
    console.error('Error getting WhatsApp providers:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.post('/whatsapp/switch-provider', async (req, res) => {
  try {
    const { providerType } = req.body;
    
    if (!providerType) {
      return res.status(400).json({
        success: false,
        error: 'Provider type is required'
      });
    }
    
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.switchProvider(providerType);
    const status = provider.getStatus();
    
    res.json({
      success: true,
      message: `Switched to ${providerType} provider`,
      currentProvider: providerType,
      status: status
    });
  } catch (error) {
    console.error('Error switching WhatsApp provider:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.get('/whatsapp/webhook-status', async (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.getCurrentProvider();
    
    if (provider.connectionType !== 'waha') {
      return res.json({
        success: false,
        error: 'Current provider is not WAHA',
        status: null
      });
    }
    
    const webhookStatus = provider.getWebhookStatus();
    
    res.json({
      success: true,
      status: webhookStatus
    });
  } catch (error) {
    console.error('Error getting webhook status:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      status: null
    });
  }
});

router.post('/whatsapp/reconfigure-webhook', async (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.getCurrentProvider();
    
    if (provider.connectionType !== 'waha') {
      return res.json({
        success: false,
        error: 'Current provider is not WAHA',
      });
    }

    // Force webhook reconfiguration
    const webhookResult = await provider.setWebhook();
    
    // Get updated webhook status
    const webhookStatus = provider.getWebhookStatus();
    
    res.json({
      success: true,
      message: 'Webhook reconfigurado',
      webhookResult: webhookResult,
      status: webhookStatus
    });
  } catch (error) {
    console.error('Error reconfiguring webhook:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.post('/whatsapp/webhook-pause', async (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.getCurrentProvider();
    
    if (provider.connectionType !== 'waha') {
      return res.json({
        success: false,
        error: 'Current provider is not WAHA',
      });
    }

    const { paused } = req.body;
    
    if (typeof paused !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'Invalid paused parameter - must be boolean'
      });
    }

    // Set webhook pause state in the provider
    const result = await provider.setWebhookPaused(paused);
    
    // Get updated webhook status
    const webhookStatus = provider.getWebhookStatus();
    
    res.json({
      success: true,
      message: paused ? 'Webhook pausado' : 'Webhook reativado',
      paused: paused,
      status: webhookStatus
    });
  } catch (error) {
    console.error('Error toggling webhook pause:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

router.get('/whatsapp/waha-health', async (req, res) => {
  try {
    const WhatsAppProviderFactory = require('../services/WhatsAppProviderFactory');
    const provider = WhatsAppProviderFactory.getCurrentProvider();
    
    // Check if current provider is WAHA
    if (provider.connectionType !== 'waha') {
      return res.json({
        success: false,
        isHealthy: false,
        version: null,
        message: `Provider atual é ${provider.connectionType}, não WAHA`,
        lastChecked: new Date().toISOString()
      });
    }
    
    console.log('🔍 Checking WAHA API health via /api/server/version...');
    
    // Call WAHA's version endpoint to check connectivity
    const healthResult = await provider.checkServerVersion();
    
    res.json({
      success: healthResult.success,
      isHealthy: healthResult.success,
      version: healthResult.version || null,
      message: healthResult.success 
        ? `✅ WAHA Conectado com sucesso (v${healthResult.version})`
        : `❌ ${healthResult.error || 'Problema ao conectar com WAHA API'}`,
      lastChecked: new Date().toISOString(),
      apiUrl: provider.apiUrl,
      hasApiKey: !!provider.apiKey
    });
  } catch (error) {
    console.error('Error checking WAHA health:', error);
    res.json({
      success: false,
      isHealthy: false,
      version: null,
      message: `❌ Erro ao verificar conectividade: ${error.message}`,
      lastChecked: new Date().toISOString()
    });
  }
});

// Error logging statistics endpoint
router.get('/debug/error-stats', async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 7;
    const stats = await errorLogger.getErrorStats(days);
    
    if (stats) {
      res.json({
        success: true,
        data: stats,
        message: `Estatísticas de erro dos últimos ${days} dias`
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Não foi possível obter estatísticas de erro'
      });
    }
  } catch (error) {
    console.error('Error getting error stats:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno ao obter estatísticas',
      error: error.message
    });
  }
});

// Force error log test endpoint (development only)
router.post('/debug/test-error-log', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        success: false,
        message: 'Endpoint de teste disponível apenas em desenvolvimento'
      });
    }

    const { category = 'SYSTEM_ERROR', message = 'Teste de log de erro via API' } = req.body;
    
    const testError = new Error(message);
    const context = {
      endpoint: '/debug/test-error-log',
      method: 'POST',
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      timestamp: new Date().toISOString()
    };
    
    const result = await errorLogger.logError(testError, ERROR_CATEGORIES[category] || ERROR_CATEGORIES.SYSTEM_ERROR, context);
    
    res.json({
      success: true,
      data: result,
      message: 'Erro de teste logado com sucesso'
    });
  } catch (error) {
    console.error('Error in test error log:', error);
    res.status(500).json({
      success: false,
      message: 'Erro ao criar log de teste',
      error: error.message
    });
  }
});

module.exports = router;