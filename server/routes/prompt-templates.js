/**
 * API Routes para gerenciamento de templates de prompt
 * Permite que salões customizem o tom de voz das respostas de IA
 */

const express = require('express');
const { body, param, validationResult } = require('express-validator');
const promptTemplates = require('../config/prompt-templates');

const router = express.Router();

/**
 * GET /api/prompt-templates
 * Listar todos os templates disponíveis
 */
router.get('/', async (req, res) => {
  try {
    const templates = promptTemplates.listTemplates();
    
    res.json({
      success: true,
      templates: templates,
      metadata: {
        totalTemplates: Object.keys(templates).length,
        customizedCount: Object.values(templates).filter(t => t.isCustomized).length
      }
    });
    
  } catch (error) {
    console.error('❌ Error listing templates:', error.message);
    res.status(500).json({
      success: false,
      error: 'Erro ao listar templates'
    });
  }
});

/**
 * GET /api/prompt-templates/:type
 * Obter template específico
 */
router.get('/:type', [
  param('type').isString().trim().notEmpty().withMessage('Tipo de template é obrigatório')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { type } = req.params;
    const template = promptTemplates.getTemplate(type);
    
    if (!template) {
      return res.status(404).json({
        success: false,
        error: `Template '${type}' não encontrado`
      });
    }

    res.json({
      success: true,
      template: {
        ...template,
        type: type,
        isCustomized: !!promptTemplates.customTemplates[type]
      }
    });
    
  } catch (error) {
    console.error(`❌ Error getting template ${req.params.type}:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter template'
    });
  }
});

/**
 * PUT /api/prompt-templates/:type
 * Atualizar template customizado
 */
router.put('/:type', [
  param('type').isString().trim().notEmpty().withMessage('Tipo de template é obrigatório'),
  body('name').isString().trim().notEmpty().withMessage('Nome é obrigatório'),
  body('template').isString().trim().notEmpty().withMessage('Template é obrigatório'),
  body('description').optional().isString().trim(),
  body('variables').optional().isArray(),
  body('maxTokens').optional().isInt({ min: 50, max: 1000 }).withMessage('maxTokens deve estar entre 50-1000'),
  body('temperature').optional().isFloat({ min: 0, max: 1 }).withMessage('temperature deve estar entre 0-1')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { type } = req.params;
    const templateData = req.body;

    // Validar template
    const validation = promptTemplates.validateTemplate(templateData);
    
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        errors: validation.errors,
        warnings: validation.warnings
      });
    }

    // Atualizar template
    promptTemplates.updateCustomTemplate(type, templateData);

    res.json({
      success: true,
      message: `Template '${type}' atualizado com sucesso`,
      template: promptTemplates.getTemplate(type),
      validation: {
        warnings: validation.warnings
      }
    });
    
  } catch (error) {
    console.error(`❌ Error updating template ${req.params.type}:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Erro ao atualizar template'
    });
  }
});

/**
 * DELETE /api/prompt-templates/:type
 * Resetar template para versão padrão
 */
router.delete('/:type', [
  param('type').isString().trim().notEmpty().withMessage('Tipo de template é obrigatório')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { type } = req.params;
    
    // Verificar se template existe
    const template = promptTemplates.getTemplate(type);
    if (!template) {
      return res.status(404).json({
        success: false,
        error: `Template '${type}' não encontrado`
      });
    }

    // Resetar para padrão
    promptTemplates.resetToDefault(type);

    res.json({
      success: true,
      message: `Template '${type}' resetado para versão padrão`,
      template: promptTemplates.getTemplate(type)
    });
    
  } catch (error) {
    console.error(`❌ Error resetting template ${req.params.type}:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Erro ao resetar template'
    });
  }
});

/**
 * POST /api/prompt-templates/:type/validate
 * Validar template sem salvar
 */
router.post('/:type/validate', [
  param('type').isString().trim().notEmpty().withMessage('Tipo de template é obrigatório'),
  body('name').isString().trim().notEmpty().withMessage('Nome é obrigatório'),
  body('template').isString().trim().notEmpty().withMessage('Template é obrigatório'),
  body('description').optional().isString().trim(),
  body('variables').optional().isArray(),
  body('maxTokens').optional().isInt({ min: 50, max: 1000 }),
  body('temperature').optional().isFloat({ min: 0, max: 1 })
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const templateData = req.body;
    const validation = promptTemplates.validateTemplate(templateData);

    res.json({
      success: true,
      validation: validation,
      message: validation.isValid ? 'Template válido' : 'Template contém erros'
    });
    
  } catch (error) {
    console.error(`❌ Error validating template:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Erro ao validar template'
    });
  }
});

/**
 * POST /api/prompt-templates/:type/test
 * Testar template com dados de exemplo
 */
router.post('/:type/test', [
  param('type').isString().trim().notEmpty().withMessage('Tipo de template é obrigatório'),
  body('variables').optional().isObject().withMessage('Variables deve ser um objeto'),
  body('useCustom').optional().isBoolean().withMessage('useCustom deve ser boolean')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        errors: errors.array()
      });
    }

    const { type } = req.params;
    const { variables = {}, useCustom = true } = req.body;

    // Obter template
    const template = promptTemplates.getTemplate(type);
    if (!template) {
      return res.status(404).json({
        success: false,
        error: `Template '${type}' não encontrado`
      });
    }

    // Dados de exemplo se não fornecidos
    const defaultVariables = {
      basePrompt: 'Você é a assistente virtual do Salão Bella, especializada em atendimento caloroso e brasileiro.',
      appointmentInfo: 'corte de cabelo amanhã às 15h com a Maria',
      customerName: 'Ana Silva',
      customerType: 'returning',
      appointmentDetails: 'Corte feminino com Maria Silva em 25/08/2025 às 15:00',
      cancellationReason: 'Compromisso urgente no trabalho',
      timeContext: 'tarde'
    };

    const testVariables = { ...defaultVariables, ...variables };

    // Processar template
    const processed = promptTemplates.processTemplate(type, testVariables);

    res.json({
      success: true,
      processed: processed,
      preview: {
        prompt: processed.prompt.substring(0, 500) + (processed.prompt.length > 500 ? '...' : ''),
        fullLength: processed.prompt.length,
        settings: {
          maxTokens: processed.maxTokens,
          temperature: processed.temperature
        }
      }
    });
    
  } catch (error) {
    console.error(`❌ Error testing template ${req.params.type}:`, error.message);
    res.status(500).json({
      success: false,
      error: 'Erro ao testar template',
      details: error.message
    });
  }
});

/**
 * GET /api/prompt-templates/cache/stats
 * Obter estatísticas do cache de templates
 */
router.get('/cache/stats', async (req, res) => {
  try {
    // Se disponível, obter stats do cache
    const HumanizedResponseCache = require('../cache/HumanizedResponseCache');
    const cache = new HumanizedResponseCache();
    
    const cacheStats = cache.getStats();
    const cacheInfo = await cache.getCacheInfo();
    
    res.json({
      success: true,
      cache: {
        memory: cacheStats,
        database: cacheInfo
      },
      templates: {
        available: Object.keys(promptTemplates.listTemplates()).length,
        customized: Object.values(promptTemplates.listTemplates()).filter(t => t.isCustomized).length
      }
    });
    
  } catch (error) {
    console.error('❌ Error getting cache stats:', error.message);
    res.status(500).json({
      success: false,
      error: 'Erro ao obter estatísticas do cache'
    });
  }
});

module.exports = router;