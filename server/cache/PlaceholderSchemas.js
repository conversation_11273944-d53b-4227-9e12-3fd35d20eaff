/**
 * PlaceholderSchemas - Sistema de Esquemas de Placeholders
 * 
 * Define quais placeholders são válidos para cada tipo de resposta,
 * garantindo templates genéricos sem dados pessoais no cache.
 */

/**
 * Schema de placeholders por tipo de resposta
 * Cada tipo define quais placeholders são permitidos
 */
const PLACEHOLDER_SCHEMAS = {
  // Confirmação de cancelamento
  'cancellation_confirm': {
    required: ['serviceName', 'professionalName', 'appointmentDate', 'appointmentTime'],
    optional: ['conversationHistory', 'customerName'],
    description: 'Template para confirmação de cancelamento de agendamentos'
  },

  // Cancelamento executado com sucesso
  'cancellation_success': {
    required: ['serviceName', 'professionalName'],
    optional: ['conversationHistory', 'cancellationReason', 'customerName'],
    description: 'Template para confirmação de cancelamento executado com sucesso'
  },

  // Confirmação de agendamento
  'appointment_confirm': {
    required: ['customerName', 'service', 'professional', 'time', 'date', 'price'],
    optional: ['duration', 'location', 'notes'],
    description: 'Template para confirmação de novos agendamentos'
  },

  // Saudações personalizadas
  'greeting_personalized': {
    required: ['customerName', 'timeOfDay'],
    optional: ['lastService', 'daysSinceLastVisit', 'weather'],
    description: 'Template para saudações contextuais'
  },

  // Oferta de reagendamento
  'rescheduling_offer': {
    required: ['customerName', 'originalDate', 'originalTime', 'service'],
    optional: ['newDate', 'newTime', 'professional', 'reason'],
    description: 'Template para ofertas de reagendamento'
  },

  // Resposta sobre disponibilidade
  'availability_response': {
    required: ['professional', 'date'],
    optional: ['availableSlots', 'nextAvailable', 'customerName'],
    description: 'Template para informações de disponibilidade'
  },

  // Mensagem de boas-vindas para novos clientes
  'welcome_new_customer': {
    required: ['customerName', 'establishmentName'],
    optional: ['services', 'promotions', 'location'],
    description: 'Template para recepção de novos clientes'
  },

  // Confirmação de solicitação de agendamento
  'appointment_request_confirm': {
    required: ['customerName', 'serviceName', 'professionalName', 'formattedDate', 'formattedTime'],
    optional: ['servicePrice', 'customerType', 'duration', 'notes'],
    description: 'Template para confirmação de dados de agendamento antes da criação'
  }
};

/**
 * Descrições amigáveis dos placeholders
 */
const PLACEHOLDER_DESCRIPTIONS = {
  // Dados do cliente
  customerName: 'nome do cliente',
  
  // Dados do serviço
  service: 'nome do serviço',
  serviceName: 'nome do serviço',
  price: 'preço do serviço',
  servicePrice: 'preço do serviço',
  duration: 'duração em minutos',
  
  // Dados do profissional  
  professional: 'nome do profissional',
  professionalName: 'nome do profissional',
  
  // Dados de data/hora
  date: 'data do agendamento',
  time: 'horário do agendamento',
  formattedDate: 'data formatada do agendamento',
  formattedTime: 'horário formatado do agendamento',
  originalDate: 'data original',
  originalTime: 'horário original',
  newDate: 'nova data',
  newTime: 'novo horário',
  
  // Contexto temporal
  timeOfDay: 'período do dia (bom dia, boa tarde, etc)',
  daysSinceLastVisit: 'dias desde a última visita',
  
  // Disponibilidade
  availableSlots: 'horários disponíveis',
  nextAvailable: 'próximo horário disponível',
  
  // Outros
  lastService: 'último serviço realizado',
  reason: 'motivo da alteração',
  rescheduleOption: 'opções de reagendamento',
  location: 'localização do estabelecimento',
  establishmentName: 'nome do estabelecimento',
  services: 'lista de serviços',
  promotions: 'promoções disponíveis',
  notes: 'observações adicionais',
  weather: 'condições climáticas',
  customerType: 'tipo de cliente (new/returning)',
  
  // Cancelamento
  appointmentId: 'ID do agendamento',
  cancellationReason: 'motivo do cancelamento',
  
  // Contexto conversacional
  conversationHistory: 'histórico das últimas mensagens da conversa'
};

/**
 * Validações específicas para tipos de placeholders
 */
const PLACEHOLDER_VALIDATIONS = {
  customerName: (value) => typeof value === 'string' && value.length > 0,
  price: (value) => typeof value === 'number' || /^\d+([.,]\d{2})?$/.test(value),
  duration: (value) => typeof value === 'number' && value > 0,
  date: (value) => typeof value === 'string' && /\d{4}-\d{2}-\d{2}/.test(value),
  time: (value) => typeof value === 'string' && /\d{2}:\d{2}/.test(value),
  daysSinceLastVisit: (value) => typeof value === 'number' && value >= 0
};

class PlaceholderSchemas {
  
  /**
   * Obtém schema para um tipo de resposta
   */
  static getSchema(responseType) {
    return PLACEHOLDER_SCHEMAS[responseType] || null;
  }

  /**
   * Lista todos os tipos de resposta disponíveis
   */
  static getAvailableTypes() {
    return Object.keys(PLACEHOLDER_SCHEMAS);
  }

  /**
   * Obtém todos os placeholders (obrigatórios + opcionais) para um tipo
   */
  static getAllPlaceholders(responseType) {
    const schema = this.getSchema(responseType);
    if (!schema) return [];
    
    return [...schema.required, ...schema.optional];
  }

  /**
   * Obtém apenas placeholders obrigatórios para um tipo
   */
  static getRequiredPlaceholders(responseType) {
    const schema = this.getSchema(responseType);
    return schema ? schema.required : [];
  }

  /**
   * Obtém apenas placeholders opcionais para um tipo
   */
  static getOptionalPlaceholders(responseType) {
    const schema = this.getSchema(responseType);
    return schema ? schema.optional : [];
  }

  /**
   * Valida se um placeholder é válido para um tipo de resposta
   */
  static isValidPlaceholder(responseType, placeholder) {
    const allPlaceholders = this.getAllPlaceholders(responseType);
    return allPlaceholders.includes(placeholder);
  }

  /**
   * Obtém descrição de um placeholder
   */
  static getPlaceholderDescription(placeholder) {
    return PLACEHOLDER_DESCRIPTIONS[placeholder] || placeholder;
  }

  /**
   * Valida valor de um placeholder
   */
  static validatePlaceholderValue(placeholder, value) {
    const validator = PLACEHOLDER_VALIDATIONS[placeholder];
    if (!validator) return true; // Se não tem validador, aceita qualquer valor
    
    return validator(value);
  }

  /**
   * Gera lista de placeholders formatada para prompts da IA
   */
  static generatePlaceholderList(responseType) {
    const schema = this.getSchema(responseType);
    if (!schema) return '';

    const required = schema.required.map(p => 
      `- {${p}} para ${this.getPlaceholderDescription(p)} (obrigatório)`
    );
    
    const optional = schema.optional.map(p => 
      `- {${p}} para ${this.getPlaceholderDescription(p)} (opcional)`
    );

    return [...required, ...optional].join('\n');
  }

  /**
   * Extrai placeholders de um template usando regex
   */
  static extractPlaceholdersFromTemplate(template) {
    const regex = /\{([a-zA-Z_][a-zA-Z0-9_]*)\}/g;
    const placeholders = [];
    let match;

    while ((match = regex.exec(template)) !== null) {
      if (!placeholders.includes(match[1])) {
        placeholders.push(match[1]);
      }
    }

    return placeholders;
  }

  /**
   * Valida se um template contém apenas placeholders válidos para o tipo
   */
  static validateTemplate(template, responseType) {
    const extractedPlaceholders = this.extractPlaceholdersFromTemplate(template);
    const validPlaceholders = this.getAllPlaceholders(responseType);
    const requiredPlaceholders = this.getRequiredPlaceholders(responseType);

    const errors = [];
    const warnings = [];

    // Verificar placeholders inválidos
    for (const placeholder of extractedPlaceholders) {
      if (!validPlaceholders.includes(placeholder)) {
        errors.push(`Placeholder inválido: {${placeholder}}`);
      }
    }

    // Verificar placeholders obrigatórios ausentes
    for (const required of requiredPlaceholders) {
      if (!extractedPlaceholders.includes(required)) {
        warnings.push(`Placeholder obrigatório ausente: {${required}}`);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      extractedPlaceholders,
      validPlaceholders
    };
  }
}

module.exports = {
  PlaceholderSchemas,
  PLACEHOLDER_SCHEMAS,
  PLACEHOLDER_DESCRIPTIONS,
  PLACEHOLDER_VALIDATIONS
};