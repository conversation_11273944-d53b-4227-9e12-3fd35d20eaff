/**
 * PlaceholderProcessor - Sistema de Processamento de Placeholders
 * 
 * Responsável por:
 * - Substituir placeholders por dados reais
 * - Validar templates e contextos
 * - Gerar prompts específicos para cada tipo de resposta
 */

const { PlaceholderSchemas } = require('./PlaceholderSchemas');

class PlaceholderProcessor {
  
  /**
   * Substitui placeholders em um template com dados do contexto
   */
  static applyPlaceholders(template, context, responseType) {
    if (!template || typeof template !== 'string') {
      throw new Error('Template deve ser uma string válida');
    }

    if (!context || typeof context !== 'object') {
      throw new Error('Context deve ser um objeto válido');
    }

    // Validar se o responseType é suportado
    const schema = PlaceholderSchemas.getSchema(responseType);
    if (!schema) {
      console.warn(`Tipo de resposta não reconhecido: ${responseType}`);
      return template; // Retorna template original se tipo não conhecido
    }

    let result = template;
    const validPlaceholders = PlaceholderSchemas.getAllPlaceholders(responseType);
    const appliedPlaceholders = [];
    const missingPlaceholders = [];

    // Substituir apenas placeholders válidos para este tipo
    for (const placeholder of validPlaceholders) {
      const regex = new RegExp(`\\{${placeholder}\\}`, 'g');
      
      if (regex.test(result) && context[placeholder] !== undefined) {
        // Validar valor antes de aplicar
        if (PlaceholderSchemas.validatePlaceholderValue(placeholder, context[placeholder])) {
          result = result.replace(regex, String(context[placeholder]));
          appliedPlaceholders.push(placeholder);
        } else {
          console.warn(`Valor inválido para placeholder {${placeholder}}:`, context[placeholder]);
        }
      } else if (regex.test(result)) {
        missingPlaceholders.push(placeholder);
      }
    }

    // Log de debugging
    if (appliedPlaceholders.length > 0) {
      console.log(`✅ Placeholders aplicados para ${responseType}:`, appliedPlaceholders);
    }
    
    if (missingPlaceholders.length > 0) {
      console.warn(`⚠️ Placeholders não encontrados no contexto:`, missingPlaceholders);
    }

    return result;
  }

  /**
   * Valida se um template está correto para um tipo de resposta
   * IMPORTANTE: Permite que a IA use apenas placeholders necessários para humanização
   */
  static validateTemplate(template, responseType) {
    const validation = PlaceholderSchemas.validateTemplate(template, responseType);
    
    if (!validation.valid) {
      console.error(`❌ Template inválido para ${responseType}:`, validation.errors);
    }
    
    // Avisos sobre placeholders ausentes são apenas informativos, não bloqueiam
    if (validation.warnings.length > 0) {
      console.log(`ℹ️ Placeholders opcionais não utilizados em ${responseType}:`, validation.warnings.map(w => w.replace('Placeholder obrigatório ausente: ', '')));
    }

    return validation;
  }

  /**
   * Gera prompt para IA criar template com placeholders específicos
   */
  static generatePromptForType(responseType, additionalInstructions = '') {
    const schema = PlaceholderSchemas.getSchema(responseType);
    
    if (!schema) {
      throw new Error(`Tipo de resposta não suportado: ${responseType}`);
    }

    const placeholderList = PlaceholderSchemas.generatePlaceholderList(responseType);
    
    const prompt = `
Você é um especialista em criar templates de mensagens para salões de beleza brasileiros.

TAREFA: Criar um template para "${responseType}" (${schema.description})

REGRAS OBRIGATÓRIAS:
1. Use APENAS os placeholders listados abaixo - NUNCA dados reais
2. Mantenha tom brasileiro, caloroso e profissional
3. Use linguagem natural e fluida
4. Seja conciso mas completo
5. NUNCA use dados específicos como nomes reais, horários reais, etc.
6. FLEXIBILIDADE: Use apenas os placeholders que fazem sentido para uma mensagem humanizada
   - Não precisa usar todos os placeholders disponíveis
   - Priorize clareza e naturalidade da mensagem

PLACEHOLDERS DISPONÍVEIS:
${placeholderList}

EXEMPLO DE FORMATO ESPERADO:
"Olá {customerName}! [sua mensagem aqui com outros placeholders...]"

${additionalInstructions}

Gere apenas o template, sem explicações adicionais.
`;

    return prompt.trim();
  }

  /**
   * Verifica se um template contém dados reais (vazamento de privacidade)
   */
  static detectDataLeakage(template) {
    const suspiciousPatterns = [
      // Nomes brasileiros comuns
      /\b(João|Maria|José|Ana|Carlos|Fernando|Ágata|Lucas|Gabriel)\b/gi,
      
      // Telefones
      /\+?55\s?[\d\s\(\)-]{10,}/g,
      
      // Emails
      /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
      
      // Horários específicos (mas permite placeholders)
      /\b(?!{time})\d{1,2}:\d{2}\b/g,
      
      // Datas específicas (mas permite placeholders)  
      /\b(?!{date})\d{1,2}\/\d{1,2}\/\d{4}\b/g,
      
      // Valores monetários específicos
      /R\$\s?\d+[,.]?\d*/g,
      
      // CPFs
      /\d{3}\.\d{3}\.\d{3}-\d{2}/g
    ];

    const leaks = [];
    
    for (const pattern of suspiciousPatterns) {
      const matches = template.match(pattern);
      if (matches) {
        leaks.push(...matches);
      }
    }

    return {
      hasLeakage: leaks.length > 0,
      suspiciousData: [...new Set(leaks)] // Remove duplicatas
    };
  }

  /**
   * Limpa template removendo possíveis dados pessoais
   */
  static sanitizeTemplate(template, responseType) {
    const leakageCheck = this.detectDataLeakage(template);
    
    if (leakageCheck.hasLeakage) {
      console.warn(`⚠️ Possível vazamento de dados detectado em template ${responseType}:`, leakageCheck.suspiciousData);
      
      // Tentar substituir dados suspeitos por placeholders
      let sanitized = template;
      
      // Substituir nomes por placeholder
      sanitized = sanitized.replace(/\b(João|Maria|José|Ana|Carlos|Fernando|Ágata|Lucas|Gabriel)\b/gi, '{customerName}');
      
      // Substituir horários específicos
      sanitized = sanitized.replace(/\b\d{1,2}:\d{2}\b/g, '{time}');
      
      // Substituir datas específicas
      sanitized = sanitized.replace(/\b\d{1,2}\/\d{1,2}\/\d{4}\b/g, '{date}');
      
      return sanitized;
    }

    return template;
  }

  /**
   * Processa contexto para garantir que contém dados necessários
   */
  static validateContext(context, responseType) {
    const requiredPlaceholders = PlaceholderSchemas.getRequiredPlaceholders(responseType);
    const missing = [];
    const invalid = [];

    for (const placeholder of requiredPlaceholders) {
      if (context[placeholder] === undefined || context[placeholder] === null) {
        missing.push(placeholder);
      } else if (!PlaceholderSchemas.validatePlaceholderValue(placeholder, context[placeholder])) {
        invalid.push({
          placeholder,
          value: context[placeholder],
          expected: PlaceholderSchemas.getPlaceholderDescription(placeholder)
        });
      }
    }

    return {
      valid: missing.length === 0 && invalid.length === 0,
      missingRequired: missing,
      invalidValues: invalid
    };
  }

  /**
   * Método utilitário para debug - mostra diferenças entre template e resultado
   */
  static debugPlaceholderApplication(template, context, result, responseType) {
    console.log(`🔍 Debug Placeholder Application for ${responseType}:`);
    console.log(`📝 Original template: ${template}`);
    console.log(`📊 Context:`, context);
    console.log(`✅ Final result: ${result}`);
    
    const validation = this.validateTemplate(template, responseType);
    console.log(`🎯 Template validation:`, validation);
    
    const leakage = this.detectDataLeakage(result);
    if (leakage.hasLeakage) {
      console.warn(`⚠️ Possible data leakage in result:`, leakage.suspiciousData);
    }
  }
}

module.exports = PlaceholderProcessor;