{"services": [{"id": "11866556", "nome": "<PERSON><PERSON><PERSON>", "duracao": 15, "preco": 5, "descricao": "<PERSON><PERSON><PERSON>", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866299", "nome": "BARBA 15", "duracao": 15, "preco": 30, "descricao": "BARBA 30", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866300", "nome": "BARBA 50", "duracao": 45, "preco": 50, "descricao": "BARBA 50", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866301", "nome": "BARBA 50 JONNIER", "duracao": 45, "preco": 50, "descricao": "BARBA 50 JONNIER", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866302", "nome": "BARBA 60", "duracao": 45, "preco": 60, "descricao": "BARBA 60", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866304", "nome": "BARBA 90", "duracao": 60, "preco": 90, "descricao": "BARBA 90", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866305", "nome": "BARBA JUAREZ", "duracao": 45, "preco": 150, "descricao": "BARBA JUAREZ", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866306", "nome": "BARBA MARCELO 60", "duracao": 60, "preco": 60, "descricao": "BARBA MARCELO 60", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866307", "nome": "BARBA STYLE", "duracao": 45, "preco": 120, "descricao": "BARBA STYLE", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866296", "nome": "Camuflagem de cabelos brancos", "duracao": 60, "preco": 0, "descricao": "É uma técnica rápida e eficiente de \"apagar\" os fios brancos sem utilizar a cobertura total. Pode ser feito localizado ou em todo o cabelo.", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866308", "nome": "COLORAÇÃO  90", "duracao": 60, "preco": 90, "descricao": "COLORAÇÃO  90", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834164", "nome": "Coloração / Tonalização", "duracao": 60, "preco": 0, "descricao": "Tintura dos Cabelos com produtos do Salão de Beleza.", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866309", "nome": "CONE INDU", "duracao": 60, "preco": 150, "descricao": "CONE INDU", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866310", "nome": "CONSULTORIA DE VISAGIMO", "duracao": 60, "preco": 1500, "descricao": "CONSULTORIA DE VISAGIMO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866311", "nome": "CORTE 100", "duracao": 30, "preco": 100, "descricao": "CORTE 100", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866312", "nome": "CORTE 120", "duracao": 60, "preco": 120, "descricao": "CORTE 120", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866313", "nome": "CORTE 300", "duracao": 60, "preco": 300, "descricao": "CORTE 300", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866314", "nome": "CORTE 70", "duracao": 30, "preco": 70, "descricao": "CORTE 70", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866315", "nome": "CORTE 75", "duracao": 60, "preco": 75, "descricao": "CORTE 75", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866316", "nome": "CORTE 80", "duracao": 60, "preco": 80, "descricao": "CORTE 80", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866317", "nome": "CORTE 80 JONIER", "duracao": 30, "preco": 80, "descricao": "CORTE 80 JONIER", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866318", "nome": "CORTE 90", "duracao": 60, "preco": 90, "descricao": "CORTE 90", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834175", "nome": "Corte a Máquina", "duracao": 30, "preco": 0, "descricao": "Corte a máquina.\r\n", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866319", "nome": "CORTE DE UNHA", "duracao": 60, "preco": 15, "descricao": "CORTE DE UNHA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866320", "nome": "CORTE HAIRSTYLE", "duracao": 45, "preco": 140, "descricao": "CORTE HAIRSTYLE", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866321", "nome": "CORTE HERBSON", "duracao": 60, "preco": 50, "descricao": "CORTE HERBSON", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866322", "nome": "CORTE INICIAL JUAREZ   (CLIENTE NOVO)", "duracao": 60, "preco": 500, "descricao": "CORTE INICIAL JUAREZ   (CLIENTE NOVO)", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866323", "nome": "CORTE JUAREZ (CLIENTE ANTIGO)", "duracao": 30, "preco": 200, "descricao": "CORTE JUAREZ (CLIENTE ANTIGO)", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866324", "nome": "CORTE KLEYTON 50", "duracao": 60, "preco": 50, "descricao": "CORTE KLEYTON 50", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866325", "nome": "CORTE LEO", "duracao": 60, "preco": 130, "descricao": "CORTE LEO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866326", "nome": "CORTE MAQUINA", "duracao": 30, "preco": 70, "descricao": "CORTE MAQUINA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866327", "nome": "CORTE MARCELO 60", "duracao": 60, "preco": 60, "descricao": "CORTE MARCELO 60", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834170", "nome": "Corte Ma<PERSON>culino", "duracao": 30, "preco": 0, "descricao": "Higienização e Secagem.", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866328", "nome": "CORTE MIGUEL", "duracao": 60, "preco": 40, "descricao": "CORTE MIGUEL", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866329", "nome": "CORTE PEZINHO", "duracao": 60, "preco": 25, "descricao": "CORTE PEZINHO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866330", "nome": "CORTE UNHA", "duracao": 60, "preco": 15, "descricao": "CORTE UNHA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866331", "nome": "DEPILAÇÃO AXILAS", "duracao": 60, "preco": 40, "descricao": "DEPILAÇÃO AXILAS", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866332", "nome": "DEPILAÇÃO COSTAS 80", "duracao": 60, "preco": 80, "descricao": "DEPILAÇÃO COSTAS 80", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866333", "nome": "DEPILAÇÃO NARIZ", "duracao": 30, "preco": 35, "descricao": "DEPILAÇÃO NARIZ", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866334", "nome": "DEPILAÇÃO ORELHA", "duracao": 30, "preco": 35, "descricao": "DEPILAÇÃO ORELHA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866335", "nome": "DEPILAÇÃO PEITO", "duracao": 60, "preco": 80, "descricao": "DEPILAÇÃO PEITO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866336", "nome": "DEPILAÇAO PEITO MAQUINA", "duracao": 60, "preco": 50, "descricao": "DEPILAÇAO PEITO MAQUINA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866337", "nome": "DEPILAÇÃO PERNA INTEIRA", "duracao": 60, "preco": 80, "descricao": "DEPILAÇÃO PERNA INTEIRA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866338", "nome": "DEPILAÇÃO PERNA MEIA", "duracao": 60, "preco": 60, "descricao": "DEPILAÇÃO PERNA MEIA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866339", "nome": "DERMAPLANING", "duracao": 60, "preco": 250, "descricao": "DERMAPLANING", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866340", "nome": "DESONDULAÇAO", "duracao": 60, "preco": 110, "descricao": "DESONDULAÇAO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834229", "nome": "Drenagem Linfática", "duracao": 60, "preco": 200, "descricao": "DRENAGEM LINFÁTICA", "categoria": "Estética Corporal", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866341", "nome": "ENGRAXADA", "duracao": 60, "preco": 30, "descricao": "ENGRAXADA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866342", "nome": "ENGRAXADA 50 (HIDRATAR COURO)", "duracao": 60, "preco": 50, "descricao": "ENGRAXADA 50 (HIDRATAR COURO)", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866343", "nome": "ENVELOPAMENTO", "duracao": 60, "preco": 110, "descricao": "ENVELOPAMENTO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866344", "nome": "ESCALDA PÉS", "duracao": 60, "preco": 40, "descricao": "ESCALDA PÉS", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866345", "nome": "ESFOLIAÇÃO CAPILAR", "duracao": 15, "preco": 50, "descricao": "ESFOLIAÇÃO CAPILAR", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834173", "nome": "Hidratação", "duracao": 15, "preco": 70, "descricao": "HIDRATAÇÃO", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866346", "nome": "HIDRATAÇAO BARBA", "duracao": 60, "preco": 25, "descricao": "HIDRATAÇAO BARBA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866347", "nome": "HIDRATAÇÃO CABELO CACHEADO", "duracao": 60, "preco": 90, "descricao": "HIDRATAÇÃO CABELO CACHEADO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834176", "nome": "Hidratação com Ampolas", "duracao": 60, "preco": 0, "descricao": "Diversas marcas de produtos para você escolher.", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866348", "nome": "HIDRATAÇAO KAMILLA", "duracao": 60, "preco": 50, "descricao": "HIDRATAÇAO KAMILLA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834180", "nome": "Higienização", "duracao": 20, "preco": 0, "descricao": "<PERSON><PERSON> os cabelos.", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834181", "nome": "Higienização + Secagem", "duracao": 20, "preco": 0, "descricao": "Lavar e secar os cabelos.", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866349", "nome": "LAVAGEM ESPECIAL", "duracao": 1, "preco": 25, "descricao": "LAVAGEM ESPECIAL", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866350", "nome": "LIMPEZA BARBA", "duracao": 60, "preco": 50, "descricao": "LIMPEZA BARBA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866351", "nome": "LIMPEZA DE PELE CORPORAL", "duracao": 60, "preco": 150, "descricao": "LIMPEZA DE PELE CORPORAL", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866352", "nome": "LIMPEZA PELE", "duracao": 60, "preco": 200, "descricao": "LIMPEZA PELE", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834188", "nome": "Manicure", "duracao": 30, "preco": 35, "descricao": "MANICURE", "categoria": "Mãos e Pés", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834189", "nome": "Manicure e Pedicure", "duracao": 60, "preco": 60, "descricao": "MANICURE E PEDICURE", "categoria": "Mãos e Pés", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866353", "nome": "MANICURE E PEDICURE FEMININA", "duracao": 60, "preco": 70, "descricao": "MANICURE E PEDICURE FEMININA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834199", "nome": "Manicure e Pedicure Masculina", "duracao": 60, "preco": 0, "descricao": "Cortar as unhas das mãos e dos pés, lixar, hidratar e cutilar.\r\n", "categoria": "Mãos e Pés", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866354", "nome": "MANICURE FEMININA", "duracao": 60, "preco": 40, "descricao": "MANICURE FEMININA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834197", "nome": "Manicure Ma<PERSON>", "duracao": 30, "preco": 0, "descricao": "Cortar as unhas das mãos, lixar, hidratar e cutilar.\r\n", "categoria": "Mãos e Pés", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866298", "nome": "Mão", "duracao": 30, "preco": 150, "descricao": "Mão", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866303", "nome": "Mão + Pé", "duracao": 20, "preco": 80, "descricao": "Mão + Pé", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834214", "nome": "Maquiagem", "duracao": 60, "preco": 60, "descricao": "MAQUIAGEM", "categoria": "Maquiagem", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866355", "nome": "MASSAGEM CAPILAR", "duracao": 45, "preco": 80, "descricao": "MASSAGEM CAPILAR", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866356", "nome": "MASSAGEM CAPILAR DEMONSTRAÇAO", "duracao": 60, "preco": 1, "descricao": "MASSAGEM CAPILAR DEMONSTRAÇAO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866357", "nome": "MASSAGEM CAPILAR KAMILLA", "duracao": 15, "preco": 80, "descricao": "MASSAGEM CAPILAR KAMILLA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866358", "nome": "MASSAGEM MODELADORA FUNC.", "duracao": 60, "preco": 100, "descricao": "MASSAGEM MODELADORA FUNC.", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866359", "nome": "MASSAGEM NOS PÉS", "duracao": 30, "preco": 70, "descricao": "MASSAGEM NOS PÉS", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866360", "nome": "MASSAGEM PEDRAS QUENTES", "duracao": 60, "preco": 200, "descricao": "MASSAGEM PEDRAS QUENTES", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866361", "nome": "MASSAGEM QUICK", "duracao": 60, "preco": 100, "descricao": "MASSAGEM QUICK", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866362", "nome": "MASSAGEM RELAXANTE 120 PARA FUNCIONARIOS", "duracao": 120, "preco": 120, "descricao": "MASSAGEM RELAXANTE 120 PARA FUNCIONARIOS", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866363", "nome": "MASSAGEM RELAXANTE COM VENTOSA", "duracao": 60, "preco": 200, "descricao": "MASSAGEM RELAXANTE COM VENTOSA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866364", "nome": "MASSAGEM RELAXANTE KAMILLA 200", "duracao": 60, "preco": 200, "descricao": "MASSAGEM RELAXANTE KAMILLA 200", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866365", "nome": "MASSAGEM TERAPEUTICA KAMILLA 250", "duracao": 60, "preco": 250, "descricao": "MASSAGEM TERAPEUTICA KAMILLA 250", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866366", "nome": "MATIZAÇÃO", "duracao": 15, "preco": 70, "descricao": "MATIZAÇÃO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866367", "nome": "MICROAGULHAMENTO", "duracao": 60, "preco": 300, "descricao": "MICROAGULHAMENTO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866368", "nome": "MICROAGULHAMENTO CAPILAR", "duracao": 60, "preco": 250, "descricao": "MICROAGULHAMENTO CAPILAR", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866369", "nome": "MODELAGEM", "duracao": 15, "preco": 40, "descricao": "MODELAGEM", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866370", "nome": "MODELAGEM EM HOTEL", "duracao": 60, "preco": 60, "descricao": "MODELAGEM EM HOTEL", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866371", "nome": "NANOBARBER", "duracao": 60, "preco": 1200, "descricao": "NANOBARBER", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866372", "nome": "NEVOU", "duracao": 60, "preco": 200, "descricao": "NEVOU", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866297", "nome": "Pé", "duracao": 30, "preco": 100, "descricao": "Pé", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834190", "nome": "Pedicure", "duracao": 30, "preco": 35, "descricao": "PEDICURE", "categoria": "Mãos e Pés", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866373", "nome": "PEDICURE COM ESCALDA PÉS", "duracao": 60, "preco": 70, "descricao": "PEDICURE COM ESCALDA PÉS", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866374", "nome": "PEDICURE FEMININA", "duracao": 30, "preco": 40, "descricao": "PEDICURE FEMININA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834198", "nome": "Pedicure Masculina", "duracao": 30, "preco": 0, "descricao": "Cortar as unhas dos pés, lixar, hidratar e cutilar.\r\n", "categoria": "Mãos e Pés", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866375", "nome": "PEEELING QUIMICO", "duracao": 60, "preco": 200, "descricao": "PEEELING QUIMICO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866376", "nome": "PENTEADO 40", "duracao": 60, "preco": 40, "descricao": "PENTEADO 40", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866377", "nome": "PENTEADO THIAGUINHO 30", "duracao": 60, "preco": 30, "descricao": "PENTEADO THIAGUINHO 30", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866378", "nome": "PIGMENTAÇÃO BARBA", "duracao": 60, "preco": 30, "descricao": "PIGMENTAÇÃO BARBA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866379", "nome": "PINTURA 95", "duracao": 60, "preco": 95, "descricao": "PINTURA 95", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866380", "nome": "PLASTICA DOS PÉS COM ESCALDA PÉS", "duracao": 60, "preco": 90, "descricao": "PLASTICA DOS PÉS COM ESCALDA PÉS", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11834167", "nome": "Reconstrução Capilar", "duracao": 60, "preco": 80, "descricao": "RECONSTRUÇAO CAPILAR", "categoria": "<PERSON><PERSON><PERSON>", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866381", "nome": "RECONSTR<PERSON><PERSON><PERSON><PERSON> CAPILAR ( MICROAGULHAMENTO )", "duracao": 60, "preco": 150, "descricao": "RECONSTR<PERSON><PERSON><PERSON><PERSON> CAPILAR ( MICROAGULHAMENTO )", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866382", "nome": "REFLEXOLOGIA", "duracao": 60, "preco": 90, "descricao": "REFLEXOLOGIA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866383", "nome": "RELAXAMENTO", "duracao": 60, "preco": 110, "descricao": "RELAXAMENTO", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866384", "nome": "REVITALIZAÇAO FACIAL", "duracao": 60, "preco": 80, "descricao": "REVITALIZAÇAO FACIAL", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866385", "nome": "SELAGEM", "duracao": 60, "preco": 30, "descricao": "SELAGEM", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866386", "nome": "SELAGEM  50", "duracao": 60, "preco": 50, "descricao": "SELAGEM  50", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866387", "nome": "SELAGEM 110", "duracao": 60, "preco": 110, "descricao": "SELAGEM 110", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866388", "nome": "SELAGEM 120", "duracao": 60, "preco": 120, "descricao": "SELAGEM 120", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866389", "nome": "SELAGEM 125,00", "duracao": 60, "preco": 125, "descricao": "SELAGEM 125,00", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866390", "nome": "SELAGEM 130", "duracao": 45, "preco": 130, "descricao": "SELAGEM 130", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866391", "nome": "SELAGEM 134", "duracao": 60, "preco": 134, "descricao": "SELAGEM 134", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866392", "nome": "SELAGEM 135", "duracao": 60, "preco": 135, "descricao": "SELAGEM 135", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866393", "nome": "SELAGEM 140", "duracao": 60, "preco": 140, "descricao": "SELAGEM 140", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866394", "nome": "SELAGEM 145", "duracao": 60, "preco": 145, "descricao": "SELAGEM 145", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866395", "nome": "SELAGEM 160", "duracao": 60, "preco": 160, "descricao": "SELAGEM 160", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866396", "nome": "SELAGEM 180", "duracao": 60, "preco": 180, "descricao": "SELAGEM 180", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866397", "nome": "SELAGEM 190", "duracao": 60, "preco": 190, "descricao": "SELAGEM 190", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866398", "nome": "SELAGEM 200", "duracao": 60, "preco": 200, "descricao": "SELAGEM 200", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866399", "nome": "SELAGEM 220", "duracao": 60, "preco": 230, "descricao": "SELAGEM 220", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866400", "nome": "SELAGEM 250", "duracao": 60, "preco": 250, "descricao": "SELAGEM 250", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866401", "nome": "SELAGEM 258", "duracao": 60, "preco": 258, "descricao": "SELAGEM 258", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866402", "nome": "SELAGEM 265", "duracao": 60, "preco": 265, "descricao": "SELAGEM 265", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866403", "nome": "SELAGEM 270", "duracao": 60, "preco": 270, "descricao": "SELAGEM 270", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866404", "nome": "SELAGEM 295", "duracao": 60, "preco": 295, "descricao": "SELAGEM 295", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866405", "nome": "SELAGEM 298", "duracao": 60, "preco": 298, "descricao": "SELAGEM 298", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866406", "nome": "SELAGEM 420", "duracao": 60, "preco": 420, "descricao": "SELAGEM 420", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866407", "nome": "SELAGEM 426", "duracao": 60, "preco": 426, "descricao": "SELAGEM 426", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866408", "nome": "SELAGEM ESPECIAL 189,00", "duracao": 60, "preco": 189, "descricao": "SELAGEM ESPECIAL 189,00", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866409", "nome": "SELAGEM FRANJA", "duracao": 45, "preco": 110, "descricao": "SELAGEM FRANJA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866410", "nome": "SELAGEM NORMAL 150", "duracao": 45, "preco": 150, "descricao": "SELAGEM NORMAL 150", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866411", "nome": "SOBRANCELHA", "duracao": 30, "preco": 70, "descricao": "SOBRANCELHA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866412", "nome": "SOBRANCELHA EMILLY", "duracao": 60, "preco": 10, "descricao": "SOBRANCELHA EMILLY", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866413", "nome": "SOBRANCELHA JUAREZ", "duracao": 30, "preco": 70, "descricao": "SOBRANCELHA JUAREZ", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866414", "nome": "SOBRANCELHA NAVALHA", "duracao": 60, "preco": 35, "descricao": "SOBRANCELHA NAVALHA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866415", "nome": "SPA COM PEDICURE", "duracao": 60, "preco": 120, "descricao": "SPA COM PEDICURE", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866416", "nome": "SPA SEM PEDICURE", "duracao": 60, "preco": 90, "descricao": "SPA SEM PEDICURE", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866417", "nome": "TERAPIA CAPILAR 1 SESSÃO DERMATITE", "duracao": 60, "preco": 180, "descricao": "TERAPIA CAPILAR 1 SESSÃO DERMATITE", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866418", "nome": "TERAPIA CAPILAR 1 SESSÃO ESTIMULO E CRESCIMENTO", "duracao": 60, "preco": 180, "descricao": "TERAPIA CAPILAR 1 SESSÃO ESTIMULO E CRESCIMENTO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866419", "nome": "TERAPIA CAPILAR 1 SESSÃO OLEOSIDADE", "duracao": 60, "preco": 180, "descricao": "TERAPIA CAPILAR 1 SESSÃO OLEOSIDADE", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866420", "nome": "TERAPIA CAPILAR 1 SESSÃO QUEDA", "duracao": 60, "preco": 180, "descricao": "TERAPIA CAPILAR 1 SESSÃO QUEDA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866421", "nome": "TERAPIA CAPILAR 12 SESSÕES QUEDA E CRESCIMENTO", "duracao": 60, "preco": 1800, "descricao": "TERAPIA CAPILAR 12 SESSÕES QUEDA E CRESCIMENTO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866422", "nome": "TERAPIA CAPILAR 2 SESSÕES QUEDA E ESTIMULO", "duracao": 60, "preco": 300, "descricao": "TERAPIA CAPILAR 2 SESSÕES QUEDA E ESTIMULO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866423", "nome": "TERAPIA CAPILAR 4 SESSÕES DERMATITE", "duracao": 60, "preco": 600, "descricao": "TERAPIA CAPILAR 4 SESSÕES DERMATITE", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866424", "nome": "TERAPIA CAPILAR 4 SESSÕES ESTÍMULO E CRESCIMENTO", "duracao": 60, "preco": 600, "descricao": "TERAPIA CAPILAR 4 SESSÕES ESTÍMULO E CRESCIMENTO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866425", "nome": "TERAPIA CAPILAR 4 SESSÕES OLEOSIDADE", "duracao": 60, "preco": 600, "descricao": "TERAPIA CAPILAR 4 SESSÕES OLEOSIDADE", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866426", "nome": "TERAPIA CAPILAR 4 SESSÕES QUEDA", "duracao": 60, "preco": 600, "descricao": "TERAPIA CAPILAR 4 SESSÕES QUEDA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866427", "nome": "TINTURA", "duracao": 60, "preco": 19, "descricao": "TINTURA", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866428", "nome": "TINTURA 105", "duracao": 60, "preco": 105, "descricao": "TINTURA 105", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866429", "nome": "TINTURA COM PRODUTO DO CLIENTE", "duracao": 60, "preco": 40, "descricao": "TINTURA COM PRODUTO DO CLIENTE", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866430", "nome": "TIRA CALO", "duracao": 60, "preco": 60, "descricao": "TIRA CALO", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866431", "nome": "TONALIZAÇÃO 70", "duracao": 60, "preco": 70, "descricao": "TONALIZAÇÃO 70", "categoria": "BARBEARIA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}, {"id": "11866432", "nome": "VENTOSATERAPIA", "duracao": 60, "preco": 90, "descricao": "VENTOSATERAPIA", "categoria": "ESTÉTICA", "ativo": true, "visivel_cliente": true, "_debugInfo": {"apiUsed": true, "endpoint": "/v1/servicos", "method": "GET", "responseTime": 857, "servicesCount": 154, "totalPages": 4, "timestamp": "2025-08-28T17:49:05.889Z"}}], "lastUpdate": "2025-08-28T17:49:05.891Z", "cacheValidityMs": 14400000}