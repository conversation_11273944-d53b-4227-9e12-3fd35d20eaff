/**
 * HumanizedResponseCache - Cache Universal de Respostas Humanizadas
 * 
 * Sistema de cache inteligente que armazena respostas de IA humanizadas
 * para reduzir custos e melhorar performance, mantendo qualidade e variação.
 * 
 * Suporta múltiplos tipos de resposta:
 * - cancellation_confirm: Confirmação de cancelamento
 * - appointment_confirm: Confirmação de agendamento  
 * - rescheduling_offer: Oferta de reagendamento
 * - greeting_personalized: Saudações personalizadas
 */

const crypto = require('crypto');
const promptTemplates = require('../config/prompt-templates');
const PlaceholderProcessor = require('./PlaceholderProcessor');
const { PlaceholderSchemas } = require('./PlaceholderSchemas');
const { errorLogger, ERROR_CATEGORIES } = require('../utils/ErrorLogger');

class HumanizedResponseCache {
  constructor() {
    this.pgClient = null;
    this.isReady = false;
    this.connectionString = process.env.DATABASE_URL || 
      'postgresql://postgres.ozhjuxrdqryytkzqahaf:<EMAIL>:5432/postgres';
    this.stats = {
      hits: 0,
      misses: 0,
      generates: 0,
      errors: 0
    };
    
    // TTL padrão para diferentes tipos de resposta (em minutos)
    this.defaultTTL = {
      cancellation_confirm: 120,    // 2 horas
      appointment_confirm: 240,     // 4 horas
      rescheduling_offer: 180,      // 3 horas
      greeting_personalized: 1440,  // 24 horas
      availability_response: 30,    // 30 minutos
      default: 120                  // 2 horas padrão
    };
    
    this.initialize();
  }

  /**
   * Initialize cache system
   */
  async initialize() {
    try {
      // Verificar se pg está disponível
      let pg;
      try {
        pg = require('pg');
      } catch (pgError) {
        console.warn('⚠️ Driver PostgreSQL não encontrado - cache em memória apenas');
        this.isReady = false;
        return;
      }

      const { Client } = pg;
      this.pgClient = new Client({
        connectionString: this.connectionString,
        ssl: { rejectUnauthorized: false }
      });
      
      console.log('🐘 Conectando ao PostgreSQL...');
      await this.pgClient.connect();
      
      // Verificar e criar tabela
      await this.ensureTableExists();
      
      this.isReady = true;
      console.log('✅ HumanizedResponseCache PostgreSQL inicializado');
      
    } catch (error) {
      console.error('❌ Erro PostgreSQL:', error.message);
      console.log('⚠️ Cache funcionando sem persistência');
      this.isReady = false;
      this.pgClient = null;
    }
  }

  /**
   * Ensure cache table exists
   */
  async ensureTableExists() {
    if (!this.pgClient) return;

    try {
      const checkQuery = `
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'humanized_template_cache'
        );
      `;

      const result = await this.pgClient.query(checkQuery);
      const tableExists = result.rows[0].exists;

      if (!tableExists) {
        console.log('📋 Criando tabela humanized_template_cache...');
        
        const createQuery = `
          CREATE TABLE humanized_template_cache (
            id SERIAL PRIMARY KEY,
            cache_key VARCHAR(100) UNIQUE NOT NULL,
            response_type VARCHAR(50) NOT NULL,
            template_text TEXT NOT NULL,
            template_version INTEGER DEFAULT 1,
            placeholders_used JSONB,
            temperature DECIMAL(3,2) DEFAULT 0.7,
            max_tokens INTEGER DEFAULT 200,
            usage_count INTEGER DEFAULT 1,
            hit_rate DECIMAL(5,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT NOW(),
            updated_at TIMESTAMP DEFAULT NOW(),
            last_used_at TIMESTAMP DEFAULT NOW(),
            expires_at TIMESTAMP,
            is_validated BOOLEAN DEFAULT FALSE,
            validation_score DECIMAL(3,2),
            has_data_leakage BOOLEAN DEFAULT FALSE
          );
          
          CREATE INDEX IF NOT EXISTS idx_template_cache_response_type ON humanized_template_cache(response_type);
          CREATE INDEX IF NOT EXISTS idx_template_cache_key ON humanized_template_cache(cache_key);
          CREATE INDEX IF NOT EXISTS idx_template_cache_expires ON humanized_template_cache(expires_at);
        `;

        await this.pgClient.query(createQuery);
        console.log('✅ Tabela humanized_template_cache criada');
      }
    } catch (error) {
      console.error('❌ Erro ao criar tabela:', error.message);
    }
  }

  /**
   * Get cached response or generate new one using AI
   * @param {string} responseType - Type of response (e.g., 'cancellation_confirm')
   * @param {object} context - Context data for the response
   * @param {function|object} generatorOrOptions - Function to generate new response via AI OR options object
   * @param {number} customTTL - Custom TTL in minutes (optional)
   * @returns {Promise<string>} The humanized response text
   */
  async getOrGenerate(responseType, context, generatorOrOptions, customTTL = null) {
    const startTime = Date.now();
    
    try {
      // Check if cache is disabled via environment variable (só ativa se explicitamente false)
      if (process.env.DISABLE_RESPONSE_CACHE !== 'false') {
        console.log(`🔄 Cache desabilitado - gerando resposta direta para ${responseType} (DISABLE_RESPONSE_CACHE=${process.env.DISABLE_RESPONSE_CACHE})`);
        this.stats.misses++;
        
        // Extract generator function
        let generator;
        let templateOptions = null;
        
        if (typeof generatorOrOptions === 'function') {
          generator = generatorOrOptions;
        } else if (generatorOrOptions && typeof generatorOrOptions.generator === 'function') {
          generator = generatorOrOptions.generator;
          templateOptions = generatorOrOptions;
        } else {
          const error = new Error('Generator function or options object is required when cache is disabled');
          await errorLogger.logError(error, ERROR_CATEGORIES.CACHE_ERROR, {
            function: 'getOrGenerate',
            responseType,
            cacheDisabled: true,
            generatorType: typeof generatorOrOptions,
            hasContext: !!context
          });
          throw error;
        }
        
        // Generate response directly (bypass cache completely)
        let generatedResponse;
        if (templateOptions && templateOptions.useTemplate !== false) {
          generatedResponse = await this.generateWithTemplate(responseType, context, templateOptions, generator);
        } else {
          generatedResponse = await generator(context);
        }
        
        const responseTime = Date.now() - startTime;
        console.log(`✅ Cache DISABLED - resposta gerada diretamente (${responseTime}ms)`);
        return generatedResponse;
      }
      
      // Generate cache key
      const cacheKey = this.generateCacheKey(responseType, context);
      
      console.log(`🔍 Cache lookup: ${responseType} | Key: ${cacheKey}`);
      
      // Try to get from cache first
      if (this.isReady) {
        const cached = await this.getCached(cacheKey);
        if (cached) {
          this.stats.hits++;
          const responseTime = Date.now() - startTime;
          console.log(`✅ Cache HIT: ${responseType} (${responseTime}ms)`);
          
          // Update usage stats
          await this.updateUsageStats(cached.id);
          
          // Aplicar placeholders do template cached
          const finalResponse = PlaceholderProcessor.applyPlaceholders(cached.template_text, context, responseType);
          
          return finalResponse;
        }
      }
      
      // Cache miss - generate new response
      this.stats.misses++;
      console.log(`❌ Cache MISS: ${responseType} - generating new response...`);
      
      // Handle both function and options object
      let generator;
      let templateOptions = null;
      
      if (typeof generatorOrOptions === 'function') {
        // Legacy mode: direct generator function
        generator = generatorOrOptions;
      } else if (typeof generatorOrOptions === 'object' && generatorOrOptions !== null) {
        // New mode: options object with template support
        generator = generatorOrOptions.generator;
        templateOptions = generatorOrOptions;
      } else {
        throw new Error('Generator function or options object is required for cache miss');
      }
      
      if (typeof generator !== 'function') {
        throw new Error('Generator function is required for cache miss');
      }
      
      // Generate new response via AI (with template support)
      let generatedResponse;
      
      if (templateOptions && templateOptions.useTemplate !== false) {
        // Use template system
        generatedResponse = await this.generateWithTemplate(responseType, context, templateOptions, generator);
      } else {
        // Use direct generator with placeholder instructions
        const placeholderPrompt = PlaceholderProcessor.generatePromptForType(responseType, 
          'IMPORTANTE: Retorne apenas o template com placeholders, SEM dados reais.');
        
        // Passar prompt para o generator se ele aceitar
        if (generator.length > 1) {
          generatedResponse = await generator(context, placeholderPrompt);
        } else {
          generatedResponse = await generator(context);
        }
        
        // Validar se resposta contém placeholders ao invés de dados reais
        const leakageCheck = PlaceholderProcessor.detectDataLeakage(generatedResponse);
        if (leakageCheck.hasLeakage) {
          console.warn(`⚠️ Generator retornou dados reais ao invés de placeholders para ${responseType}`);
          generatedResponse = PlaceholderProcessor.sanitizeTemplate(generatedResponse, responseType);
        }
      }
      
      this.stats.generates++;
      
      if (!generatedResponse) {
        const error = new Error('Generator returned empty response');
        await errorLogger.logError(error, ERROR_CATEGORIES.CACHE_ERROR, {
          function: 'getOrGenerate',
          responseType,
          context,
          hasGenerator: !!generator,
          generatorType: typeof generator,
          useTemplate: templateOptions?.useTemplate
        });
        throw error;
      }
      
      // Cache the generated template (com placeholders)
      if (this.isReady) {
        const ttl = customTTL || this.defaultTTL[responseType] || this.defaultTTL.default;
        await this.setCached(cacheKey, responseType, generatedResponse, context, ttl);
      }
      
      // Aplicar placeholders ao template para retornar resposta final
      const finalResponse = PlaceholderProcessor.applyPlaceholders(generatedResponse, context, responseType);
      
      const responseTime = Date.now() - startTime;
      console.log(`✅ Generated template and applied placeholders: ${responseType} (${responseTime}ms)`);
      
      return finalResponse;
      
    } catch (error) {
      this.stats.errors++;
      console.error(`❌ Cache error for ${responseType}:`, error.message);
      
      // Fallback: try generator anyway
      if (typeof generator === 'function') {
        try {
          return await generator();
        } catch (genError) {
          console.error(`❌ Generator also failed:`, genError.message);
          throw genError;
        }
      }
      
      throw error;
    }
  }

  /**
   * Generate cache key based ONLY on response type (templates genéricos)
   * IMPORTANTE: Não inclui dados do usuário para garantir reutilização e privacidade
   * @param {string} responseType 
   * @param {object} context - Context data (IGNORADO para cache key)
   * @returns {string} Cache key
   */
  generateCacheKey(responseType, context) {
    // NOVA ABORDAGEM: Cache key baseado apenas no tipo de resposta
    // Isso permite que um template seja reutilizado para todos os usuários
    // Sem hash de contexto = sem dados pessoais no cache key
    return responseType;
  }

  /**
   * Normalize context data for consistent cache keys
   * @param {object} context 
   * @returns {object} Normalized context
   */
  normalizeContext(context) {
    const normalized = {};
    
    // Standard fields with normalization
    if (context.service) {
      normalized.service = context.service.toLowerCase().trim();
    }
    
    if (context.professional) {
      normalized.professional = context.professional.toLowerCase().trim();
    }
    
    if (context.timeContext) {
      // Normalize time context (hoje, amanhã, etc.)
      normalized.timeContext = context.timeContext.toLowerCase().trim();
    }
    
    if (context.customerType) {
      normalized.customerType = context.customerType.toLowerCase().trim();
    }
    
    // Sort keys for consistency
    const sortedKeys = Object.keys(normalized).sort();
    const sortedContext = {};
    sortedKeys.forEach(key => {
      sortedContext[key] = normalized[key];
    });
    
    return sortedContext;
  }

  /**
   * Get cached response
   * @param {string} cacheKey 
   * @returns {Promise<object|null>} Cached response or null
   */
  async getCached(cacheKey) {
    if (!this.isReady || !this.pgClient) return null;
    
    try {
      const query = `
        SELECT id, template_text, created_at, expires_at, usage_count, placeholders_used
        FROM humanized_template_cache 
        WHERE cache_key = $1
      `;

      console.log('🔍 Buscando cache key:', cacheKey);
      const result = await this.pgClient.query(query, [cacheKey]);
      console.log('📊 Registros encontrados:', result.rows.length);
      
      if (result.rows.length === 0) {
        console.log('❌ Cache MISS: Nenhum registro encontrado para', cacheKey);
        return null; // Cache miss
      }

      const cached = result.rows[0];
      
      // Check expiration in JavaScript to avoid timezone issues
      if (cached.expires_at) {
        const now = new Date();
        const expiresAt = new Date(cached.expires_at);
        console.log('🕐 Verificação de expiração:', {
          now: now.toISOString(),
          expires: expiresAt.toISOString(),
          expired: now > expiresAt
        });
        
        if (now > expiresAt) {
          console.log('⏰ Cache EXPIRED: Registro expirado para', cacheKey);
          return null; // Cache expired
        }
      }
      
      console.log('✅ Cache HIT: Registro encontrado e válido');
      console.log('   - ID:', cached.id);
      console.log('   - Template:', cached.template_text?.substring(0, 50) + '...');
      console.log('   - Raw placeholders_used:', cached.placeholders_used);

      // Parse placeholders_used safely (JSONB comes as array, JSON as string)
      let parsedPlaceholders = [];
      try {
        if (Array.isArray(cached.placeholders_used)) {
          // JSONB already parsed (correct PostgreSQL behavior)
          parsedPlaceholders = cached.placeholders_used;
        } else if (typeof cached.placeholders_used === 'string') {
          // JSON string that needs parsing
          parsedPlaceholders = JSON.parse(cached.placeholders_used);
        } else if (cached.placeholders_used) {
          console.warn('⚠️ Unexpected placeholders_used type:', typeof cached.placeholders_used);
        }
        console.log('   - Parsed placeholders:', parsedPlaceholders);
      } catch (parseError) {
        console.warn('⚠️ Erro parsing placeholders_used:', parseError.message);
        console.warn('   Raw data:', cached.placeholders_used);
        // Continue sem quebrar o sistema
      }

      // Add parsed placeholders to the result
      cached.parsed_placeholders = parsedPlaceholders;

      return cached;
      
    } catch (error) {
      console.error('❌ Error getting cached template:', error.message);
      return null;
    }
  }

  /**
   * Set cached template (com placeholders, SEM dados pessoais)
   * @param {string} cacheKey 
   * @param {string} responseType 
   * @param {string} templateText - Template com placeholders
   * @param {object} context - Context (usado para extrair placeholders)
   * @param {number} ttlMinutes 
   */
  async setCached(cacheKey, responseType, templateText, context, ttlMinutes) {
    if (!this.isReady || !this.pgClient) return;
    
    try {
      // Validar template antes de salvar
      const validation = PlaceholderProcessor.validateTemplate(templateText, responseType);
      const leakageCheck = PlaceholderProcessor.detectDataLeakage(templateText);
      
      // Sanitizar template se houver vazamento de dados
      let sanitizedTemplate = templateText;
      if (leakageCheck.hasLeakage) {
        console.warn(`⚠️ Vazamento de dados detectado em ${responseType}, sanitizando...`);
        sanitizedTemplate = PlaceholderProcessor.sanitizeTemplate(templateText, responseType);
      }
      
      // Extrair placeholders utilizados do template
      const usedPlaceholders = PlaceholderSchemas.extractPlaceholdersFromTemplate(sanitizedTemplate);
      console.log('📝 Placeholders a serem salvos:', usedPlaceholders);
      
      const expiresAt = ttlMinutes ? new Date(Date.now() + ttlMinutes * 60 * 1000) : null;

      const query = `
        INSERT INTO humanized_template_cache 
        (cache_key, response_type, template_text, placeholders_used, expires_at, has_data_leakage, is_validated)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        ON CONFLICT (cache_key) DO UPDATE SET
          template_text = EXCLUDED.template_text,
          placeholders_used = EXCLUDED.placeholders_used,
          usage_count = humanized_template_cache.usage_count + 1,
          last_used_at = NOW(),
          updated_at = NOW(),
          has_data_leakage = EXCLUDED.has_data_leakage,
          is_validated = EXCLUDED.is_validated
      `;

      console.log('💾 Salvando no PostgreSQL:', {
        cacheKey,
        responseType,
        template: sanitizedTemplate.substring(0, 50) + '...',
        placeholders: JSON.stringify(usedPlaceholders)
      });

      const result = await this.pgClient.query(query, [
        cacheKey,
        responseType,
        sanitizedTemplate,
        JSON.stringify(usedPlaceholders),
        expiresAt,
        leakageCheck.hasLeakage,
        validation.valid
      ]);
      
      console.log('✅ Template salvo no PostgreSQL:', {
        rowCount: result.rowCount,
        comando: result.command,
        cacheKey
      });
      
      console.log(`💾 Cached template: ${responseType} (expires: ${ttlMinutes ? ttlMinutes + 'min' : 'never'})`);
      
    } catch (error) {
      console.error('❌ Error caching template:', error.message);
    }
  }

  /**
   * Update usage statistics for cached response
   * @param {number} cacheId 
   */
  async updateUsageStats(cacheId) {
    if (!this.isReady || !this.pgClient) return;
    
    try {
      const query = `
        UPDATE humanized_template_cache 
        SET 
          usage_count = usage_count + 1, 
          last_used_at = NOW(),
          hit_rate = ROUND((usage_count + 1)::DECIMAL / (usage_count + 1) * 100, 2)
        WHERE id = $1
      `;

      await this.pgClient.query(query, [cacheId]);
      
    } catch (error) {
      console.warn('⚠️ Error updating usage stats:', error.message);
    }
  }

  /**
   * Generate prompt hash for invalidation when prompts change
   * @param {string} responseType 
   * @returns {string} Prompt hash
   */
  generatePromptHash(responseType) {
    // Simple hash based on response type and current date (daily rotation)
    const today = new Date().toDateString();
    return crypto
      .createHash('md5')
      .update(`${responseType}_${today}`)
      .digest('hex')
      .substring(0, 8);
  }

  /**
   * Clean expired cache entries (PostgreSQL version)
   */
  async cleanExpiredCache() {
    if (!this.isReady || !this.pgClient) return;
    
    try {
      const result = await this.pgClient.query(
        'DELETE FROM humanized_template_cache WHERE expires_at < NOW()'
      );
      
      const deletedCount = result.rowCount || 0;
      if (deletedCount > 0) {
        console.log(`🧹 Cleaned ${deletedCount} expired cache entries`);
      }
      
    } catch (error) {
      console.error('❌ Error cleaning expired cache:', error.message);
    }
  }

  /**
   * Get cache statistics
   * @returns {object} Cache stats
   */
  getStats() {
    const total = this.stats.hits + this.stats.misses;
    const hitRate = total > 0 ? (this.stats.hits / total * 100).toFixed(1) : 0;
    
    return {
      ...this.stats,
      hitRate: parseFloat(hitRate),
      isReady: this.isReady,
      totalRequests: total
    };
  }

  /**
   * Get detailed cache information from database (PostgreSQL version)
   */
  async getCacheInfo() {
    if (!this.isReady || !this.pgClient) {
      return { error: 'Cache not ready' };
    }
    
    try {
      // Get total templates
      const totalResult = await this.pgClient.query(
        'SELECT COUNT(*) as count FROM humanized_template_cache'
      );
      
      // Get active (non-expired) templates
      const activeResult = await this.pgClient.query(
        'SELECT COUNT(*) as count FROM humanized_template_cache WHERE expires_at IS NULL OR expires_at > NOW()'
      );
      
      // Group by response type
      const typeResult = await this.pgClient.query(`
        SELECT response_type, COUNT(*) as count, AVG(usage_count) as avg_usage
        FROM humanized_template_cache 
        WHERE expires_at IS NULL OR expires_at > NOW()
        GROUP BY response_type
        ORDER BY count DESC
      `);
      
      const typeStats = {};
      typeResult.rows.forEach(row => {
        typeStats[row.response_type] = {
          templates: parseInt(row.count),
          avgUsage: parseFloat(row.avg_usage).toFixed(1)
        };
      });
      
      return {
        totalEntries: parseInt(totalResult.rows[0].count),
        activeEntries: parseInt(activeResult.rows[0].count),
        typeBreakdown: typeStats,
        memoryStats: this.getStats()
      };
      
    } catch (error) {
      console.error('❌ Error getting cache info:', error.message);
      return { error: error.message };
    }
  }

  /**
   * Force cache refresh for specific type (PostgreSQL version)
   * @param {string} responseType 
   */
  async invalidateType(responseType) {
    if (!this.isReady || !this.pgClient) return;
    
    try {
      const result = await this.pgClient.query(
        'DELETE FROM humanized_template_cache WHERE response_type = $1',
        [responseType]
      );
      
      const deletedCount = result.rowCount || 0;
      console.log(`🔄 Invalidated ${deletedCount} templates for type: ${responseType}`);
      
    } catch (error) {
      console.error(`❌ Error invalidating cache type ${responseType}:`, error.message);
    }
  }

  /**
   * Generate response using template system
   * @param {string} responseType - Type of response
   * @param {object} context - Context data
   * @param {object} templateOptions - Template options
   * @param {function} generator - Fallback generator function
   * @returns {Promise<string>} Generated response
   */
  async generateWithTemplate(responseType, context, templateOptions, generator) {
    try {
      // Prepare template variables
      const templateVariables = {
        basePrompt: templateOptions.basePrompt || '',
        appointmentInfo: context.appointmentInfo || context.timeContext || '',
        appointmentDetails: context.appointmentDetails || '',
        customerName: context.customerName || '',
        customerType: context.customerType || 'returning',
        cancellationReason: context.cancellationReason || '',
        cancelledAppointment: context.cancelledAppointment || '',
        availableSlots: context.availableSlots || '',
        recentHistory: context.recentHistory || '',
        timeContext: context.timeContext || '',
        ...templateOptions.variables || {} // Allow custom variables
      };

      // Process template
      const processedTemplate = promptTemplates.processTemplate(responseType, templateVariables);
      
      console.log(`🎯 Using template: ${processedTemplate.metadata.templateName}`);
      
      // Call generator with processed template
      const response = await generator(processedTemplate);
      
      return response;
      
    } catch (templateError) {
      console.warn(`⚠️ Template error for ${responseType}:`, templateError.message);
      console.log('🔄 Falling back to direct generator...');
      
      // Fallback to direct generator
      return await generator();
    }
  }

  /**
   * Get available template types
   * @returns {object} Available templates with metadata
   */
  getAvailableTemplates() {
    return promptTemplates.listTemplates();
  }

  /**
   * Update custom template
   * @param {string} templateType - Type of template
   * @param {object} templateData - Template data
   */
  updateTemplate(templateType, templateData) {
    return promptTemplates.updateCustomTemplate(templateType, templateData);
  }

  /**
   * Reset template to default
   * @param {string} templateType - Type of template
   */
  resetTemplate(templateType) {
    return promptTemplates.resetToDefault(templateType);
  }

  /**
   * Validate template data
   * @param {object} templateData - Template data to validate
   * @returns {object} Validation result
   */
  validateTemplate(templateData) {
    return promptTemplates.validateTemplate(templateData);
  }
}

module.exports = HumanizedResponseCache;