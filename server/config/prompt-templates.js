/**
 * Sistema de Templates de Prompt Configuráveis
 * 
 * Permite que cada salão customize o tom de voz das respostas de IA
 * sem hardcode no sistema.
 */

class PromptTemplates {
  constructor() {
    // Templates padrão - podem ser sobrescritos por configuração
    this.defaultTemplates = {
      cancellation_confirm: {
        name: 'Confirmação de Cancelamento',
        description: 'Template para confirmar cancelamento de agendamento',
        template: `Contexto da conversa anterior: {conversationHistory}

Cliente quer cancelar: {serviceName} com {professionalName} em {appointmentDate} às {appointmentTime}

Responda de forma natural e humana:
1. Confirme que entendeu o pedido de cancelamento
2. Pergunte o motivo de forma casual e empática  
3. Use linguagem brasileira natural, sem formalismo
4. Máximo 1-2 frases curtas
5. Use emojis com moderação

IMPORTANTE: Seja conversacional, não robótico. Use apenas o PRIMEIRO NOME do cliente. Exemplo: "Vi que você quer cancelar a barba com o <PERSON>, <PERSON>. Me conta o que aconteceu? 😊"`,
        variables: ['conversationHistory', 'serviceName', 'professionalName', 'appointmentDate', 'appointmentTime'],
        maxTokens: 120,
        temperature: 0.6
      },

      cancellation_success: {
        name: 'Cancelamento Executado',
        description: 'Template para confirmar que cancelamento foi executado com sucesso',
        template: `Contexto da conversa: {conversationHistory}

Cancelamento executado:
- Serviço: {serviceName} 
- Profissional: {professionalName}
- Motivo: {cancellationReason}

Responda de forma natural e empática:
1. Confirme que o cancelamento foi feito
2. Seja positivo e acolhedor
3. Deixe porta aberta para reagendar
4. Use linguagem brasileira casual
5. Máximo 1-2 frases

IMPORTANTE: Seja humano, não formal. Use apenas o PRIMEIRO NOME do cliente. Exemplo: "Pronto Fernando, cancelei pra você! Se quiser remarcar depois é só chamar. 🙌"`,
        variables: ['conversationHistory', 'serviceName', 'professionalName', 'cancellationReason'],
        maxTokens: 100,
        temperature: 0.7
      },

      appointment_confirm: {
        name: 'Confirmação de Agendamento',
        description: 'Template para confirmar criação de agendamento',
        template: `Histórico das últimas mensagens:
{conversationHistory}

Cliente: {customerName} ({customerType})
Agendamento criado: {serviceName} com {professionalName} em {formattedDate} às {formattedTime}
ID do Agendamento: {appointmentId}
Valor: R$ {servicePrice}

Responda de forma natural e humana:
1. Confirme o agendamento com entusiasmo
2. Reforce os detalhes importantes (data, hora, profissional)
3. Use linguagem brasileira calorosa e acolhedora
4. MÁXIMO 1-2 frases curtas, simulando conversa humana
5. Use emojis com moderação

IMPORTANTE: Celebre o agendamento! Use apenas o PRIMEIRO NOME do cliente. Exemplo: "Pronto Fernando! Agendei sua barba com o João para sábado às 15h. Vai ficar top! 🔥"`,
        variables: ['conversationHistory', 'customerName', 'customerType', 'serviceName', 'professionalName', 'formattedDate', 'formattedTime', 'appointmentId', 'servicePrice'],
        maxTokens: 120,
        temperature: 0.6
      },

      rescheduling_offer: {
        name: 'Oferta de Reagendamento',
        description: 'Template para oferecer reagendamento após cancelamento',
        template: `{{basePrompt}}

Contexto: Cliente cancelou agendamento e informou motivo: {{cancellationReason}}
Agendamento cancelado: {{cancelledAppointment}}
Próximos horários disponíveis: {{availableSlots}}

Tarefa: Gere uma resposta empática que:
1. Demonstre compreensão pelo motivo do cancelamento
2. Ofereça reagendamento de forma natural
3. Apresente as opções de horário disponíveis
4. Mantenha tom positivo e prestativo
5. Máximo 3 frases

IMPORTANTE: Seja compreensivo e proativo em oferecer soluções.`,
        variables: ['basePrompt', 'cancellationReason', 'cancelledAppointment', 'availableSlots'],
        maxTokens: 250,
        temperature: 0.5
      },

      appointment_request_confirm: {
        name: 'Confirmação de Solicitação de Agendamento',
        description: 'Template para solicitar confirmação antes de criar agendamento',
        template: `Histórico das últimas mensagens:
{conversationHistory}

Cliente: {customerName} ({customerType})
Dados para agendamento:
- Serviço: {serviceName} (R$ {servicePrice})
- Profissional: {professionalName}
- Data: {formattedDate}
- Horário: {formattedTime}

Responda de forma natural e humana:
1. Confirme que entendeu a solicitação
2. Apresente os detalhes de forma resumida e clara
3. Peça confirmação de forma casual e amigável
4. MÁXIMO 1-2 frases curtas, simulando conversa humana
5. Use emojis com moderação

IMPORTANTE: Seja conversacional! Use apenas o PRIMEIRO NOME do cliente. Exemplo: "Perfeito Fernando! Entendi que você quer agendar barba com o João para sábado às 15h por R$ 30. Posso confirmar? ✅"`,
        variables: ['conversationHistory', 'customerName', 'customerType', 'serviceName', 'servicePrice', 'professionalName', 'formattedDate', 'formattedTime'],
        maxTokens: 150,
        temperature: 0.6
      },

      greeting_personalized: {
        name: 'Saudação Personalizada',
        description: 'Template para saudações personalizadas baseadas no histórico',
        template: `Histórico das últimas mensagens:
{conversationHistory}

Cliente: {customerName} ({customerType})
Contexto temporal: {timeContext}
Agendamentos futuros: {futureAppointments}

Responda de forma natural e humana:
1. Seja apropriada para o horário/contexto
2. Demonstre conhecimento do histórico (se cliente conhecido)
3. Mencione agendamentos futuros se houver
4. Use tom acolhedor e brasileiro
5. MÁXIMO 1-2 frases curtas, simulando conversa humana
6. Abra espaço para o cliente se expressar

IMPORTANTE: Seja genuíno e natural! Use apenas o PRIMEIRO NOME do cliente (Fernando, não Fernando Luiz). Exemplo: "Oi João! Tudo certo para sua barba amanhã às 15h? 😊"`,
        variables: ['conversationHistory', 'customerName', 'customerType', 'timeContext', 'futureAppointments'],
        maxTokens: 100,
        temperature: 0.7
      },

      availability_suggestion: {
        name: 'Sugestão de Disponibilidade Humanizada',
        description: 'Template para apresentar horários disponíveis de forma natural e humana',
        template: `Histórico das últimas mensagens:
{conversationHistory}

Dados da disponibilidade consultada:
- Data: {targetDate}
- Profissional: {professionalName}
- Horários disponíveis: {availableSlots}
- Total de slots: {totalSlots}

Responda de forma natural e humanizada:
1. JAMAIS liste todos os horários - seja seletivo e inteligente
2. Agrupe os horários por período: manhã (8h-12h), tarde (12h-18h), noite (18h+)
3. Sugira os melhores horários (início de período, horários "redondos")
4. Use linguagem natural: "de manhã", "à tarde", "final da tarde"
5. Máximo 1-2 frases curtas e diretas
6. Se tem muitos horários, mencione "várias opções" e destaque os melhores

FORMATO ESPERADO:
- Com poucos horários (≤6): "O João tem livre às 9h, 14h e 16h. Qual prefere?"
- Com muitos horários (>6): "O João está bem livre! De manhã tem 9h e 10h, à tarde 14h, 15h e 16h30. Que período combina mais?"
- Dia cheio: "O João tem várias opções amanhã: manhã a partir das 9h, tarde das 14h às 17h. Qual horário pensa?"

IMPORTANTE: Seja conversacional, não robótico! Use apenas o PRIMEIRO NOME do cliente. Transforme dados técnicos em conversa natural.`,
        variables: ['conversationHistory', 'targetDate', 'professionalName', 'availableSlots', 'totalSlots'],
        maxTokens: 150,
        temperature: 0.6
      }
    };

    // Templates customizados (carregados de configuração/banco)
    this.customTemplates = {};
    
    // Load custom templates if available
    this.loadCustomTemplates();
  }

  /**
   * Carregar templates customizados de arquivo ou banco
   */
  loadCustomTemplates() {
    try {
      // Tentar carregar de arquivo de configuração
      const fs = require('fs');
      const path = require('path');
      const customPath = path.join(__dirname, 'custom-prompt-templates.json');
      
      if (fs.existsSync(customPath)) {
        const customData = JSON.parse(fs.readFileSync(customPath, 'utf8'));
        this.customTemplates = customData;
        console.log('✅ Custom prompt templates loaded from file');
      }
    } catch (error) {
      console.log('ℹ️ No custom prompt templates found, using defaults');
    }
  }

  /**
   * Obter template por tipo
   * @param {string} templateType - Tipo do template
   * @returns {object|null} Template encontrado
   */
  getTemplate(templateType) {
    // Priorizar templates customizados
    if (this.customTemplates[templateType]) {
      return this.customTemplates[templateType];
    }
    
    // Fallback para templates padrão
    return this.defaultTemplates[templateType] || null;
  }

  /**
   * Processar template com variáveis
   * @param {string} templateType - Tipo do template
   * @param {object} variables - Variáveis para substituição
   * @returns {object} Template processado com prompt final
   */
  processTemplate(templateType, variables = {}) {
    const template = this.getTemplate(templateType);
    
    if (!template) {
      throw new Error(`Template '${templateType}' não encontrado`);
    }

    // Substituir variáveis no template
    let processedPrompt = template.template;
    
    // Substituir cada variável
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      const value = variables[key] || '';
      processedPrompt = processedPrompt.replace(new RegExp(placeholder, 'g'), value);
    });

    // Verificar se todas as variáveis foram substituídas
    const remainingVars = processedPrompt.match(/\{\{[^}]+\}\}/g);
    if (remainingVars) {
      console.warn(`⚠️ Variáveis não substituídas em template '${templateType}':`, remainingVars);
    }

    return {
      prompt: processedPrompt,
      maxTokens: template.maxTokens || 150,
      temperature: template.temperature || 0.3,
      metadata: {
        templateType,
        templateName: template.name,
        processedAt: new Date().toISOString()
      }
    };
  }

  /**
   * Atualizar template customizado
   * @param {string} templateType - Tipo do template
   * @param {object} templateData - Dados do template
   */
  updateCustomTemplate(templateType, templateData) {
    // Validar template
    if (!templateData.template || !templateData.name) {
      throw new Error('Template deve ter pelo menos "name" e "template"');
    }

    this.customTemplates[templateType] = {
      ...templateData,
      updatedAt: new Date().toISOString()
    };

    // Persistir se possível
    this.saveCustomTemplates();
    
    console.log(`✅ Template '${templateType}' atualizado`);
  }

  /**
   * Salvar templates customizados
   */
  saveCustomTemplates() {
    try {
      const fs = require('fs');
      const path = require('path');
      const customPath = path.join(__dirname, 'custom-prompt-templates.json');
      
      fs.writeFileSync(customPath, JSON.stringify(this.customTemplates, null, 2));
    } catch (error) {
      console.error('❌ Erro ao salvar templates customizados:', error.message);
    }
  }

  /**
   * Listar todos os templates disponíveis
   * @returns {object} Lista de templates com metadados
   */
  listTemplates() {
    const templates = {};
    
    // Adicionar templates padrão
    Object.keys(this.defaultTemplates).forEach(key => {
      templates[key] = {
        ...this.defaultTemplates[key],
        source: 'default',
        isCustomized: !!this.customTemplates[key]
      };
    });

    // Adicionar/sobrescrever com templates customizados
    Object.keys(this.customTemplates).forEach(key => {
      templates[key] = {
        ...this.customTemplates[key],
        source: 'custom',
        isCustomized: true
      };
    });

    return templates;
  }

  /**
   * Resetar template para versão padrão
   * @param {string} templateType - Tipo do template
   */
  resetToDefault(templateType) {
    if (this.customTemplates[templateType]) {
      delete this.customTemplates[templateType];
      this.saveCustomTemplates();
      console.log(`✅ Template '${templateType}' resetado para padrão`);
    }
  }

  /**
   * Validar template antes de salvar
   * @param {object} templateData - Dados do template
   * @returns {object} Resultado da validação
   */
  validateTemplate(templateData) {
    const errors = [];
    const warnings = [];

    // Validações obrigatórias
    if (!templateData.name) {
      errors.push('Nome do template é obrigatório');
    }

    if (!templateData.template) {
      errors.push('Conteúdo do template é obrigatório');
    }

    // Validações de formato
    if (templateData.template) {
      // Verificar se contém {{basePrompt}}
      if (!templateData.template.includes('{{basePrompt}}')) {
        warnings.push('Template não inclui {{basePrompt}} - pode não manter consistência com marca');
      }

      // Verificar variáveis declaradas vs usadas
      const usedVars = (templateData.template.match(/\{\{([^}]+)\}\}/g) || [])
        .map(v => v.replace(/[{}]/g, ''));
      
      const declaredVars = templateData.variables || [];
      
      usedVars.forEach(usedVar => {
        if (!declaredVars.includes(usedVar)) {
          warnings.push(`Variável '${usedVar}' usada mas não declarada em 'variables'`);
        }
      });
    }

    // Validar configurações de AI
    if (templateData.maxTokens && (templateData.maxTokens < 50 || templateData.maxTokens > 1000)) {
      warnings.push('maxTokens recomendado entre 50-1000');
    }

    if (templateData.temperature && (templateData.temperature < 0 || templateData.temperature > 1)) {
      errors.push('temperature deve estar entre 0 e 1');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

// Export singleton instance
module.exports = new PromptTemplates();